<template>
  <SdcCascader 
    ref="cascader" 
    :getData="getData" 
    :lang="lang" 
    :level="level" 
    :showTotal="showTotal" 
    :value.sync="internalValue" 
    :map="cascaderProps" 
    v-bind="$attrs" 
    v-on="$listeners" 
    :formatSelected="formatSelected" 
    :separator="separator" 
    class="sdc-position-cascader"
  />
</template>

<script>
  import SdcCascader from 'packages/cascader'
  import PositionService from 'api/position.service'
  export default {
    name: 'sdc-position-cascader',
    components: { SdcCascader },
    props: {
      getPositionData: Function,
      map: Object,
      props: Object,
      value: {
        require: true
      },
      includeClans: {
        type: Array,
        default: () => []
      },
      lang: {
        type: String,
        defalut: 'zh'
      },
      separator: {
        type: String,
        default: '/'
      },
      level: {
        type: Number,
        default: 3
      },
      showTotal: {
        type: Boolean,
        default: true
      }
    },
    watch: {
      includeClans: {
        handler(newVal, oldVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            this.$nextTick(() => {
              this.$refs.cascader.onloadOptions()
              this.clearSelected()
            })
          }
        },
        deep: true
      }
    },
    computed: {
      cascaderProps() {
        const config = this.props || this.map || {} // 兼容老版本的map
        return {
          multiple: config.multiple !== false, // 兼容老版本，默认true
          emitPath: !!config.emitPath,
          value: config.value || 'value',
          label: config.label || (this.lang === 'en' ? 'labelEn' : 'label'),
          children: config.children || 'children'
        }
      },
      internalValue: {
        get() {
          return this.value
        },
        set(newValue) {
          this.$emit('input', newValue)
        }
      }
    },
    methods: {
      async getData() {
        try {
          const result = this.getPositionData ? await this.getPositionData() : await PositionService.getPositionData({ includeClans: this.includeClans })
          // 内置数据源一二三级id有相同的，需要转换
          if (!this.getPositionData && !this.cascaderProps.emitPath && this.level === 3) {
            result.forEach(item => {
              item.value = `prefix0-${item.value}`
              if (item.children && item.children.length) {
                item.children.forEach(child => {
                  child.value = `prefix1-${child.value}`
                })
              }
            })
          }
          return result 
        } catch (error) {
          return [] 
        }
      },
      getCheckedNodes(leafOnly = false) {
        return this.$refs.cascader.getCheckedNodes(leafOnly)
      },
      clearSelected() {
        this.$refs.cascader.clearSelected()
      },
      formatSelected(node) {
        return {
          postFullName: node.pathLabels.join(this.separator),
          postName: node.label
        }
      }
    }
  }
</script>
