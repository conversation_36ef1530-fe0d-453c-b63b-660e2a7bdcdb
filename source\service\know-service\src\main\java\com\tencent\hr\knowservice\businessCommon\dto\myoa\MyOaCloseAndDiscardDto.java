package com.tencent.hr.knowservice.businessCommon.dto.myoa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class MyOaCloseAndDiscardDto {
    /**
     * 所属业务领域，单据会被MyOA归类到指定的领域中。可用的领域请参考下面的 业务领域分类表 一节
     */
    @NotEmpty(message = "category 不能为空")
    private String category;
    /**
     * 流程名称。标明单据所属的业务系统（流程）。比如：针对报销审批单据，它是费用系统的报销流程创建的。因此其 process_name 为 Cost/ExpenseProcess
     */
    @NotEmpty(message = "process_name 不能为空")
    @JsonProperty(value = "process_name")
    private String processName;
    /**
     * 流程实例标识。通常是其对应的业务单据的流水号
     */
    @NotEmpty(message = "process_inst_id 不能为空")
    @JsonProperty(value = "process_inst_id")
    private String processInstId;
    /**
     * 也发发生的节点。当前的审批单是报销流程中，申请人直接上级审批。那么这个字段的值即可叫做 申请人直接上级审批，或者对应的英文名称
     */
    @JsonProperty(value = "activity")
    private String activity;
    /**
     * 当前审批单据的处理人的英文名，比如：kevinbyang
     */
    private String handler;


}
