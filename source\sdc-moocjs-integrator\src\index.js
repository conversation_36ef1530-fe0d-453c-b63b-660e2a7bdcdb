
let messageCallback = null
let onloadCallback = null
let setPauseCallback = null
let setPlayCallback = null
let hideFinishStatusCallback = null
function getMessageMethods (e) {
  if (e.data.type === 'Mooc-task') {
    if (e.data.events === 'accept') {
      onloadCallback && onloadCallback(e)
    }
    if (e.data.events === 'setPause') {
      setPauseCallback && setPauseCallback(e)
    }
    if (e.data.events === 'setPlay') {
      setPlayCallback && setPlayCallback(e)
    }
    if (e.data.events === 'hideFinishStatus') {
      hideFinishStatusCallback && hideFinishStatusCallback(e)
    }
    messageCallback && messageCallback(e.data)
  }
}

let throttle = (func, wait) => {
  let previous = 0
  return function () {
    const context = this
    const args = arguments
    const now = Date.now()
    if (now - previous > wait) {
      func.apply(context, args)
      previous = now
    }
  }
}

const SDK = {
  // 通知父元素当前页面加载完成，并接收父级的回应
  onload: (callback) => {
    SDK.postMessage('connect')
    onloadCallback = callback
  },
  // 接收父级暂停视频的消息
  setPause: (callback) => {
    setPauseCallback = callback
  },
  // 接收父级开始视频的消息
  setPlay: (callback) => {
    setPlayCallback = callback
  },
  hideFinishStatus: (callback) => {
    hideFinishStatusCallback = callback
  },
  play: () => {
    SDK.postMessage('play')
  },
  pause: () => {
    SDK.postMessage('pause')
  },
  complete: (init) => {
    SDK.postMessage('complete', init)
  },
  // 异常信息发送给父级
  sendErrorInfo: (params) => {
    SDK.postMessage('sendErrorInfo', params)
  },
  // 考试系统-开始考试/开始练习
  startAnswer: () => {
    SDK.postMessage('startAnswer')
  },
  // 考试系统-结束考试/结束练习
  /* 
    params: {
      is_finished: Boolean // 是否完成考试/练习
      is_cheat: Boolean // 是否作弊
      score: number // 考试得分
      elapsed_seconds: integer // 本次学习的持续时间(秒)
    }
  */
  endAnswer: (params) => {
    SDK.postMessage('endAnswer', params)
  },
  // 查看考试详情
  answerDetail: () => {
    SDK.postMessage('answerDetail')
  },
  // 考试详情返回首页
  detailBackHome: () => {
    SDK.postMessage('detailBackHome')
  },
  // 监听鼠标移动事件，与父页面通信
  mousemove: () => {
    throttle = throttle(() => {
      SDK.postMessage('mousemove')
    }, 2000)
    document.addEventListener('mousemove', throttle)
  },
  removeMouseListener: () => {
    document.removeEventListener('mousemove', throttle)
  },
  // 所有来自iframe的消息事件
  messageListener: (callback) => {
    messageCallback = callback
  },
  // 向父级发送消息
  postMessage: (events, params, orgin = '*') => {
    window.parent.postMessage({
      type: 'Mooc-task',
      events,
      params
    }, orgin)
  },

  // 注册iframe通信监听事件
  registerMessageListener: () => {
    window.addEventListener('message', getMessageMethods)
  },
  // 移除iframe通信监听事件
  removeMessageListener: () => {
    window.removeEventListener('message', getMessageMethods)
  },

  removeEvent: () => {
    SDK.removeMouseListener()
    SDK.removeMessageListener()
  }

}

SDK.registerMessageListener()

export default SDK
