package com.tencent.hr.knowservice.courseInteraction.dto.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: vincentyqwu
 * @createDate: 2021/7/8
 * @version: 1.0
 */
@Data
@ApiModel
public class ConInteractiveConfig{

    private String courseId;

    /**
     *
     */
    private Integer actType;




}
