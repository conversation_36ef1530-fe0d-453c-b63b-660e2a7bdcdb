<template>
  <div id="app">
    <sdc-label-show-mob
      :actType="actType"
      :courseId="courseId"
      :labelNodeEnv="labelNodeEnv"
      :isH5="isH5"
      :isMock="isMock"
      :isPreview="isPreview"
      :previewLbael="previewLbael"
      @toSearchPage="toSearchPage"
    />
  </div>
</template>

<script>
export default {
  name: 'App',
  data () {
    return {
      LabelObj: {}
    }
  },
  computed: {
    actType() {
      return this.LabelObj.actType || ''
    },
    courseId() {
      return this.LabelObj.courseId || ''
    },
    labelNodeEnv() {
      return this.LabelObj.labelNodeEnv || 'production'
    },
    // 是否开启调试
    isH5() {
      return this.LabelObj.isH5 || false
    },
    isMock() { // 本地数据调试
      return this.LabelObj.isMock || false
    },
    isPreview() { // 是否是预览功能 为true时需要传previewLbael
      return this.LabelObj.isPreview || false
    },
    previewLbael() { // 是否是预览功能
      return this.LabelObj.previewLbael || []
    },
  },
  created () {
    if (typeof window !== 'undefined' && typeof window.LabelShowMob !== 'object') {
      window.LabelShowMob = {}
    }
    let parentNode = this.$root.$el.id
    if (parentNode === 'showLabel') {
      this.LabelObj = window.LabelShowMob
    } else {
      if (typeof window.LabelShowMob[parentNode] !== 'object') {
        window.LabelShowMob[parentNode] = {}
      }
      this.LabelObj = window.LabelShowMob[parentNode]
    }
  },
  methods: {
    toSearchPage(data) {
      window.LabelMobileTagsInfo = data
      var event = new Event('toSearchPage')
      window.dispatchEvent(event, data)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
