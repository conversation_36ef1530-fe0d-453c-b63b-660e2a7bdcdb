package com.tencent.hr.knowservice.framework.constant;

/**
 * 用户角色枚举
 */
public enum UserRoleEnum {

    SupperAdmin("#R_SUPManger","超级管理员"),
    //CompanyTrainingAdmin("#CompanyTrainingManager_V8","公司级培训管理员"),
    //BGTrainingAdmin("#BGTrainingManager_V8","BG级培训管理员"),
    //DeptTrainingAdmin("#BGTrainingManager_V8","部门级培训管理员"),
    CompanyTrainingAdmin("#R_Company_TrainingAdmin","公司级培训管理员"),
    BGTrainingAdmin("#R_BG_TrainingAdmin","BG级培训管理员"),
    DeptTrainingAdmin("#R_Dept_TrainingAdmin","部门级培训管理员"),

    ExamAdmin("#R_Exam_Admin","考试管理员");
    /**
     * 角色代码
     */
    private String roleCode;
    /**
     * 角色名称
     */
    private String roleName;

    UserRoleEnum(String roleCode,String roleName) {
        this.roleCode = roleCode;
        this.roleName = roleName;
    }

    public String getRoleCode() {
        return this.roleCode;
    }
    public String getRoleName() {
        return this.roleName;
    }
}
