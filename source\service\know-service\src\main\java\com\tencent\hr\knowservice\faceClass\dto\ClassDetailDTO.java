package com.tencent.hr.knowservice.faceClass.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/09/13/16:33
 * @version: 1.0
 */
@Data
public class ClassDetailDTO {

    /**
     * 班级id
     */
    private Integer classId;

    /**
     * 封面地址
     */
    private String photoUrl;

    /**
     * 授课方式 授课形式 1 线下面试 2 在线授课 3 网络研讨会
     */
    private Integer teachingType;

    /**
     * 讲师
     */
    private String teacherNames;

    /**
     * 城市
     */
    private String city;

    /**
     * 具体班级授课地址
     */
    private String location;

    /**
     * 会议号
     */
    private String meetingCode;

    /**
     * 会议地址
     */
    private String meetingUrl;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班级开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date startTime;

    /**
     * 班级结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date endTime;

    /**
     * 课程简介（移动端简介，计划弃用，后面统一使用课程简介）
     */
    private String mobileCourseDesc;

    /**
     * 课程简介
     */
    private String courseDesc;

    /**
     * 课程标签
     */
    private String labels;

    /**
     * 是否开启互动课堂
     */
    private Boolean isEnabledClassroom;

    /**
     * 互动课堂地址
     */
    private String classroomUserUrl;

    /**
     * 是否签到
     */
    private Boolean isSign;
}
