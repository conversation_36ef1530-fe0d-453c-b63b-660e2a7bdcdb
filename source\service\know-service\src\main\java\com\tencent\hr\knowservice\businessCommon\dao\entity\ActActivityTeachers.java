package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * act_activity_teachers
 * <AUTHOR>
@Data
public class ActActivityTeachers implements Serializable {
    private Integer id;

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 内部分享人id
     */
    private Integer staffId;

    /**
     * 内部讲师姓名
     */
    private String staffName;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private Integer oldSyncId;

    private static final long serialVersionUID = 1L;
}