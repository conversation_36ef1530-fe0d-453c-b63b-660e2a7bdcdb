@import "./vars.less";
@import "./svg-icon.less";
@import "./toast.less";

.sdc-selector {
  display: flex;
  &--textarea {
    height: 150px;
    flex-direction: column-reverse;
  }
  &--normal {
    align-items: center;
  }
  .textarea-bar {
    margin-bottom: 10px;
    font-size: @font-14;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    color: @color-text-black;
    .num {
      font-size: @font-14;
      color: @color-text-gray;
    }
    .el-button--mini {
      font-size: 14px;
      height: 30px;
      padding: 6px 15px;
    }
  }
  .selector-container {
    display: inline-block;
    font-size: @font-14;
    flex: 1;
    height: 40px;
    border: 1px solid @color-bd;
    background-color: @color-text-white;
    border-radius: 4px;
    box-sizing: border-box;

    &.selector-container--normal {
      width: 0;
    }

    &.is-disabled {
      background-color: @color-bg-disabled;
    }
    &--focus {
      border-right: 1px solid @color-theme !important;
      border-color: @color-theme;
      z-index: 1;
    }
    &--normal {
      padding-top: 3px;
      border-right: none;
      padding-left: 5px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &--medium {
      padding-top: 1px;
      height: 36px;
    }
    &--small {
      padding-top: 1px;
      height: 32px;
    }
    &--textarea {
      padding: 5px 10px;
      overflow: auto;
    }
    &:hover:not(.selector-container--focus) {
      border-right: 1px solid @color-text-light !important;
      border-color: @color-text-light;
      z-index: 1;
    }
    .el-loading-spinner {
      margin-top: -10px !important;
    }
    .container-inner {
      position: relative;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;
      padding-right: 1px;
      .el-input__suffix{
        display: none;
        .el-icon-circle-close{
          cursor: pointer;
          font-size: @font-16;
          color: @color-text-light;
        }
      }
      &:hover {
        overflow: visible;
        .el-input__suffix {
          display: block;
        }
      }
      .tags {
        position: absolute;
        height: 45px;
        max-width: 80%;
        white-space: nowrap;
        padding: 0 3px;
        overflow-x: auto;
        overflow-y: hidden;
        line-height: 28px;
        &--medium {
          height: 41px;
          line-height: 24px;
        }
        &--small {
          height: 37px;
          line-height: 20px;
        }
      }
      .el-tag {
        font-size: 14px;
      }
      .tag {
        margin: 2px 6px 2px 0;
        cursor: pointer;
        &.el-tag.el-tag--info {
          background-color: @color-bg-disabled;
          border-color: #eee;
          color: @color-text-black;
        }
      }
      .el-input__inner {
        border: none;
        font-size: 14px;
        padding: 0 0 0 5px;
        .el-input__icon {
          transition: all .3s;
        }
      }
      .el-input__inner:focus + .el-input__suffix{
        display: block;
      }
      .el-autocomplete {
        flex-grow: 1;
        line-height: 24px;
      }
    }
  }
  .suffix-open {
    display: inline-block;
    margin-left: -1px;
    line-height: 40px;
    height: 40px;
    &--medium {
      height: 36px;
      line-height: 36px;
    }
    &--small {
      height: 32px;
      line-height: 32px;
    }
    .el-button {
      padding: 11px 10px;
      &--medium {
        height: 36px;
        padding: 9px 9px;
      }
      &--small {
        height: 32px;
        padding: 7px 8px;
      }
      height: 40px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .suffix-num {
    font-size: @font-14;
    color: @color-text-gray;
    margin-left: 9px;
    white-space: nowrap;
  }
}
.el-autocomplete-suggestion {
  li.highlighted,
  li:hover {
    .item-name,
    .item-count {
      color: @color-theme;
    }
  }
  .dropdown--empty {
    text-align: center;
    color: @color-text-gray;
  }
}
.selector-modal, .selector-modal-append-to-body{
  .sdc-modal .modal-dialog .modal-body {
    padding-left: 12px;
  }
  .el-tree-node__content {
    height: 42px;
  }
  .side {
    box-sizing: border-box;
    width: 50%;
    margin: 10px 0;
  }
  .left-side {
    .side();
    float: left;
    border-right: 1px solid #eee;
    padding-right: 4px;
    overflow: auto;
  }
  .right-side {
    .side();
    float: right;
    padding-left: 20px;
  }
  .tree-list {
    height: 450px;
    >.el-tree {
      display: inline-block;
      min-width: 100%;
    }
    .tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: @font-14;
      .el-checkbox {
        margin-right: 10px;
        .el-checkbox__inner {
          width: 16px;
          height: 16px;
          border-color: #ccc;
          &:after {
            top: 2px;
            left: 5px;
          }
        }
      }
      .tree-node-avatar {
        width: 25px;
        border-radius: 50%;
        margin-right: 8px;
      }
      .tree-node-text {
        vertical-align: middle;
        padding-right: 5px;
      }
    }
  }
  .selected-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: @color-text-light;
    height: 40px;
    font-size: @font-14;
    line-height: 40px;
    .el-icon-delete{
      margin-right: 10px;
      color: @color-text-light;
      font-size: @font-16;
      cursor: pointer;
    }
  }
  .selected-list {
    height: 360px;
    overflow: auto;
    .list-item {
      color: @color-text-black;
      min-height: 42px;
      display: flex;
      align-items: center;
      font-size: @font-14;
      padding: 5px 5px;
      justify-content: space-between;
      &:hover {
        background-color: @color-bg-light;
      }
      .list-item-avatar {
        width: 25px;
        border-radius: 50%;
        margin-right: 8px;
      }
      .list-item-name {
        line-height: 20px;
        margin-right: 20px;
        vertical-align: middle;
      }
      .list-item-icon {
        color: @color-text-gray;
        cursor: pointer;
        margin-right: 3px;
        i.el-icon-error {
          font-size: @font-18;
        }
      }
    }
  }
  .modal-buttons {
    padding: 5px 0;
    margin-top: 15px;
    text-align: right;
    .el-button {
      width: 80px;
      &:first-child {
        margin-right: 10px;
      }
    }
  }
}
.selector-modal-append-to-body{
  &.sdc-modal .modal-dialog .modal-body {
    padding-left: 12px;
  }
}