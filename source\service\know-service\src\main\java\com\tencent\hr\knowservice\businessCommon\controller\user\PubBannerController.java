package com.tencent.hr.knowservice.businessCommon.controller.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubBanner;
import com.tencent.hr.knowservice.businessCommon.service.PubBannerService;
import com.tencent.hr.knowservice.framework.annotation.AuthorityAudited;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.tencent.hr.knowservice.framework.constant.UserRoleEnum.SupperAdmin;

/**
 * 通用的Banner用户端接口
 */
@Slf4j
@RestController
@RequestMapping("/api/business-common/user/banner/")
public class PubBannerController {
    @Autowired
    PubBannerService bannerService;


    @GetMapping("get-banner-list")
    public TransDTO<Object> getBannersInfos(@RequestParam(value = "banner_name", required = false) String bannerName ,
                                            @RequestParam(value = "status", required = false) Integer status) {
        List<PubBanner> pubBanners = bannerService.getBannersInfos(bannerName,status);
        return new TransDTO<Object>().withCode(HttpStatus.OK.value()).withSuccess(true).withData(pubBanners);
    }




}
