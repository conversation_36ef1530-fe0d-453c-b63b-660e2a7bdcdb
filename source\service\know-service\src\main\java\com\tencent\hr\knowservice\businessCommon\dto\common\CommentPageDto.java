package com.tencent.hr.knowservice.businessCommon.dto.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

@Data
public class CommentPageDto<T> extends Page<T> {

    /**
     * 总评数
     */
    private long commentCount;

    /**
     * 是否有隐藏评论
     */
    private Boolean hasShow;

    /**
     * 管理员信息
     */
    private List<ActAuthorDto> authorList;

    /**
     * 管理员id
     */
    private List<String> authorId;

    /**
     * 管理员英文名
     */
    private List<String> authorName;
    /**
     * 所评论的课程名称
     */
    private String name;

    public CommentPageDto(long current, long size, boolean isSearchCount) {
        super(current, size, isSearchCount);
    }
}
