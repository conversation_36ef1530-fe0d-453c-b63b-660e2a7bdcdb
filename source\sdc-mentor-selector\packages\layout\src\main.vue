<template>
  <sdc-container custom-class="sdc-layout">
    <sdc-header :scope="scope" :layout="layoutConfig" :menus="navbarMenus" :avatar="avatarMenus" :navData="navData">
      <slot name="header-logo" slot="logo" v-if="has('logo', 'layoutConfig')"/>
      <slot name="header-menus" slot="menus" v-if="has('menus', 'layoutConfig')"/>
      <slot name="header-links" slot="links" v-if="has('links', 'layoutConfig')"/>
      <slot name="header-icons" slot="icons" v-if="has('icons', 'layoutConfig')"/>
      <slot name="header-avatar" slot="avatar" v-if="has('avatar', 'layoutConfig')"/>
      <slot v-if="empty"/>
    </sdc-header>
    <sdc-content :scope="scope" :layout="contentLayout" :menus="sidebarMenus" @toggleCollapse="handleToggleCollapse" :sidebarCollapse="sidebarCollapse" @menuContextmenu="handleMenuContextmenu">
      <slot name="content-sidebar" slot="sidebar"/>
      <slot name="content-main" slot="main"/>
    </sdc-content>
  </sdc-container>
</template>

<script>
  import { oneOf } from 'sdc-webui/src/utils/main'
  import { layout } from 'mixins'
  import SdcContainer from 'packages/container'
  import SdcHeader from 'packages/header'
  import SdcContent from 'packages/content'

  export default {
    name: 'sdc-layout',
    mixins: [layout],
    provide() {
      return {
        openeds: this.openeds || []
      }
    },
    props: {
      scope: {
        type: String,
        default: 'oa',
        validator(val) {
          return val && oneOf(val, ['oa', 'oc'], 'scope')
        }
      },
      headerLayout: {
        type: Array,
        default: () => []
      },
      contentLayout: {
        type: Array,
        default: () => ['sidebar']
      },
      navbarMenus: {
        type: Object,
        default: () => ({ active: '', map: {}, data: [], maxMenuCount: 6, adaptive: false })
      },
      avatarMenus: {
        type: Object,
        default: () => ({ url: '', avatarUrl: '', defaultUrl: '', map: {}, data: [] })
      },
      sidebarMenus: {
        type: Object,
        default: () => ({ active: '', homeLink: '', defaultOpeneds: [], homeText: '', map: {}, data: [] })
      },
      sidebarCollapse: {
        type: Boolean,
        default: false
      },
      openeds: {
        type: Array,
        default: () => {
          return []
        }
      },
      navData: {
        type: Object,
        default() {
          return null
        }
      }
    },
    computed: {
      layoutConfig() {
        if (this.headerLayout && this.headerLayout.length > 0) {
          return this.headerLayout
        }
        let defaultLayout = []
        if (this.scope === 'oa') {
          defaultLayout = ['logo', 'search', 'feedback', 'links']
        } else if (this.scope === 'oc') {
          defaultLayout = ['logo', 'menus', 'icons', 'avatar']
        }
        return defaultLayout
      }
    },
    created() {
      this.$bus.$on('search', keyword => this.$emit('search', keyword))
      this.$bus.$on('avatar', type => this.$emit('avatar', type))
    },
    components: {
      SdcContainer,
      SdcHeader,
      SdcContent
    },
    methods: {
      handleToggleCollapse(collapse) {
        // 继续向上传递
        this.$emit('toggleCollapse', collapse)
      },
      changeCollapse(collapse) {
        this.$bus.$emit('changeCollapse', collapse)
      },
      handleMenuContextmenu(event, menu) {
        // 继续向上传递
        this.$emit('menuContextmenu', event, menu)
      }
    }
  }
</script>
