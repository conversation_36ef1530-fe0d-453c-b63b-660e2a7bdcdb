package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActCourseSummary;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ActCourseSummaryMapper extends BaseMapper<ActCourseSummary> {
    int deleteByPrimaryKey(Integer courseId);

    int insert(ActCourseSummary record);

    int insertSelective(ActCourseSummary record);

    ActCourseSummary selectByPrimaryKey(Integer courseId);

    int updateByPrimaryKeySelective(ActCourseSummary record);

    int updateByPrimaryKey(ActCourseSummary record);
}