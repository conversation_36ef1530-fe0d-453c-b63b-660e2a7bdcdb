package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActClassTeachers;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActClassTeachersMapper extends BaseMapper<ActClassTeachers> {
    int deleteByPrimaryKey(Integer id);

    int insert(ActClassTeachers record);

    int insertSelective(ActClassTeachers record);

    ActClassTeachers selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ActClassTeachers record);

    int updateByPrimaryKey(ActClassTeachers record);

    List<ActClassTeachers> findClassInnerTeachers(@Param("classId") Integer classId);
}