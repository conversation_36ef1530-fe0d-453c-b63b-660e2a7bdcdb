export default [
    {
        label_id: 152241,
        label_name: "vincentyqwu官方标签1",
        label_type: "1",
        creator_id: "68317",
        creator_name: "vincentyqwu",
        join_type: 3,
        category_id: 8,
        category_name: "T族",
        category_full_name: "通道职位/T族",
        parent_id: 1,
        full_path: ",1,8,",
        order_no: 1
    },
    {
        label_id: 152257,
        label_name: "55",
        label_type: "2",
        creator_id: "68317",
        creator_name: "vincentyqwu",
        join_type: 3,
        category_id: 32,
        category_name: "自定义",
        category_full_name: "其他/自定义",
        parent_id: 31,
        full_path: ",31,32,",
        order_no: 2
    },
    {
        label_id: 151969,
        label_name: "dlp",
        label_type: "3",
        creator_id: "68317",
        creator_name: "vincentyqwu",
        join_type: 3,
        category_id: 32,
        category_name: "自定义",
        category_full_name: "其他/自定义",
        parent_id: 31,
        full_path: ",31,32,",
        order_no: 2
    },
    {
        label_id: 140547,
        label_name: "数据治理",
        label_type: "1",
        creator_id: "24673",
        creator_name: "happyhu",
        join_type: 1,
        category_id: 76,
        category_name: "研发",
        category_full_name: "技术/研发",
        parent_id: 49,
        full_path: ",49,76,",
        order_no: 3
    },
    {
        label_id: 136714,
        label_name: "风险预判(风险预控)",
        label_type: "1",
        creator_id: "24673",
        creator_name: "happyhu",
        join_type: 1,
        category_id: 38,
        category_name: "明方向",
        category_full_name: "管理技能/明方向",
        parent_id: 37,
        full_path: ",37,38,",
        order_no: 3
    },
    {
        label_id: 4,
        label_name: "测试开发",
        label_type: "1",
        creator_id: "24673",
        creator_name: "happyhu",
        join_type: 1,
        category_id: 8,
        category_name: "T族",
        category_full_name: "通道职位/T族",
        parent_id: 1,
        full_path: ",1,8,",
        order_no: 3
    },
    {
        label_id: 3,
        label_name: "应用开发",
        label_type: "1",
        creator_id: "24673",
        creator_name: "happyhu",
        join_type: 1,
        category_id: 8,
        category_name: "T族",
        category_full_name: "通道职位/T族",
        parent_id: 1,
        full_path: ",1,8,",
        order_no: 3
    },
    {
        label_id: 148289,
        label_name: "的往期多无群111",
        label_type: "2",
        creator_id: "24673",
        creator_name: "happyhu",
        join_type: 1,
        category_id: 32,
        category_name: "自定义",
        category_full_name: "其他/自定义",
        parent_id: 31,
        full_path: ",31,32,",
        order_no: 4
    }
]