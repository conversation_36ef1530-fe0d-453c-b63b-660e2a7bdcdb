<template>
  <SdcCascader ref="cascader" :getData="getData" :lang="lang" :value.sync="internalValue" :level="level" :map="cascaderProps" v-bind="$attrs" v-on="$listeners" :formatSelected="formatSelected" :separator="separator" class="sdc-area-selector"/>
</template>

<script>
  import SdcCascader from 'packages/cascader'
  import CoreService from 'api/core.service'
  export default {
    name: 'sdc-area-selector',
    components: { SdcCascader },
    props: {
      getLocationList: [Promise, Function],
      map: Object,
      value: {
        require: true
      },
      includeRegionList: {
        type: Array,
        default: () => [100, 200, 300, 400, 500]
      },
      includeCountryList: {
        type: Array,
        default: () => []
      },
      lang: {
        type: String,
        defalut: 'zh'
      },
      separator: {
        type: String,
        default: '/'
      },
      level: {
        type: Number,
        default: 3
      }
    },
    watch: {
      includeRegionList: {
        handler(newVal, oldVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            this.$nextTick(() => {
              this.$refs.cascader.onloadOptions()
            })
          }
        },
        deep: true
      },
      includeCountryList: {
        handler(newVal, oldVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            this.$nextTick(() => {
              this.$refs.cascader.onloadOptions()
              this.clearSelected()
            })
          }
        },
        deep: true
      }
    },
    computed: {
      cascaderProps() {
        const config = this.map || {}
        return {
          multiple: !!config.multiple,
          emitPath: !!config.emitPath,
          value: config.value || 'item_id',
          label: config.label || (this.lang === 'en' ? 'item_name_en' : 'item_name_cn'),
          children: config.children || 'children'
        }
      },
      internalValue: {
        get() {
          return this.value
        },
        set(newValue) {
          this.$emit('input', newValue)
        }
      }
    },
    methods: {
      async getData() {
        try {
          const result = this.getLocationList ? ((typeof this.getLocationList === 'function') ? await this.getLocationList() : await this.getLocationList) : await CoreService.getLocationList({ includeRegionList: this.includeRegionList, includeCountryList: this.includeCountryList })
          // 内置数据源一二三级id有相同的，需要转换
          if (!this.getLocationList && !this.cascaderProps.emitPath && this.level === 3) {
            result.forEach(item => {
              item.item_id = `prefix0-${item.item_id}`
              if (item.children && item.children.length) {
                item.children.forEach(child => {
                  child.item_id = `prefix1-${child.item_id}`
                })
              }
            })
          }
          return result 
        } catch (error) {
          return [] 
        }
      },
      getCheckedNodes(leafOnly = false) {
        return this.$refs.cascader.getCheckedNodes(leafOnly)
      },
      clearSelected() {
        this.$refs.cascader.clearSelected()
      },
      formatSelected(node) {
        return {
          postFullName: node.pathLabels.join(this.separator)
        }
      }
    }
  }
</script>
