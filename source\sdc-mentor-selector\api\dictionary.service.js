import { DataType, DataStorage, STORAGE_TYPE } from 'sdc-core'
import CoreService from './core.service'
export default class DictionaryService {
  static getData(dictionaryIdList) {
    const params = {
      queryCondition: {
        argMap: {
          dictionaryIdList: [dictionaryIdList] // 需要查询的字典
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-dictionary-item-realtime/sdc-webui/data', { params })
  }

  static getList(type) {
    type = DictionaryService.getType(type)
    const cacheKey = `sdc:ui-dict-list-${type}`
    const hasCache = DataStorage.contains(cacheKey, STORAGE_TYPE.Session)
    const promise = hasCache ? Promise.resolve(DataStorage.get(cacheKey, { storageType: STORAGE_TYPE.Session })) : DictionaryService.getData(type)
    return promise.then(res => {
      let result = {
        Dictionary: {
          Items: []
        }
      }
      if (!hasCache) {
        result.Dictionary.Items = res.content.map(item => {
          return {
            label: item.itemNameCn,
            value: item.itemId
          }
        })
      } else {
        result = res
      }
      !hasCache && result && DataStorage.set(cacheKey, result, { storageType: STORAGE_TYPE.Session })
      return result.Dictionary.Items
    })
  }

  static getType(type) {
    if (DataType.isNumber(type)) return type
    if (DataType.isString(type)) {
      return isNaN(type * 1) ? type : type * 1
    }
  }
}
