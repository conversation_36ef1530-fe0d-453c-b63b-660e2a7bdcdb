package com.tencent.hr.knowservice.businessCommon.dto.xapi;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 调用规则引擎返回的数据结构
 */
@Data
public class RuleRetData {

    // 员工ID
    private Integer staffId;

    // 员工姓名
    private String staffName;

    // 账户类型
    private String acctTypeCode;

    // 子账户类型
    private String subAcctTypeCode;

    // 授予积分
    private BigDecimal creditPoint;

    // 用户类型
    private String actorType;

    // 备注
    private String remark;

    // 应用编码
    private String appCode;

    // 当日限额
    private BigDecimal pointLimitDaily;

    // 当周限额
    private BigDecimal pointLimitWeekly;

    // 当月限额
    private BigDecimal pointLimitMonthly;

    // 年限额
    private BigDecimal pointLimitYearly;

    // 单次限额
    private BigDecimal pointLimitSingle;

    // 到期时间
    private Date expireTime;

    // 有效期限 单位日
    private Long expirePeriod;

    // 额外积分
    private BigDecimal extPoint;

    // 额外积分说明
    private String extPointRemark;

    // 首次行为积分
    private BigDecimal firstTimePoint;

    // 首次行为编码
    private String firstTimeCode;

    // 首次行为说明
    private String firstTimePointRemark;

    // 业务编码
    private String busiCode;

    // 行为
    private String verbName;

    // 行为id
    private String verbId;

    private Boolean objectTypeDimension;

    private Boolean verbIdDimension;

    public RuleRetData() {
        this.creditPoint = BigDecimal.ZERO;
        this.pointLimitSingle = BigDecimal.ZERO;
        this.pointLimitDaily = BigDecimal.ZERO;
        this.pointLimitWeekly = BigDecimal.ZERO;
        this.pointLimitMonthly = BigDecimal.ZERO;
        this.pointLimitYearly = BigDecimal.ZERO;
        this.extPoint = BigDecimal.ZERO;
        this.extPointRemark = "";
        this.firstTimePoint = BigDecimal.ZERO;
        this.firstTimeCode = "";
        this.firstTimePointRemark = "";
        this.acctTypeCode = "common";
        this.remark = "";
        this.appCode = "qlearning";
    }

}
