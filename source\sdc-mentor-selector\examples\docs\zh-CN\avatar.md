## Avatar 头像
> 贡献者：cxyxhhuang(黄鑫杰)；最近更新时间：2021-01-25

用户头像，支持单独使用

### 基础用法

默认头像，有名字

:::demo
```html
<template>
  <sdc-avatar name="test"/>
</template>
```
:::

使用默认插槽也可以添加名字

:::demo
```html
<template>
  <sdc-avatar>cxyxhhuang</sdc-avatar>
</template>
```
:::

带下拉选项的头像

:::demo 若想给下拉选项添加点击事件，可以给data子项添加type属性，然后绑定click事件，点击之后会调用handleClick事件并传递type属性，比如下面的退出子项
```html
<template>
  <sdc-avatar :avatar="avatar" name="miraclehe" @click="handleClick"/>
</template>
<script>
  export default {
    data() {
      return {
        avatar: {
          url: 'http://hrstaff.oa.com/hr/HRStaff', // 点击头像跳转地址
          avatarUrl: 'examples/assets/img/avatar.png', // 显示的头像URL
          defaultUrl: '', // 头像加载失败显示图片的URL
          map: {
            url: 'link',
            text: 'name'
          },
          data: [
            { name: '个人信息', link: 'http://hrstaff.oa.com/hr/HRStaff', type: 'info' },
            { name: '个人空间', link: 'http://hrstaff.oa.com/hr/HRStaff', disabled: true }, // disabled禁用某一项
            { name: '退出', divided: true, type: 'exit' } // 需要分隔一下，添加type属性
          ]
        }
      }
    },
    methods: {
      handleClick(type) {
        console.log(type)
      }
    }
  }
</script>
```
:::

### Avatar Attributes
| 参数            | 说明                     | 类型    | 可选值                                             | 默认值                                                                                          |
| --------------- | ------------------------ | ------- | -------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| avatar     | 头像菜单配置 | object  | —                                                  | —


### Avatar Events
| 事件名称       | 说明                             | 回调参数          |
| -------------- | -------------------------------- | ----------------- |
| click         | 点击下拉子项触发           | data里填入的type属性 |