package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 音视频文件详细信息
 * @TableName pub_file_info_detail
 */
@TableName(value ="pub_file_info_detail")
@Data
public class PubFileInfoDetail implements Serializable {
    /**
     * 素材id
     */
    @TableId
    private Integer fileId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 视频编码名称
     */
    private String videoCodecName;

    /**
     * 视频宽度
     */
    private String width;

    /**
     * 视频高度
     */
    private String height;

    /**
     * 分辨率
     */
    private String displayRatio;

    /**
     * 音视频时长
     */
    private Double duration;

    /**
     * 视频的比特率
     */
    private String videoBitRate;

    /**
     * 音频编码名称
     */
    private String audioCodecName;

    /**
     * 音频码率
     */
    private String audioSampleRate;

    /**
     * 音频的比特率
     */
    private String audioBitRate;

    /**
     * 文件详细信息
     */
    private String fileInfo;

    @TableField(exist = false)
    private String contentId;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PubFileInfoDetail other = (PubFileInfoDetail) that;
        return (this.getFileId() == null ? other.getFileId() == null : this.getFileId().equals(other.getFileId()))
            && (this.getFilePath() == null ? other.getFilePath() == null : this.getFilePath().equals(other.getFilePath()))
            && (this.getVideoCodecName() == null ? other.getVideoCodecName() == null : this.getVideoCodecName().equals(other.getVideoCodecName()))
            && (this.getWidth() == null ? other.getWidth() == null : this.getWidth().equals(other.getWidth()))
            && (this.getHeight() == null ? other.getHeight() == null : this.getHeight().equals(other.getHeight()))
            && (this.getDisplayRatio() == null ? other.getDisplayRatio() == null : this.getDisplayRatio().equals(other.getDisplayRatio()))
            && (this.getDuration() == null ? other.getDuration() == null : this.getDuration().equals(other.getDuration()))
            && (this.getVideoBitRate() == null ? other.getVideoBitRate() == null : this.getVideoBitRate().equals(other.getVideoBitRate()))
            && (this.getAudioCodecName() == null ? other.getAudioCodecName() == null : this.getAudioCodecName().equals(other.getAudioCodecName()))
            && (this.getAudioSampleRate() == null ? other.getAudioSampleRate() == null : this.getAudioSampleRate().equals(other.getAudioSampleRate()))
            && (this.getAudioBitRate() == null ? other.getAudioBitRate() == null : this.getAudioBitRate().equals(other.getAudioBitRate()))
            && (this.getFileInfo() == null ? other.getFileInfo() == null : this.getFileInfo().equals(other.getFileInfo()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
            && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdateId() == null ? other.getUpdateId() == null : this.getUpdateId().equals(other.getUpdateId()))
            && (this.getUpdateName() == null ? other.getUpdateName() == null : this.getUpdateName().equals(other.getUpdateName()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getFileId() == null) ? 0 : getFileId().hashCode());
        result = prime * result + ((getFilePath() == null) ? 0 : getFilePath().hashCode());
        result = prime * result + ((getVideoCodecName() == null) ? 0 : getVideoCodecName().hashCode());
        result = prime * result + ((getWidth() == null) ? 0 : getWidth().hashCode());
        result = prime * result + ((getHeight() == null) ? 0 : getHeight().hashCode());
        result = prime * result + ((getDisplayRatio() == null) ? 0 : getDisplayRatio().hashCode());
        result = prime * result + ((getDuration() == null) ? 0 : getDuration().hashCode());
        result = prime * result + ((getVideoBitRate() == null) ? 0 : getVideoBitRate().hashCode());
        result = prime * result + ((getAudioCodecName() == null) ? 0 : getAudioCodecName().hashCode());
        result = prime * result + ((getAudioSampleRate() == null) ? 0 : getAudioSampleRate().hashCode());
        result = prime * result + ((getAudioBitRate() == null) ? 0 : getAudioBitRate().hashCode());
        result = prime * result + ((getFileInfo() == null) ? 0 : getFileInfo().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdateId() == null) ? 0 : getUpdateId().hashCode());
        result = prime * result + ((getUpdateName() == null) ? 0 : getUpdateName().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fileId=").append(fileId);
        sb.append(", filePath=").append(filePath);
        sb.append(", videoCodecName=").append(videoCodecName);
        sb.append(", width=").append(width);
        sb.append(", height=").append(height);
        sb.append(", displayRatio=").append(displayRatio);
        sb.append(", duration=").append(duration);
        sb.append(", videoBitRate=").append(videoBitRate);
        sb.append(", audioCodecName=").append(audioCodecName);
        sb.append(", audioSampleRate=").append(audioSampleRate);
        sb.append(", audioBitRate=").append(audioBitRate);
        sb.append(", fileInfo=").append(fileInfo);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updateId=").append(updateId);
        sb.append(", updateName=").append(updateName);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}