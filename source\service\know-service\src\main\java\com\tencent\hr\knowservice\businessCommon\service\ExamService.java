/*package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.dto.exam.ExamSearchDto;
import com.tencent.hr.knowservice.businessCommon.proxy.ExamServiceApi;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExamService {
    @Autowired
    ExamServiceApi examServiceApi;

    *//**
     * 获取考试列表
     * @param searchDto
     * @return
     *//*
    public TransDTO getExamList(ExamSearchDto searchDto) {
        try {
            TransDTO result = examServiceApi.getExamList(searchDto);
            if (!result.getSuccess() || result.getCode() != HttpStatus.SC_OK) {
                log.error("获取考试列表失败,msg={}", result.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error("获取考试列表失败!", e);
        }
        return null;
    }

    *//**
     * 获取练习列表
     * @param searchDto
     * @return
     *//*
    public TransDTO getPracticeList(ExamSearchDto searchDto) {
        try {
            TransDTO result = examServiceApi.getPracticeList(searchDto);
            if (!result.getSuccess() || result.getCode() != HttpStatus.SC_OK) {
                log.error("获取练习列表失败,msg={}", result.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error("获取练习列表失败!", e);
        }
        return null;
    }

    *//**
     * 获取考试结果
     *
     * @param examId     考试主键id
     * @param staffNames 考生姓名,多人用;分隔
     * @param startTime  考生考试时间前置区间
     * @param endTime    考生考试前置区间
     * @return
     *//*
    public TransDTO getExamResultList(String examId,String staffNames, String startTime,String endTime) {
        try {
            TransDTO result = examServiceApi.getExamResultList(examId,staffNames,startTime,endTime);
            if (!result.getSuccess() || result.getCode() != HttpStatus.SC_OK) {
                log.error("获取考试结果失败,msg={}", result.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error("获取考试结果失败!", e);
        }
        return null;
    }*/


    package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.dto.exam.ExamSearchDto;
import com.tencent.hr.knowservice.businessCommon.proxy.ExamServiceApi;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
    @Service
    public class ExamService {
    @Value("${extapi.exam.host}")
    private String examHost;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${com.appSetting.tenantCode}")
    private String tenantCode;

    @Autowired
    ExamServiceApi examServiceApi;

    /**
     * 获取考试列表
     *
     * @param searchDto
     * @return
     */
    public TransDTO getExamList(ExamSearchDto searchDto) {
        String result;
        String path = "/api/v1/extapi/exam/exam/list";

        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        String jsonData = JsonUtil.toJson(searchDto);
        result = HttpUtil.sendPostByRestTemplate(examHost, path, jsonData, header, errorMsg);
        TransDTO dto = toTransDTO(result, errorMsg);
        return dto;
    }

    /**
     * 获取考试信息
     * @param examId
     * @return
     */
    public TransDTO getExamInfo(String examId) {
        String result;
        String path = "/api/v1/extapi/exam/" + examId;
        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(examHost, path, header, errorMsg);
        TransDTO dto = toTransDTO(result, errorMsg);
        return dto;
    }

    /**
     * 获取练习列表
     *
     * @param searchDto
     * @return
     */
    public TransDTO getPracticeList(ExamSearchDto searchDto) {

        String result;
        String path = "/api/v1/extapi/practice/list";

        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        String jsonData = JsonUtil.toJson(searchDto);
        result = HttpUtil.sendPostByRestTemplate(examHost, path, jsonData, header, errorMsg);
        TransDTO dto = toTransDTO(result, errorMsg);
        return dto;
    }

    /**
     * 获取考试结果
     *
     * @param examId     考试主键id
     * @param staffNames 考生姓名,多人用;分隔
     * @param startTime  考生考试时间前置区间
     * @param endTime    考生考试前置区间
     * @return
     */
    public TransDTO getExamResultList(String examId, String staffNames, String startTime, String endTime) {

        String result;
        String path = "/api/v1/extapi/testing/exam_results_list?exam_id=" + examId;
        if (!StringUtils.isEmpty(staffNames)) {
            path += "&staff_names=" + staffNames;
        }
        if (!StringUtils.isEmpty(startTime)) {
            path += "&start_time=" + startTime;
        }
        if (!StringUtils.isEmpty(endTime)) {
            path += "&end_time=" + endTime;
        }

        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(examHost, path, header, errorMsg);
        TransDTO dto = toTransDTO(result, errorMsg);
        return dto;
    }

    /**
     * 获取分类
     *
     * @param categoryType 分类类型 1-题目分类 2-试卷分类 3-练习分类 4-考试分类
     * @return
     */
    public TransDTO getCategories(String categoryType) {

        String result;
        String path = "/api/v1/extapi/category/select?app_id=" + appId + "&tenant_code=" + tenantCode + "&category_type=" + categoryType;

        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(examHost, path, header, errorMsg);
        TransDTO dto = toTransDTO(result, errorMsg);
        return dto;
    }

    private TransDTO toTransDTO(String result, StringBuffer errorMsg) {
        TransDTO dto = new TransDTO();
        if (!errorMsg.toString().isEmpty()) {
            dto.withSuccess(false).withData(errorMsg).withCode(500);
        } else {
            Map<String, Object> resultMap = JsonUtils.jsonToMap(result);
            if (resultMap.containsKey("success")) {
                boolean flag = String.valueOf(resultMap.get("success")).equals("true");
                dto.withSuccess(flag);
                dto.withCode(Integer.valueOf(resultMap.get("code").toString()));
                dto.withData(resultMap.get("data"));
                dto.withMessage((String) resultMap.get("message"));
            }
        }
        return dto;
    }


}
