package com.tencent.hr.knowservice.faceClass.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * act_user_sign
 * <AUTHOR>
@Data
public class ActUserSign implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 单据id(班级Id/活动id)
     */
    private Integer actId;

    /**
     * 单据类型(3 班级 4 活动)
     */
    private Byte actType;

    /**
     * 学员Id
     */
    private Integer staffId;

    /**
     * 学员姓名
     */
    private String empName;

    /**
     * 签到方式（1二维码签到 2 人工签到）
     */
    private Byte signType;

    /**
     * 签到次序(如需要签多次到的用此字段区分，比如上午签到为1，下午签到为2等)
     */
    private Integer signOrder;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private Integer oldSyncId;

    private static final long serialVersionUID = 1L;
}