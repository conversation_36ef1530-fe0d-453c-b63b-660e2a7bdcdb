{"name": "@tencent/sdc-labeling", "version": "0.14.63", "description": "sdc labeling", "main": "lib/sdc-labeling.common.js", "files": ["lib", "packages"], "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service build --target lib  --dest lib packages/index.js", "pub": "npm publish && npm version patch"}, "style": "lib/sdc-labeling-uicss", "dependencies": {"@tencent/autotracker-beacon-oa": "^4.3.8", "core-js": "^3.6.5", "element-ui": "2.13.0", "vue": "^2.6.11", "zhihui-editor-sdk": "^2.0.6"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.6", "@vue/cli-plugin-eslint": "~4.5.6", "@vue/cli-service": "~4.5.6", "axios": "^0.21.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}