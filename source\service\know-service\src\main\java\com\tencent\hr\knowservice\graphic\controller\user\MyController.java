package com.tencent.hr.knowservice.graphic.controller.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.graphic.dto.MyGraphicAccessRecordsPageDTO;
import com.tencent.hr.knowservice.graphic.dto.MyGraphicPageConditionDTO;
import com.tencent.hr.knowservice.graphic.dto.MyGraphicPageDTO;
import com.tencent.hr.knowservice.graphic.service.MyService;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 个人中心
 */
@RestController
@RequestMapping("/api/graphic/user/my")
public class MyController {

    @Autowired
    MyService myService;

    /**
     * 获取我的图文列表
     * @param myGraphicPageConditionDTO
     * @return
     */
    @PostMapping("/my_graphic_page")
    public TransDTO getMyGraphicPage(@RequestBody MyGraphicPageConditionDTO myGraphicPageConditionDTO){
        TransDTO<Object> transDTO = new TransDTO<>();
        IPage<MyGraphicPageDTO> page = myService.getMyGraphicPage(myGraphicPageConditionDTO);
        return transDTO.withData(page).withCode(HttpStatus.SC_OK).withSuccess(true);
    }

    /**
     * 获取我的浏览记录
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/graphic_visit_records")
    public TransDTO<IPage<MyGraphicAccessRecordsPageDTO>> getMyGraphicAccessRecords(@RequestParam("page_no") Integer pageNo, @RequestParam("page_size") Integer pageSize) {
        IPage<MyGraphicAccessRecordsPageDTO> page = myService.getMyGraphicAccessRecords(pageNo, pageSize);
        return new TransDTO<IPage<MyGraphicAccessRecordsPageDTO>>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(page);
    }
}
