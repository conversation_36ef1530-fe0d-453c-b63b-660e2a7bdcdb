package com.tencent.hr.knowservice.businessCommon.dto.classRoom;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/21/10:30
 * @version: 1.0
 */
@Data
public class InteractiveClassroomAssociationInDTO {

    /**
     * 课程id--数据类型唯一标识，比如课程id，活动id
     */
    @NotNull(message = "数据唯一标识不能为空")
    private String  itemId;

    /**
     * 数据类型 3-班级 4-活动
     */
    @NotNull(message = "数据类型不能为空")
    private Integer actType;

    /**
     * 学员权限范围 0 公开 1 所关联数据自身权限范围 2 特定学员
     */
    @NotNull(message = "学员权限范围不能为空")
    private Integer authScope;

    /**
     * 特定目标学员列表
     */
    private String classroomTargetList;
}
