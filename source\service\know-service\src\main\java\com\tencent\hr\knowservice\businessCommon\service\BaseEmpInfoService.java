package com.tencent.hr.knowservice.businessCommon.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.dao.entity.VBaseEmpInfo;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.VBaseEmpInfoMapper;
import com.tencent.hr.knowservice.businessCommon.dto.dosResDto.DosResDataDto;
import com.tencent.hr.knowservice.businessCommon.dto.dosResDto.DosStaffContentDto;
import com.tencent.hr.knowservice.businessCommon.dto.staffInfo.StaffInfoDto;
import com.tencent.hr.knowservice.businessCommon.vo.DeptBpVo;
import com.tencent.hr.knowservice.utils.JsonUtils;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class BaseEmpInfoService {
    @Resource
    VBaseEmpInfoMapper baseEmpInfoMapper;

    @Autowired
    RedisUtil redisUtil;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${extapi.dos.host}")
    private String dosHost;

    @Autowired
    BaseProxyService baseProxyService;

    /**
     * 获取人员基础信息
     *
     * @param staffId
     * @return
     */
    public VBaseEmpInfo getEmpInfo(Integer staffId) {
        String cacheKey = CommonCacheKeyEnum.BaseEmpInfo.getKeyName() + staffId;
        VBaseEmpInfo result = null;
        Object resultObj = redisUtil.get(cacheKey);
        if (resultObj == null) {
            VBaseEmpInfo data = baseEmpInfoMapper.getBaseEmpInfo(staffId);
            if (data != null) {
                result = new VBaseEmpInfo();
                result.setStaffId(data.getStaffId());
                result.setEmpNameCh(data.getEmpNameCh());
                result.setEmpNameEn(data.getEmpNameEn());
                result.setDeptId(data.getDeptId());
                result.setDeptFullName(data.getDeptFullName());
                result.setEmpStatusName(data.getEmpStatusName());
                result.setEmpTypeName(data.getEmpTypeName());
                redisUtil.set(cacheKey, result, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
            }
        } else {
            result = (VBaseEmpInfo) resultObj;
        }
        return result;
    }

    /**
     * 缓存所有的在职正式员工
     *
     * @return
     */
    public Integer cacheAllEmpInfo() {
        String cacheKey = CommonCacheKeyEnum.BaseEmpInfo.getKeyName();
        List<VBaseEmpInfo> list = baseEmpInfoMapper.getAllBaseEmpInfo();
        for (VBaseEmpInfo entity : list) {
            cacheKey = CommonCacheKeyEnum.BaseEmpInfo.getKeyName() + entity.getStaffId();
            redisUtil.set(cacheKey, entity, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
        }
        return list.size();
    }

    /**
     * 通过英文名字查询员工基本信息
     * @param staffNames
     * @return
     */
    public List<VBaseEmpInfo> getEmpInfosByNames(List<String> staffNames) {
        return baseEmpInfoMapper.getEmpInfosByNames(staffNames);
    }

    /**
     * 通过部门id获取员工信息
     * @param deptId
     * @return
     */
    public List<VBaseEmpInfo> getBaseEmpInfoByDeptId(String deptId) {
        return baseEmpInfoMapper.getBaseEmpInfoByDeptId(deptId);
    }

    /**
     * 获取组织BP
     */
    public List<Integer> getBpStaffs(Integer staffId){
        return baseEmpInfoMapper.getBpStaffs(staffId);
    }

    /**
     * 根据组织id 获取组织bp
     * @param depIds
     * @return
     */
    public List<DeptBpVo> getBpByDeptIds(List<String> depIds){
        return baseEmpInfoMapper.getBpByDeptIds(depIds);
    }


    /**
     * 从dos获取员工信息。信息字段在dos那边决定
     * dos不支持高并发， 所以需要使用定时任务每天去缓存全量数据。没有的数据再走实时接口去查询）
     * @param staffName
     * @return
     */
    public DosStaffContentDto getDosStaffInfo(String staffName) {
        //做缓存
        String cacheKey = CommonCacheKeyEnum.BaseDosStaffInfoByName.getKeyName() + staffName;
        DosStaffContentDto result = null;
        Object resultObj = redisUtil.get(cacheKey);
        if (null == resultObj){
            //构建dos需要的查询条件
            List<String> staffAccountNameList = new ArrayList<>();
            staffAccountNameList.add(staffName);
            Map<String,Object> staffAccountNameMap = new HashMap<>();
            staffAccountNameMap.put("staffAccountNameList",staffAccountNameList);
            Map<String,Object> argMap = new HashMap<>();
            argMap.put("argMap",staffAccountNameMap);
            Map<String,Object> queryCondition = new HashMap<>();
            queryCondition.put("queryCondition",argMap);
            //获取数据
            DosResDataDto data = getDosStaffData(queryCondition);
            result = data.getContent().get(0);
            //放入缓存
            if (result != null) {
                redisUtil.set(cacheKey, result, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime()*3);
            }
            return result;
        }else {
            result = (DosStaffContentDto) resultObj;
        }
        return result;
    }

    /**
     * 全量同步用户信息的定时任务（使用递归进行数据同步）
     */
    public void dosStaffFullDataSync(String sequenceNo,Integer prevId) {
        //构建dos需要的查询条件
        Map<String,Object> argMap = new HashMap<>();
        Map<String,Object> queryConditionMap = new HashMap<>();
        queryConditionMap.put("argMap",argMap);
        queryConditionMap.put("fullPull",true);
        if (StringUtils.isNotBlank(sequenceNo)){
            queryConditionMap.put("sequenceNo",sequenceNo);
        }
        if (prevId != null) {
            queryConditionMap.put("prevId",prevId);
        }
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put("queryCondition",queryConditionMap);
        DosResDataDto data = getDosStaffData(paraMap);
        //开始递归
        if (null != data){
            if (data.getHasNext() && null != data.getPrevId() &&  StringUtils.isNotBlank(data.getSequenceNo()) && null != data.getContent()){
                List<DosStaffContentDto> dosStaffInfoList = data.getContent();
                for (DosStaffContentDto dosStaffInfo : dosStaffInfoList){
                    String cacheKey = CommonCacheKeyEnum.BaseDosStaffInfoByName.getKeyName() + dosStaffInfo.getStaffAccountName();
                    redisUtil.set(cacheKey, dosStaffInfo, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime()*3);
                }
                dosStaffFullDataSync(data.getSequenceNo(),data.getPrevId());
            }else {
                log.info("data.getHasNext为false了,停止请求");
            }
        }else {
            log.info("dos 返回的data的值为null,停止请求");
        }
    }

    /**
     * 获取dos的数据
     * @param queryCondition
     * @return
     */
    public DosResDataDto getDosStaffData(Map<String,Object> queryCondition){
        //向dos发送请求，dos的接口需要和应用一一对应
        String api = "/api/esb/dos-interface-server/open-api/config/hrmd/md-api-public-core-staff-info-realtime/QLearningService/data";
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appId, appToken);
        try {
            String dosResult = baseProxyService.postForApiWithHeaders(dosHost, api, queryCondition, headers);
            //处理结果
            TransDTO<DosResDataDto> dosResultDTO = JsonUtils.jsonToBeanNoSnakeCase(dosResult, new TypeReference<TransDTO<DosResDataDto>>() {});
            //请求失败
            if (StringUtils.isBlank(dosResult) || null == dosResultDTO || !dosResultDTO.getSuccess()) {
                log.error("获取dos消息失败,result={}", dosResult);
                return null;
            }
            DosResDataDto data = dosResultDTO.getData();
            return data;
        } catch (Exception e) {
            log.error("请求dos接口失败", e);
            return null;
        }
    }
}
