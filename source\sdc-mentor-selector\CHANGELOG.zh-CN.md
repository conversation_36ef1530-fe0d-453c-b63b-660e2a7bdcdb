## 更新日志   
### 1.0.17
*2024-12-10*
- 1.0.17-beta功能上线
- 
### 1.0.17-beta.3
*2024-09-19*
#### 修复缺陷
- 级联选择器组件（工作地、省市、职位、职级、管理主体、员工子类型）
  - 删除tag时，在change事件暴露前修改双向绑定值
  
### 1.0.17-beta.2
*2024-09-05*
#### 优化
- 级联选择器组件（工作地、省市、职位、职级、管理主体、员工子类型）
  - 调整tag盒子样式
  
### 1.0.17-beta.1
*2024-09-02*
#### 修复缺陷
- 组织选择器
  - 去除限制组织选择范围【includeUnitSortIDs】默认值
  - 新增传参【isLimitUnitExpand】是否限制展开范围中最小级别的组织，默认true。仅限制组织选择范围时有效
  
### 1.0.16
*2024-08-30*
#### 修复缺陷
- 级联选择器组件（工作地、省市、职位、职级、管理主体、员工子类型）
  - 修复v-model异步传参回显失效

### 1.0.14-beta.27 —— 1.0.15 【级联选择器有bug，请勿使用】
*2024-04-08 —— 2024-08-28*
#### 缺陷
- 级联选择器组件（工作地、省市、职位、职级、管理主体、员工子类型）
  - v-model异步传参时，没有获取数据源，导致回显失败

### 1.0.14-beta.26
*2024-03-12*
#### 新特性
- StaffSelector 员工选择器
  - 新增传参【range.staffTypeIdList】 设置仅选择对应员工类型的员工
  
### 1.0.14-beta.25
*2024-01-09*
#### 修复缺陷
- StaffSelector 员工选择器
  - 调整精准搜索传参
  
### 1.0.14-beta.24
*2023-12-28*
#### 修复缺陷
- StaffSelector 员工选择器, PostSelector 岗位选择器
  - 修复多选时，在弹窗面板勾选组织的查询条件与展开组织的查询条件不一致
  
### 1.0.14-beta.23
*2023-12-21*
#### 新特性
- StaffSelector 员工选择器
  - 新增传参【range.isContainSubStaff】 是否包含子级组织下的员工 默认false
  - 新增传参【range.managerPositionLevelIdList】 设置仅选择对应职级的员工
  
### 1.0.14-beta.22
*2023-12-14*
#### 修复缺陷
- PostSelector 岗位选择器, UnitSelector 组织选择器, StaffSelector 员工选择器
  - 修复缺陷：在多选且禁用时，展示了清空按钮。（部分项目复现）
  
### 1.0.14-beta.21
*2023-12-07*
#### 优化
- StaffSelector 员工选择器
  - 模糊搜索名称时，会精准搜索一次，将精准搜索到的结果放到筛选下拉框第一位
  
### 1.0.14-beta.20
*2023-12-06*
#### 新特性
- AreaSelector 工作地选择器
  - 新增传参【includeCountryList】  可展示的国家集合（默认全展示）
  
### 1.0.14-beta.19
*2023-12-05*
#### 优化
- UnitSelector 组织选择器
  - 模糊搜索支持组织全路径搜索
- PostSelector 岗位选择器, UnitSelector 组织选择器, StaffSelector 员工选择器
  - 模糊搜索匹配的关键词高亮颜色调整为蓝色#3464e0
 
### 1.0.14-beta.18
*2023-12-01*
#### 新特性
- PostSelector 岗位选择器, UnitSelector 组织选择器, StaffSelector 员工选择器
  - 新增传参【defaultExpandedKeys】  一级默认展开的节点的unitID的数组
- PostSelector 岗位选择器
  - 全选勾选仅对当前勾选组织的下级岗位生效，不勾选到下级组织的岗位
#### 优化
- PostSelector 岗位选择器, UnitSelector 组织选择器, StaffSelector 员工选择器
  - 选择面板点击叶子节点会触发勾选/取消勾选（此前仅点击勾选框触发）

### 1.0.14-beta.17
*2023-11-29*
#### 修复缺陷
- PostSelector 岗位选择器
  - 修复缺陷：在传参【range.unitID】为Number类型，【range.isContainSubUnit】为false时，没有正确展示数据

### 1.0.14-beta.16
*2023-11-20*
#### 新特性
- UnitSelector 组织选择器
  - 更换数据源
  - 传参【unitID】增加 Array类型，可通过传入根组织id集合限制多个根组织
  
### 1.0.14-beta.15
*2023-11-15*
#### 新特性
- Content 组件增加传参【menus.uniqueOpened】  是否只保持一个子菜单的展开，默认false

### 1.0.14-beta.14
*2023-10-16*
#### 新特性
- 更换所有数据源，【/api/esb/】更换为【/api/sso/】
- 请求头去除token配置

### 1.0.14-beta.13
*2023-10-12*
#### 新特性
- StaffSelector 员工选择器
  - 新增传参【props.unitID】 组织ID字段名
  - 新增传参【props.unitName】 组织名称字段名
  - 新增传参【props.unitFullName】 组织全路径字段名

- DictSelector 字典选择器
  - 更换数据源


### 1.0.14-beta.12
*2023-09-25*
#### 新特性
- position-cascader 职位级联选择器
  - 更换数据源
  - 移除【valueType】传参，暂不支持Guid
  - 
#### 优化
- PostSelector 岗位选择器
  - 传参【range.NotContainVirtuaIUnit】调整为【range.NotContainVirtualUnit】，
  
### 1.0.14-beta.11
*2023-09-21*
#### 优化
- PostSelector 岗位选择器
  - change事件返回组织id【UnitID】
  - 
- AreaSelector 工作地选择器
  - 测试环境数据源地址【dev-】切换为【uat-】

### 1.0.14-beta.10
*2023-09-20*
#### 优化
- UnitSelector 组织选择器， StaffSelector 员工选择器， PostSelector 岗位选择器
  - 传入根组织后，查询到的组织会按照传入的根组织顺序展示

### 1.0.14-beta.9
*2023-09-20*
#### 新特性
- UnitSelector 组织选择器， StaffSelector 员工选择器， PostSelector 岗位选择器
  - 传入根组织【unitID】时，会先查对应的组织
  
- StaffSelector 员工选择器
  传参【range.unitID】调整，支持Number/Array类型

### 1.0.14-beta.8
*2023-09-19*
#### 新特性
- UnitSelector 组织选择器， StaffSelector 员工选择器， PostSelector 岗位选择器
  - 新增传参【showFullTag】  是否在输入框中展示完整的tag,  默认false
  
### 1.0.14-beta.7
*2023-09-15*
#### 新特性
- UnitSelector 组织选择器， StaffSelector 员工选择器， PostSelector 岗位选择器
  - 新增传参【modalWidth】  弹窗自定义宽度
  - 输入框搜索防抖时间调整到500ms

- PostSelector 岗位选择器
  - 更换数据源
  - 新增传参【selectClass】 选择框自定义类
  - 新增传参【modalClass】  弹窗自定义类
  - 传参【range.unitID】调整，支持Number/Array类型，传Array会先查出对应的组织
  
- AreaSelector 工作地选择器
  - 新增传参【includeRegionList】 可展示的大区集合
  
### 1.0.14-beta.6
*2023-08-25*
#### 新特性
- StaffSelector 员工选择器
  - 新增传参【props.engName】 员工英文名字段名

### 1.0.14-beta.5
*2023-08-09*
#### 新特性
- UnitSelector 组织选择器， StaffSelector 员工选择器
  - 新增传参【selectClass】 选择框自定义类
  - 新增传参【modalClass】  弹窗自定义类

### 1.0.14-beta.4
*2023-07-17*
#### 优化
- AreaSelector 工作地选择器 
  - 对应order_id为0的排序放到最后位置

### 1.0.14-beta.3
*2023-07-14*
#### 优化
- AreaSelector 工作地选择器 
  - 根据对应order_id从小到大排序

### 1.0.14-beta.2
*2023-07-11*
#### Bug修复
- AreaSelector 工作地选择器
  - 修改文档 【showTotal】字段默认值为【false】
- PostSelector 岗位选择器， StaffSelector 员工选择器
  - 修复缺陷：限制选项范围时，第二次打开选择面板还会调用查询方法，导致面板勾选回显不正确


### 1.0.14-beta.1
- AreaSelector 工作地选择器
  - 新增传参【level】 级联选择器展示的层级数， 可选值1，2，3 默认值 3

### 1.0.14
- 1.0.13-beta功能上线

### 1.0.13-beta.7
- uat环境默认请求测试接口

### 1.0.13-beta.6
- 员工选择器
  - 更换数据源
  - 新增传参【includeOnBoarding】 是否包含待入职员工
  - 新增传参【includePartTimePost】 是否显示组织下的兼岗员工 
  - 新增传参【range.contractCompanyIdList】 合同公司ID集合, 仅选择该合同下的员工 
  - 新增传参【range.manageUnitIdList】 管理主体ID集合, 仅选择该管理主体下的员工 

### 1.0.13-beta.5
- 员工选择器支持粘贴回车批量选择
- selector.js去除参数getPasteResult默认值，默认值会导致岗位选择器粘贴内容带回车或者;时报错

### 1.0.13-beta.4
- 岗位、组织、员工选择器增加清空所有选项按钮

### 1.0.13-beta.3
- 岗位、组织、员工选择器增加按id搜索
- 岗位选择器增加参数 showPostID，是否在筛选时展示PostID，默认不展示

### 1.0.13-beta.2
- 修复组织选择器多选判断，仅根据multiple属性值判断

### 1.0.13-beta.1
- 支持通过window.SDC_DATA_DOMAIN设置请求域名

### 1.0.12
- 新增工作地选择器组件
- 组织、岗位增加新功能特性

### 1.0.12-beta.22【工作地接口地址有误，请勿在生产使用】
*2022-11-16*
#### 优化
- 组织选择器、职级选择器 
  - 优化el-checkbox样式导致展开选择面板会闪一下勾

### 1.0.12-beta.21【工作地接口地址有误，请勿在生产使用】
*2022-11-16*
#### 新特性
- 组织选择器、岗位选择器
  - 增加showLastLevels参数，默认true, 设置tag是否只展示最后一级

### 1.0.12-beta.20【工作地接口地址有误，请勿在生产使用】
*2022-11-15*
#### 优化
- 组织选择器增加组件传参字段'containUnitLocationCode'

### 1.0.12-beta.19【工作地接口地址有误，请勿在生产使用】
*2022-11-11*
- 调整地址选择器初始化时搜索框的左边距

### 1.0.12-beta.18【工作地接口地址有误，请勿在生产使用】
*2022-11-11*
- 修复地址选择器map传参导致无法筛选bug

### 1.0.12-beta.17【工作地接口地址有误，请勿在生产使用】
*2022-11-10*
- 将请求地址默认设为正式环境地址

### 1.0.12-beta.16【工作地接口地址有误，请勿在生产使用】
*2022-11-08*
- 地址选择器增加监听页面宽度改变

### 1.0.12-beta.15【工作地接口地址有误，请勿在生产使用】
*2022-11-04*
- 地址选择器将中国大陆数据源改为3级

### 1.0.12-beta.14【工作地接口地址有误，请勿在生产使用】
*2022-11-01*
- 替换地址选择器请求地址

### 1.0.12-beta.13【工作地接口地址有误，请勿在生产使用】
*2022-10-27*
- 修复textarea样式问题

### 1.0.12-beta.12【工作地接口地址有误，请勿在生产使用】
*2022-10-27*
- 修复selector样式问题

### 1.0.12-beta.11【工作地接口地址有误，请勿在生产使用】
*2022-10-27*
- 单选选择器去除后面空白

### 1.0.12-beta.10【工作地接口地址有误，请勿在生产使用】
*2022-10-24*
- 修复组织选择器更新unitID打开弹窗不重新请求数据bug

### 1.0.12-beta.8【工作地接口地址有误，请勿在生产使用】
*2022-10-24*
- 修复1.0.12-beta.7打包错误问题

### 1.0.12-beta.7(此版本有问题，请不要使用)
*2022-10-21*
- 修复工作地选择器选择面板位置不跟随bug

### 1.0.12-beta.6【工作地接口地址有误，请勿在生产使用】
*2022-10-17*
- 新增工作地选择器（因为接口原因，正式环境暂时不可用）
- 组织选择器支持设置根组织

### 1.0.12-beta.1
*2022-08-05*
- 员工选择器新增合同公司筛选
- 岗位选择器支持虚拟组织过滤，支持控制是否显示下级组织岗位

### 1.0.11
*2022-06-28*
#### 修复bug
- 修复按钮样式被覆盖问题

### 1.0.10
*2022-06-28*
#### Header
- 内网默认导航数据更新

### 1.0.10-beta.20
*2022-05-26*
#### Bug修复
- 修复按钮文字颜色被覆盖bug

### 1.0.10-beta.19
*2022-05-26*
- 修改导航图标

### 1.0.10-beta.18
*2022-04-22*
- 修改导航样式

### 1.0.10-beta.17
*2022-04-21*
- 修改按钮样式

### 1.0.10-beta.16
*2022-04-21*
- 修改按钮样式

### 1.0.10-beta.15
*2022-04-21*
- Table、Input组件样式调整, Header组件跳转组件逻辑调整

### 1.0.10-beta.14
*2022-04-13*
- Header 
 - 内网导航菜单 “人事管理>发起申诉” 更名为 “人事管理>申诉平台”

### 1.0.10-beta.13
*2022-04-11*
- Header 
 - 内网导航菜单 “人事管理>发起申诉” 更新链接

### 1.0.10-beta.12
*2022-04-11*
- Header 
 - 内网导航菜单 “人事管理>发起申诉” 更新链接

### 1.0.10-beta.11
*2022-03-07*
- Header 
  - menu加上title属性

### 1.0.9
*2022-03-07*
- 更新正式版本

### 1.0.9-beta.11
*2022-02-24*
#### 修复问题
- 修复1.0.9-beta.10版本打包问题，请不要使用1.0.9-beta.10版本

### 1.0.9-beta.10
*2022-02-24*
#### 新增功能
- Layout、Content新增方法控制侧边栏是否折叠

### 1.0.9-beta.9
*2022-01-17*
#### 优化
- Header 
  - 内网默认导航数据添加菜单项 “人事管理>保护系统”

### 1.0.9-beta.8
*2021-12-27*
#### Bug修复
- ExceptionPage 
  - 修复sdc-exception-page组件404图片不显示bug

### 1.0.9-beta.7
*2021-12-27*
#### Bug修复
- 职级职位选择器
  - 修复当v-model值不在选项里时的空指针报错问题


### 1.0.9-beta.6
*2021-12-22*
#### 新特性
- 职级职位选择器
  - 增加valueType字段，可设置value的值类型为Guid
  - 增加props属性，可以设置multiple、emitPath等，原有map属性移到props
  - 增加getCheckedNodes方法
#### Bug修复
- 职级职位选择器
  - 修复v-model设置初始值无效的问题
#### 优化
- 选择器
  - 弹窗高度自适应窗口变化


### 1.0.9-beta.5
*2021-11-30*
#### Bug修复
- 职级职位选择器
  - 修复@change事件输出的选中对象没有label、value字段，并增加path字段

### 1.0.9-beta.4
*2021-11-19*
#### 新特性
- 选择器
  - 输入框内按回车默认选择第一行
  
- 职级职位选择器
  - 增加separator参数用于多级关联
  - 增加defaultValue用于单选模式默认显示


### 1.0.9-beta.2
*2021-11-16*
#### Bug修复
- 组织选择器
  - 修复过滤字段错误


### 1.0.9-beta.1
*2021-11-10*
#### 新特性
- 组织选择器
  - 增加unitSortId参数，限制组织选择范围
  - 增加includeUnitSortIDs参数，限制组织选择范围
- 职级职位选择器
  - 增加单选模式
#### BUG修复
- 组织、人员、岗位选择器
  - 修复在一些操作下弹窗树节点选中状态会错乱的bug

### 1.0.8
*2021-08-25*
- 更新正式版本

### 1.0.8-beta.5
*2021-08-23*
#### 新特性
- Wxheader
  - 添加enableSidebar配置项, 是否启用侧边栏

### 1.0.8-beta.4
*2021-08-05*
#### 新特性
- 组织选择器
  - 增加containUnitIDPath参数，即选中项对象是否包含完整组织ID路径属性

### 1.0.8-beta.3
*2021-07-22*
#### 新特性
- 页面异常
  - 增加home参数，设置主页跳转的页面


### 1.0.8-beta.2
*2021-07-22*
#### Bug修复
- 员工选择器
  - 过滤掉待入职和未入职员工


### 1.0.8-beta.1
*2021-07-08*
#### Bug修复
- 选择器
  - 修复textarea格式选择器button不能disabeld的问题
- 更新sdc-core版本1.0.7
  - 修复组件内ajax请求headers影响业务系统headers的问题


### 1.0.7
*2021-06-09*

- 更新正式版本

### 1.0.6-beta.10
*2021-04-13*
#### 优化
- 页面头部navbar
  - 适配原有逻辑，maxMenuCount为菜单项数量（优化“更多”下拉菜单中只有一个菜单项的问题）

### 1.0.6-beta.9
*2021-04-12*
#### 新特性
- 页面头部navbar（请使用1.0.6-beta.10及之后版本）
  - menus对象增加maxMenuCount和adaptive属性，可自定义或自适应navbar展开菜单项的数量
- 选择器 (UnitSelector、StaffSelector、PostSelector)
  - 可通过退格键（Backspace）删除已选项
#### Bug修复
- QueryPanel 查询条件面板
  - 修复query事件无效的问题

### 1.0.6-beta.8
*2021-04-06*
#### 特性
- sdc-wxheader
  - 新增应用市场定制页面头部业务组件
### 1.0.6-beta.7
*2021-03-26*
#### 优化
- 选择器
  - 按业务系统站点协议访问对应的数据源（针对浏览器对跨协议的cookie限制）
  - 对于未登录/登录过期情况返回提示（仅生产环境）

### 1.0.6-beta.6
*2021-03-19*
#### Bug修复
- 修复侧边部分点击区域无效

### 1.0.6-beta.5
*2021-03-18*
#### Bug修复
- 修复选择器弹窗树节点文字过多溢出的bug

### 1.0.6-beta.4
*2021-03-18*
#### Bug修复
- 修复头nav部显示bug

### 1.0.6-beta.3
*2021-03-17*
#### Bug修复
- 修复header和layout组件avator事件无效的bug

### 1.0.6-beta.2
*2021-03-10*
#### Bug修复
- 修复基础字典选择器回显值时输入框显示出value（而非label）的bug
#### 新特性
- 基础字典选择器
  - 增加属性：size | disabled | no-match-text
  - 增加事件：visible-change | remove-tag | clear | blur | focus

### 1.0.5
*2021-03-08*
#### 新特性
- layout组件新增头像名字配置
#### 优化
- 修复navbar更多下拉菜单点击区域不占满bug


### 1.0.4-beta.7
*2021-02-26*
#### Bug修复
- 修复vue-fragment打包报错bug
### 1.0.4-beta.5

*2021-01-28*

#### Bug修复
- Prompt
  - 修复prompt组件传入close属性时，调用hide方法失效bug

#### 新特性
- 新增Header、Content、Avatar组件

#### 优化  
- 新增SDC UI迁移SDC WebUI方案

### 1.0.4-beta.2

*2021-01-21*

#### 优化

- 更新Selector (StaffSelector、PostSelector)
  - 增加range参数, 可仅选择某组织下的子级员工/岗位

### 1.0.3

*2021-01-14*

- **正式发布 SDC WebUI v1.0.3**

### 1.0.3-beta.13
*2021-01-13*

- utils
  - 修复选择器组件使用textEllipsis方法时的IE兼容问题

### 1.0.3-beta.12
*2021-01-13*

- 失败版本, 废弃

### 1.0.3-beta.11
*2021-01-12*

- Selector、Cascader
  - 修复按需加载组件时，textEllipsis没有加载的问题

### 1.0.3-beta.10
*2021-01-08*

- Layout
  - 左侧菜单支持默认展开子菜单
  - 左侧菜单项添加 `disabled` 配置项
#### 优化
  - 移除对`@tencent/sdc-vue`的并行依赖项

### 1.0.3-beta.9
*2021-01-06*

- Layout
  - 修复左上角导航背景色hex颜色值IE兼容问题
### 1.0.3-beta.8
*2021-01-06*

- 解决IE兼容性问题
  - 解决src目录引入路径问题


### 1.0.3-beta.6
*2020-12-18*

- 更新Table
  - 解决单选行的问题

#### 优化

- 更新StaffSelector
  - 解决粘贴接口限制个数问题

### 1.0.3-beta.3

*2020-12-11*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 增加清空选中项的clearSelected方法
  - 修复选择器因el-tabs渲染延迟导致选项tag无法显示的问题
- Layout
  - 内外网默认布局配置调整，scope值不限制Header区域模块显示
  - 左上角导航菜单添加默认导航数据

### 0.0.49-beta

*2020-11-25*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 解决禁止搜索前提下文本框placeholder失效的问题
  - 离开文本框时清空关键字
- 修正utils别名问题以及优化方法导出
  - 将utils方法一一导出，优化性能
  - 修改为sdc-webui/src/utils/main，防止与应用程序的utils冲突
- 更新QueryPanel
  - 修复重置选项部分元素无法被清除的BUG
  - 修复QueryPanel样式在el-form中显示错乱的问题

### 0.0.46-beta

*2020-11-25*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 优化关键字高亮
  - 解决单选有输入情况下，通过弹窗选择后输入不能清除的bug

### 1.0.2

*2020-11-20*

- **正式发布 SDC WebUI v1.0.2**

### 0.0.45-beta

*2020-11-20*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 解决弹窗树第一次加载数据失败时，因懒加载无法重新获取数据的bug
  - 增加文档里对使用自定义数据源的描述细节
  - 解决PostSelector组织节点checkbox显示bug
- 更新sdc-core项目文档
  - 更新sdc-core为1.0.3
  - 更新DataApi支持多根地址映射

### 0.0.43-beta

*2020-11-18*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 新增search属性，可启用是否搜索功能(默认为true)

### 0.0.41-beta

*2020-11-06*

#### 优化

- 更新sdc-core项目文档
  - 更新sdc-core为1.0.1
  - 删除DataFetch，更新DataHttp可配置多实例

### 0.0.39-beta

*2020-11-05*

#### 优化

- 更新StaffSelector
  - 修改某些输入速率下，下拉列表渲染头像错误
- 更新sdc-core项目文档

### 0.0.38-beta

*2020-11-04*

#### 优化

- 更新Layout
  - 新增sidebarCollapse参数, 可配置默认是否收起
  - 优化当headerLayout传空数组时，用户可以完全自定义顶部区域(默认插槽)

### 0.0.37-beta

*2020-11-04*

#### 优化

- 更新StaffSelector
  - 修改粘贴模式，粘贴单个员工时出现下拉框供选择

### 0.0.36-beta

*2020-11-03*

#### 优化

- 更新StaffSelector
  - 修复粘贴无法选择离职员工问题

### 0.0.35-beta

*2020-11-02*

#### 优化

- 新增组件Table
- 新增组件QueryPanel
- 新增组件QueryResult

### 0.0.34-beta

*2020-10-31*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 增加尺寸，可设置常规、medium、small三种
  - 修复粘贴时不响应@change, v-model问题

### 0.0.33-beta

*2020-10-31*

#### 优化

- 更新默认API
  - 暂时去除X-Requested-With，修复预检请求报错问题

### 0.0.32-beta

*2020-10-30*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 增加showTotal属性
  - 修复small尺寸选择器受外部样式影响问题
- 更新Alert组件
  - 增加快捷调用方式，直接传message即可
- 更新Prompt组件
  - 增加快捷调用方式，直接传message和confirm即可，新增close回调参数

### 1.0.1

*2020-10-29*

- **正式发布 SDC WebUI v1.0.1**

### 0.0.31-beta

*2020-10-28*

#### 优化

- 更新HRDataService
  - 新增生产环境和测试环境并存配置
- 更新Selector
  - 修复small尺寸选择器按钮在el-form-item里样式偏移问题
  - 将disabled属性修改为响应式

### 1.0.0

*2020-10-27*

- **正式发布 SDC WebUI v1.0.0**

### 0.0.30-beta

*2020-10-27*

#### 优化

- 更新UnitSelector
  - 增加filterEnableFlag，includeVirtualUnit属性
- 切换到HRData正式环境体验

### 0.0.29-beta

*2020-10-26*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 无匹配数据、获取数据失败提示的多语言
  - 增加获取树节点数据失败时的后续操作
  - 增加disabled属性，完善相应样式
- 修改部分组件样式，与设计师的版本一致

### 0.0.28-beta

*2020-10-21*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 修复无法正常加载的BUG
  - 删除mock

### 0.0.27-beta

*2020-10-21*

#### 优化

- 更新DictSelector
  - 将type更新为数字或字符串类型
  - 扩展100+字典类型
- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 添加FilterEnableFlag参数只获取有效的数据(默认为true)
- 更新HRFlowEngine
  - 更新文档(重命名为FlowEngine)

### 0.0.26-beta

*2020-10-20*

#### 优化

- 更新DictSelector
  - 添加更多的字典类型
  - 更新markdown
- 更新PositionCascader
  - 更新markdown

### 0.0.25-beta

*2020-10-20*

#### 优化

- 更新Modal
  - 去掉header和footer的阴影
  - 将底部操作按钮向右对齐
- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 对接HRData真实数据接口联调
  - 配置组件第一次请求缓存

### 0.0.24-beta

*2020-10-14*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)、Cascader
  - 添加白色背景色样式
  - 在markdown文档中增加选择器宽度的提醒，避免Tag区域样式显示问题
- 更新PostSelector
  - 修改类别(CategoryID、CategoryName、category)字段 -> 组织（UnitID、UnitName、unit）字段

### 0.0.23-beta

*2020-09-30*

#### 优化

- 扩展Prompt方法hide
  - 增加Vue实例方法$sdc.prompt.hide
- 更新Header
  - 更新背景色根据主题动态发生变化
- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 无匹配数据或获取数据失败时，利用下拉列表（搜索时）和toast（粘贴时）给予用户反馈
  - 修改UnitSelector后端接口默认字段

### 0.0.22-beta

*2020-09-30*

#### 优化

- 重命名ConfirmModal
  - 将ConfirmModal重命名为Prompt
  - 增加Vue实例方法$sdc.prompt替换sdc-confirm-modal
- 新增消息弹窗Alert
  - 增加Vue实例方法$sdc.alert

### 0.0.21-beta

*2020-09-29*

#### BUG修复

- 更新SvgIcon
  - 解决图标按需加载时影响网络请求baseUrl

### 0.0.20-beta

*2020-09-28*

#### BUG修复

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 解决在el-form中因line-height导致的样式错乱问题
- 更新Cascader（PostionCascader）
  - 解决在el-form中因line-height导致的样式错乱问题
- 更新SvgIcon
  - 解决图标按需加载无法显示的问题

### 0.0.19-beta

*2020-09-27*

#### 优化

- 更新SvgIcon组件
  - 解决图标鼠标移入高亮的issue
- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 将弹窗的scrollable属性更新为adaptive
  - 取消数据源映射属性map，增加选中项映射属性props
  - 修改样式（选择框、弹窗）
  - 更新markdown文档

### 0.0.18-beta

*2020-09-24*

#### 优化

- 重命名职位级联选择器
  - 将PostCascader变更为PositionCascader
- 新增SvgIcon组件
  - 修改webpack配置
- 更新StaffSelector
  - 解决头像加载问题，考虑加载失败情况

### 0.0.17-beta

*2020-09-23*

#### 优化

- 更新Selector (UnitSelector、StaffSelector、PostSelector)、PostCascader
  - 更新markdown文档
- PostCascader
  - 修改组件默认数据源的字段名，和map映射的字段名

#### Bug 修复

- StaffSelector
  - map映射的字段名增加avatar

### 0.0.16-beta

*2020-09-22*

#### Bug 修复

- 更新Selector (UnitSelector、StaffSelector、PostSelector)
  - 修复组件在多选模式下不能初始化
  - 更新markdown文档
