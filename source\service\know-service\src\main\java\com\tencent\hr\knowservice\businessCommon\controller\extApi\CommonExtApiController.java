package com.tencent.hr.knowservice.businessCommon.controller.extApi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dao.entity.BaseDepartment;
import com.tencent.hr.knowservice.businessCommon.dao.entity.VBaseEmpInfo;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.BaseDepartmentMapper;
import com.tencent.hr.knowservice.businessCommon.dto.dosResDto.DosStaffContentDto;
import com.tencent.hr.knowservice.businessCommon.dto.staffInfo.StaffInfoDto;
import com.tencent.hr.knowservice.businessCommon.service.BaseEmpInfoService;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.tencent.hr.knowservice.businessCommon.service.ClassRoomService;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/2/24
 * @version: 1.0
 */

@Slf4j
@RestController
@RequestMapping("/api/ext/businessCommon")
public class CommonExtApiController {

    @Autowired
    BaseEmpInfoService empInfoService;

    @Autowired
    BaseDepartmentMapper departmentMapper;

    @Autowired
    ClassRoomService classRoomService;


    /**
     * 获取员工信息
     *
     * @param staffNamesStr
     * @return
     */
    @GetMapping("/baseinfo")
    public TransDTO getEmpBaseInfo(@RequestParam("staff_names") String staffNamesStr) {
        List<String> staffNames = Arrays.asList(staffNamesStr.split(";"));
        TransDTO dto = new TransDTO<>().withCode(HttpStatus.SC_OK).withSuccess(true);
        List<VBaseEmpInfo> result = empInfoService.getEmpInfosByNames(staffNames);
        dto.withData(result);
        return dto;
    }

    /**
     * 获取部门列表
     *
     * @return
     */
    @GetMapping("/get-dept-list")
    public List<BaseDepartment> getOperateSignature(
            @RequestParam(value = "deptLevelMax", required = false) String deptLevelMax) {

        QueryWrapper<BaseDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", 1);

        if (null != deptLevelMax) {
            queryWrapper.le("dept_level", deptLevelMax);
        }

        return departmentMapper.selectList(queryWrapper);
    }

    /**
     * 检查用户访问互动课堂的权限
     * @param classroomId
     * @param staffId
     * @return
     */
    @GetMapping("/classroom/checkAuth")
    public TransDTO<Boolean> checkClassroomAuth(
            @RequestParam("classroom_id") String classroomId,
            @RequestParam(value = "staff_id",required = false) String staffId){

        if (StringUtils.isEmpty(classroomId)){
            throw new LogicException("课堂id不能为空");
        }
        boolean result =  classRoomService.checkClassroomAuth(classroomId,staffId);
        return new TransDTO<Boolean>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(result);
    }

    /**
     * 课程更新后对互动课堂信息进行同步
     * @param params
     * @return
     */
    @PostMapping("/syncClassroom")
    public TransDTO syncClassroom(@RequestBody Map<String,Object> params){
        if(params == null){
            throw new LogicException("params is null");
        }
        Object itemId = params.get("item_id");
        Object actType = params.get("act_type");
        if(itemId == null){
            throw new LogicException("item_id is null");
        }
        if(actType == null){
            throw new LogicException("act_type is null");
        }
        classRoomService.syncClassroom((int)actType,itemId.toString());
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK);
    }

    /**
     * 从dos获取员工信息。信息字段在dos那边决定。现在同步的接口数据是：DosStaffContentDto实体中的字段信息数据
     * （dos不支持高并发， 所以需要使用定时任务每天去缓存全量数据。没有的数据再走实时接口去查询）
     * @param staffName
     * @return
     */
    @GetMapping("getDosStaffInfo")
    public TransDTO<DosStaffContentDto> getDosStaffInfo(String staffName){
        DosStaffContentDto data = empInfoService.getDosStaffInfo(staffName);
        TransDTO<DosStaffContentDto> transDto = new TransDTO<>();
        transDto.setData(data);
        return transDto;
    }

}
