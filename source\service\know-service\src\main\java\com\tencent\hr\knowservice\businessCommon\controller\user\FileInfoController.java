package com.tencent.hr.knowservice.businessCommon.controller.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileInfoDto;
import com.tencent.hr.knowservice.businessCommon.dto.PubFilePageDto;
import com.tencent.hr.knowservice.businessCommon.service.PubFileInfoService;
import com.tencent.hr.knowservice.businessCommon.vo.PubFileInfoVo;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/businessCommon/user/pub-file")
public class FileInfoController {

    @Autowired
    private PubFileInfoService fileInfoService;

    /**
     * 素材列表查询
     */
    @PostMapping("/get-file-list")
    public TransDTO<IPage<PubFileInfoVo>> getPubFileInfoList(@RequestBody PubFilePageDto dto) {
        return fileInfoService.getPubFileInfoList(dto);
    }

    /**
     * 重新转码
     */
    @GetMapping("reTransCode/{id}")
    public TransDTO reTransCode(@PathVariable("id") String id) {
        return fileInfoService.reTransCode(id);
    }

    /**
     * 查询素材引用
     *
     * @param fileId
     * @return
     */
    @GetMapping("pub-file-refer/{file_id}")
    public TransDTO pubFileRefer(@PathVariable("file_id") Integer fileId) {
        return fileInfoService.referByFileId(fileId, false);
    }

    /**
     * 根据素材id查询素材
     *
     * @param fileId
     * @return
     */
    @GetMapping("file/{fileId}")
    public TransDTO getPubFileInfo(@PathVariable Integer fileId) {
        PubFileInfoDto fileInfo = fileInfoService.getFileInfoByFileId(fileId);
        if (fileInfo.getFileId() == null){
            return new TransDTO().withSuccess(false).withMessage("素材不存在！").withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        }
        return new TransDTO<PubFileInfoDto>().withData(fileInfo).withCode(200);
    }
}
