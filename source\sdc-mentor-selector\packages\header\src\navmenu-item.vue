<template>
  <fragment>
    <sdc-link customClass="sdc-nav-menu-item" v-for="(item, index) in data"
              :to="item.url" :key="index">
      <component :is="hasChildren(item) ? 'el-submenu' : 'el-menu-item'"
                 :index="item.key" :class="item.className" popper-class="sdc-nav-menu-popper">
        <template :slot="hasChildren(item)?'title':'default'">
          <i v-if="item.icon" :class="item.icon"></i>
          {{item.text}}
        </template>
        <nav-menu-item :data="item.children" v-if="hasChildren(item)"/>
      </component>
    </sdc-link>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import SdcLink from 'packages/link'

  export default {
    name: 'nav-menu-item',
    props: {
      data: {
        type: Array,
        default() {
          return []
        }
      }
    },
    methods: {
      hasChildren(item) {
        return Array.isArray(item.children) && item.children.length > 0
      }
    },
    components: {
      Fragment,
      SdcLink
    }
  }
</script>
