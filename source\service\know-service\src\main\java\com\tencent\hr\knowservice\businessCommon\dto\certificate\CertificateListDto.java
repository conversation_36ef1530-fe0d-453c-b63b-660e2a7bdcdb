package com.tencent.hr.knowservice.businessCommon.dto.certificate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description: 列表返回DTO
 * @author: shi<PERSON>wang
 * @createDate: 2022/4/24
 * @version: 1.0
 */
@Data
public class CertificateListDto {

    /**
     * 证书模板id
     */
    private String certificateId;

    private String certificateName;

    private Integer certificateValidType;

    /**
     * 证书有效时间值
     */
    private Integer certificateValidTime;

    /**
     * 证书有效时间单位。1-天；2-月；3-年
     */
    private Integer certificateValidTimeUnit;


    private String creatorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdAt;


    private String updateName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;

    /**
     * 发放证书总数
     */
    private Long totalNum;
    /**
     * 生效证书 = 总数 - 过期 - 回收。自行计算
     */
    private Long effectNum;

    /**
     * 过期数量
     */
    private Long overdueNum;

    /**
     * 回收数量
     */
    private Long withdrawNum;

    /**
     * 证书是否上链
     */
    private Boolean useBlockChain;
}
