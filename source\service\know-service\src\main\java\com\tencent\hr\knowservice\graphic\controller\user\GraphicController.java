package com.tencent.hr.knowservice.graphic.controller.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.constans.LRSVerbs;
import com.tencent.hr.knowservice.businessCommon.dto.TransCustomDTO;
import com.tencent.hr.knowservice.businessCommon.dto.xapi.TXAgent;
import com.tencent.hr.knowservice.businessCommon.proxy.AdapterServiceApi;
import com.tencent.hr.knowservice.framework.advice.exception.AuthException;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.advice.exception.NotFoundException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.entity.Graphic;
import com.tencent.hr.knowservice.graphic.dto.*;
import com.tencent.hr.knowservice.graphic.dto.credit.CreditResDto;
import com.tencent.hr.knowservice.graphic.dto.extapi.RelationGraphicDTO;
import com.tencent.hr.knowservice.graphic.service.AdminService;
import com.tencent.hr.knowservice.graphic.service.GraphicService;
import com.tencent.hr.knowservice.graphic.service.LRSService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 图文用户端
 */
@RestController
@RequestMapping("/api/graphic/user/graphic")
@Validated
public class GraphicController {
    @Autowired
    GraphicService graphicService;

    @Autowired
    AdapterServiceApi adapterServiceApi;

    @Autowired
    AdminService adminService;

    @Autowired
    LRSService lrsService;

    @Value("${graphic.shortUrl}")
    private String shortUrl;


    /**
     * 保存草稿
     *
     * @param graphicAndExtendContentDTO
     * @return
     */
    @PostMapping("/save_draft")
    public TransDTO<Integer> saveDraft(@RequestBody @Valid GraphicAndExtendContentDTO graphicAndExtendContentDTO) {
        Integer num = graphicService.saveDraft(graphicAndExtendContentDTO);
        if (num < 1) {
            throw new LogicException("保存失败！");
        }
        return new TransDTO<Integer>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(graphicAndExtendContentDTO.getGraphic().getGraphicId());
    }

    /**
     * 新建图文
     *
     * @param graphicAndExtendContentDTO
     * @return
     */
    @PostMapping("/add_graphic")
    public TransDTO<CreditResDto> addGraphic(@RequestBody @Valid GraphicAndExtendContentDTO graphicAndExtendContentDTO) {
        //必要字段校验
        Graphic graphic = graphicAndExtendContentDTO.getGraphic();
        graphicService.requiredFieldsCheck(graphic);
        // textToAudio
        ContextEntity current = GatewayContext.current();
        StringBuffer error = new StringBuffer();
        String contentId = graphicService.textToAudio(graphic, error,current.getStaffId(),current.getStaffName());
        if (error.length() > 0) {
            return new TransDTO<CreditResDto>().withSuccess(false).withMessage("文本转语音出错:" + error);
        }
        graphicAndExtendContentDTO.getGraphic().setAudioContentId(contentId);
        //新增
        CreditResDto creditResDto = new CreditResDto();
        Integer num = graphicService.add(graphicAndExtendContentDTO);
        if (num < 1) {
            throw new LogicException("新增失败！");
        }
        //创建积分
        String credit = graphicService.getCredit(graphic, graphicAndExtendContentDTO.getRelationContent());
        if (StringUtils.isNotBlank(credit)) {
            creditResDto.setCredit(credit);
        }
        creditResDto.setData(graphic.getGraphicId());
        return new TransDTO<CreditResDto>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(creditResDto);
    }

    /**
     * 编辑图文
     * 本人、作者、管理员和超级管理员才能编辑
     *
     * @param graphicAndExtendContentDTO
     * @return
     */
    @PostMapping("/modify")
    public TransDTO<CreditResDto> modify(@RequestBody @Valid GraphicAndExtendContentDTO graphicAndExtendContentDTO) {
        Graphic graphic = graphicAndExtendContentDTO.getGraphic();
        if (null == graphic) {
            throw new LogicException("缺少必要参数");
        }
        if (null == graphic.getGraphicId()) {
            throw new LogicException("图文id不能为空！");
        }
        if (!graphicService.graphicAllAuthCheck(graphic.getGraphicId())) {
            throw new AuthException("您暂时没有访问该页面的权限！");
        }

        //必要字段校验
        graphicService.requiredFieldsCheck(graphic);
//        //重名校验
//        Graphic tempInDb = graphicService.findByGraphicNameSimple(graphic.getGraphicName());
//        if (null != tempInDb && !tempInDb.getGraphicId().equals(graphic.getGraphicId())) {
//            throw new LogicException("名称为“" + graphic.getGraphicName() + "”的图文已经存在，请修改名称");
//        }
        // textToAudio
        ContextEntity current = GatewayContext.current();
        StringBuffer error = new StringBuffer();
        String contentId = graphicService.textToAudio(graphic, error,current.getStaffId(),current.getStaffName());
        if (error.length() > 0) {
            return new TransDTO<CreditResDto>().withSuccess(false).withMessage("文本转语音出错:" + error);
        }
        graphicAndExtendContentDTO.getGraphic().setAudioContentId(contentId);
        CreditResDto creditResDto = new CreditResDto();
        Integer num = graphicService.modify(graphicAndExtendContentDTO);
        if (num < 1) {
            throw new LogicException("编辑失败！");
        }
        //创建积分 --发布状态下才能去获取积分
        if (1 == graphic.getGraphicStatus() && StringUtils.isBlank(graphic.getOldGraphicId())) {
            Graphic graphicInDb = graphicService.getGraphicByIdEnable(graphic.getGraphicId());
            String credit = graphicService.getCredit(graphicInDb, graphicAndExtendContentDTO.getRelationContent());
            if (StringUtils.isNotBlank(credit)) {
                creditResDto.setCredit(credit);
            }
        }
        creditResDto.setData(graphic.getGraphicId());
        return new TransDTO<CreditResDto>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(creditResDto);
    }

    /**
     * 修改图文状态
     *
     * @param graphicStatus
     * @param graphicId
     * @return
     */
    @GetMapping("/modify_status")
    public TransDTO<Integer> modifyStatus(@RequestParam("graphic_status") Integer graphicStatus,
                                          @RequestParam("graphic_id") Integer graphicId) {
        Integer num = 0;
        if (graphicService.graphicAllAuthCheck(graphicId)) {
            num = graphicService.modifyStatus(graphicStatus, graphicId);
        }
        if (num < 1) {
            throw new LogicException("状态变更失败！");
        }
        return new TransDTO<Integer>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(graphicId);
    }

    /**
     * 浏览数
     *
     * @param graphicId
     * @return
     */
    @GetMapping("/add_view")
    public TransDTO<Boolean> addViewCount(@RequestParam(value = "graphic_id") Integer graphicId) {
        TransDTO<Boolean> dto = new TransDTO<>();
        if (graphicId == null) {
            return dto.withCode(org.apache.http.HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("图文id不能为空");
        }
        Boolean result = graphicService.addViewCount(graphicId);
        return dto.withSuccess(true).withCode(org.apache.http.HttpStatus.SC_OK).withData(result);
    }

    /**
     * 预览图文
     *
     * @param graphicId
     * @return
     */
    @GetMapping("/get_graphic_details")
    public TransDTO<GraphicDetailsDTO> getGraphicDetails(@RequestParam("graphic_id") Integer graphicId) {
        if (!graphicService.graphicAllAuthCheck(graphicId)) {
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        GraphicDetailsDTO graphicDetailsDTO = graphicService.getGraphicDetails(graphicId);
        if (null == graphicDetailsDTO) {
            return new TransDTO<GraphicDetailsDTO>().withSuccess(false).withCode(HttpStatus.SC_NOT_FOUND).withMessage("当前图文不存在");
        }
        return new TransDTO<GraphicDetailsDTO>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(graphicDetailsDTO);
    }

    /**
     * 浏览图文
     *
     * @param graphicId
     * @return
     */
    @GetMapping("/view_graphic")
    public TransDTO<GraphicDetailsDTO> viewGraphic(@RequestParam(value = "graphic_id") Integer graphicId,
                                                   @RequestParam(value = "enable_view", required = false) Boolean enableView) {
        GraphicDetailsDTO graphicDetailsDTO;
        Graphic graphic = graphicService.getGraphicById(graphicId);
        String staffId = GatewayContext.current().getStaffId();
        //浏览图文
        if (null == enableView || enableView) {
            if (!graphicService.graphicAllAuthCheck(graphicId) && !graphicService.checkTargetList(staffId, graphic.getTargetList())) {
                throw new AuthException("您暂时没有访问该页面的权限！");
            }
            graphicDetailsDTO = graphicService.viewGraphic(graphicId);
            graphicDetailsDTO.setCanEdit(graphicService.graphicAllAuthCheck(graphicId));
        } else {
            //预览图文
            if (!graphicService.graphicAllAuthCheck(graphicId)) {
                throw new AuthException("您暂时没有访问该页面的权限！");
            }
            graphicDetailsDTO = graphicService.getGraphicDetails(graphicId);
        }
        return new TransDTO<GraphicDetailsDTO>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(graphicDetailsDTO);
    }

    /**
     * 上报访问记录
     * 里面的分享的积分上报逻辑只和分享人、作品作者相关。和当前登录人无关。所以积分结果不用返回给当前登录人
     *
     * @param graphicId
     * @param sharerId
     * @param sharerName
     * @param fromType
     * @param recordId
     * @return
     */
    @GetMapping("/view_graphic/record")
    public TransDTO<Integer> updateGraphicViewRecord(@RequestParam(value = "graphic_id") Integer graphicId,
                                                     @RequestParam(value = "sharer_id", required = false) Integer sharerId,
                                                     @RequestParam(value = "sharer_name", required = false) String sharerName,
                                                     @RequestParam(value = "from_type", required = false) String fromType,
                                                     @RequestParam(value = "record_id", required = false) Integer recordId) {
        TransDTO<Integer> dto = new TransCustomDTO<>();
        dto.withCode(org.apache.http.HttpStatus.SC_OK);
        Graphic graphic = graphicService.getGraphicByIdEnable(graphicId);
        if (null == graphic) {
            dto.setSuccess(false);
            return dto;
        }
        Integer id = graphicService.updateGraphicViewRecord(graphicId, sharerId, sharerName, fromType, recordId, graphic);
        if (id != null){
            //学习记录上报
            graphicService.lrsStudy(id,graphic);
        }
        //表示第一次上传学习记录。判断是否需要进行判断分享逻辑
        if (null == recordId && null != sharerId && null != sharerName) {
            if (null != id) {
                ContextEntity current = GatewayContext.current();
                String staffId = current.getStaffId();
                String staffName = current.getStaffName();
                //更新积分。构建作者信息
                List<TXAgent> txAgents = new ArrayList<>();
                //给互动人和作者下发积分，多个作者的情况下，每人下发1分
                List<AuthorsDTO> authorsDTOs = JsonUtil.getListByJson(graphic.getAuthors(), AuthorsDTO.class);
                if (CollectionUtils.isNotEmpty(authorsDTOs)){
                    for (AuthorsDTO authorsDTO : authorsDTOs){
                        TXAgent authorTxAgent = new TXAgent(String.valueOf(authorsDTO.getStaffid()), authorsDTO.getStaffname(), "author");
                        txAgents.add(authorTxAgent);
                    }
                }
                //当前访问人也要上报
                TXAgent loginAgent = new TXAgent(staffId, staffName, "agent");
                txAgents.add(loginAgent);
                //上报分享积分，但是不对外展示。分享的逻辑应该是分享人和作者获取，和当前登录人无关
                lrsService.addLrsRecord(String.valueOf(sharerId), sharerName, LRSVerbs.shared, String.valueOf(graphicId), graphic.getGraphicName(), null, txAgents);
            }
        }
        dto.setSuccess(true);
        dto.setData(id);
        return dto;
    }

    /**
     * 删除图文
     *
     * @param graphicId
     * @return
     */
    @GetMapping("/delete_graphic")
    public TransDTO<Boolean> deleteGraphic(@RequestParam("graphic_id") Integer graphicId) {
        Integer num = 0;
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
        Graphic graphic = new Graphic();
        graphic.setGraphicId(graphicId);
        //删除标志
        graphic.setEnabled(false);
        graphic.setUpdateId(staffId);
        graphic.setUpdateName(staffName);
        graphic.setUpdateAt(new Date());
        //校验权限
        if (graphicService.graphicNormalAuthCheck(graphicId)) {
            graphicService.removeGraphicBasicINfoCache(graphicId);
            graphicService.removeViewGraphicCache(graphicId);
            num = graphicService.delete(graphic);
        } else {
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        //校验是否成功
        if (num < 1) {
            throw new LogicException("删除失败！");
        }
        graphicService.removeGraphicBasicINfoCache(graphicId);
        graphicService.removeViewGraphicCache(graphicId);
        return new TransDTO<Boolean>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(true);
    }

    /**
     * 我的图文列表
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("get_graphic_list")
    public TransDTO<IPage<GraphicListDTO>> getGraphicList(@RequestParam("page_no") Integer pageNo,
                                                          @RequestParam("page_size") Integer pageSize) {
        TransDTO<IPage<GraphicListDTO>> dto = new TransDTO<>();
        IPage<GraphicListDTO> page = graphicService.getGraphicList(pageNo, pageSize);
        return dto.withSuccess(true).withCode(org.apache.http.HttpStatus.SC_OK).withData(page);
    }

    /**
     * 检查是否在白名单
     *
     * @return
     */
    @GetMapping("graphic_visitor_check")
    public TransDTO<Boolean> checkVisitor() {
        String staffId = GatewayContext.current().getStaffId();
        boolean enableVisitor = graphicService.checkVisitor(staffId);
        if (!enableVisitor) {
            throw new AuthException("图文功能目前处于邀约灰度阶段，您暂未进入邀约作者名单，如需认证为图文作者，请联系circlechai详细咨询");
        }
        return new TransDTO<Boolean>().withCode(HttpStatus.SC_OK).withSuccess(true).withData(enableVisitor);
    }

    @PostMapping("/addRelationContents")
    public TransDTO<Integer> addRelationContents(@RequestBody RelationContentOutDTO relationContentDTO, @RequestParam("graphic_id") Integer graphicId) {
        Integer result = graphicService.addRelationContents(relationContentDTO, graphicId);
        return new TransDTO<Integer>().withCode(HttpStatus.SC_OK).withSuccess(true).withData(result);
    }

    /**
     * 获取当前图文的关联内容
     *
     * @param graphicId 图文id
     * @return
     */
    @GetMapping("/get_relation_content")
    public TransDTO<RelationContentOutDTO> getRelationContent(@RequestParam("graphic_id") Integer graphicId) {
        TransDTO<RelationContentOutDTO> dto = new TransDTO<>();
        if (graphicId == null) {
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("图文id不能为空");
        }
        RelationContentOutDTO result = graphicService.getRelationContent(graphicId);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(result);
    }

    /**
     * 删除关联内容
     *
     * @param id
     * @return
     */
    @GetMapping("/del_relation_content")
    public TransDTO<Boolean> delRelationContent(@RequestParam("id") Integer id) {
        TransDTO<Boolean> dto = new TransDTO<>();
        Boolean result = graphicService.delRelationContent(id);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(result);
    }

    /**
     * 获取课程所关联的图文,通过actType
     *
     * @param actType
     * @param itemId
     */
    @GetMapping("/get_relation_graphic_by_actType")
    public TransDTO<IPage<RelationGraphicDTO>> getRelationGraphicByActType(@RequestParam("act_type") Integer actType,
                                                                           @RequestParam("act_id") String itemId,
                                                                           @RequestParam(value = "order_by_desc", required = false) String orderByDesc,
                                                                           @RequestParam("page_no") Integer pageNo,
                                                                           @RequestParam("page_size") Integer pageSize) {
        TransDTO<IPage<RelationGraphicDTO>> dto = new TransDTO<>();
        IPage<RelationGraphicDTO> page = graphicService.getRelationGraphicByActType(actType, itemId, orderByDesc, pageNo, pageSize);
        return dto.withSuccess(true).withCode(org.apache.http.HttpStatus.SC_OK).withData(page);
    }

    /**
     * 通过笔记id获取图文id，并跳转到图文id
     *
     * @param noteId
     * @return
     */
    @GetMapping("/main/note-write/note-details")
    public void getRelationGraphicDTOS(@RequestParam("id") String noteId,
                                       HttpServletResponse response) {
        Integer graphicId = graphicService.getGraphicIdByNoteId(noteId);
        if (null == graphicId) {
            throw new NotFoundException("当前图文不存在！");
        }
        response.addHeader("location", shortUrl + graphicId);
        response.addHeader("content-type", "text/html; charset=UTF-8");
        response.setStatus(302);
    }
}
