package com.tencent.hr.knowservice.businessCommon.controller.common;


import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.service.ActCommonService;
import com.tencent.hr.knowservice.businessCommon.service.FileProdConnService;
import com.tencent.hr.knowservice.framework.dto.ActExtendStudyDto;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/ql/common")
public class ActCommonController {


    @Autowired
    private ActCommonService actCommonService;

    @Autowired
    private FileProdConnService fileProdConnService;

    /**
     * 添加延伸学习
     * @param extendStudyDto
     * @return
     */
    @PostMapping("/file-prod/add-file-prod")
    TransDTO addExtendStudy(@RequestBody ActExtendStudyDto extendStudyDto) {
        TransDTO<Object> dto = new TransDTO<>();
            boolean obj = fileProdConnService.addExtendStudy(extendStudyDto);
            dto.withSuccess(obj).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     *  获取延伸学习数据
     * @param actType
     * @param title
     * @param prodId   用于排除已选的数据
     * @param prodType
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/file-prod/conn-list")
    TransDTO getFileProdList(@RequestParam(name = "act_type", required = false) String actType,
                       @RequestParam(name = "title", required = false) String title,
                       @RequestParam(name = "prod_id", required = false) String prodId,
                       @RequestParam(name = "prod_type", required = false) String prodType,
                       @RequestParam(name = "page_no", required = true) int pageNo,
                       @RequestParam(name = "page_size", required = false, defaultValue = "20") int pageSize) {
        TransDTO<Object> dto = new TransDTO<>();

            Object obj = actCommonService.getFileProdList(actType, title, prodId, prodType, pageNo, pageSize);
            dto.withData(obj).withSuccess(true).withCode(HttpStatus.SC_OK);


        return dto;
    }


}
