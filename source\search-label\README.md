# test

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### 关于插件 打包时需要修改package.json文中里面的name、version、main的值
```
测试环境
name: "@tencent/sdc-search-label-test"
version: "0.0.1" (改成最新的版本+1)
main: "lib/sdc-search-label-test.common.js"
```

```
生产环境
name: "@tencent/sdc-search-label"
version: "0.10.10" (改成最新的版本+1)
main: "lib/sdc-search-label.common.js"
```
### npm仓库地址：https://mirrors.tencent.com/#/private/npm
