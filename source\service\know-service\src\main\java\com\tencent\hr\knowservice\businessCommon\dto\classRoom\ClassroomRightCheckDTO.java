package com.tencent.hr.knowservice.businessCommon.dto.classRoom;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/29/15:36
 * @version: 1.0
 */
@Data
public class ClassroomRightCheckDTO {

    /**
     * 课程开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 课程结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 互动课堂id
     */
    private String classroomId;

    /**
     * 课程id
     */
    private String itemId;

    /**
     * 是否开启互动课堂
     */
    private Boolean hasClassroom;

    /**
     * 数据类型 3-班级 4-活动
     */
    private Integer actType;
}
