<template>
  <fragment>
    <sdc-selector :custom-class="`sdc-staff-selector ${selectClass}`" pasteable mode="staff" ref="selector">
      <template slot-scope="{data}" slot="selector-item">
        <div class="selector-item" :key="data.option[selectorMap.staffID]">
          <img class="item-avatar" :src="avatarUrl" v-set-img="data.option[selectorMap.avatar]"/>
          <span class="item-name" v-html="highlight(data.option[selectorMap.staffName],data.keyword)"></span>
          <span class="item-former-name" v-if="data.option.IsFormerName"></span>
        </div>
      </template>
    </sdc-selector>
    <sdc-selector-modal
      ref="modal"
      :data="selected"
      :selectedNew="selectedNew"
      :riskResultProp="riskResult"
      :studentStaffId="studentStaffId"
      :student_resume_id="student_resume_id"
      :workPlaceInfo="workPlaceInfo"
      :deptId="deptId"
      :talentAndSelf="talentAndSelf"
      :failNum="failNum"
      mode="staff"
      @getReason="(value) => $emit('getReason', value)"
      @changeFailNum="changeFailNum"
      >
      <!-- :operatorId="operatorId" -->
      <template slot-scope="{data}" slot="item">
        <div class="selector-item" :key="data.option[selectorMap.staffID]">
          <img class="item-avatar" :src="avatarUrl" v-set-img="data.option[selectorMap.avatar]"/>
          <span class="item-name" v-html="highlight(data.option[selectorMap.staffName],data.keyword)"></span>
          <span class="item-former-name" v-if="data.option.IsFormerName"></span>
        </div>
      </template>
    </sdc-selector-modal>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { selector, highlight, locale, encryption } from 'mixins'
  import StaffService from 'api/staff.service'
  import SdcSelector from 'packages/selector'
  import SdcSelectorModal from './modal'
  import SetImg from 'directives/set-img'
  import loadingFn from 'packages/loading'
  import http from 'api/https.js'

  export default {
    name: 'sdc-mentor-selector',
    mixins: [selector, highlight, locale, encryption],
    directives: {
      setImg: SetImg
    },
    props: {
      entryReason: { // 匹配原因回显--部分系统保存草稿时需要用  先取组件传进来的，没有就拿"核心人事"的数据
        type: String,
        default: ''
      },
      studentStaffId: { // 新员工staffId
        type: [String, Number],
        default: ''
      },
      student_resume_id: { // 新员工的简历id
        type: [String, Number],
        default: ''
      },
      workPlaceInfo: { // 工作地信息 非必传
        type: Object,
        default: () => (
          {
            placeName: '',
            place_id: ''
          }
        )
      },
      deptId: { // 学生的最小组织id 非必传
        type: [String, Number],
        default: ''
      },
      // isSelf: { // 是否是本人修改
      //   type: Boolean,
      //   default: false
      // },
      platform: { // 平台(人才透视系统1、招聘2、入职准备3、入职入场4、大异动系统5)
        type: String,
        default: ''
      },
      operaTypeId: { // 操作人身份：组织bp：1，直接上级：2，员工本人：3，招聘经理：4，运营人员：5
        type: [String, Number],
        default: ''
      },
      // operatorId: { // 操作人staffId(必传)
      //   type: [String, Number],
      //   default: ''
      // },
      env: { // 接口环境
        type: String,
        default: 'prod'
      },
      icon: {
        type: String,
        default: 'staff'
      },
      includeDimission: {
        type: Boolean,
        default: false
      },
      includeOnBoarding: {
        type: Boolean,
        default: false
      },
      includePartTimePost: {
        type: Boolean,
        default: true
      },
      useFormerNameSearch: {
        type: Boolean,
        default: false
      },
      getDataList: {
        type: Function,
        default: StaffService.getDataList
      },
      getPasteResult: {
        type: Function,
        default: StaffService.getPasteResult
      },
      getTreeData: {
        type: Function,
        default: StaffService.getTreeData
      },
      getChildrenData: {
        type: Function,
        default: StaffService.getChildrenData
      }
    },
    data() {
      const selectorMap = {
        staffID: 'StaffID',
        staffName: 'StaffName',
        engName: 'EngName',
        unitID: 'UnitID',
        unitName: 'UnitName',
        unitFullName: 'UnitFullName',
        avatar: 'Avatar',
        type: {
          staff: 'staff',
          unit: 'unit'
        }
      }
      return {
        valueKey: selectorMap.staffName,
        nodeKey: selectorMap.staffID,
        selectedText: this.$st('sdc.staffSelector.selected'),
        totalText: this.$st('sdc.staffSelector.total'),
        modalProps: {
          title: this.$st('sdc.staffSelector.title')
        },
        treeProps: {
          isLeaf: 'isLeaf'
        },
        selectorProps: { ...{ staffID: 'StaffID', staffName: 'StaffName', avatar: 'Avatar', engName: 'EngName', unitID: 'UnitID', unitName: 'UnitName', unitFullName: 'UnitFullName' }, ...this.props },
        selectorMap,
        queryParams: {
          includeDimission: this.includeDimission,
          includeOnBoarding: this.includeOnBoarding,
          includePartTimePost: this.includePartTimePost,
          useFormerNameSearch: this.useFormerNameSearch
        },
        selectedNew: [], // 当校验有风险时，用于存储选中的数据
        disabledMentorList: [], // 禁用的导师列表
        riskResult: {}, // 导师匹配的风险结果 是否符合认证要求：DISABLED(-1, "禁用"),QUALIFIED(1, "合格"),RISKY(3, "风险");L4Manage(4, "L4以上高管");
        failNum: 1, // 同一个导师校验失败的次数
        specialPlatform: [2, '2'], // 不需要核心人事"原因"数据的平台
        operatorRightMap: [], // 操作人的管理权限范围
        interfaceTypeId: '', // 接口返回的操作人身份（没有传身份参数时使用）
        requireWrongNum: 0, // 操作人身份接口报错次数
        requireWrongLoaading: false // 操作人身份接口报错 刷新时的loading
      }
    },
    computed: {
      avatarUrl() {
        return require('packages/theme-grace/img/avatar.gif')
      },
      baseUrl() {
        const env = window.SDC_BUILD_ENV || this.env || 'production'
        const proUrl = '//learn.woa.com/training'
        const devUrl = '//test-portal-learn.woa.com/training'
        return ['production', 'uat', 'prd', 'prod'].includes(env) ? proUrl : devUrl
      },
      talentAndSelf() { // 人才透视系统并且是本人修改
        return [1, '1'].includes(this.platform) && ['3'].includes(this.typeId)
      },
      typeId() { // 操作人身份
        if (this.interfaceTypeId) {
          return this.interfaceTypeId
        }
        let id = ''
        if (String(this.platform) === '1') { // 人才透视
          id = this.operaTypeId || ''
        } else if (String(this.platform) === '2') { // 招聘
          id = this.operaTypeId || '4'
        } else if (['3', '4', '5'].includes(String(this.platform))) { // 入职入场、入职准备、异动
          id = this.operaTypeId || ''
        }
        return String(id)
      }
    },
    mounted() {},
    watch: {
      talentAndSelf: {
        handler(newValue) {
          if (newValue) {
            this.getDisabledMentorList()
          }
        },
        immediate: true
      },
      typeId: {
        handler(newValue) {
          if (String(this.platform) === '1' && !newValue) return // 人才透视系统必填
          if (String(this.platform) !== '1' && this.typeId === '3') return // "非人才透视系统"选择了"员工本人"不让选，只有"人才透视系统"才能选"员工本人"
          if (this.interfaceTypeId) return // 防止死循环
          console.log('typeId-----: ', newValue)
          if (newValue && ['4', '5'].includes(newValue)) { // 招聘经理、运营人员
            this.operatorRightMap = []
            return
          }
          this.operatorRightCheck()
        },
        immediate: true,
        deep: true
      }
    },
    methods: {
      changeFailNum(num) {
        this.failNum = num
      },
      // 获取禁用的导师列表
      getDisabledMentorList() {
        http.get(this.baseUrl + '/api/tutor/user/disabled/tutors').then((res) => {
          this.disabledMentorList = res || []
        })
      },
      // 选中导师后的逻辑 先发请求校验风险 没有风险才回填导师
      onSelectedCheck(data) {
        if (data && data.StaffID) {
          this.failNum = 1
          loadingFn(this.$st('sdc.loading'))
          const beforeObj = {
            student_staff_id: this.studentStaffId || '', // 候选人的员工id
            student_resume_id: this.student_resume_id || '', // 候选人的简历id
            student_org_id: String(this.deptId) === '0' ? 0 : this.deptId || '', // 候选人的最小组织id
            student_work_place_id: this.workPlaceInfo ? this.workPlaceInfo.place_id || '' : '', // 学生的工作地id
            tutor_staff_id: data.StaffID || '', // 导师id
            // staff_id: this.operatorId || '', // 当前操作的员工
            type_id: this.typeId || '', // 员工身份信息
            t: Date.now()
          }
          const params = {
            condition: this.encrypteParams(beforeObj)
          }
          http.get(this.baseUrl + `/api/tutor/user/riskCheck`, { params, timeout: 3000 }).then((res) => {
            if (res) {
              const obj = { ...res, getRiskFail: false, entry_reason: '' }
              if (!this.specialPlatform.includes(String(this.platform))) {
                obj.entry_reason = res.entry_reason || ''
              }
              this.riskResult = obj
              if ([1, '1', 2, '2'].includes(this.riskResult.check_result) || (this.talentAndSelf && [3, '3'].includes(this.riskResult.check_result)) || (!this.talentAndSelf && [4, '4'].includes(this.riskResult.check_result))) {
                this.change([data])
              } else {
                this.selectedNew = [data]
                this.$refs.modal && this.$refs.modal.showModal()
              }
            } else {
              this.riskResult = { getRiskFail: true }
              this.selectedNew = [data]
              this.$refs.modal && this.$refs.modal.showModal()
            }
          }).catch(err => {
            console.error('riskCheck---err: ', err)
            this.riskResult = { getRiskFail: true }
            this.selectedNew = [data]
            this.$refs.modal && this.$refs.modal.showModal()
          }).finally(() => {
            loadingFn.hide()
          })
        }
      },
      // 编辑回显时的校验处理逻辑
      onSelectedCheckEdit(data) {
        if (data && data.StaffID) {
          const beforeObj = {
            student_staff_id: this.studentStaffId || '', // 候选人的员工id
            student_resume_id: this.student_resume_id || '', // 候选人的简历id
            student_org_id: String(this.deptId) === '0' ? 0 : this.deptId || '', // 候选人的最小组织id
            student_work_place_id: this.workPlaceInfo ? this.workPlaceInfo.place_id || '' : '', // 学生的工作地id
            tutor_staff_id: data.StaffID || '', // 导师id
            // staff_id: this.operatorId || '', // 当前操作的员工
            type_id: this.typeId || '', // 员工身份信息
            t: Date.now()
          }
          const params = {
            condition: this.encrypteParams(beforeObj)
          }
          http.get(this.baseUrl + `/api/tutor/user/riskCheck`, { params, timeout: 3000 }).then((res) => {
            if (!res) {
              this.riskResult = { getRiskFail: true, entry_reason: this.entryReason || '' }
              return
            }
            const obj = { ...res, getRiskFail: false, entry_reason: '' }
            if (this.entryReason) {
              obj.entry_reason = this.entryReason
            } else if (!this.specialPlatform.includes(String(this.platform))) {
              obj.entry_reason = res.entry_reason || ''
            }
            this.riskResult = obj
          }).catch(() => {
            this.riskResult = { getRiskFail: true, entry_reason: this.entryReason || '' }
          })
        }
      },
      setSelectedInit(val) {
        if (val) {
          this.setSelected(val)
          this.onSelectedCheckEdit(val)
        }
      },
      getCurrentItem(item) {
        return {
          key: item[this.nodeKey] || 0,
          text: item[this.valueKey] || '',
          tags: { minLength: 14, before: 8, after: 5 },
          modal: { minLength: 21, before: 9, after: 11 }
        }
      },
      // 获取操作人的管理权限范围、操作人身份
      operatorRightCheck(isReflesh = false) {
        // if (!this.operatorId) return
        if (isReflesh) {
          this.requireWrongLoaading = true
        }
        const params = { type_id: this.typeId, t: Date.now() }
        http.get(this.baseUrl + `/api/tutor/user/rightCheck`, { params, timeout: 3000 }).then((res) => {
          console.log('operatorRightCheck------res: ', res)
          this.requireWrongNum = 0
          this.interfaceTypeId = res.opt_staff_type_id ? String(res.opt_staff_type_id) : ''
          let result = res.org_ids
          if (!result || (result && !result.length)) { // 无权限，按"运营人员"处理
            this.interfaceTypeId = '5'
            result = []
          } else if (result && result.length === 1 && result[0] === 0) { // 全公司
            result = [] // 不设置默认是 全公司
          }
          this.operatorRightMap = result
        }).catch((err) => {
          console.log('err~~~~: ', err)
          this.requireWrongNum++
          if (this.requireWrongNum >= 3) {
            this.requireWrongNum = 0
            this.interfaceTypeId = '5'
            this.operatorRightMap = []
          }
        }).finally(() => {
          if (isReflesh) {
            setTimeout(() => {
              this.requireWrongLoaading = false
            }, 150)
          }
        })
      }
    },
    components: {
      Fragment,
      SdcSelector,
      SdcSelectorModal
    }
  }
</script>
