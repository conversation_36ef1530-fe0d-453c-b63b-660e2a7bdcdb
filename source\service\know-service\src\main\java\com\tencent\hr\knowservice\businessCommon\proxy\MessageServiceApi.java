package com.tencent.hr.knowservice.businessCommon.proxy;


import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.message.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name="training-portal-common",url="${project.portal-common-host}")
public interface MessageServiceApi {

    /**
     * 发送邮件
     * @param msg
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-mail")
    TransDTO sendMail(@RequestBody MailMessage msg) ;

    /**
     * 发送企业微信tips消息
     * @param msg
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-tips")
    TransDTO sendTips(@RequestBody TipsMessage msg) ;

    /**
     * 发送短信
     * @param msg
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-sms")
    TransDTO sendSMS(@RequestBody SmsMessage msg) ;

    /**
     * 发送机器人消息 （推荐）
     * @param msg
     * {
     *     "title":"测试消费服务发送机器人消息",
     *     "msg_type":"markdown",
     *     "content":"{\"content\":\"vincentyqwu 学员名称 20200601测试 课程/专题名  0%  学习进度\\n这是 机器人 内容\"}",
     *     "receiver":"vincentyqwu;graywu;",
     *     "sender":"xiaoteng",
     *     "can_send":true,
     *     "prepare_send_time":"2022-06-01 10:00:02",
     *     "act_type":11
     * }
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-bot")
    TransDTO sendBot(@RequestBody BotMessage msg) ;

    /**
     * 发送机器人消息(兼容mooc,后续过期）
     * @param msg
     * {
     *     "title":"测试消费服务发送机器人消息",
     *     "content":"vincentyqwu 学员名称 20200601测试 课程/专题名  0%  学习进度\n这是 机器人 内容",
     *     "receiver":"vincentyqwu;graywu;",
     *     "sender":"xiaoteng",
     *     "can_send":true,
     *     "prepare_send_time":"2022-06-01 10:00:02",
     *     "act_type":11
     * }
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-bot-msg")
    TransDTO sendBotForMooc(@RequestBody BotMessage msg) ;

    /**
     * 发送HR助手消息
     * @param msg
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-hr-assistant")
    TransDTO sendHrAssistant(@RequestBody HrAssistantMessage msg) ;

    /**
     * 发送日历
     * @param msg
     * @return
     */
    @PostMapping("/api/v1/common/service/message/send-calendar")
    TransDTO sendCalendar(@RequestBody CalendarMessage msg) ;
}
