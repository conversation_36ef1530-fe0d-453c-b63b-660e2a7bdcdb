package com.tencent.hr.knowservice.courseInteraction.dao;

import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.courseInteraction.constans.CacheKeyEnum;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.InteractiveConfig;
import com.tencent.hr.knowservice.courseInteraction.dto.condition.ConInteractiveConfig;
import com.tencent.hr.knowservice.utils.CommonUtils;
import com.tencent.hr.knowservice.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


/**
 * @description: mongo中interactiveConfig的dao
 * @author: shi<PERSON><PERSON>
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Service
public class InteractiveDao {

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    RedisUtil redisUtil;

    private final String collectionName="interactiveConfig";

    /**
     * 查询条件转换
     * @param condition
     * @return
     */
    private Query convertCondition(ConInteractiveConfig condition) {
        Query query = new Query();
        //课程id
        if (null != condition.getCourseId()){
            query.addCriteria(Criteria.where("courseId").is(condition.getCourseId()));
        }
        //课程类型
        if (null != condition.getActType()){
            query.addCriteria(Criteria.where("actType").is(condition.getActType()));
        }
        return query;
    }

    /**
     * 根据查询条件获取配置信息
     * @param condition
     * @return
     */
    public InteractiveConfig getInteractionConfigByCondition(ConInteractiveConfig condition) {
        Query query = convertCondition(condition);
        InteractiveConfig data = mongoTemplate.findOne(query, InteractiveConfig.class,collectionName);
        return data;
    }

    /**
     * 根据_id 查询互动配置
     */
    public InteractiveConfig getInteractionConfigById(String _id) {
        String redisKey = CacheKeyEnum.BaseInteractiveInfo.getKey() + _id;
        Object redisVal = redisUtil.get(redisKey);
        if (null != redisVal){
            return (InteractiveConfig) redisVal;
        }else {
            Query query = new Query(Criteria.where("_id").is(_id));
            InteractiveConfig data = mongoTemplate.findOne(query, InteractiveConfig.class,collectionName);
            redisUtil.set(redisKey,data, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
            return data;
        }
    }
    /**
     * 保存课程互动规则
     * @param entity
     * @return
     */
    public String saveInteractiveConfig(InteractiveConfig entity){
        String key = entity.get_id();
        if(StringUtils.isEmpty(key)) {
            key = CommonUtils.generateShortUUID();
            entity.set_id(key);
        }
        mongoTemplate.save(entity,collectionName);
        return key;
    }

    /**
     * 查询互动标题
     * @param id 唯一id
     * @return
     */
    public InteractiveConfig getInteractionTitleById(String id){
        // 查询互动标题
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id));
        query.fields().include("configurationsOfSelect.title").include("configurationsOfSelect.title_en").include("configurationsOfSelect.interactiveId");
       return mongoTemplate.findOne(query, InteractiveConfig.class,collectionName);
    }

}
