package com.tencent.hr.knowservice.framework.config.interceptor;

import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.dto.MultiLang.Lang;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.FakeUserDto;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 登录控制
 */
@Component
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {

    @Value("${spring.profiles.active}")
    private String env;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        String staffId = "";
        String staffName = "";
        String globalId = "";
        String nickName = "";
        String appKey = "";
        String corpKey = "";
        String corpId = "";
        String corpName = "";
        String platform = "";
        String regionId = "";
        String channel = "";
        String language = "";
        /*String logstr = "Current网关header信息：";
        Enumeration<String> er = request.getHeaderNames();
        while(er.hasMoreElements()) {
            String name = (String) er.nextElement();
            String value = request.getHeader(name);
            logstr += name + "=" + value + ";";
        }
        log.info(logstr);*/
        channel = request.getHeader(Constants.TASHeaderEnum.CHANNEL.toString());
        staffId = request.getHeader(Constants.TASHeaderEnum.STAFF_ID.toString());
        staffName = request.getHeader(Constants.TASHeaderEnum.USER_NAME.toString());
        globalId = request.getHeader(Constants.TASHeaderEnum.GLOBAL_ID.toString());
        nickName = request.getHeader(Constants.TASHeaderEnum.NICK_NAME.toString());
        corpKey = request.getHeader(Constants.TASHeaderEnum.CORP_KEY.toString());
        appKey = request.getHeader(Constants.TASHeaderEnum.APP_KEY.toString());
        corpId = request.getHeader(Constants.TASHeaderEnum.CORP_ID.toString());
        corpName = request.getHeader(Constants.TASHeaderEnum.CORP_NAME.toString());
        platform = request.getHeader(Constants.TASHeaderEnum.PLAT_FORM.toString());
        regionId = request.getHeader(Constants.TASHeaderEnum.REGION_ID.toString());
        language = request.getHeader(Constants.TASHeaderEnum.Language.toString());
        //sso取网关的appid,esb和pub去用户自己传的appid
        if ("ESB".equals(channel) || "PUB".equals(channel)) {
            appKey = request.getHeader(Constants.TASHeaderEnum.HRGW_APPID.toString());
            corpId = request.getHeader(Constants.TASHeaderEnum.CC_CORPID.toString());
            corpName = request.getHeader(Constants.TASHeaderEnum.CC_CORPNAME.toString());
        }
        //本地环境id
        if ("dev".equals(env) && StringUtils.isBlank(staffId)) {
            staffId = FakeUserDto.STAFF_ID.toString();
            staffName = FakeUserDto.STAFF_NAME.toString();
        }
        String uri = request.getRequestURI();
        //非定时任务的路径，判断staffId。没有登录不能访问
        if (StringUtils.isEmpty(staffId) && !uri.startsWith("/api/crontab/") && !uri
                .startsWith("/api/custom-right/ext/") && !(uri.startsWith("/api/label/user/") && "ESB"
                .equalsIgnoreCase(channel))) {
            log.warn("当前用户未登录！");
            return false;
        }
        ContextEntity current = new ContextEntity();
        current.setStaffId(staffId);
        current.setStaffName(staffName);
        current.setGlobalId(globalId);
        current.setNickName(nickName);
        current.setAppId(appKey);
        current.setCorpKey(corpKey);
        current.setCorpId(corpId);
        current.setCorpName(corpName);
        current.setPlatform(platform);
        current.setRegionId(regionId);
        current.setChannel(channel);
        current.setLanguage(StringUtils.isEmpty(language) ? Lang.ZH_CN : language);//默认中文  中文：zh-cn 英文：en-us
        current.setCurrentRequest(request);
        GatewayContext.set(current);

        return true;
    }
}
