// 整个包的入口
// 统一导出
// 导出颜色选择器组件
import Vue from 'vue'
import { Dialog, Button, Image, Link } from 'element-ui'
import ElementUI from 'element-ui';
Vue.use(ElementUI)
import 'element-ui/lib/theme-chalk/index.css';

import "element-ui/packages/theme-chalk/lib/base.css";
import "element-ui/packages/theme-chalk/lib/dialog.css";
import "element-ui/packages/theme-chalk/lib/message.css";
import "element-ui/packages/theme-chalk/lib/message-box.css";
import "element-ui/packages/theme-chalk/lib/image.css";
Vue.use(Dialog)
Vue.use(Button)
Vue.use(Image)
Vue.use(Link)


import sdcSearchLabel from "./comment";
 
const components = [
  sdcSearchLabel
]
// 定义install方法 接收Vue作为参数，如果使用use注册插件，那么所有的组件都会被注册
const install = function (Vue) {
  if (install.installed) return;
  components.map(component => Vue.component(component.name, component))
}

// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  install,
  sdcSearchLabel
}

