package com.tencent.hr.knowservice.courseInteraction.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.courseInteraction.dao.InteractiveRecordDetails;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-10-12 09:33:27
 */
@Mapper
public interface InteractiveRecordMapper extends BaseMapper<InteractiveRecordEntity> {

    /**
     * 自定义批量插入 回显id
     * @param list 数据集 每次最多 100条
     */
    void insertBatch(@Param("list") List<InteractiveRecordEntity> list);

    InteractiveRecordDetails getInteractiveRecords(@Param("id") String id);
}
