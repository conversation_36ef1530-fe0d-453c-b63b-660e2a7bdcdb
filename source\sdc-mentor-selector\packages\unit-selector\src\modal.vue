<template>
  <div class="selector-modal">
    <sdc-modal ref="modal" :title="modalProps.title" :width="modalWidth || '750px'" adaptive :showFooter="false" :customClass="modalClass" :appendToBody="modalAppendToBody" :class="{'selector-modal-append-to-body': modalAppendToBody}">
      <div slot="body">
        <div class="left-side">
          <div class="tree-list">
            <el-tree ref="tree" :check-strictly="true" :props="treeProps" :data="treeData" lazy :node-key="nodeKey" :load="loadNode" v-if="opened" v-loading="loading" @node-click="nodeClick">
              <span class="tree-node" slot-scope="{ node }">
                <span class="tree-node-text">{{ node.label }}</span>
                <!-- <el-checkbox v-model="data.checked" @change="handleCheckChange(node)" @click.native.stop/> -->
                <el-checkbox v-if="filterValue.length === 0 || !(filterKey in node.data) || filterValue.indexOf(node.data[filterKey]) > -1" v-model="node.checked" @change="handleCheckChange(node)"  @click.native.stop/>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="right-side">
          <div class="selected-info">
            <span>{{selectedItemsText}}</span>
            <i class="el-icon-delete" @click="handleClear" v-show="selectedData.length"></i>
          </div>
          <div class="selected-list" :key="reloadKey">
            <div class="list-item" v-for="(item, index) in getSelectedItems(selectedData)" :key="index">
              <el-tooltip placement="top-end" :content="item._text" :disabled="item._text.length <= item._modal.minLength" popper-class="sdc-selector-modal-popper">
                <span class="list-item-name">{{textEllipsis(item._text, item._modal) }}</span>
              </el-tooltip>
              <span @click="handleDelete(item)" class="list-item-icon"><i class="el-icon-error"></i></span>
            </div>
          </div>
          <div class="modal-buttons">
            <el-button size="small" @click="hide">{{$st('sdc.selector.modal.cancel')}}</el-button>
            <el-button size="small" type="primary" @click="handleConfirm">{{$st('sdc.selector.modal.ok')}}</el-button>
          </div>
        </div>
      </div>
    </sdc-modal>
  </div>
</template>

<script>
  import { DataUtil } from 'sdc-core'
  import { textEllipsis, isNotLogin } from 'sdc-webui/src/utils/main'
  import { modal, locale, map } from 'mixins'
  import Toast from 'packages/toast'

  export default {
    name: 'selector-modal',
    inject: ['multiple', 'textarea', 'nodeKey', 'change', 'selectedText', 'treeProps', 'modalProps', 'queryParams', 'getTreeData', 'getCurrentItem', 'filterKey', 'filterValue', 'modalClass', 'modalWidth', 'defaultExpandedKeys', 'modalAppendToBody'],
    mixins: [modal, locale, map],
    props: {
      data: {
        type: Array,
        default: () => []
      },
      unitID: {
        type: [Array, Number],
        default: 0
      },
      isLimitUnitExpand: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        selectedCount: 0,
        selectedData: [],
        treeData: [],
        opened: false,
        loading: false,
        resolveTree: null,
        filterLowLevel: null,
        reloadKey: 0
      }
    },
    mounted() {
      if (this.filterValue) {
        const unitSortIDs = [2, 7, 1, 8, 6] // 6-bg、8-线、1-部门、7-中心、2-组
        for (const unitSortID of unitSortIDs) {
          if (this.filterValue.includes(unitSortID)) {
            this.filterLowLevel = unitSortID
            break
          }
        }
      }
    },
    computed: {
      selectedItemsText() {
        return this.selectedText.replace('$count', this.selectedCount)
      }
    },
    watch: {
      // unitID改变重新设置为初始状态
      unitID() {
        this.opened = false
        this.treeData = []
      }
    },
    methods: {
      textEllipsis,
      showModal() {
        if (this.opened) {
          // 非首次打开
          const level0Node = this.$refs.tree.root
          if (level0Node.childNodes.length === 0) {
            this.loadNode(level0Node, this.resolveTree)
          }
        }
        this.opened === false && (this.opened = true) // 首次打开再访问数据
        this.selectedData = DataUtil.clone(this.data)
        this.selectedCount = this.selectedData.length
        const traverse = (node) => {
          const childNodes = node.root ? node.root.childNodes : node.childNodes
          childNodes.forEach(node => {
            node.checked = this.selectedData.some(item => item[this.nodeKey] === node.data[this.nodeKey])
            traverse(node)
          })
        }
        if (this.$refs.tree) {
          traverse(this.$refs.tree.root)
        }
        this.show()
      },
      handleCheckChange(node) {
        const { isLeaf, ...data } = node.data
        if (node.checked && !this.multiple) {
          this.selectedData.forEach(item => this.handleDelete(item))
        }
        this.updateSelectedData(data, node.checked ? 'add' : 'delete')
      },
      handleDelete(data) {
        this.updateSelectedData(data, 'delete')
        const node = this.$refs.tree.getNode(data[this.nodeKey])
        if (node) {
          node.checked = false
        }
      },
      handleConfirm() {
        const data = DataUtil.clone(this.selectedData)
        this.change(data)
        this.hide()
      },
      updateSelectedData(data, type) {
        if (type === 'add') {
          if (!this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])) {
            this.selectedData.push(data)
            this.selectedCount++
          }
        } else if (type === 'delete') {
          const index = this.selectedData.findIndex(item => item[this.nodeKey] === data[this.nodeKey])
          if (index !== -1) {
            this.selectedData.splice(index, 1)
            this.selectedCount--
          }
        }
      },
      loadNode(node, resolve) {
        if (!this.resolveTree) {
          this.resolveTree = resolve
        }
        // 判断组件是否有传unitID进来，有传的话需要兼容处理，原先Number类型，现在Array类型，需要把Number转Array
        const id = node.level === 0 ? (this.unitID ? (!Array.isArray(this.unitID) ? [this.unitID] : this.unitID) : this.unitID) : node.data[this.nodeKey]
        this.loading = true
        this.getTreeData(id, { ...this.queryParams }).then(res => {
          if (this.loading) {
            this.loading = false
          }
          // 限制最小级别不能展开
          if (this.filterLowLevel) {
            res = res.map(item => ({
              ...item,
              isLeaf: item.isLeaf || (this.isLimitUnitExpand ? this.filterLowLevel === item[this.filterKey] : false)
            }))
          }
          resolve(res)
          if (this.$refs.tree) {
            const checkedNodeKeys = res.filter(data => this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])).map(item => item[this.nodeKey])
            this.$refs.tree.setCheckedKeys([...checkedNodeKeys, ...this.$refs.tree.getCheckedKeys()])
          }
          if (node.level === 0 && this.defaultExpandedKeys && this.defaultExpandedKeys.length) {
            node.childNodes.forEach(item => {
              if (this.defaultExpandedKeys.includes(item.data[this.nodeKey])) {
                item.expand()
              }
            })
          }
        }).catch(res => {
          resolve([])
          Toast(isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed'), 2000)
          if (this.loading) {
            this.loading = false
          }
          node.expanded = false
          node.loaded = false
          node.isLeaf = false
        })
      },
      handleClear() {
        this.selectedData.length = 0
        this.reloadKey++
        this.selectedCount = 0
        this.$refs.tree.setCheckedKeys([])
      },
      nodeClick(data, node) {
        if ((this.filterValue.length === 0 || !(this.filterKey in node.data) || this.filterValue.indexOf(node.data[this.filterKey]) > -1) && node.isLeaf) {
          node.checked = !node.checked
          this.handleCheckChange(node)
        }
      }
    }
  }
</script>
