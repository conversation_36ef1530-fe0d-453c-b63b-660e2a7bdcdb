package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class ExtendContentOutDTO {

    private Integer id;
    /**
     * 关联产品类型
     *
     */
    @NotNull(message = "关联课程类型不能为空")
    private Integer contentModuleId;
    /**
     * 关联产品类型名称
     *
     */
    @NotBlank(message = "关联课程类型名称不能为空")
    private String contentModuleName;
    /**
     * 关联产品id
     */
    private String contentItemId;
    /**
     * 1：站内内容 2:自定义内容
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;
    /**
     * 产品名称
     */
    @NotBlank(message = "内容名称不能为空")
    private String contentName;

    /**
     * 跳转链接
     */
    @NotBlank(message = "链接不能为空")
    private String href;

    /**
     * 封面图id
     */
    private String contentCoverImgId;
    /**
     * 观看数
     */
    private Integer viewCount;
    /**
     * 评分
     */
    private Double avgScore;
    /**
     * 关联产品创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
    private Date contentCreatedTime;
    /**
     * 可参与人群
     */
    private String targetPeople;
}
