import babel from '@rollup/plugin-babel';
import { terser } from 'rollup-plugin-terser';

export default {
  input: 'src/index.js',
  output: [
    {
      file: 'dist/sdc-moocjs-integrator.js',
      format: 'umd',
      name: 'MOOC<PERSON><PERSON>',
    },
    {
      file: 'dist/sdc-moocjs-integrator.min.js',
      format: 'umd',
      name: 'MOOC<PERSON><PERSON>',
      plugins: [terser()],
    },
  ],
  plugins: [babel({ babelHelpers: 'bundled' })],
};