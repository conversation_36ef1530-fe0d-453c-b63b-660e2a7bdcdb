package com.tencent.hr.knowservice.faceClass.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.faceClass.dto.ClassDetailDTO;
import com.tencent.hr.knowservice.faceClass.service.FaceClassService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/8/18
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/faceClass/user")
public class FaceClassUserController {

    @Autowired
    FaceClassService faceClassService;

    /**
     * 获取班级详情信息
     * @param classId
     * @return
     */
    @GetMapping("/classDetail")
    public TransDTO getClassInfo(@RequestParam("class_id") Integer classId){
        ClassDetailDTO classDetail = faceClassService.getClassDetail(classId);
        return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true).withData(classDetail);
    }

}
