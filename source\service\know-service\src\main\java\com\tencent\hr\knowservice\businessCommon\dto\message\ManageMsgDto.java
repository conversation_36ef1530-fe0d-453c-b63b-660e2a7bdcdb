package com.tencent.hr.knowservice.businessCommon.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class ManageMsgDto extends EntityBase implements Serializable {

    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 发件人
     */
    private String sender;

    /**
     * 接收人
     */
    private String receiver;

    /**
     * 邮件抄送人
     */
    private String mailCc;
    /**
     * 邮件密送人
     */
    private String bcc;
    /**
     * 是否需要发送
     */
    private Boolean canSend;

    /**
     * 日历提醒组织方
     */
    private String organize;
    /**
     * 地址
     */
    private String location;
    /**
     * 开始时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 发送日期
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 定时发送时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date prepareSendTime;

    /**
     * 实际发送人数
     */
    private int sendCount;

    /**
     * 是否发送成功
     */
    private Boolean isSucessed;

    /**
     * 备注，可存失败原因
     */
    private String remark;


    private static final long serialVersionUID = 1L;
}
