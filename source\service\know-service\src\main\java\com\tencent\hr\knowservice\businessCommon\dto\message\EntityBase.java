package com.tencent.hr.knowservice.businessCommon.dto.message;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: vincentyqwu
 * @createDate: 2022/7/4
 * @version: 1.0
 */
@Data
public class EntityBase {
    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    public void SetCreatorAndUpdater(Integer staffId, String staffName){
        setCreatorId(staffId);
        setCreatorName(staffName);
        setCreatedAt(new Date());
        setUpdateId(staffId);
        setUpdateName(staffName);
        setUpdatedAt(new Date());
    }

    public void SetCreator(Integer staffId, String staffName){
        setCreatorId(staffId);
        setCreatorName(staffName);
        setCreatedAt(new Date());
    }

    public void SetUpdater(Integer staffId, String staffName){
        setUpdateId(staffId);
        setUpdateName(staffName);
        setUpdatedAt(new Date());
    }
}
