package com.tencent.hr.knowservice.businessCommon.controller.common;


import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.common.ActPraiseDto;
import com.tencent.hr.knowservice.businessCommon.service.ActPraiseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.security.auth.message.AuthException;
import java.util.ArrayList;

/**
 * 点赞功能
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/businessCommon/common/praise/")
public class ActPraiseCommonController {


    @Autowired
    private ActPraiseService actPraiseService;

    /**
     * 判断是否点赞
     *
     * @param actType
     * @param actId
     * @param interactId   评论id或提问id
     * @param interactType 1 评论  2 提问
     * @return
     * @throws AuthException
     */
    @GetMapping("check-praised")
    public TransDTO checkPraised(@RequestParam("act_type") String actType,
                                 @RequestParam("act_id") String actId,
                                 @RequestParam(value = "interact_id", required = false) Integer interactId,
                                 @RequestParam(value = "interact_type", required = false) Integer interactType,
                                 @RequestParam(value = "staff_id", required = false) Integer staffId
    ) {


        TransDTO<Object> dto = new TransDTO<>();
        ActPraiseDto praiseDto = actPraiseService.checkPraised(actType, actId, interactId, interactType);
        dto.withData(praiseDto != null).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 判断是否点赞
     *
     * @param actType
     * @param actId
     * @param interactType 1 评论  2 提问
     * @return
     * @throws AuthException
     */
    @GetMapping("check-praised-batch")
    public TransDTO checkPraisedBatch(@RequestParam("act_type") String actType,
                                      @RequestParam("act_id") String actId,
                                      @RequestParam(value = "interact_type", required = false) Integer interactType,
                                      @RequestParam(value = "staff_id", required = false) Integer staffId
    ) throws AuthException {
        TransDTO<Object> dto = new TransDTO<>();
        ArrayList<ActPraiseDto> actPraiseDtos = actPraiseService.checkPraisedBatch(actType, actId, interactType);
        dto.withData(actPraiseDtos).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 用户点赞
     *
     * @param actType
     * @param actId
     * @param interactId   评论id或提问id
     * @param interactType 1 评论  2 提问
     * @return
     * @throws AuthException
     */
    @GetMapping("add-praise")
    public TransDTO addPraise(@RequestParam("act_type") String actType,
                              @RequestParam("act_id") String actId,
                              @RequestParam(value = "act_name", required = false) String actName,
                              @RequestParam(value = "interact_id", required = false) Integer interactId,
                              @RequestParam(value = "interact_type", required = false) Integer interactType,
                              @RequestParam(value = "staff_id", required = false) Integer staffId,
                              @RequestParam(value = "staff_name", required = false) String staffName,
                              @RequestParam(value = "author", required = false) String author
    ) throws Exception {

        TransDTO<Object> dto = new TransDTO<>();
        boolean b = actPraiseService.addPraise(actType, actId, interactId, interactType);
        dto.withSuccess(b).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 取消点赞
     *
     * @param actType
     * @param actId
     * @param interactId   评论id或提问id
     * @param interactType 1 评论  2 提问
     * @param staffId
     * @return
     * @throws AuthException
     */
    @GetMapping("delete-praise")
    public TransDTO deletePraise(@RequestParam("act_type") String actType,
                                 @RequestParam("act_id") String actId,
                                 @RequestParam(value = "act_name", required = false) String actName,
                                 @RequestParam(value = "interact_id", required = false) Integer interactId,
                                 @RequestParam(value = "interact_type", required = false) Integer interactType,
                                 @RequestParam(value = "staff_id", required = false) Integer staffId,
                                 @RequestParam(value = "staff_name", required = false) String staffName,
                                 @RequestParam(value = "author", required = false) String author
    ) {
        TransDTO<Object> dto = new TransDTO<>();
        boolean state = actPraiseService.deletePraise(actType, actId, interactId, interactType);
        dto.withData(state).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }
}
