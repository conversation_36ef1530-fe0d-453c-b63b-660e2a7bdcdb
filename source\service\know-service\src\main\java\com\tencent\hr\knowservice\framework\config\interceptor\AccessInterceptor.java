package com.tencent.hr.knowservice.framework.config.interceptor;

import com.tencent.hr.auth.sdk.common.constants.AuthConstants;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubQlDictItemMapper;
import com.tencent.hr.knowservice.framework.annotation.AuthorityAudited;
import com.tencent.hr.knowservice.framework.constant.UserRoleEnum;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.framework.dto.UserRoleDto;
import com.tencent.hr.knowservice.framework.service.AuthorityService;
import com.tencent.hr.knowservice.mooc.constant.CacheKeyEnum;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

import static com.tencent.hr.knowservice.framework.constant.UserRoleEnum.*;

@Component
@Slf4j
public class AccessInterceptor implements HandlerInterceptor {

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    private AuthorityService authorityService;

    @Autowired
    PubQlDictItemMapper pubQlDictItemMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 判断这个人是不是这个角色，如果是有权访问
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
//            //拿到对应接口方法上的权限注解
            AuthorityAudited annotation = handlerMethod.getMethod().getAnnotation(AuthorityAudited.class);
            Integer staffId = Integer.parseInt(GatewayContext.get().getStaffId());
            if (annotation == null) {
                return true;
            } else {
                if ("dev".equals(env) && staffId == null) {
                    staffId = 68317;
                }
                log.debug("运行环境:{},用户staffId:{}", env, staffId);

                //拿到个人所拥有权限码
                UserRoleDto userRoles = authorityService.getUserRoles(AuthConstants.TENCENT, staffId.toString());

                UserRoleEnum[] value = annotation.value();
                List<UserRoleEnum> list = Arrays.asList(value);

                if (CollectionUtils.isEmpty(list)) {
                    return true;
                }
//                //判断个人所拥有的权限码是否包含接口的权限码
                if (userRoles.isExamAdmin() && list.contains(ExamAdmin)) {
                    return true;
                }
                if (userRoles.isSupperAdmin() && list.contains(SupperAdmin)) {
                    return true;
                }
                if (userRoles.isBGTrainingAdmin() && list.contains(BGTrainingAdmin)) {
                    return true;
                }
                if (userRoles.isDeptTrainingAdmin() && list.contains(DeptTrainingAdmin)) {
                    return true;
                }
                if (userRoles.isCompanyTrainingAdmin() && list.contains(UserRoleEnum.CompanyTrainingAdmin)) {
                    return true;
                }

                response.setStatus(200);
                response.setContentType("application/json; charset=utf-8");
                TransDTO<String> dto = new TransDTO<>();
                dto.withSuccess(false).withCode(Constants.ErrorCodeEnum.AUTH_CHECK_HR_RIGHT_FAIL.getCode()).withMessage("该操作无权限，请联系管理员！");
                response.getWriter().write(JsonUtil.toJson(dto));
                return false;

            }
        }
        return true ;
    }
}
