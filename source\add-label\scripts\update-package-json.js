const fs = require('fs')
const path = require('path')

const packageJsonPath = path.join(__dirname, '../package.json')
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

// 根据环境变量设置新的值
const envPackageName = process.env.VUE_APP_PACKAGE_NAME || packageJson.name
const envPackageVersion = process.env.VUE_APP_PACKAGE_VERSION || packageJson.version
const envMainEntry = process.env.VUE_APP_MAIN_ENTRY || packageJson.main

// 更新package.json的值
packageJson.name = envPackageName
packageJson.version = envPackageVersion
packageJson.main = envMainEntry

// 将更新后的JSON写回文件
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))