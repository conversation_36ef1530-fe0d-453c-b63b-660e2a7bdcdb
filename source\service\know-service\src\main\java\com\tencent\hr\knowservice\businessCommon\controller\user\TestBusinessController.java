package com.tencent.hr.knowservice.businessCommon.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.myoa.MyOaCloseAndDiscardDto;
import com.tencent.hr.knowservice.businessCommon.service.MessageService;
import jodd.net.HttpStatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/businessCommon/user/test")
public class TestBusinessController {

    @Autowired
    MessageService service;

    /**
     * 测试消除待办
     * @param process_inst_id
     * @return
     */
    @GetMapping("/close-myoa")
    public TransDTO closeMyoa(@Param("process_inst_id") String process_inst_id){
        MyOaCloseAndDiscardDto discardDto = new MyOaCloseAndDiscardDto();
        discardDto.setProcessInstId(process_inst_id);
        service.CloseMyOa(discardDto);
        return new TransDTO<>().withCode(HttpStatus.ok().status());
    }
}
