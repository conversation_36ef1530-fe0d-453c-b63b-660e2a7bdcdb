package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.knowservice.businessCommon.dao.entity.FileProdConn;
import com.tencent.hr.knowservice.businessCommon.dto.ExtendContentPageDTO;
import com.tencent.hr.knowservice.graphic.dto.FileProConnMatrixDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface FileProdConnMapper extends BaseMapper<FileProdConn> {
    int deleteByPrimaryKey(Integer id);

    int insert(FileProdConn record);

    int insertSelective(FileProdConn record);

    FileProdConn selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FileProdConn record);

    int updateByPrimaryKey(FileProdConn record);

    /**
     * 分页获取延伸课程
     * @param actId
     * @param page
     * @return
     */
    IPage<ExtendContentPageDTO> queryExtendContentByActId(@Param("actId") Integer actId,@Param("actType") Integer actType,@Param("usageScenario")Integer usageScenario,IPage<ExtendContentPageDTO> page);

    /**
     * 批量添加
     * @param addConns
     */
    void insertBatch(@Param("list") List<FileProdConn> addConns);

    /**
     * 修改置顶状态
     * @param fileProdConn
     * @return
     */
    int updateTopedStatus(FileProdConn fileProdConn);

    List<ExtendContentPageDTO> queryExtendContentByActId(@Param("actId") Integer actId,@Param("actType") Integer actType,@Param("usageScenario")Integer usageScenario);

    /**
     * 修改删除状态
     * @param id
     * @return
     */
    int updateDelState(Integer id);

    /**
     * 获取需要从成长矩阵更新的数据
     * @param syncTime
     * @param count
     * @return
     */
    List<FileProConnMatrixDTO> getUpdateContents(Date syncTime, Integer count);

    /**
     * 批量修改
     * @param updateFileProdConns
     */
    void updateBatch(List<FileProdConn> updateFileProdConns);
    /**
     * 批量修改
     * @param updateFileProdConns
     */
    void updateConnTypeBatch(List<FileProdConn> updateFileProdConns);
}