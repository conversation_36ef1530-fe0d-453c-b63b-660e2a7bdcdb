package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.proxy.CommonServiceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: shi<PERSON><PERSON>
 * @createDate: 2022/12/2
 * @version: 1.0
 */
@Service
public class PortalCommonService {

    @Autowired
    CommonServiceApi commonServiceApi;

    /**
     * 获取智绘封面
     * @param title
     * @param num
     * @return
     */
    public String getAICover(String title, Integer num) {
        TransDTO result = commonServiceApi.getSmartCover(title, num);
        String data = String.valueOf(result.getData());
        int indexU = data.indexOf("url=");
        int indexEnd = data.indexOf("}");
        StringBuffer buffer = new StringBuffer();
        for(int i = indexU + 4; i < indexEnd; i++){
            buffer.append(data.charAt(i));
        }
        return buffer.toString();
    }
}
