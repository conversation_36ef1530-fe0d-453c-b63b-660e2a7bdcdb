package com.tencent.hr.knowservice.businessCommon.constans;

import com.tencent.hr.knowservice.businessCommon.dto.xapi.TXVerb;

public enum LRSVerbs {


    praise("praise", "praise","点赞"),
    cancel_praise("cancel_praise", "praise", "取消点赞"),
    fav("fav", "fav","收藏"),
    cancel_fav("cancel_fav","fav","取消收藏"),
    score("score","score","评分"),
    update_score("update_score","score","更新评分"),
    cancel_score("cancel_score","score","取消评分"),
    vote("vote","vote","投票"),
    update_vote("update_vote","vote","更新投票"),
    cancel_vote("cancel_vote","vote","取消投票"),
    follow("follow","follow","关注"),
    cancel_attention("cancel_attention","attention","取消关注"),
    comment("comment","comment","评论（评论、提问、回复）"),
    update_comment("update_comment","comment","修改评论"),
    stick_comment("stick_comment","comment","置顶"),
    cancel_stick_comment("cancel_stick_comment","comment","取消置顶"),
    hide_comment("hide_comment","comment","隐藏评论"),
    show_comment("show_comment","comment","显示评论"),
    delete_comment("delete_comment","comment","删除评论"),
    live_subscribe("live_subscribe","live_subscribe", "预约直播"),
    cancel_live_subscribe("cancel_live_subscribe","live_subscribe", "取消预约直播"),
    review_subscribe("review_subscribe","review_subscribe", "预约回看"),
    cancel_review_subscribe("cancel_review_subscribe","review_subscribe", "取消预约回看"),
    registration("registration","registration","报名"),
    cancel_registration("cancel_registration","registration","取消报名"),
    study("study","study","学习"),
    update_study("update_study","study","学习"),
    exam("exam","exam","参加考试"),
    shared("shared","shared","分享"),
    start("start","start","开始"),
    suspended("suspended","suspended","暂停"),
    end("end","end","结束"),
    lecture_identify("lecture_identify","lecture_identify","讲师认证"),
    lecture_teach("lecture_teach","lecture_teach","讲师授课"),
    expert_identify("expert_identify","expert_identify","行家认证"),
    expert_teach("expert_teach","expert_teach","行家授课"),
    visit("visit","visit","访问"),

    played("played","study","播放"),
    paused("paused","study","暂停"),
    resumed("resumed","study","继续播放"),
    skipped("skipped","study","跳过"),
    backed("backed","study","回退"),
    terminated("terminated","study","终止"),
    finished("finished","study","结束"),
    completed("completed","study","完成"),
    change_volume("change_volume","study","调整音量"),

    publish("publish","publish","发布"),
    add_excellent("excellent","excellent","加精"),
    cancel_excellent("cancel_excellent","excellent","取消加精"),
    tag("tag","tag","添加标签"),
    official_tag("official_tag","official_tag","设置为官方标签"),
    ;


    private String type;
    private String name;
    private String sort;
    private TXVerb txVerb;


    public TXVerb getTXVerb(){
        return txVerb;
    }

    private LRSVerbs(String type, String sort, String name){
        this.name = name;
        this.type = type;
        this.sort = sort;
        this.txVerb = createTXVerb(type, sort, name);
    }


    private static TXVerb createTXVerb(String verbId, String sort, String verbName) {
        TXVerb v = new TXVerb();
        v.setId(verbId);
        v.setName(verbName);
        v.setSort(sort);
        v.setObjectType(verbId);
        return v;
    }


    public String getType() {
        return type;
    }
    public String getName() {
        return name;
    }
    public String getSort(){
        return sort;
    }

}
