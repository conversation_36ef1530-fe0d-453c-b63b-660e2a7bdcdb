import Vue from 'vue'
import App from './App.vue'

// 引入sdc-label-show-mob组件
import sdcLabelShowMob from '@tencent/sdc-label-show-mob'
import '@tencent/sdc-label-show-mob/lib/sdc-label-show-mob.css'
// import sdcLabelShowMob from '@tencent/sdc-label-show-mob-test'
// import '@tencent/sdc-label-show-mob-test/lib/sdc-label-show-mob-test.css'
Vue.use(sdcLabelShowMob)

Vue.config.productionTip = false

/*********模板引用可以直接new*********/
new Vue({
  render: h => h(App),
}).$mount('#labelShow')
/*********************************/

/********单页面需要在组件上new******/
let LabelObj = {
  App,
  Vue
}
window.LabelShowMob = LabelObj
/********************************/