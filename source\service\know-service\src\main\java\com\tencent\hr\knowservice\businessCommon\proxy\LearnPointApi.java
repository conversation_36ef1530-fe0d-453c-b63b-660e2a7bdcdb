package com.tencent.hr.knowservice.businessCommon.proxy;

import com.tencent.hr.base.dto.TransDTO;

import com.tencent.hr.knowservice.businessCommon.dto.common.TaskListResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name="learn-credit-service",url="${project.learn-credit-service}")
public interface LearnPointApi {

    /**
     * 是否正式员工
     * @param staffId
     * @return
     */
    @GetMapping("/api/v1/user/isFormalStaff/{staffId}")
    TransDTO isFormalStaff(@PathVariable("staffId") Integer staffId) ;

    /**
     * 用户任务结果查询
     * @param taskCodeList
     * @return
     */
    @GetMapping("/api/v1/user/task/resultList")
    TransDTO<List<TaskListResultDTO>> resultList(@RequestParam("taskCodeList") String taskCodeList, @RequestParam("staffId")Integer staffId) ;
}
