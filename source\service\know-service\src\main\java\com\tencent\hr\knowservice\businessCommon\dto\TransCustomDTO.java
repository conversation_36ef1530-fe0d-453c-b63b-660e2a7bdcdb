package com.tencent.hr.knowservice.businessCommon.dto;

import com.tencent.hr.base.dto.TransDTO;

public class TransCustomDTO<T> extends TransDTO<T> {
    private String remark;
    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public TransCustomDTO<T> withRemark(String remark) {
        this.remark = remark;
        return this;
    }
    public TransDTO<T> withDataCovert(TransDTO<T> dto) {
        this.withSuccess(dto.getSuccess());
        this.withCode(dto.getCode());
        this.withData(dto.getData());
        this.withMessage(dto.getMessage());
        return this;
    }
}
