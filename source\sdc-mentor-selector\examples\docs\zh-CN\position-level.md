## PositionLevel 职级选择器
> 贡献者：v_whaigong(龚文海), 最近更新时间：2024-04-12

用于选择职级

### 基础用法

提供2种方式选择职级，单选和多选。职级选择器目前默认使用多选模式。<br/>
<br/>1. 展开下拉面板选择； 2. 输入关键字搜索，使用下拉菜单展示筛选后的职级<br/>

:::demo 默认多选，`v-model` 的值为当前被选中的职级选项的 **value** 属性值。可通过 `map.multiple` 属性设置单选，`v-model` 的值为当前被选中的职级选项的 **value** 属性值集合。
```html
<template>
  <div class="block">
    <span class="demonstration">基础单选</span>
    <sdc-position-level v-model="value1" @change="selectorChange" :map="map" placeholder="基础单选"/>
  </div>
  <div class="block">
    <span class="demonstration">基础多选</span>
    <sdc-position-level v-model="value2" @change="selectorChange" placeholder="基础多选"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: [],
        map: {
          multiple: false
        }
      }
    },
    methods: {
      selectorChange(val) {
        console.log(val)
      }
    }
  }
</script>
```
:::

### 限制职级选择范围

用于仅提供某条件下的职级选择
<br/>提醒: 动态切换条件时，会清空已有选项

:::demo 可通过 `range.positionSystemTypeIdList` 属性设置仅选择某通道族体系类型下的职级;可通过 `range.positionSystemIdList` 属性设置仅选择对应通道族体系下的职级（默认是[1],管理职级）;可通过 `range.positionClanIdList` 属性设置仅选择某职位族下的职级;可通过 `range.positionLevelIdList` 属性设置仅选择对应职级Id的职级。
```html
<template>
  <div class="block block-3">
    <span class="demonstration">管理职级</span>
    <sdc-position-level :range="range" v-model="value"/>
  </div>
  <div class="block block-3">
    <span class="demonstration">专业职级</span>
    <sdc-position-level :range="range2" v-model="value2"/>
  </div>
  <div class="block block-3">
    <span class="demonstration">海外职级</span>
    <sdc-position-level :range="range3" v-model="value3" collapseTags :filterable="false"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value: [60, 61],
        range: {
          positionSystemTypeIdList: [0], // 通道族体系类型Id集合
          positionSystemIdList: [1], // 通道族体系集合 1代表管理职级 默认是 1
          positionClanIdList: [15], // 职位族Id集合
          positionLevelIdList: [60, 61, 62] // 职级Id集合
        },
        value2: [],
        range2: {
          positionSystemIdList: [2], // 通道族体系集合 2代表专业职级
        },
        value3: [],
        range3: {
          positionSystemIdList: [3], // 通道族体系集合 3代表海外职级
        }
      }
    }
  }
</script>
```
:::

### 多语言

设置选项展示的语言
:::demo 属性 `lang` 定义了展示语言，默认中文，可选值`en`。 
```html
<template>
  <sdc-position-level v-model="value" lang="en"/>
</template>
<script>
  export default {
    data(){
      return{
        value: ''
      }
    }
  }
</script>
```
:::
### 多选Tag展示
:::demo 多选模式下，默认情况下会展示所有已选中的选项的Tag，你可以使用`collapseTags`来折叠Tag。 可以使用`tagsLength`来设置Tag最大展示文字数
```html
  <div class="block">
    <span class="demonstration">默认显示所有Tag</span>
    <sdc-position-level v-model="value1" @change="selectorChange"/>
  </div>
  <div class="block">
    <span class="demonstration">折叠展示Tag</span>
    <sdc-position-level v-model="value2" @change="selectorChange" collapseTags :tagsLength="7"/>
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    },
    methods: {
      selectorChange(val) {
        console.log(val)
      }
    }
  }
</script>
```
:::

### 仅展示最后一级

可以仅在输入框中显示选中项最后一级的职级，而不是选中职级所在的完整路径。

:::demo 属性 `showAllLevels` 定义了是否显示完整的路径，将其赋值为`false`则仅显示最后一级。
```html
<template>
  <sdc-position-level v-model="value" :showAllLevels="false"/>
</template>
<script>
  export default {
    data(){
      return{
        value: [60, 61]
      }
    }
  }
</script>
```
:::

### 可搜索

:::demo 可以使用`filterable`属性来开启搜索功能, 默认开启搜索功能。
```html
  <div class="block">
    <span class="demonstration">不可搜索</span>
    <sdc-position-level v-model="value1" :filterable="false"/>
  </div>
  <div class="block">
    <span class="demonstration">可搜索</span>
    <sdc-position-level v-model="value2" />
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**small**尺寸
```html
<template>
  <div class="block">
    <span class="demonstration">默认尺寸</span>
    <sdc-position-level v-model="value1"/>
  </div>
  <div class="block">
    <span class="demonstration">较小尺寸</span>
    <sdc-position-level size="small" v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 数据源

默认职级数据从远程服务器获取

:::demo 使用 `data` 属性来设置选项数据源，或者使用`promise`来设置访问选项数据源的方法。 如果你的数据源中不包含 `value` 和 `label` 默认字段，可以通过 `valueMap` 和`labelMap` 属性来指定
```html
<template>
  <sdc-position-level v-model="value1" :data="remoteData" :map="map"/>
  <sdc-position-level v-model="value2" :promise="promise" :map="map" style="margin-left: 20px;"/>
</template>
<script>
  export default {
    data(){
      return{
        value1: 'L3',
        value2: 'L4',
        map: {
          value: 'group',
          label: 'mark'
          // children: 'children'
        },
        remoteData: [
          { 
            group: 'L1', 
            mark: '选项1',
            children: [
              {
                group: 'L3', 
                mark: '子集2', 
              }
            ]
          },
          { 
            group: 'L2', 
            mark: '选项2',
            children: [
              {
                group: 'L4', 
                mark: '子集3', 
              }
            ]
          }
        ],
        promise: null,
      }
    },
    created() {
      this.promise = new Promise((resolve, reject) => {
        resolve(this.remoteData)
      })
    }
  }
</script>
```
:::

### PositionLevel Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | boolean/string/number | — | — |
| size | 输入框尺寸 | string | medium/small/mini | — |
| disabled | 是否禁用 | boolean | — | false |
| placeholder | 占位符 | string | — | — |
| lang | 语言 | string | 中文: zh，英文: en | zh |
| range | 限制选项范围，具体见下表 | Object | — | — |
| collapseTags | 多选模式下是否折叠Tag | boolean | — | false |
| tagsLength | Tag最大展示文字数, 最小1 | number | — | 13 |
| filterable | 是否可搜索选项 | boolean | — | true |
| showAllLevels | 输入框中是否显示选中值的完整路径 | boolean | — | true |
| data | 自定义选项 | array | — | [ ] |
| promise | 覆盖组件内部获取选项数据源的默认方法，`resolve` 函数的参数需要是一个由选项组成的数组 | Promise | — | — |
| showTotal | 是否显示后置的已选数量 | boolean | — | false |
| separator | 选项分隔符 | string | — | 斜杠'/' |
| map | 映射配置，具体见下表 | object | — | — |
| customClass | 自定义类名 | string | — | — |

### PositionLevel Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项, 包含label、value、path数组、fullName |

### PositionLevel Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| clearSelected | 用于清空选中项 | — |
| getCheckedNodes | 获取选中的节点 | (leafOnly) 是否只是叶子节点，默认值为 false |


### map
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value | 指定选项的值为选项对象的某个属性值 | string | — | 'value' |
| label | 指定选项标签为选项对象的某个属性值 | string | — | 'label' |
| children | 指定选项的子选项为选项对象的某个属性值 | string | — | 'children' |
| emitPath | 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 | boolean | — | false |
| multiple | 是否多选 | boolean | — | true |

### range 
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| positionSystemTypeIdList | 通道族体系类型Id集合 | array | — | [0] |
| positionSystemIdList | 通道族体系Id集合 | array | — | [1] |
| positionClanIdList | 职位族Id集合 | array | — | - |
| positionLevelIdList | 职级Id集合 | array | — | - |
