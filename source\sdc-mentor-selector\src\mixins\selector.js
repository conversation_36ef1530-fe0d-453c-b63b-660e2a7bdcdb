import { DataType, DataUtil } from 'sdc-core'
import { oneOf } from '../utils/main'
import { SDC_CLEAR_QUERY_FORM } from '../config/constant'

export default {
  provide() {
    return {
      height: this.height,
      multiple: this.multiple,
      textarea: this.textarea,
      icon: this.icon || 'el-icon-check',
      map: this.selectorMap,
      search: this.search,
      disabled: this.disabled,
      size: this.size,
      showLastLevels: this.showLastLevels,
      valueKey: this.valueKey,
      nodeKey: this.nodeKey,
      treeProps: this.treeProps,
      modalProps: this.modalProps,
      change: this.change,
      placeholder: this.placeholder,
      selectedText: this.selectedText,
      totalText: this.totalText,
      checkItemExist: this.checkItemExist,
      getDataList: this.getDataList,
      getPasteResult: this.getPasteResult,
      getCurrentItem: this.getCurrentItem,
      getTreeData: this.getTreeData,
      getChildrenData: this.getChildrenData,
      queryParams: this.queryParams,
      getSelected: () => this.selected,
      showSelectorModal: () => this.$refs.modal.showModal(),
      getDisabled: () => this.disabled,
      showTotal: this.showTotal,
      getRange: () => this.range,
      filterKey: this.filterKey,
      filterValue: this.filterValue,
      modalClass: this.modalClass,
      modalWidth: this.modalWidth,
      showFullTag: this.showFullTag,
      defaultExpandedKeys: this.defaultExpandedKeys,
      modalAppendToBody: this.modalAppendToBody,
      onSelectedCheck: this.onSelectedCheck,
      getDisabledMentorList: () => this.disabledMentorList,
      getRiskResult: () => this.riskResult,
      setRiskResult: this.setRiskResult,
      getBaseUrl: () => this.baseUrl,
      specialPlatform: this.specialPlatform,
      getTypeId: () => this.typeId, // 实时响应的操作人身份
      getOperatorRightMap: () => this.operatorRightMap, // 实时响应的操作人管理权限范围
      getPlatform: () => this.platform,
      getWrongNum: () => this.requireWrongNum,
      operatorRightCheck: this.operatorRightCheck,
      getWrongLoaading: () => this.requireWrongLoaading
    }
  },
  props: {
    multiple: Boolean,
    textarea: Boolean,
    search: {
      type: Boolean,
      default: true
    },
    value: {
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      // default: 'small',
      validator(val) {
        return oneOf(val, ['medium', 'small'], 'size')
      }
    },
    // range: {
    //   type: Object,
    //   default() {
    //     return {
    //       unitID: this.operatorRightMap || '', // 操作人权限范围
    //       manageUnitIdList: [10101],
    //       staffTypeIdList: [2, 6]
    //     }
    //   }
    // },
    props: {
      type: Object,
      default() {
        return {}
      }
    },
    showTotal: {
      type: Boolean,
      default: true
    },
    height: Number,
    placeholder: {
      type: String,
      default: '请选择导师'
    },
    getDataList: {
      type: Function,
      default() {
        return {}
      }
    },
    getPasteResult: {
      type: Function
    },
    selectClass: {
      type: String,
      default: ''
    },
    modalClass: {
      type: String,
      default: ''
    },
    modalWidth: {
      type: String,
      default: ''
    },
    showFullTag: {
      type: Boolean,
      default: false
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    modalAppendToBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selected: [], // 当校验无风险时，直接显示的数据
      selectorWidth: 0,
      valueKey: '',
      nodeKey: '',
      queryParams: {},
      filterKey: '',
      filterValue: ''
    }
  },
  computed: {
    range() {
      return {
        unitID: !this.operatorRightMap || !this.operatorRightMap.length ? null : this.operatorRightMap, // 操作人权限范围
        manageUnitIdList: [10101],
        staffTypeIdList: [2, 6],
        isContainSubStaff: true
      }
    }
  },
  watch: {
    selected() {
      if (!this.textarea) {
        this.$refs.selector.resetInputSize()
      }
    }
  },
  created() {
    this.$bus.$on(SDC_CLEAR_QUERY_FORM, this.clearSelected)
  },
  destroyed() {
    this.$bus.$off(SDC_CLEAR_QUERY_FORM, this.clearSelected)
  },
  methods: {
    setRiskResult(result) {
      this.riskResult = result
    },
    clearSelected() {
      this.setSelected([])
    },
    setSelected(val) {
      let items = DataUtil.clone(val, { asArray: true }) || []
      if (!this.multiple && items.length) {
        items = items.slice(0, 1)
      }
      this.change(this.getTransformData(items, { mode: 'out-in' }))
    },
    change(val) {
      this.selected = val
      this.$emit('input', this.getSelectedResult({ multiple: this.multiple }))
      this.$emit('change', this.getSelectedResult({ multiple: this.multiple, mode: 'item' }))
    },
    getSelectedResult({ multiple = true, mode = 'id' } = {}) {
      if (multiple) {
        return mode === 'id' ? this.selected.map(item => item[this.nodeKey]) : this.getTransformData(this.selected, { mode: 'in-out' })
      } else {
        if (DataType.isEmptyArray(this.selected)) return ''
        return mode === 'id' ? this.selected[0][this.nodeKey] : this.getTransformData(this.selected, { mode: 'in-out' })[0]
      }
    },
    getTransformData(data, { mode = 'out-in' } = {}) {
      // if (DataType.isEmptyObject(this.props)) return data
      return data.map(item => {
        const newItem = {}
        if (mode === 'out-in') {
          for (const prop in this.selectorMap) {
            newItem[this.selectorMap[prop]] = item[this.selectorProps[prop]]
          }
        } else if (mode === 'in-out') {
          for (const prop in this.selectorProps) {
            newItem[this.selectorProps[prop]] = item[this.selectorMap[prop]]
          }
        }
        return newItem
      })
    },
    checkItemExist(data) {
      return item => item[this.nodeKey] === data[this.nodeKey]
    }
  }
}
