import CryptoJS from 'crypto-js'
let encodedKey = "OGlhKElUMnNLd15mRnNeKw=="
encodedKey = atob(encodedKey)
const key = CryptoJS.enc.Utf8.parse(encodedKey)
export default {
  methods: {
    encrypteParams(params) {
      try {
        if (!this.isObject(params)) {
          console.log('参数不是对象: ', params)
          return ''
        }
        const message = new URLSearchParams(params).toString()
        const encryptedContent = CryptoJS.AES.encrypt(message, key, {
          mode: CryptoJS.mode.ECB,  
          padding: CryptoJS.pad.Pkcs7
        })
        const encStr = encryptedContent.ciphertext.toString()
        return encStr
      } catch (error) {
        console.log('error---参数错误: ', error)
        return ''
      }
    },
    isObject(obj) {
      return Object.prototype.toString.call(obj) === '[object Object]'
    },
    decrypteParams(params) {
      try {
        if (!params) {
          console.log('参数错误: ', params)
          return ''
        }
        const decryptedContent = CryptoJS.AES.decrypt(CryptoJS.format.Hex.parse(params), key, {
          mode: CryptoJS.mode.ECB,  
          padding: CryptoJS.pad.Pkcs7
        })
        const decStr = CryptoJS.enc.Utf8.stringify(decryptedContent)
        return decStr
      } catch (error) {
        console.log('error---参数错误: ', error)
        return ''
      }
    }
  }
}
