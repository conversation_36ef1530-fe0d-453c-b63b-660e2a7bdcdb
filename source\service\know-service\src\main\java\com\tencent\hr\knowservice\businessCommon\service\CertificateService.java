package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.VBaseEmpInfo;
import com.tencent.hr.knowservice.businessCommon.dto.certificate.AsyncGenerateCertificateDto;
import com.tencent.hr.knowservice.businessCommon.dto.certificate.CertificateListDto;
import com.tencent.hr.knowservice.businessCommon.dto.certificate.GenerateCertificateDto;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.mooc.dto.moocCourse.MoocCertificateDto;
import com.tencent.hr.knowservice.mooc.dto.moocCourse.MoocCertificateListDto;
import com.tencent.hr.knowservice.mooc.dto.moocUser.MoocStudentCertificateDto;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 证书相关
 */
@Slf4j
@Service
public class CertificateService {

    @Value("${extapi.certificate.host}")
    private String contentCenterHost;
    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${com.appSetting.tenantCode}")
    private String tenantCode;

    @Autowired
    private BaseEmpInfoService empInfoService;

    /**
     * 获取证书模板列表
     *
     * @param certificateName
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Object getCertificateList(String certificateName, Integer pageNo, Integer pageSize) throws Exception {

        if (pageNo == null) {
            pageNo = 1;
        }

        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        String staffName = current.getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        String path = "/api/v1/extapi/manage/certificate/list?current=" + pageNo + "&size=" + pageSize;
        if (StringUtils.isNotEmpty(certificateName)) {
            path = path + "&certificateName=" + certificateName;
        }
        String result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);

        ObjectMapper objectMapper = new ObjectMapper();
        log.info("getCertificateList 返回参数:{}", result);
        HashMap hashMap = objectMapper.readValue(result, HashMap.class);
        boolean success = (boolean) hashMap.get("success");
        if (success) {
            Object data = hashMap.get("data");
            return data;
        } else {
            throw new LogicException(String.valueOf(hashMap.get("msg")));
        }
    }

    /***
     * 异步创建用户证书
     * @param staffId
     * @param certificateId
     * @return
     */
    public String generateUserCertificate(String actId, String actType, Integer staffId, String certificateId) {

        if (StringUtils.isEmpty(certificateId) || staffId == null) {
            return null;
        }
        VBaseEmpInfo empInfo = empInfoService.getEmpInfo(staffId);
        if (empInfo != null) {
            String loginUserId = staffId.toString();
            String loginUserName = empInfo.getEmpNameEn();
            HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, loginUserId, loginUserName);
            header.add("Content-Type", "application/json; charset=UTF-8");
            StringBuffer errorMsg = new StringBuffer();
            String path = "/api/v1/extapi/user/generate/certificate/async";
            GenerateCertificateDto certificateDto = new GenerateCertificateDto();
            certificateDto.setCertificateId(certificateId);
            certificateDto.setUserId(String.valueOf(staffId));
            certificateDto.setUserName(empInfo.getEmpNameCh());
            certificateDto.setUserEngName(empInfo.getEmpNameEn());
            certificateDto.setUserOrg(empInfo.getDeptFullName());
            certificateDto.setItemType(actType);
            certificateDto.setItemFromId(actId);
            certificateDto.setCreatedStatus("1");
            certificateDto.setRepeatSendCertificate(false);
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonData;
            try {
                jsonData = objectMapper.writeValueAsString(certificateDto);
            } catch (JsonProcessingException e) {
                return null;
            }
            String result = HttpUtil.sendPostByRestTemplate(contentCenterHost, path, jsonData, header, errorMsg);
            JSONObject object = new JSONObject(result);
            boolean success = (boolean) object.get("success");
            log.info("颁发证书 staffId:{} certificateTemplateId:{} 返回参数： {}", result);
            if (success) {
                String data = String.valueOf(object.get("data"));
                return data;
            } else {
                throw new LogicException(String.valueOf(object.get("msg")));
            }
        }
        return null;
    }

    /**
     * 根据编号获取信息
     *
     * @param certificateId
     * @return
     */
    public Object getUserCertificateInfo(String certificateId) throws Exception {
        if (StringUtils.isEmpty(certificateId)) {
            return null;
        }
        ContextEntity current = GatewayContext.current();
        String loginUserId = current.getStaffId();
        String loginUserName = current.getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, loginUserId, loginUserName);
        StringBuffer errorMsg = new StringBuffer();
        String path = "/api/v1/extapi/user/certificate/my/certificates/" + certificateId;
        String result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        ObjectMapper objectMapper = new ObjectMapper();
        log.info("getUserCertificateInfo certificateId：{} 返回参数:{}", certificateId, result);

        HashMap hashMap = objectMapper.readValue(result, HashMap.class);
        boolean success = (boolean) hashMap.get("success");
        if (success) {
            Object data = hashMap.get("data");
            return data;
        } else {
            throw new LogicException(String.valueOf(hashMap.get("msg")));
        }
    }

    /**
     * 根据编号批量获取信息
     *
     * @return
     */
    public Object getUserCertificateInfoList(MoocCertificateListDto moocCertificateListDto) throws Exception {
        if (CollectionUtils.isEmpty(moocCertificateListDto.getCertificateBatchNos())) {
            return null;
        }
        ContextEntity current = GatewayContext.current();
        String loginUserId = current.getStaffId();
        String loginUserName = current.getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, loginUserId, loginUserName);
        StringBuffer errorMsg = new StringBuffer();
        String path = "/api/v1/extapi/user/certificate/my/certificates";
        MoocStudentCertificateDto moocStudentCertificateDto = new MoocStudentCertificateDto();
        moocStudentCertificateDto.setCurrent(moocCertificateListDto.getCurrent());
        moocStudentCertificateDto.setSize(moocCertificateListDto.getSize());
        moocStudentCertificateDto.setCertificateStatus(1);
        moocStudentCertificateDto.setCertificateBatchNos(moocCertificateListDto.getCertificateBatchNos());
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonData;
        try {
            jsonData = objectMapper.writeValueAsString(moocStudentCertificateDto);
        } catch (JsonProcessingException e) {
            return null;
        }
        String result = HttpUtil.sendPostByRestTemplate(contentCenterHost, path, jsonData, header, errorMsg);
        log.info("getUserCertificateInfoList 返回参数:{}", result);
        HashMap hashMap = objectMapper.readValue(result, HashMap.class);
        boolean success = (boolean) hashMap.get("success");
        if (success) {
            LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) hashMap.get("data");
            return data.get("records");
        } else {
            throw new LogicException(String.valueOf(hashMap.get("message")));
        }
    }

    /**
     * 用户根据证书编号获取证书信息
     *
     * @return
     */
    public Object getUserCertificateInfoByNo(String certificateBatchNo) throws Exception {
        if (StringUtils.isEmpty(certificateBatchNo)) {
            return null;
        }
        ContextEntity current = GatewayContext.current();
        String loginUserId = current.getStaffId();
        String loginUserName = current.getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, loginUserId, loginUserName);
        StringBuffer errorMsg = new StringBuffer();
        String path = "/api/v1/extapi/user/certificate/my/certificates/" + certificateBatchNo;
        String result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        log.info("getUserCertificateInfoList 返回参数:{}", result);
        ObjectMapper objectMapper = new ObjectMapper();
        HashMap hashMap = objectMapper.readValue(result, HashMap.class);
        boolean success = (boolean) hashMap.get("success");
        if (success) {
            LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) hashMap.get("data");
            return data;
        } else {
            throw new LogicException(String.valueOf(hashMap.get("message")));
        }
    }
}
