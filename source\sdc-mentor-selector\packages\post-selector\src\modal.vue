<template>
  <div class="selector-modal">
    <sdc-modal ref="modal" :title="modalProps.title" :width="modalWidth || '800px'" adaptive :showFooter="false" :customClass="modalClass" :appendToBody="modalAppendToBody" :class="{'selector-modal-append-to-body': modalAppendToBody}">
      <div slot="body">
        <div class="left-side" style="width:53%;">
          <div class="tree-list">
            <el-tree ref="tree" :check-strictly="true" :props="treeProps" :data="treeData" lazy node-key="id" :load="loadNode" v-if="opened" v-loading="loading" @node-click="nodeClick">
              <span class="tree-node" slot-scope="{ node, data }">
                <span class="tree-node-text">{{ node.label }}</span>
                <el-checkbox v-model="node.checked" v-if="multiple || data.type === map.type.post" :indeterminate="node.indeterminate" @change="handleCheckChange(node)" @click.native.stop/>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="right-side" style="width:47%;">
          <div class="selected-info">
            <span>{{selectedItemsText}}</span>
            <i class="el-icon-delete" @click="handleClear" v-show="selectedData.length"></i>
          </div>
          <div class="selected-list" :key="reloadKey">
            <div class="list-item" v-for="(item, index) in getSelectedItems(selectedData)" :key="index">
              <el-tooltip placement="top-end" :content="item._text" :disabled="item._text.length <= item._modal.minLength" popper-class="sdc-selector-modal-popper">
                <span class="list-item-name">{{textEllipsis(item._text, item._modal) }}</span>
              </el-tooltip>
              <span @click="handleDelete(item)" class="list-item-icon"><i class="el-icon-error"></i></span>
            </div>
          </div>
          <div class="modal-buttons">
            <el-button size="small" @click="hide">{{$st('sdc.selector.modal.cancel')}}</el-button>
            <el-button size="small" type="primary" @click="handleConfirm">{{$st('sdc.selector.modal.ok')}}</el-button>
          </div>
        </div>
      </div>
    </sdc-modal>
  </div>
</template>

<script>
  import { DataUtil } from 'sdc-core'
  import { textEllipsis, hasOwn, isNotLogin } from 'sdc-webui/src/utils/main'
  import { modal, locale, map } from 'mixins'
  import Toast from 'packages/toast'

  export default {
    name: 'selector-modal',
    inject: ['multiple', 'textarea', 'nodeKey', 'map', 'change', 'selectedText', 'treeProps', 'modalProps', 'queryParams', 'getTreeData', 'getChildrenData', 'getCurrentItem', 'getRange', 'modalClass', 'modalWidth', 'defaultExpandedKeys', 'modalAppendToBody'],
    mixins: [modal, locale, map],
    props: {
      data: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        selectedCount: 0,
        selectedData: [],
        treeData: [],
        opened: false,
        loading: false,
        resolveTree: null,
        lastLimitUnitID: '',
        reloadKey: 0
      }
    },
    computed: {
      selectedItemsText() {
        return this.selectedText.replace('$count', this.selectedCount)
      }
    },
    methods: {
      textEllipsis,
      showModal() {
        if (this.opened) { // 非首次打开
          const level0Node = this.$refs.tree.root
          if (level0Node.childNodes.length === 0) {
            // 上一次打开加载失败时，需重新加载
            this.loadNode(level0Node, this.resolveTree)
          } else if (hasOwn(this.getRange(), 'unitID') && this.getRange().unitID !== this.lastLimitUnitID) {
            // 用户外部设置unitID时，需重新加载
            level0Node.childNodes = []
            this.loadNode(level0Node, this.resolveTree)
            this.lastLimitUnitID = this.getRange().unitID
          }
        } else { // 首次打开再访问数据
          this.lastLimitUnitID = this.getRange().unitID
          this.opened = true
        }
        this.selectedData = DataUtil.clone(this.data)
        this.selectedCount = this.selectedData.length
        const traverse = (node) => {
          const childNodes = node.root ? node.root.childNodes : node.childNodes
          childNodes.forEach(node => {
            const data = node.data
            if (data.type === this.map.type.post) {
              const checked = this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])
              if (checked !== node.checked) {
                this.handleCheckItem(node, checked)
              }
            }
            traverse(node)
          })
        }
        if (this.$refs.tree) {
          traverse(this.$refs.tree.root)
        }
        this.show()
      },
      handleCheckItem(node, checked) {
        node.indeterminate = checked === 'half'
        node.checked = checked === true
        const childNodes = node.childNodes.filter(item => item.data.type === this.map.type.post)
        childNodes.forEach(child => {
          this.handleCheckItem(child, checked)
        })
        const parent = node.parent
        if (parent && parent.level !== 0) {
          this.reInitChecked(parent)
        }
      },
      handleCheckChange(node) {
        const { id, label, type, isLeaf, indeterminate, ...data } = node.data
        this.handleCheckItem(node, node.checked)
        if (type === this.map.type.post) {
          if (!this.multiple && node.checked) {
            this.selectedData.forEach(data => {
              this.handleDelete(data)
            })
          }
          this.updateSelectedData([data], node.checked ? 'add' : 'delete')
        } else if (type === this.map.type.unit) {
          this.getChildrenData(data[this.map.unitID], { ...this.queryParams, ...this.getRange() }).then(res => {
            this.updateSelectedData(res, node.checked ? 'add' : 'delete')
          })
        }
      },
      handleDelete(data) {
        this.updateSelectedData([data], 'delete')
        const node = this.$refs.tree.getNode(data[this.map.postID])
        if (node) {
          this.handleCheckItem(node, false)
        }
      },
      handleConfirm() {
        const data = DataUtil.clone(this.selectedData)
        this.change(data)
        this.hide()
      },
      reInitChecked(node) {
        const { all, none, half } = this.getChildState(node.childNodes.filter(item => item.data.type === this.map.type.post))
        // const data = node.data
        if (all) {
          node.checked = true
          node.indeterminate = false
        } else if (half) {
          node.checked = false
          node.indeterminate = true
        } else if (none) {
          node.checked = false
          node.indeterminate = false
        }
        const parent = node.parent
        if (parent && parent.level !== 0) {
          this.reInitChecked(parent)
        }
      },
      getChildState(nodes) {
        let all = nodes.length > 0
        let none = true
        nodes.forEach(node => {
          if (node.checked !== true || node.indeterminate) {
            all = false
          }
          if (node.checked !== false || node.indeterminate) {
            none = false
          }
        })
        return { all, none, half: !all && !none }
      },
      updateSelectedData(list, type) {
        if (type === 'add') {
          list.forEach(data => {
            if (!this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])) {
              this.selectedData.push(data)
              this.selectedCount++
            }
          })
        } else if (type === 'delete') {
          list.forEach(data => {
            const index = this.selectedData.findIndex(item => item[this.nodeKey] === data[this.nodeKey])
            if (index !== -1) {
              this.selectedData.splice(index, 1)
              this.selectedCount--
            }
          })
        }
      },
      loadNode(node, resolve) {
        if (!this.resolveTree) {
          this.resolveTree = resolve
        }
        // 判断组件是否有传unitID进来，有传的话需要兼容处理，原先Number类型，现在Array类型，需要把Number转Array
        const baseId = this.getRange().unitID ? (!Array.isArray(this.getRange().unitID) ? [this.getRange().unitID] : this.getRange().unitID) : 0
        const id = node.level === 0 ? baseId : node.data[this.map.unitID]
        const isCache = (!hasOwn(this.getRange(), 'unitID') && node.level === 0) // 由外部给定顶层组织，和点击node时不读取缓存
        const { isContainSubUnit, NotContainVirtualUnit } = this.getRange()
        const onlyPost = hasOwn(this.getRange(), 'unitID') && (this.getRange().unitID > 0 || (Array.isArray(this.getRange().unitID) && this.getRange().unitID.length)) && !isContainSubUnit
        node.level === 0 && (this.loading = true)
        this.getTreeData(id, { ...this.queryParams, isCache, onlyPost, NotContainVirtualUnit }).then(res => {
          let postList = res[this.map.type.post] || []
          let unitList = res[this.map.type.unit] || []
          // 添加tree节点需要的一些属性
          postList = postList.map(data => ({
            ...data,
            id: data[this.nodeKey], // 用于设置checkbox的选中状态
            label: data[this.map.postName],
            isLeaf: true,
            type: this.map.type.post,
            // checked: this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey]),
            indeterminate: false
          }))
          unitList = unitList.map(data => ({
            ...data,
            id: 'unitID_' + data[this.map.unitID], // 用于设置checkbox的选中状态
            label: data[this.map.unitName],
            isLeaf: false,
            type: this.map.type.unit,
            // checked: false,
            indeterminate: false
          }))
          const children = postList.concat(unitList) // 给定顶级组织时只限制下级岗位
          if (this.loading) {
            this.loading = false
          }
          resolve(children)
          if (this.$refs.tree) {
            const checkedNodeKeys = postList.filter(data => this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])).map(item => item[this.nodeKey])
            this.$refs.tree.setCheckedKeys([...checkedNodeKeys, ...this.$refs.tree.getCheckedKeys()])
          }
          this.reInitChecked(node)
          if (node.level === 0 && this.defaultExpandedKeys && this.defaultExpandedKeys.length) {
            node.childNodes.forEach(item => {
              if (this.defaultExpandedKeys.includes(item.data[this.map.unitID])) {
                item.expand()
              }
            })
          }
        }).catch(res => {
          resolve([])
          Toast(isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed'), 2000)
          if (this.loading) {
            this.loading = false
          }
          node.expanded = false
          node.loaded = false
          node.isLeaf = false
        })
      },
      handleClear() {
        this.selectedData.length = 0
        this.reloadKey++
        this.selectedCount = 0
        this.$refs.tree.setCheckedKeys([])
      },
      nodeClick(data, node) {
        if (data.type === this.map.type.post) {
          node.checked = !node.checked
          this.handleCheckChange(node)
        }
      }
    }
  }
</script>
