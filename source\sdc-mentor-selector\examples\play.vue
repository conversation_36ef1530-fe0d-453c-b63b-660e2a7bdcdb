<template>
  <div>
    <div>
      <!-- <sdc-wxheader ref="wxheader" :wxData="wxdata" :activeAppKey="appKey" :userName="userName" :avatarUrl="avatar" enableSidebar></sdc-wxheader> -->
    </div>
    <div class="staff-demo">
      <div class="demo-block">
        <div>
          <el-input style="width: 50%;" v-model="entryReason" placeholder="请输入需要传入的原因"></el-input>
          <el-button style="margin: 0 0 10px 20px" @click="setMember">回显成员{{ staffTestData.StaffName }}</el-button>
          <el-button style="margin: 0 0 10px 20px" @click="clearMember">清除回显</el-button>
        </div>
        <div class="demo-info">1、导师选择器 </div>
        <!-- platform 系统code：1-人才透视系统 2-招聘系统 3-入职准备系统 4-入职入场系统 5-大异动系统 -->
        <div style="margin-bottom: 20px;">
         系统:
          <el-radio-group v-model="platform" style="margin-left: 20px;" @change="updateMontorCom">
            <el-radio label="1">人才透视系统</el-radio>
            <el-radio label="2">招聘系统</el-radio>
            <el-radio label="3">入职准备系统</el-radio>
            <el-radio label="4">入职入场系统</el-radio>
            <el-radio label="5">大异动系统</el-radio>
          </el-radio-group>
        </div>
        <div style="margin-bottom: 20px;">
         身份:
          <el-radio-group v-model="operaTypeId" style="margin-left: 20px;" @change="updateMontorCom">
            <el-radio label="">不传</el-radio>
            <el-radio label="1">组织BP</el-radio>
            <el-radio label="2">直接上级</el-radio>
            <el-radio label="3">员工本人</el-radio>
            <el-radio label="4">招聘经理</el-radio>
            <el-radio label="5">运营人员</el-radio>
          </el-radio-group>
        </div>
        <div style="margin-bottom: 20px;display: flex;align-items: center;">
         新员工工作地:
          <sdc-area-selector style="width: 400px;margin-left: 20px;" v-model="placeId" placeholder="请选择" @change="placeChange"/>
        </div>
        <!-- <div style="margin-bottom: 20px;">
          是否是本人修改:
          <el-radio-group v-model="isSelf" style="margin-left: 20px;" @change="updateMontorCom">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </div> -->
        <div style="width: 400px;">
          <!-- <mentor-selector ref="mentorSelector" v-model="staffData1" env="test" :entryReason="entryReason" :operatorId="operatorId" :operaTypeId="operaTypeId" :studentStaffId="studentStaffId" :workPlaceInfo="workPlaceInfo" :deptId="deptId" :platform="platform" :isSelf="isSelf" @change="handleChangeStaff" @getReason="getReason"/> -->
          <mentor-selector
            v-if="showMentorCom"
            ref="mentorSelector"
            v-model="staffData1"
            env="test"
            :entryReason="entryReason"
            :operatorId="operatorId"
            :operaTypeId="operaTypeId"
            :studentStaffId="studentStaffId"
            :student_resume_id="student_resume_id"
            :workPlaceInfo="workPlaceInfo"
            :deptId="deptId"
            :platform="platform"
            @change="handleChangeStaff"
            @getReason="getReason"
          />
        </div>
        {{staffData1}}
      </div>
      <!-- <div class="demo-block">
        <div class="demo-info">1、组织选择器：【多选】【普通框】模式 </div>
        <unit-selector textarea multiple v-model="unitData2" placeholder="请输入组织关键字或点击选择更多组织" @change="handleChangeUnit" />
        {{unitData2}}
      </div> -->
      <!-- <div class="demo-block">
        <div class="demo-info">1、员工选择器：【多选】【textarea】模式 </div>
        <staff-selector multiple v-model="staffData3" placeholder="请输入员工中英文关键字或点击选择更多员工" @change="handleChangeStaff" />
        {{staffData3}}
      </div> -->
    </div>
    <!-- <div class="unit-demo">
      <div class="demo-block">
        <div class="demo-info">1、组织选择器：【单选】【普通框输入】模式</div>
        <unit-selector v-model="unitData1" :props="props"></unit-selector>
      </div>
      <div class="demo-block">
        <div class="demo-info">1-1、组织选择器：【单选】【普通框输入】模式，限制在部门</div>
        <unit-selector v-model="unitData11" :props="props" :includeUnitSortIDs=[1,6]></unit-selector>
      </div>
      <div class="demo-block">
        <div class="demo-info">
          2、组织选择器：【多选】【普通框输入】【标签】模式
          <el-dropdown split-button size="mini" type="primary" style="margin-left:10px;" @command="handleCommand">
            标签位置
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="bottom">下 侧</el-dropdown-item>
              <el-dropdown-item command="right">右 侧</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <unit-selector v-model="unitData2" multiple :props="props" :placement="placement"></unit-selector>
      </div>
      <div class="demo-block">
        <div class="demo-info">3、组织选择器：【多选】【普通框】【树形弹窗】模式 [限制组织属性为部门]</div>
        <unit-selector v-model="unitData3" multiple :props="props" :includeUnitSortIDs=[1]>
        </unit-selector>
      </div>
      <div class="demo-block">
        <div class="demo-info">4、组织选择器：【多选】【textarea输入】【树形弹窗】【标签】模式 </div>
        <unit-selector v-model="unitData4" multiple textarea :props="props"></unit-selector>
      </div>
      <div class="demo-block">
        <div class="demo-info">1、职位选择器：【单选】</div>
        <position-cascader v-model="positionData" :multiple="false" separator="-"></position-cascader>
        <button @click="consoleData">返回值</button>
      </div>
      <div>
        <div class="demo-block">
          <position-cascader v-model="positionData" :multiple="false" separator="-"></position-cascader>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { highlight } from 'mixins'
// import UnitSelector from 'packages/unit-selector'
// import StaffSelector from 'packages/staff-selector'
// import PositionCascader from 'packages/position-cascader'
import MentorSelector from 'packages/mentor-selector'
import SdcAreaSelector from 'packages/area-selector'
import { DataHttp, DataStorage, STORAGE_TYPE } from 'sdc-core'
export default {
  mixins: [highlight],
  components: {
    // UnitSelector,
    // StaffSelector,
    // PositionCascader,
    MentorSelector,
    SdcAreaSelector
  },
  data() {
    return {
      showMentorCom: true, // 是否显示导师组件，用来刷新组件
      placeId: '', // 工作地
      entryReason: '',
      studentStaffId: '322991',
      student_resume_id: '', // 候选人简历id
      operatorId: '24673', // 操作人staffId
      operaTypeId: '4', // 操作人身份：招聘经理：4，组织bp：1，直接上级：2，员工本人：3，招聘系统默认是4，入职入场+入职准备+异动系统默认是1，人才透视需要外部传值，没有默认
      platform: '1',
      isSelf: false,
      staffTestData: {
        // Avatar: "http://rhrc.woa.com/photo/100/shizhouwang.png",
        // StaffID: 186627,
        // StaffName: "shizhouwang(王世洲)"
        // Avatar: "http://rhrc.woa.com/photo/100/adasrzou.png",
        // StaffID: 251022,
        // StaffName: "adasrzou(邹司蕊)"
        Avatar: "http://rhrc.woa.com/photo/100/grangeliang.png",
        StaffID: 150185,
        StaffName: "grangeliang(梁宇)"
      },
      disabledData: {
        Avatar: "http://rhrc.woa.com/photo/100/uyleechen.png",
        StaffID: 324614,
        StaffName: "uyleechen(陈晨阳)"
      },
      deptId: 64710, // 学生的最小组织id
      workPlaceInfo: { // 新人工作地信息
        // placeName: '深圳总部',
        // place_id: 1
      },
      positionData: [],
      positonDefaultValue: [],
      customGetPositionData: null,
      unitData1: '', // [{ unitName: 'TEG技术工程事业群', unitId: 958, id: '00000958' }],
      unitData2: '',
      unitData3: '',
      unitData4: '',
      unitData11: '',
      staffData1: '',
      staffData2: '',
      staffData3: '',
      staffData4: '',
      props: {
        children: 'underUnit',
        label: 'unitName',
        value: 'unitId'
      },
      placement: 'bottom',
      options: [
        {
          "value": "1",
          "label": "技术类",
          "children": [{
            "value": "1-1",
            "label": "软件开发",
            "children":
              [
                { "value": "1-1-1", "label": "后台开发" },
                { "value": "1-1-2", "label": "测试开发" }
              ]
          }, {
            "value": "1-2",
            "label": "技术运营"
          }, {
            "value": "1-3",
            "label": "技术研究",
            "children": [
              { "value": "1-3-1", "label": "机器学习" },
              { "value": "1-3-2", "label": "计算机视觉" }
            ]
          }]
        },
        {
          "value": "2",
          "label": "产品类",
          "children": [
            { "value": "2-1", "label": "产品经理" },
            { "value": "2-2", "label": "游戏策划" }
          ]
        }
      ],
      wxdata: {
        "corpName": "zoe测试OKR",
        "menus": [

          {
            "appKey": "contacts",
            "appName": "人事通讯录",
            "appUrl": "http://contacts-wxvendor.test-caagw.yunassess.com/"
          },
          {
            "appKey": "regist",
            "appName": "入职管理",
            "appUrl": "http://regist-wxvendor.test-caagw.yunassess.com/pc"
          },
          {
            "appKey": "beiluo",
            "appName": "人才库",
            "appUrl": "http://beiluo-wxvendor.test-caagw.yunassess.com/"
          },
          {
            "appKey": "xinyun",
            "appName": "工资单",
            "appUrl": "http://xinyun-wxvendor.test-caagw.yunassess.com/metatemplate"
          },
          {
            "appKey": "enteperformance",
            "appName": "绩效考核",
            "appUrl": "http://enteperformance-wxvendor.test-caagw.yunassess.com/"
          },
          {
            "appKey": "fact",
            "appName": "背景调查",
            "appUrl": "http://fact-wxvendor.test-caagw.yunassess.com/"
          },
          {
            "appKey": "vi",
            "appName": "远程面试",
            "appUrl": "http://vi-wxvendor.test-caagw.yunassess.com/"
          },
          {
            "appKey": "h5platform",
            "appName": "活动宝",
            "appUrl": "http://h5platform-wxvendor.test-caagw.yunassess.com/"
          }
        ],
        "homeUrl": "http://assistant-wxvendor.test-caagw.yunassess.com/management",
        "guideUrl": "http://assistant-wxvendor.test-caagw.yunassess.com/management/guide",
        "userMenus": [
          {
            "target": "_blank",
            "name": "智能客服",
            "url": "https://aiwx.html5.qq.com/chat?key=be12f87b223653c6a867a2898ccdf5c58755ac8f6dad4f680ef0af308909300751edea14253fa432b47c52002fb0d58701ed78777efcdd618d8cbb66e67b3640e76b9bcfa1f6d3914fd9ef153aba27b0&id="
          },
          {
            "name": "qq",
            "url": "https://www.qq.com"
          }
        ]
      },
      appKey: 'contacts',
      userName: 'zhijie',
      avatar: 'http://wework.qpic.cn/bizmail/ibNWofxhmpySxHiajpWLv0nJM28OR12SSfS9g53GhJYbo98DH62PTrWm4g/0',
      map: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      remoteData: [
        {
          value: "1",
          label: "技术类",
          children: [{
            value: "1-1",
            label: "软件开发",
            children:
              [
                { value: "1-1-1", label: "后台开发", children: undefined },
                { value: "1-1-2", label: "测试开发" }
              ]
          }, {
            value: "1-2",
            label: "技术运营"
          }, {
            value: "1-3",
            label: "技术研究",
            children: [
              { value: "1-3-1", label: "机器学习" },
              { value: "1-3-2", label: "计算机视觉" }
            ]
          }]
        },
        {
          value: "2",
          label: "产品类",
          children: [
            { value: "2-1", label: "产品经理" },
            { value: "2-2", label: "游戏策划" }
          ]
        }
      ]
    }
  },
  computed: {
  },
  methods: {
    placeChange(value) {
      if (value && value.length) {
        const item = value[0]
        this.workPlaceInfo = {
          placeName: item.fullName,
          place_id: item.item_id
        }
        console.log('this.workPlaceInfo: ', this.workPlaceInfo)
      } else {
        this.workPlaceInfo = {}
      }
    },
    updateMontorCom() {
      this.showMentorCom = false
      this.$nextTick(() => {
        this.showMentorCom = true
      })
      this.staffData1 = ''
    },
    clearMember() {
      this.$refs.mentorSelector.clearSelected()
    },
    setMember() {
      if (!this.entryReason) {
        // this.entryReason = '我是测试原因'
      }
      this.$nextTick(() => {
        this.$refs.mentorSelector.setSelectedInit(this.staffTestData)
      })
    },
    getReason(value) {
      console.log('value~~~: ', value)
    },
    handleCommand(command) {
      this.placement = command
    },
    handleChangeUnit(res) {
      console.log(res)
    },
    handleChangeStaff(res) {
      console.log('成员~~~', res)
    },
    consoleData() {
      console.log(this.positionData)
    },
    getPositionDataTree() {
      const http = DataHttp.getInstance({
        multi: true,
        axios: DataHttp.create({
          withCredentials: true,
          retry: 0,
          headers: { 'X-Requested-With': 'XMLHttpRequest' }
        }),
        map: { result: 'data', success: res => res }
      })
      const cacheKey = 'campus:position-list'
      const hasCache = DataStorage.contains(cacheKey, STORAGE_TYPE.Session)
      const promise = hasCache ? Promise.resolve(DataStorage.get(cacheKey, { storageType: STORAGE_TYPE.Session })) : http.get('http://local.woa.com:8001/zhaopin/campus/campusCenterApi/v1/dictionary/getPositionTree')
      return promise.then(res => {
        console.log(hasCache)
        if (!hasCache) {
          res.forEach(item1 => {
            item1.children.forEach(item2 => {
              item2.children.forEach(item3 => {
                item3.children = undefined
              })
            })
          })
          console.log(res)
          DataStorage.set(cacheKey, res, { storageType: STORAGE_TYPE.Session })
          return Promise.resolve(res)
        }
        return Promise.resolve(res)
      })
    }
  },
  mounted() {
    // setTimeout(() => {
    //   this.$refs.mentorSelector.setSelectedInit(this.disabledData)
    // }, 1000)
  },
  created() {
    this.customGetPositionData = this.getPositionDataTree
  }
}
</script>

<style lang="less">
.staff-demo {
  width: 70%;
}
.demo-info {
  font-size: 15px;
  margin-bottom: 30px;
  color: #999;
}
.unit-demo {
  width: 50%;
  display: inline-block;
}
.demo-block {
  margin: 100px 100px;
  //width: 400px;
}
</style>
