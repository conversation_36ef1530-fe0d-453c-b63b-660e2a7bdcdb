<template>
  <div id="app" class="sdc-label-v8-main">
    <sdc-labeling
      :labelNodeEnv="labelNodeEnv"
      :course_name="courseData.course_name"
      :course_id="courseData.net_course_id"
      :course_type="courseData.course_type"
      @getSelectedLabelList="getSelectedLabelList"
      >
      <template  v-slot:showContent>
        <div class="add-label">
          <img class="add-label-img" alt="" src="./assets/images/addLabel.png" style="height:16px;width:16px;margin-right: 5px;">
          <span>打标签</span>
        </div>
      </template>
    </sdc-labeling>
  </div>
</template>

<script>
export default {
  name: 'App',
  data () {
    return {
      LabelObj: {},
      courseData: {
        course_name: '',
        net_course_id: '',
        course_type: ''
      }
    }
  },
  computed: {
    labelNodeEnv() {
      return this.LabelObj.labelNodeEnv || 'production'
    }
  },
  created () {
    if (typeof window !== 'undefined' && typeof window.sdcLabeling !== 'object') {
      window.sdcLabeling = {}
    }
    let parentNode = this.$root.$el.id
    if (parentNode === 'sdcLabeling') {
      this.LabelObj = window.sdcLabeling
    } else {
      if (typeof window.sdcLabeling[parentNode] !== 'object') {
        window.sdcLabeling[parentNode] = {}
        return
      }
      console.log('window.sdcLabeling[parentNode]: ', window.sdcLabeling[parentNode])
      this.LabelObj = window.sdcLabeling[parentNode]
      this.courseData.course_name = this.LabelObj.course_name
      this.courseData.net_course_id = this.LabelObj.net_course_id
      this.courseData.course_type = this.LabelObj.course_type
    }
  },
  methods: {
    getSelectedLabelList(value) {
      console.log('getSelectedLabelListValue: ', value)
      // 传事件给label-show组件 更新标签列表
      window.LabelListData = value
      var event = new Event('updateLabelList')
      window.dispatchEvent(event)
    }
  }
}
</script>

<style lang="scss" scoped>
.sdc-label-v8-main {
  display: flex;
  .add-label {
      padding: 6px 10px 6px 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      cursor: pointer;
      color: #fff;
      font-size: 14px;
      background-color: #3464e0;
      line-height: 16px;
      min-width: 76px;
      &:hover {
          color: #eee;
          background-color: #005893;
      }
  }  
}
</style>
