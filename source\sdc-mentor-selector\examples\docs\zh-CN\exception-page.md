## ExceptionPage 页面异常
> 贡献者：miraclehe(何名宇)；cxyxhhuang(黄鑫杰)；最近更新时间：2021-12-27；

通用异常页面(如401, 404等)配置。

### 基础用法

401无权限

:::demo 使用 `code` 属性来设置异常类型，可选值："401"(无权限)、"404(出错页面)"，`authority`属性设置无权限时跳转的页面，`home`设置主页跳转的页面。
```html
<template>
  <sdc-exception-page code="401" authority="/401" home="/home"/>
</template>
```
:::

404出错页面

:::demo 使用 `code` 属性来设置异常类型，可选值："401"(无权限)、"404(出错页面)"。
```html
<template>
  <sdc-exception-page code="404"/>
</template>
```
:::

### ExceptionPage Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| code | 异常类型 | string | 401/404 | — |
| authority | 无权限跳转页面 | string | — | /authority |
| home | 主页跳转的页面 | string | — | /authority |
| customClass | 自定义类名 | string | — | — |

### ExceptionPage Slots
| 名称      | 说明
|---------- |-------------------------------- |
| — | 自定义右侧内容 |
