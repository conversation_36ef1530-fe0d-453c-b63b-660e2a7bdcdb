## @tencent/sdc-webui

基于Vue和Element UI创建的HR中台PC业务组件库, [文档点这里](https://sdcwebui.pages.woa.com/#/zh-CN/component/install)。

### 安装
```bash
tnpm install @tencent/sdc-webui
```

### 引入
可以采取以下三种引入方式：
- 全量引入
- 按需引入
- CDN引入

#### 完整引入

为了在代码中方便引入npm包，可在webpack.base.conf.js (也可在默认的webpack.config.js）中配置别名：
```javascript
module.exports = {
  ...,
  resolve: {
    alias: {
      'sdc-webui': '@tencent/sdc-webui'
    }
  }
}
```
**注意：@vue/cli3脚手架生成的项目在vue.config.js中配置。**

在 main.js 中写入以下内容：

```javascript
import '~sdc-webui/lib/theme-grace/index.css'; // 也可放在App.vue中引入
import Vue from 'vue';
import SDC from 'sdc-webui';
import App from './App.vue';

Vue.use(SDC);

new Vue({
  el: '#app',
  render: h => h(App)
});
```

**注意：样式文件需要单独引入(此时不要在babel中配置按需加载选项)。**

#### 按需引入

借助 [<font color=#409EFF>babel-plugin-component</font>](https://github.com/QingWei-Li/babel-plugin-component)，我们可以只引入需要的组件，以达到减小项目体积的目的。

首先，安装 babel-plugin-component：

```bash
tnpm install babel-plugin-component -D
```

然后，将 .babelrc 修改为：

```json
{
  "presets": [["es2015", { "modules": false }]],
  "plugins": [
    [
      "component",
      {
        "libraryName": "sdc-webui",
        "styleLibraryName": "theme-grace"
      }
    ]
  ]
}
```

接下来，如果你只希望引入部分组件，比如 只需要引入素材通用模块中的Content（latex转义html解析），那么需要在 main.js 中写入以下内容：

```javascript
import Vue from 'vue';
import { StaffSelector, UnitSelector, PostSelector } from 'sdc-webui';
import App from './App.vue';

Vue.component(StaffSelector.name, StaffSelector);
Vue.component(UnitSelector.name, UnitSelector);
Vue.component(PostSelector.name, PostSelector);

/* 或写为
 * Vue.use(StaffSelector)
 * Vue.use(UnitSelector)
 * Vue.use(PostSelector)
 */

new Vue({
  el: '#app',
  render: h => h(App)
});
```

**注意：按需加载不需要单独引入样式文件。**

#### CDN引入
首先配置webpack.base.conf.js (也可在默认的webpack.config.js） 将sdc-webui加入到externals中：
```javascript
module.exports = {
  ...,
  externals: {
    'sdc-webui': 'SDC'
  }
}
```
**注意：CDN引入不需要安装sdc-webui。**

其次在main.js中引入sdc-webui：
```javascript
import Vue from 'vue';
import SDC from 'sdc-webui';

Vue.use(SDC);
```
最后在index.html中引入对应的脚本与样式：
```html
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>SDC CDN引入</title>
    <link rel="stylesheet" href="./node_modules/@tencent/sdc-webui/lib/theme-grace/index.css">
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script src="./node_modules/@tencent/sdc-webui/lib/index.js"></script>
    <script src="./node_modules/@tencent/sdc-webui/lib/umd/locale/zh-CN.js"></script>
  </body>
</html>
```
**注意：样式与脚本可在线上环境来自于CDN，开发环境引入本地即可。**
