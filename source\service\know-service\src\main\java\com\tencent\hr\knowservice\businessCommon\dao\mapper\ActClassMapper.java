package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActClass;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ActClassDTO;
import com.tencent.hr.knowservice.faceClass.dto.ClassDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ActClassMapper extends BaseMapper<ActClass> {
    int deleteByPrimaryKey(Integer classId);

    int insert(ActClass record);

    int insertSelective(ActClass record);

    ActClass selectByPrimaryKey(Integer classId);

    int updateByPrimaryKeySelective(ActClass record);

    int updateByPrimaryKey(ActClass record);

    ActClassDTO findActClassById(@Param("classId") Integer classId);

    ClassDetailDTO getClassDetail(@Param("classId") Integer classId);
}