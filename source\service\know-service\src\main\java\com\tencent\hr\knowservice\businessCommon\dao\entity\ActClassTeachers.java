package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * act_class_teachers
 * <AUTHOR>
@Data
public class ActClassTeachers implements Serializable {
    private Integer id;

    /**
     * 班级id
     */
    private Integer classId;

    /**
     * 讲师id
     */
    private Integer staffId;

    /**
     * 讲师姓名
     */
    private String staffName;

    /**
     * 讲师类型 1 内部讲师  2 外部讲师
     */
    private Byte teacherType;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private Integer oldSyncId;

    private static final long serialVersionUID = 1L;
}