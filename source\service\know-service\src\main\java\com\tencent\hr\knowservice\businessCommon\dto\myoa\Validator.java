package com.tencent.hr.knowservice.businessCommon.dto.myoa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class Validator {

    /**
     * 验证器类型,包括:NumberRange, DateRange, CharRange, Regex, StringLength
     */
    private String type;

    /**
     * 提交表单用，参数
     */
    private List<String> arguments;

    /**
     * 提交表单用，验证失败的错误消息
     */
    @JsonProperty(value = "error_message")
    private String errorMessage;
}
