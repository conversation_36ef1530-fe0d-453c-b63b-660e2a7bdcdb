package com.tencent.hr.knowservice.faceClass.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.faceClass.dao.entity.ActUserSign;
import org.springframework.stereotype.Repository;

@Repository
public interface ActUserSignMapper extends BaseMapper<ActUserSign> {
    int deleteByPrimaryKey(Integer id);

    int insert(ActUserSign record);

    int insertSelective(ActUserSign record);

    ActUserSign selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ActUserSign record);

    int updateByPrimaryKey(ActUserSign record);
}