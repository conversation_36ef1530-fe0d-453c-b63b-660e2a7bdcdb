<template>
  <div class="sdc-selector" :class="[customClass, textarea ? 'sdc-selector--textarea' : 'sdc-selector--normal']" :style="{height: textarea ? containerHeight + 'px' : ''}">
    <sdc-selector-container :pasteable="pasteable" ref="container" >
      <sdc-selector-tags v-if="textarea"/>
      <div class="tags" ref="tags" :style="{'max-width': multiple ? '80%' : '100%'}" :class="[size ? 'tags--' + size : '']" v-else>
        <sdc-selector-tags @change="resetInputSize"/>
      </div>
      <template slot-scope="{ data }" slot="item">
        <slot name="selector-item" :data="data" />
      </template>
    </sdc-selector-container>
    <sdc-selector-toolbar/>
  </div>
</template>

<script>
  import { classes } from 'mixins'
  import SdcSelectorContainer from './container'
  import SdcSelectorTags from './tags'
  import SdcSelectorToolbar from './toolbar'
  import { addResizeListener, removeResizeListener } from 'sdc-webui/src/utils/resize'
  export default {
    name: 'sdc-selector',
    provide() {
      return {
        mode: this.mode,
        pasteable: this.pasteable
      }
    },
    inject: ['textarea', 'multiple', 'size', 'height', 'getSelected', 'change'],
    mixins: [classes],
    props: {
      mode: String, // 选择器类别(如: unit, staff, post)
      pasteable: Boolean // 是否支持粘贴功能
    },
    computed: {
      containerHeight() {
        return this.textarea && this.height ? this.height : ''
      }
    },
    mounted() {
      if (!this.textarea) {
        addResizeListener(this.$el, this.handleResize)
      }
    },
    destroyed() {
      if (!this.textarea && this.$el && this.handleResize) {
        removeResizeListener(this.$el, this.handleResize)
      }
    },
    methods: {
      handleResize() {
        this.resetInputSize()
      },
      resetInputSize() {
        if (this.textarea) return
        this.$nextTick(() => {
          const tags = this.$refs.tags
          const input = this.$refs.container.$refs.input.$el
          input.style.marginLeft = tags.clientWidth + 'px'
          tags.scrollLeft = tags.scrollWidth
        })
      }
    },
    components: {
      SdcSelectorContainer,
      SdcSelectorTags,
      SdcSelectorToolbar
    }
  }
</script>
