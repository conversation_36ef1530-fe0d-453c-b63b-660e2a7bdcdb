package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 延伸课程分页
 */
@Data
public class ExtendContentPageDTO {

    private Integer id;
    /**
     * 标题
     */
    private String connProdName;

    /**
     * 跳转链接
     */
    private String connProdUrl;
    /**
     * 1：置顶 0：非置顶
     */
    private Integer toped;

    /**
     * 关联时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone ="GMT+8")
    private Date createdAt;
    /**
     * 产品类型
     *  8 新图文
     */
    private Integer prodType;

    /**
     * 产品id
     */
    private Integer prodId;

    /**
     * 关联产品类型
     */
    private Integer connProdModuleId;
    /**
     * 关联产品类型名称
     */
    private String connProdModuleName;
    /**
     * 关联产品id
     */
    private String connProdItemId;

    /**
     * 1：站内内容 2:自定义内容
     */
    private Integer contentType;
    /**
     * 观看数
     */
    private Integer viewCount;
    /**
     * 评分
     */
    private Double avgScore;
    /**
     * 关联产品创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone ="GMT+8" )
    private Date connProdCreatedTime;

    /**
     * 可参与人群
     */
    private String targetPeople;

    /**
     * 点赞数
     */
    private Integer praiseCount;

    /**
     * 封面图上传途径(contentcenter | zhihui | ql)
     */
    private String photoStorageType;

    /**
     * 封面图ID(内容中心的id或是智绘设计的id)
     */
    private String photoId;

    /**
     * 课程图片URL
     */
    private String photoUrl;

    /**
     * 课程简介
     */
    private String description;


    private Integer duration;

}
