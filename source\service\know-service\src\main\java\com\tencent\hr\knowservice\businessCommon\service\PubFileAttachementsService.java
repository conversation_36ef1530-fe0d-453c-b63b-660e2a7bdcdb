package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileAttachements;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubFileAttachementsMapper;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileAttachementsDto;
import com.tencent.hr.knowservice.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pub_file_attachements(素材课件附件信息)】的数据库操作Service实现
* @createDate 2022-12-01 15:14:01
*/
@Service
public class PubFileAttachementsService {

    @Value("${netcourse.videoHost}")
    private String videoHost;

    @Autowired
    ContentCenterService contentCenterService;


    @Resource
    private PubFileAttachementsMapper attachementsMapper;

    public void addAttachements(PubFileAttachementsDto attachementsDto) {

        PubFileAttachements attachements = new PubFileAttachements();
        BeanUtils.copyProperties(attachementsDto, attachements);

        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        String staffName = current.getStaffName();

        Date date = new Date();
        attachements.setCreatedAt(date);
        attachements.setCreatorId(Integer.valueOf(staffId));
        attachements.setCreatorName(staffName);

        attachementsMapper.insert(attachements);
    }

    public void deleteCaption(Integer fileId) {
        QueryWrapper<PubFileAttachements> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        queryWrapper.eq("attachement_type", "Caption");
        queryWrapper.isNull("deleted_at");
        List<PubFileAttachements> attachements = attachementsMapper.selectList(queryWrapper);

        Date date = new Date();
        if (CollectionUtils.isNotEmpty(attachements)) {
            ContextEntity current = GatewayContext.current();
            Integer staffId = Integer.valueOf(current.getStaffId());
            String staffName = current.getStaffName();
            attachements.forEach(attachement -> {
                attachement.setDeletedAt(date);
                attachement.setUpdatedAt(date);
                attachement.setUpdateId(staffId);
                attachement.setUpdateName(staffName);
                attachementsMapper.updateById(attachement);
            });
        }

    }

    public List<PubFileAttachementsDto> findAttachementsFileId(Integer fileId) {
        QueryWrapper<PubFileAttachements> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        queryWrapper.eq("attachement_type", "Caption");
        queryWrapper.isNull("deleted_at");
        List<PubFileAttachements> attachements = attachementsMapper.selectList(queryWrapper);
        ArrayList<PubFileAttachementsDto> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachements)) {
            attachements.forEach(pubFileAttachements -> {
                PubFileAttachementsDto attachementsDto = new PubFileAttachementsDto();
                BeanUtils.copyProperties(pubFileAttachements, attachementsDto);
                dtos.add(attachementsDto);
            });
        }

        return dtos;
    }

    public boolean deleteAttachementsByContentId(String contentId, Integer fileId) {
        boolean ret = false;
        if (StringUtils.isEmpty(contentId) || fileId == null) {
            return ret;
        }
        QueryWrapper<PubFileAttachements> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("url", contentId);
        queryWrapper.eq("file_id", fileId);
        queryWrapper.eq("attachement_type", "Caption");
        queryWrapper.isNull("deleted_at");
        PubFileAttachements attachement = attachementsMapper.selectOne(queryWrapper);
        if (attachement != null) {
            ContextEntity current = GatewayContext.current();
            Integer staffId = Integer.valueOf(current.getStaffId());
            String staffName = current.getStaffName();
            attachement.setDeletedAt(new Date());
            attachement.setUpdateId(staffId);
            attachement.setUpdateName(staffName);
            attachementsMapper.updateById(attachement);
            ret = true;
        }
        return ret;
    }

    public List<PubFileAttachementsDto> getCaptionsByFileId(Integer fileId) {
        List<PubFileAttachementsDto> result = new ArrayList<>();
        if (fileId !=null && Arrays.asList(35388,35389,35390,35391,35392,35413).contains(fileId)){
            return result;
        }
        QueryWrapper<PubFileAttachements> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        queryWrapper.eq("attachement_type", "Caption");
        queryWrapper.isNull("deleted_at");
        List<PubFileAttachements> attachements = attachementsMapper.selectList(queryWrapper);
        for (PubFileAttachements item : attachements) {
            PubFileAttachementsDto entity = new PubFileAttachementsDto();
            entity.setAttachementType("Caption");
            entity.setTitle(item.getTitle());
            entity.setSize(item.getSize());
            entity.setFileName(item.getFileName());
            entity.setFileId(item.getFileId());
            String url = item.getUrl();
            if (url != null && url.contains(".")) {
                //老系统上传或自动生成的字幕，video/2019/9156/caption/94e410b2551c3bf6-zh.srt
                url = "//" + videoHost + "/" + url;
            } else {
                //新系统上传到内容中心
                String contentId = item.getContentId();
                if(!org.apache.commons.lang3.StringUtils.isEmpty(contentId)) {
                    entity.setContentId(contentId);
                    url = contentCenterService.getContentUrl(contentId);
                }
            }

            entity.setUrl(url);
            result.add(entity);
        }

        return result;
    }


}




