package com.tencent.hr.knowservice.courseInteraction.dto.condition;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 互动提交答案
 * <AUTHOR>
 */
@Data
public class InteractiveAnswers {

    @NotBlank(message = "interactiveId 不能为空")
    private String interactiveId;

    @NotBlank(message = "questionId 不能为空")
    private String questionId;

    /**
     * 用户互动行为
     */
    private String activeAnswer;
    /**
     * 是否正确
     */
    private Boolean enabledCorrect;
    /**
     * 互动分数
     */
    private Float score;
    /**
     * 是否完成（即判断用户是否符合继续学习条件）
     */
    private Boolean completion;

}
