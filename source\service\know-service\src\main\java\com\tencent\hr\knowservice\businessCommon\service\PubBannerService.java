package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.constans.PubBannerEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubBanner;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubBannerMapper;
import com.tencent.hr.knowservice.businessCommon.dto.manage.PubBannerDto;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;

import com.tencent.hr.knowservice.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.util.*;

/**
 * Banner业务逻辑层
 */
@Slf4j
@Service
public class PubBannerService {

    @Resource
    PubBannerMapper bannerMapper;

    @Autowired
    RedisUtil redisUtil;

    public PubBanner addBanner(PubBannerDto pubBannerDto) {
        int count = findByName(pubBannerDto.getBannerName());
        if (count > 0) {
            throw new LogicException(-100, "名称为“" + pubBannerDto.getBannerName() + "”的banner已经存在，请修改名称");
        }
        if (!pubBannerDto.getLinkUrl().startsWith("http://") && !pubBannerDto.getLinkUrl().startsWith("https://")) {
            throw new LogicException("跳转链接有误，须填写协议，以http(s)://开头");
        }
        PubBanner pubBanner = new PubBanner();
        BeanUtils.copyProperties(pubBannerDto, pubBanner);
        ContextEntity contextEntity = GatewayContext.get();
        pubBanner.setCreatorName(contextEntity.getStaffName());
        pubBanner.setCreatorId(Integer.parseInt(contextEntity.getStaffId()));
        pubBanner.setCreatedAt(new Date());
        pubBanner.setEnabled(true);
        pubBanner.setStatus(PubBannerEnum.TobeShelves.getCode());
        pubBanner.setActId(pubBannerDto.getActId());
        pubBanner.setActType(pubBannerDto.getActType());
        pubBanner.setBannerType(2);
        pubBanner.setDecription(pubBannerDto.getDecription());
        pubBanner.setBgColor(pubBanner.getBgColor());
        pubBanner.setViewCount(pubBannerDto.getViewCount());
        updateAddChange();
        pubBanner.setOrderNo(0);
        bannerMapper.insert(pubBanner);
        redisUtil.del(CommonCacheKeyEnum.PubBanner.getKeyName());
        return pubBanner;
    }

    /**
     * 获得当前最大排序
     */
    public int getMaxOrderNO() {
        return bannerMapper.selectMaxOrderNo();
    }

    /**
     * 所有位置下移动一位
     */
    public int updateAddChange() {
        return bannerMapper.updateAddChange();
    }


    /**
     * 通过名字，查询对应的对象数量
     *
     * @param bannerName
     * @return
     */
    public int findByName(String bannerName) {
        return bannerMapper.selectCount(new QueryWrapper<PubBanner>().eq("banner_name", bannerName).eq("enabled", 1));
    }


    public PubBanner updateBanner(PubBannerDto pubBannerDto) {
        if (pubBannerDto.getId() == null) {
            throw new LogicException("轮播图id为空");
        }
        if (pubBannerDto.getBannerName() == null) {
            throw new LogicException("轮播图名字不能为空");
        }
        PubBanner pubBannerExist = bannerMapper.selectByPrimaryKey(pubBannerDto.getId());
        // 对重名做处理
        if (!pubBannerExist.getBannerName().equals(pubBannerDto.getBannerName())) {
            int count = findByName(pubBannerDto.getBannerName());
            if (count > 0) {
                throw new LogicException("名称为“" + pubBannerDto.getBannerName() + "”的banner已经存在，请修改名称");
            }
        }
        if (!pubBannerDto.getLinkUrl().startsWith("http://") && !pubBannerDto.getLinkUrl().startsWith("https://")) {
            throw new LogicException("跳转链接有误，须填写协议，以http(s)://开头");
        }
        PubBanner pubBanner = bannerMapper.selectByPrimaryKey(pubBannerDto.getId());
        BeanUtils.copyProperties(pubBannerDto, pubBanner);
        ContextEntity contextEntity = GatewayContext.get();
        pubBanner.setUpdateId(Integer.parseInt(contextEntity.getStaffId()));
        pubBanner.setUpdateName(contextEntity.getStaffName());
        pubBanner.setUpdatedAt(new Date());
        bannerMapper.updateByPrimaryKeySelective(pubBanner);
        String key = CommonCacheKeyEnum.PubBanner.getKeyName();
        redisUtil.del(key);
        return pubBanner;
    }


    public void deleteBanner(Integer pubBannerId) {
        PubBanner pubBanner = bannerMapper.selectByPrimaryKey(pubBannerId);
        pubBanner.setEnabled(false);
        ContextEntity contextEntity = GatewayContext.get();
        pubBanner.setUpdateId(Integer.parseInt(contextEntity.getStaffId()));
        pubBanner.setUpdateName(contextEntity.getStaffName());
        pubBanner.setUpdatedAt(new Date());
        bannerMapper.updateByPrimaryKeySelective(pubBanner);
        bannerMapper.changeOrderByDelete(pubBannerId);
        String key = CommonCacheKeyEnum.PubBanner.getKeyName();
        redisUtil.del(key);
    }

    public void onOffBanner(Integer pubBannerId, Integer statusId) {
        if (pubBannerId == null) {
            throw new LogicException("轮播图id不能为空");
        }
        if (statusId == null) {
            throw new LogicException("轮播图状态不能为空");
        }
        PubBanner pubBanner = bannerMapper.selectByPrimaryKey(pubBannerId);
        pubBanner.setStatus(statusId);
        ContextEntity contextEntity = GatewayContext.get();
        pubBanner.setUpdateId(Integer.parseInt(contextEntity.getStaffId()));
        pubBanner.setUpdateName(contextEntity.getStaffName());
        pubBanner.setUpdatedAt(new Date());
        bannerMapper.updateById(pubBanner);
        String key = CommonCacheKeyEnum.PubBanner.getKeyName();
        redisUtil.del(key);
    }

    public IPage<PubBanner> getBannersInfos(Integer current, Integer size, String bannerName, Integer status) {
        IPage<PubBannerDto> page = new Page<>(current, size);
        IPage<PubBanner> carouselsInfosIPage = bannerMapper.selectPageBannerInfos(page, bannerName, status);
        return carouselsInfosIPage;
    }
    public List<PubBanner> getBannersInfos(String bannerName,Integer status) {
        if(status == null){
            status =PubBannerEnum.OnShelves.getCode();
        }
        String key = CommonCacheKeyEnum.PubBanner.getKeyName();
        Object o = redisUtil.get(key);
        if(!ObjectUtils.isEmpty(o)){
            return (List<PubBanner>) redisUtil.get(key);
        }
        List<PubBanner> carouselsInfos = bannerMapper.selectPageBannerInfos( bannerName,status);
        redisUtil.set(key,carouselsInfos);
        redisUtil.expire(key, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
        return carouselsInfos ;
    }

    /**
     * 轮播图排序
     *
     * @param bannerId,orderNo
     * @return
     */
    @Transactional
    public void order(Integer bannerId, Integer orderNo) {
        PubBanner pubBanner = bannerMapper.selectById(bannerId);
        if (ObjectUtils.isEmpty(pubBanner)) {
            throw new LogicException("id为" + bannerId + "的banner不存在，请检查！");
        }
        orderByParam(pubBanner, orderNo);
    }

    /**
     * 轮播图排序,如果向下排，之间的元素向上移，NO-1；反之NO+1
     *
     * @param pubBanner,orderNo
     * @return
     */
    public void orderByParam(PubBanner pubBanner, Integer orderNo) {
        Integer beforeNo = pubBanner.getOrderNo();
        if (beforeNo < orderNo) {
            bannerMapper.changeOrderToUp(orderNo, beforeNo);
        } else if (beforeNo > orderNo) {
            bannerMapper.changeOrderToDown(orderNo, beforeNo);
        } else {
            return;
        }
        pubBanner.setOrderNo(orderNo);
        bannerMapper.updateById(pubBanner);
    }
}
