package com.tencent.hr.knowservice.courseInteraction.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.SelectContent;
import lombok.Data;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/10/19/14:40
 * @version: 1.0
 */
@Data
public class InteractiveRecordInfoDto {

    /**
     * 记录唯一主键
     */
    private Integer id;

    /**
     * 互动规则id
     */
    private String interactiveConfigId;

    /**
     * 互动id
     */
    private String interactiveId;

    /**
     * 试题id
     */
    private String questionId;
    /**
     * 互动类型 CHOOSE--选择 VOTE-投票 (时间切片-快照)
     */
    private String activeType;

    /**
     * 互动行为
     */
    private String activeAnswer;

    /**
     * 是否正确
     */
    private boolean enabledCorrect;

    /**
     * 互动标题
     */
    private String title;

    /**
     * 互动标题英文版
     */
    private String titleEn;

    /**
     * 互动介绍
     */
    private String introduction;

    /**
     * 互动介绍英文版
     */
    private String introductionEn;

    /**
     * 试题信息
     */
    private SelectContent selectContent;

    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String createdAt;
}
