package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileAdmins;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileAttachements;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PubFileDto {
    /**
     * 素材id
     */
    private Integer fileId;
    /**
     * 素材名称，用于显示给用户看
     */
    private String fileShowName;

    /**
     * 上传的原始文件名
     */
    private String fileName;

    /**
     * 存放到服务器的新文件名
     */
    private String newFileName;

    /**
     * 文件后缀名
     */
    private String fileExtension;

    /**
     * 文件物理存储路径
     */
    private String filePath;

    /**
     * 文件查看url，暂时不用
     */
    private String fileUrl;


    /**
     * 素材对应的模块
     */
    private String appModule;

    private Boolean catalogue;

    /**
     * 素材来源
     */
    private String contentType;

    /**
     * 文章内容
     */
    private String content;

    @JsonIgnore
    private String extContent;
    /**
     * 素材描述
     */
    private String fileDesc;

    /**
     * 素材类型(来源字典表 标准课件 、非标准课件、案例库)
     */
    private String fileType;

    private List<PubFileAdmins> admins;

    /**
     * 文件大小
     */
    private Long fileSize;


    /**
     * 字数
     */
    private Integer wordNum;

    /**
     * 是否需要解压缩(0 否 1 是)
     */
    private Integer needDecompress;

    /**
     * 启动文件名称
     */
    private String startFileName;


    /**
     * 状态（ 1：待转码  2：正在转码 3：转码失败 4：待分发   5：正在分发 6：分发失败 7：待移动端转码  8：正在移动端转码 9：转码移动端失败 10：待移动端分发   11：正在移动端分发 12：移动端分发失败 13：成功 14 正在音频转码 ）
     */
    private Integer status;


    /**
     * hls文件存放服务器列表，用逗号分隔
     */
    private String hlsStorageServer;

    /**
     * 课件的语言（用于语音识别）
     */
    private String language;

    /**
     * 文件id
     */
    private String contentId;

    /**
     * 字幕内容id
     */
    private PubFileAttachements pubFileAttachements;

    /**
     * 视频时长
     */
    private int duration;

    /**
     * 引用次数
     */
    public Integer referCount;

    /**
     * 创建人Id
     */

    private Integer creatorId;


    /**
     * 创建人姓名
     */
    private String creatorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date deletedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date updatedAt;

    private Boolean draft;


}
