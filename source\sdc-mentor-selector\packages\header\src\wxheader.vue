<template>
  <div :class="['sdc-wxheader', 'sdc-header', `sdc-header-${this.scope}`]">
    <div class="header-inner">
      <div class="header-left" :class="{ 'has-nav': has('nav') }">
        <div class="nav" v-if="has('nav')">
          <sdc-nav-menu :scope="scope" :menuData="navData">
            <i class="el-icon-menu"></i>{{ $st("sdc.layout.navMenu") }}
          </sdc-nav-menu>
        </div>
        <div class="logo">
          <slot name="logo">
            <!-- 租户名 -->
            <div class="logo-text ellipsis" :title="companyName">
              {{ companyName }}
            </div>
          </slot>
        </div>
      </div>
      <div class="header-center">
        <div class="menu-list">
          <slot name="menus">
            <!-- 导航菜单 -->
            <sdc-navbar :menus="menus" v-if="showMenuList" />
          </slot>
        </div>
      </div>
      <div class="header-right">
        <div class="header-right-inner">
          <div class="header-icons">
            <a v-if="appStoreURL" :href="appStoreURL || 'javascript:void(0);'">
              <i class="icon-appstore"></i> 商品中心
            </a>
            <a v-if="homeURL" :href="homeURL || 'javascript:void(0);'">
              <i class="el-icon-setting"></i> 设置
            </a>
          </div>
          <!-- 头像 -->
          <div class="header-avatar">
            <sdc-avatar :avatar="avatarConfig" @click="handleClick">
              <open-data type="userName" :openid="userName"></open-data>
            </sdc-avatar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { layout, locale } from 'mixins'
import SdcNavMenu from './navmenu'
import SdcNavbar from './navbar'
import openData from './openData'
import SdcAvatar from 'packages/avatar'

export default {
  name: 'sdc-wxheader',
  mixins: [layout, locale],
  props: {
    scope: {
      type: String,
      default: 'oc'
    },
    navData: {
      type: Object,
      default() {
        return null
      }
    },
    wxData: {
      type: Object,
      default() {
        return null
      }
    },
    // 当前选择的应用
    activeAppKey: String,
    // 用户名
    userName: String,
    // 用户头像地址
    avatarUrl: String,
    // 退出登录操作
    logoutMethod: Function,
    // 是否启用侧边栏
    enableSidebar: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      // 侧边栏js地址
      siderJsUrl: '//cdn.m.tencent.com/cloud_assistant/static/js/sider.js'
    }
  },
  computed: {
    menus() {
      if (this.wxData && this.wxData.menus) {
        const result = {
          active: this.activeAppKey,
          map: {},
          data: {}
        }
        result.data = this.wxData.menus.map(item => {
          return {
            key: item.appKey,
            url: item.appUrl,
            text: item.appName,
            target: '_self'
          }
        })
        return result
      }
      return {}
    },
    showMenuList() {
      return this.menus.data && this.menus.data.length > 0
    },
    headerLayout() {
      if (this.layout && this.layout.length > 0) {
        return this.layout
      }
      let defaultLayout = []
      if (this.scope === 'oa') {
        defaultLayout = ['logo', 'search', 'feedback', 'links']
      } else if (this.scope === 'oc') {
        defaultLayout = ['logo', 'menus', 'icons', 'avatar']
      }
      return defaultLayout
    },
    companyName() {
      return this.wxData ? this.wxData.corpName : ''
    },
    appStoreURL() {
      return this.wxData ? this.wxData.appStoreUrl : ''
    },
    homeURL() {
      return this.wxData ? this.wxData.homeUrl : ''
    },
    avatarConfig() {
      const config = {
        avatarUrl: this.avatarUrl,
        data: []
      }
      if (this.wxData && this.wxData.userMenus) {
        config.data = this.wxData.userMenus.map(item => {
          return {
            text: item.name,
            type: item.name
          }
        })
      }
      config.data.push({ text: '退出', type: 'logout' })
      return config
    }
  },
  created() {
    console.log("enableSidebar", this.enableSidebar)
    if (this.enableSidebar) {
      this.importSidebarJs()
    }
  },
  methods: {
    has(key) {
      return this.headerLayout && this.headerLayout.some(item => item === key)
    },
    handleClick(type) {
      if (type === 'logout') {
        if (this.logoutMethod) {
          this.logoutMethod()
          return
        }
        window.location = `/_logout?_${new Date().getTime()}`
        return
      }
      if (this.wxData && this.wxData.userMenus) {
        const val = this.wxData.userMenus.find(item => item.name === type)
        val && window.open(val.url, val.target || '_self')
      }
    },
    initOpenDataComplate(enable) {
      this.$bus.$emit('initOpenDataComplate', enable)
    },
    // 动态引入js并初始化
    importSidebarJs() {
      if (window.$siderTest) return
      console.log("importSidebarJs")
      const scriptDom = document.createElement('script')
      scriptDom.type = "text/javascript"
      scriptDom.src = this.siderJsUrl
      scriptDom.onload = () => {
        console.log("$siderTest", window.$siderTest)
        if (window.$siderTest) {
          window.$siderTest = {
            isShowss: true
          }
        }
      }
      document.head.appendChild(scriptDom)
    }
  },
  components: {
    SdcNavMenu,
    SdcNavbar,
    SdcAvatar,
    openData
  }
}
</script>
