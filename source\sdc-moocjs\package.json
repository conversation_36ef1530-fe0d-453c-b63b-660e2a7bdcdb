{"name": "@tencent/sdc-moocjs", "version": "1.0.13", "description": "Mooc与第三方页面信息交互SDK", "main": "dist/sdc-moocjs.min.js", "scripts": {"dev": "rollup --config rollup.config.js", "build": "rollup --config rollup.config.js", "test": "echo \"Error: no test specified\" && exit 1"}, "license": "ISC", "devDependencies": {"@babel/core": "^7.18.5", "@babel/preset-env": "^7.18.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup": "^2.75.6", "rollup-plugin-terser": "^7.0.2"}}