import { DataStorage, STORAGE_TYPE } from 'sdc-core'
import StaffService from './staff.service'
import CoreService from './core.service'

export default class PostService {
  // 获取岗位
  static postPost({ recordStatusList = [1], limit = 1000, postIdList = undefined, orgIdList = undefined, postNameLike = undefined, includeSubOrgFlag = 0, orgTypeIdList = [1] } = {}) {
    const params = {
      queryCondition: {
        limit,
        argMap: {
          postIdList, // 搜索岗位id 
          postNameLike, // 搜索员工名称 
          orgIdList: orgIdList !== undefined ? Array.isArray(orgIdList) ? orgIdList : [orgIdList] : undefined, // 组织id
          recordStatusList, // 有效:1,无效:0
          includeSubOrgFlag, // 是否包含下级组织 包含:1，不包含:0
          orgTypeIdList // 组织类型Id；1：实体组织，2：虚拟组织
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-post-realtime/sdc-webui/data', { params })
  }
  
  static getDataList(name, { count = 10, filterEnableFlag = true, unitID = undefined, isContainSubUnit = true, NotContainVirtualUnit = false } = {}) {
    const params = {
      limit: count,
      orgIdList: unitID,
      recordStatusList: filterEnableFlag ? [1] : [1, 0], // 组织状态,
      orgTypeIdList: NotContainVirtualUnit ? [1, 2] : [1],
      includeSubOrgFlag: isContainSubUnit ? 1 : 0
    }
    const isNumber = !isNaN(parseFloat(name)) && isFinite(name)
    const queryKey = isNumber ? 'postIdList' : 'postNameLike'
    params[queryKey] = isNumber ? [Number(name)] : name
    return PostService.postPost(params).then(res => PostService.mapPostData(res.content || []))
  }

  static getTreeData(unitId, { isCache = false, filterEnableFlag = true, onlyPost = false, NotContainVirtualUnit = false } = {}) {
    // 如果传入是空数组，需要转成0，不然会查所有组织
    if (Array.isArray(unitId) && !unitId.length) {
      unitId = 0
    }
    const cacheKey = filterEnableFlag ? 'sdc:ui-post-list' : 'sdc:ui-post-all'
    const hasCache = DataStorage.contains(cacheKey, STORAGE_TYPE.Session)
    let promiseOrg = Promise.resolve()
    // 如果unitId为数组，说明是组件传参，需要查组织
    if (!onlyPost || Array.isArray(unitId)) {
      const paramsKey = Array.isArray(unitId) ? 'orgIdList' : 'parentOrgId'
      const params = {
        orgTypeIdList: NotContainVirtualUnit ? [1, 2] : [1]
      }
      params[paramsKey] = unitId
      promiseOrg = isCache && hasCache ? Promise.resolve(DataStorage.get(cacheKey, { storageType: STORAGE_TYPE.Session })) : StaffService.postOrg(params)
    }
    let promisePost = Promise.resolve()
    // 如果unitId为数组，说明是组件传参，只查组织，不查岗位
    if (unitId > 0 && !Array.isArray(unitId)) {
      const params = {
        orgIdList: unitId,
        recordStatusList: filterEnableFlag ? [1] : [1, 0], // 组织状态
        orgTypeIdList: NotContainVirtualUnit ? [1, 2] : [1]
      }
      promisePost = PostService.postPost(params)
    }
    return Promise.all([promiseOrg, promisePost]).then(res => {
      // 兼容老版本，老版本组织返回字段为【SubUnitList】，新数据源返回字段【content】，存入sessionStorage是【SubUnitList】，所以需要判断是从缓存取还是从新数据源接口取
      const SubUnitList = res[0] ? isCache && hasCache ? (res[0].SubUnitList || []) : StaffService.mapOrgData(res[0].content || []) : []
      isCache && !hasCache && res[0] && DataStorage.set(cacheKey, { SubUnitList: SubUnitList }, { storageType: STORAGE_TYPE.Session })
      const PostList = PostService.mapPostData(res[1] ? (res[1].content || []) : [])
      return {
        unit: SubUnitList,
        post: PostList
      }
    })
  }

  static getChildrenData(unitId, { filterEnableFlag = true, isContainSubUnit = true, NotContainVirtualUnit = false }) {
    const params = {
      orgIdList: unitId,
      recordStatusList: filterEnableFlag ? [1] : [1, 0], // 组织状态
      orgTypeIdList: NotContainVirtualUnit ? [1, 2] : [1],
      includeSubOrgFlag: 0
    }
    return PostService.postPost(params).then(res => PostService.mapPostData(res.content || []))
  }
  
  /**
   * 格式化postList，新的数据源字段与之前不同，需map转成老版本字段
   * @param {Array} postList 
   * @returns  {Array}
   */
  static mapPostData(postList) {
    return postList.map(item => {
      const { orgFullNameCn, postId, postNameCn, orgId, ...other } = item
      const post = {
        PostFullName: `${orgFullNameCn}/${postNameCn}`,
        PostID: postId,
        PostName: postNameCn,
        UnitID: orgId,
        orgFullNameCn: orgFullNameCn,
        ...other
      }
      return post
    })
  }
}
