package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActNetCourseSummary;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ActNetCourseSummaryMapper extends BaseMapper<ActNetCourseSummary> {
    int deleteByPrimaryKey(Integer netCourseId);

    int insert(ActNetCourseSummary record);

    int insertSelective(ActNetCourseSummary record);

    ActNetCourseSummary selectByPrimaryKey(Integer netCourseId);

    int updateByPrimaryKeySelective(ActNetCourseSummary record);

    int updateByPrimaryKey(ActNetCourseSummary record);
    
}