package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfo;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfoDetail;
import com.tencent.hr.knowservice.businessCommon.dto.PubFilePageDto;
import com.tencent.hr.knowservice.businessCommon.vo.PubFileInfoVo;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pub_file_info(素材表)】的数据库操作Mapper
* @createDate 2022-11-22 14:29:47
* @Entity com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfo
*/
public interface PubFileInfoMapper extends BaseMapper<PubFileInfo> {

    /**
     * 设置 batch_no, is_trans_sucessed 为空 使定时任务能够调用重新转码
     * @param id
     */
    void updateByFileId(@Param("id") String id);

    /**
     * 素材上传更新状态为转码中
     * @return
     */
    List<String> getPubFileContentIds();

    List<PubFileInfo> batchByIds(List<String> list);

    /**
     * 根据内容id 更新转码状态
     * @param id
     */
    void updateStatusByContentId(@Param("id") String id);

    PubFileInfoDetail getpubFileDetail(@Param("contentId") String contentId);

    List<Integer> getFileIdsByStaffId(@Param("staffId") String staffId);

    IPage<PubFileInfoVo> pages(@Param("page") IPage page, @Param("dto") PubFilePageDto dto, @Param("current") ContextEntity current, @Param("isAdmin") Boolean isAdmin);
}




