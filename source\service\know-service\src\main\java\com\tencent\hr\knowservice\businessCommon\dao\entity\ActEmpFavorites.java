package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * act_emp_favorites
 *
 *
 *
 */
@Data
public class ActEmpFavorites implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户Id
     */
    private Integer staffId;

    /**
     * 用户名
     */
    private String empName;

    /**
     * 课程/网课/直播id
     */
    private String actId;

    /**
     * 单据类型(1 面授课 2 网络课 3 班级 4 活动 5 直播)
     */
    private Byte actType;

    /**
     * 课程/网课/直播名称
     */
    private String actName;

    /**
     * 互动id（评论/提问）
     */
    private Integer interactId;

    /**
     * 单据类型(1 评论 2提问,3 课程)
     */
    private Integer interactType;

    /**
     * 老系统Id
     */
    private Integer oldSyncId;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;

    private static final long serialVersionUID = 1L;

}