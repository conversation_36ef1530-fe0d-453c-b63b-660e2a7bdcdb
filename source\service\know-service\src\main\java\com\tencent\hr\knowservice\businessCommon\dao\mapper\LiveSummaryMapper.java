package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.LiveSummary;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface LiveSummaryMapper extends BaseMapper<LiveSummary> {
    int deleteByPrimaryKey(String liveId);

    int insert(LiveSummary record);

    int insertSelective(LiveSummary record);

    LiveSummary selectByPrimaryKey(String liveId);

    int updateByPrimaryKeySelective(LiveSummary record);

    int updateByPrimaryKey(LiveSummary record);
}