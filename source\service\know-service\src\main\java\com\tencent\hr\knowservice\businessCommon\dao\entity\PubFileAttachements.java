package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 素材课件附件信息
 * @TableName pub_file_attachements
 */
@TableName(value ="pub_file_attachements")
@Data
public class PubFileAttachements implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 素材id
     */
    private Integer fileId;

    /**
     * 附属信息类型（Audio 音频 Caption 字幕 Image 截图提示 PlayList 不同码率视频 M-Video 移动端自适应播放列表）
     */
    private String attachementType;

    /**
     * 视频宽度(对视频有效)
     */
    private String width;

    /**
     * 视频高度(对视频有效)
     */
    private String height;

    /**
     * 分辨率(对视频有效)
     */
    private String displayRatio;

    /**
     * 音视频时长(对音视频有效)
     */
    private BigDecimal duration;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 显示标题
     */
    private String title;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 文件路径
     */
    private String url;

    /**
     * 生成方式（1 用户上传  2  程序语音识别）
     */
    private Integer generateType;

    /**
     * 接入内容中心的contentid
     */
    private String contentId;
    /**
     * 备注
     */
    private String remark;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}