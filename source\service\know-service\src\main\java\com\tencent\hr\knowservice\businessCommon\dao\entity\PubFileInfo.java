package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * 素材表
 * @TableName pub_file_info
 */
@TableName(value ="pub_file_info")
@Data
public class PubFileInfo implements Serializable {
    /**
     * 素材id
     */
    @TableId(type = IdType.AUTO)
    private Integer fileId;

    /**
     * 应用 id
     */
    private String appId;

    /**
     * 素材名称，用于显示给用户看
     */
    private String fileShowName;

    /**
     * 上传的原始文件名
     */
    private String fileName;

    /**
     * 存放到服务器的新文件名
     */
    private String newFileName;

    /**
     * 文件后缀名
     */
    private String fileExtension;

    /**
     * 文件物理存储路径
     */
    private String filePath;

    /**
     * 文件查看url，暂时不用
     */
    private String fileUrl;

    /**
     * 素材对应的模块
     */
    private String appModule;

    /**
     * 素材类型(来源字典表 标准课件 、非标准课件、案例库)
     */
    private String fileType;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 素材描述
     */
    private String fileDesc;

    /**
     * 是否需要解压缩(0 否 1 是)
     */
    private Integer needDecompress;

    /**
     * 启动文件名称
     */
    private String startFileName;

    /**
     * 是否需要转码(0 无需转码 1需要转码)
     */
    private Integer needTranscode;

    /**
     * 批量读取标记，读取后更新此字段
     */
    private String batchNo;

    /**
     * 转码是否成功（0 失败 1成功）
     */
    private Integer isTransSucessed;

    private Boolean catalogue;

    /**
     * 转码重试次数
     */
    private Integer transCount;

    /**
     * 转码后的物理路径
     */
    private String transcodeFilePath;

    /**
     * 
     */
    private String transcodeHlsFilePath;

    /**
     * 转码时间
     */
    private Date transcodeTime;

    /**
     * 文件分发后的url
     */
    private String distributeFileUrl;

    /**
     * 分发是否成功（0 失败 1成功）
     */
    private Integer isDistributeSucessed;

    /**
     * 分发次数
     */
    private Integer distributeCount;

    /**
     * 分发时间
     */
    private Date distributeTime;

    /**
     * 移动端转码重试次数
     */
    private Integer mobileTransCount;


    /**
     * 移动端分发次数
     */
    private Integer mobileDistributeCount;

    /**
     * 老系统中的视频地址
     */
    private String oldViewPath;

    /**
     * 外网音频地址(用于背景播放)
     */
    private String internetVideoPath;

    /**
     * 老系统中网络Id
     */
    private Integer activityFk;

    /**
     * 是否支持移动端（0 不支持 1 支持）
     */
    private Integer supportMobile;

    /**
     * 不同分辨率的版本列表用逗号分隔（0：不生成其他版本 1：1080p  2：720P  3：480P  4：360P 5：270p ）
     */
    private String diffRateVersion;

    /**
     * 是否分离过音频
     */
    private Integer isSplitAudio;

    /**
     * 状态（ 1：待转码  2：正在转码 3：转码失败 4：待分发   5：正在分发 6：分发失败 7：待移动端转码  8：正在移动端转码 9：转码移动端失败 10：待移动端分发   11：正在移动端分发 12：移动端分发失败 13：成功 14 正在音频转码 ）
     */
    private Integer status;

    /**
     * mp4文件存放服务器列表，用逗号分隔
     */
    private String storageServer;

    /**
     * hls文件存放服务器列表，用逗号分隔
     */
    private String hlsStorageServer;

    /**
     * 课件的语言（用于语音识别）
     */
    private String language;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 上云的结果状态（0 失败 1 成功）
     */
    private Integer toCloudResult;

    /**
     * 接入内容中心的contentid
     */
    private String contentId;

    /**
     * 上云时间
     */
    private Date toCloudTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 字数
     */
    private Integer wordNum;

    /**
     * MD5
     */
    private byte[] md5;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PubFileInfo other = (PubFileInfo) that;
        return (this.getFileId() == null ? other.getFileId() == null : this.getFileId().equals(other.getFileId()))
            && (this.getFileShowName() == null ? other.getFileShowName() == null : this.getFileShowName().equals(other.getFileShowName()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getNewFileName() == null ? other.getNewFileName() == null : this.getNewFileName().equals(other.getNewFileName()))
            && (this.getFileExtension() == null ? other.getFileExtension() == null : this.getFileExtension().equals(other.getFileExtension()))
            && (this.getFilePath() == null ? other.getFilePath() == null : this.getFilePath().equals(other.getFilePath()))
            && (this.getFileUrl() == null ? other.getFileUrl() == null : this.getFileUrl().equals(other.getFileUrl()))
            && (this.getAppModule() == null ? other.getAppModule() == null : this.getAppModule().equals(other.getAppModule()))
            && (this.getFileType() == null ? other.getFileType() == null : this.getFileType().equals(other.getFileType()))
            && (this.getFileSize() == null ? other.getFileSize() == null : this.getFileSize().equals(other.getFileSize()))
            && (this.getNeedDecompress() == null ? other.getNeedDecompress() == null : this.getNeedDecompress().equals(other.getNeedDecompress()))
            && (this.getStartFileName() == null ? other.getStartFileName() == null : this.getStartFileName().equals(other.getStartFileName()))
            && (this.getNeedTranscode() == null ? other.getNeedTranscode() == null : this.getNeedTranscode().equals(other.getNeedTranscode()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getIsTransSucessed() == null ? other.getIsTransSucessed() == null : this.getIsTransSucessed().equals(other.getIsTransSucessed()))
            && (this.getTransCount() == null ? other.getTransCount() == null : this.getTransCount().equals(other.getTransCount()))
            && (this.getTranscodeFilePath() == null ? other.getTranscodeFilePath() == null : this.getTranscodeFilePath().equals(other.getTranscodeFilePath()))
            && (this.getTranscodeHlsFilePath() == null ? other.getTranscodeHlsFilePath() == null : this.getTranscodeHlsFilePath().equals(other.getTranscodeHlsFilePath()))
            && (this.getTranscodeTime() == null ? other.getTranscodeTime() == null : this.getTranscodeTime().equals(other.getTranscodeTime()))
            && (this.getDistributeFileUrl() == null ? other.getDistributeFileUrl() == null : this.getDistributeFileUrl().equals(other.getDistributeFileUrl()))
            && (this.getIsDistributeSucessed() == null ? other.getIsDistributeSucessed() == null : this.getIsDistributeSucessed().equals(other.getIsDistributeSucessed()))
            && (this.getDistributeCount() == null ? other.getDistributeCount() == null : this.getDistributeCount().equals(other.getDistributeCount()))
            && (this.getDistributeTime() == null ? other.getDistributeTime() == null : this.getDistributeTime().equals(other.getDistributeTime()))
            && (this.getMobileTransCount() == null ? other.getMobileTransCount() == null : this.getMobileTransCount().equals(other.getMobileTransCount()))
            && (this.getMobileDistributeCount() == null ? other.getMobileDistributeCount() == null : this.getMobileDistributeCount().equals(other.getMobileDistributeCount()))
            && (this.getOldViewPath() == null ? other.getOldViewPath() == null : this.getOldViewPath().equals(other.getOldViewPath()))
            && (this.getInternetVideoPath() == null ? other.getInternetVideoPath() == null : this.getInternetVideoPath().equals(other.getInternetVideoPath()))
            && (this.getActivityFk() == null ? other.getActivityFk() == null : this.getActivityFk().equals(other.getActivityFk()))
            && (this.getSupportMobile() == null ? other.getSupportMobile() == null : this.getSupportMobile().equals(other.getSupportMobile()))
            && (this.getDiffRateVersion() == null ? other.getDiffRateVersion() == null : this.getDiffRateVersion().equals(other.getDiffRateVersion()))
            && (this.getIsSplitAudio() == null ? other.getIsSplitAudio() == null : this.getIsSplitAudio().equals(other.getIsSplitAudio()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getStorageServer() == null ? other.getStorageServer() == null : this.getStorageServer().equals(other.getStorageServer()))
            && (this.getHlsStorageServer() == null ? other.getHlsStorageServer() == null : this.getHlsStorageServer().equals(other.getHlsStorageServer()))
            && (this.getLanguage() == null ? other.getLanguage() == null : this.getLanguage().equals(other.getLanguage()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
            && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdateId() == null ? other.getUpdateId() == null : this.getUpdateId().equals(other.getUpdateId()))
            && (this.getUpdateName() == null ? other.getUpdateName() == null : this.getUpdateName().equals(other.getUpdateName()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getToCloudResult() == null ? other.getToCloudResult() == null : this.getToCloudResult().equals(other.getToCloudResult()))
            && (this.getContentId() == null ? other.getContentId() == null : this.getContentId().equals(other.getContentId()))
            && (this.getToCloudTime() == null ? other.getToCloudTime() == null : this.getToCloudTime().equals(other.getToCloudTime()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (Arrays.equals(this.getMd5(), other.getMd5()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getFileId() == null) ? 0 : getFileId().hashCode());
        result = prime * result + ((getFileShowName() == null) ? 0 : getFileShowName().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getNewFileName() == null) ? 0 : getNewFileName().hashCode());
        result = prime * result + ((getFileExtension() == null) ? 0 : getFileExtension().hashCode());
        result = prime * result + ((getFilePath() == null) ? 0 : getFilePath().hashCode());
        result = prime * result + ((getFileUrl() == null) ? 0 : getFileUrl().hashCode());
        result = prime * result + ((getAppModule() == null) ? 0 : getAppModule().hashCode());
        result = prime * result + ((getFileType() == null) ? 0 : getFileType().hashCode());
        result = prime * result + ((getFileSize() == null) ? 0 : getFileSize().hashCode());
        result = prime * result + ((getNeedDecompress() == null) ? 0 : getNeedDecompress().hashCode());
        result = prime * result + ((getStartFileName() == null) ? 0 : getStartFileName().hashCode());
        result = prime * result + ((getNeedTranscode() == null) ? 0 : getNeedTranscode().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getIsTransSucessed() == null) ? 0 : getIsTransSucessed().hashCode());
        result = prime * result + ((getTransCount() == null) ? 0 : getTransCount().hashCode());
        result = prime * result + ((getTranscodeFilePath() == null) ? 0 : getTranscodeFilePath().hashCode());
        result = prime * result + ((getTranscodeHlsFilePath() == null) ? 0 : getTranscodeHlsFilePath().hashCode());
        result = prime * result + ((getTranscodeTime() == null) ? 0 : getTranscodeTime().hashCode());
        result = prime * result + ((getDistributeFileUrl() == null) ? 0 : getDistributeFileUrl().hashCode());
        result = prime * result + ((getIsDistributeSucessed() == null) ? 0 : getIsDistributeSucessed().hashCode());
        result = prime * result + ((getDistributeCount() == null) ? 0 : getDistributeCount().hashCode());
        result = prime * result + ((getDistributeTime() == null) ? 0 : getDistributeTime().hashCode());
        result = prime * result + ((getMobileTransCount() == null) ? 0 : getMobileTransCount().hashCode());
        result = prime * result + ((getMobileDistributeCount() == null) ? 0 : getMobileDistributeCount().hashCode());
        result = prime * result + ((getOldViewPath() == null) ? 0 : getOldViewPath().hashCode());
        result = prime * result + ((getInternetVideoPath() == null) ? 0 : getInternetVideoPath().hashCode());
        result = prime * result + ((getActivityFk() == null) ? 0 : getActivityFk().hashCode());
        result = prime * result + ((getSupportMobile() == null) ? 0 : getSupportMobile().hashCode());
        result = prime * result + ((getDiffRateVersion() == null) ? 0 : getDiffRateVersion().hashCode());
        result = prime * result + ((getIsSplitAudio() == null) ? 0 : getIsSplitAudio().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getStorageServer() == null) ? 0 : getStorageServer().hashCode());
        result = prime * result + ((getHlsStorageServer() == null) ? 0 : getHlsStorageServer().hashCode());
        result = prime * result + ((getLanguage() == null) ? 0 : getLanguage().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdateId() == null) ? 0 : getUpdateId().hashCode());
        result = prime * result + ((getUpdateName() == null) ? 0 : getUpdateName().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getToCloudResult() == null) ? 0 : getToCloudResult().hashCode());
        result = prime * result + ((getContentId() == null) ? 0 : getContentId().hashCode());
        result = prime * result + ((getToCloudTime() == null) ? 0 : getToCloudTime().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + (Arrays.hashCode(getMd5()));
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fileId=").append(fileId);
        sb.append(", fileShowName=").append(fileShowName);
        sb.append(", fileName=").append(fileName);
        sb.append(", newFileName=").append(newFileName);
        sb.append(", fileExtension=").append(fileExtension);
        sb.append(", filePath=").append(filePath);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", appModule=").append(appModule);
        sb.append(", fileType=").append(fileType);
        sb.append(", fileSize=").append(fileSize);
        sb.append(", needDecompress=").append(needDecompress);
        sb.append(", startFileName=").append(startFileName);
        sb.append(", needTranscode=").append(needTranscode);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", isTransSucessed=").append(isTransSucessed);
        sb.append(", transCount=").append(transCount);
        sb.append(", transcodeFilePath=").append(transcodeFilePath);
        sb.append(", transcodeHlsFilePath=").append(transcodeHlsFilePath);
        sb.append(", transcodeTime=").append(transcodeTime);
        sb.append(", distributeFileUrl=").append(distributeFileUrl);
        sb.append(", isDistributeSucessed=").append(isDistributeSucessed);
        sb.append(", distributeCount=").append(distributeCount);
        sb.append(", distributeTime=").append(distributeTime);
        sb.append(", mobileTransCount=").append(mobileTransCount);
        sb.append(", mobileDistributeCount=").append(mobileDistributeCount);
        sb.append(", oldViewPath=").append(oldViewPath);
        sb.append(", internetVideoPath=").append(internetVideoPath);
        sb.append(", activityFk=").append(activityFk);
        sb.append(", supportMobile=").append(supportMobile);
        sb.append(", diffRateVersion=").append(diffRateVersion);
        sb.append(", isSplitAudio=").append(isSplitAudio);
        sb.append(", status=").append(status);
        sb.append(", storageServer=").append(storageServer);
        sb.append(", hlsStorageServer=").append(hlsStorageServer);
        sb.append(", language=").append(language);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updateId=").append(updateId);
        sb.append(", updateName=").append(updateName);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", toCloudResult=").append(toCloudResult);
        sb.append(", contentId=").append(contentId);
        sb.append(", toCloudTime=").append(toCloudTime);
        sb.append(", remark=").append(remark);
        sb.append(", md5=").append(md5);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}