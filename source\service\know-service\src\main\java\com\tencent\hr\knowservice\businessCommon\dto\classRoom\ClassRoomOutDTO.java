package com.tencent.hr.knowservice.businessCommon.dto.classRoom;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/28/15:46
 * @version: 1.0
 */
@Data
public class ClassRoomOutDTO {
    private Integer id;

    /**
     * 课程id--数据类型唯一标识，比如课程id，活动id
     */
    private String itemId;

    /**
     * 数据类型
     */
    private Integer actType;

    /**
     * 数据类型名称
     */
    private String actTypeName;

    /**
     * 互动课堂状态 0关闭 1开启
     */
    private Byte classroomStatus;

    /**
     * 互动课堂id
     */
    private String classroomId;

    /**
     * 互动课堂用户地址
     */
    private String classroomUserUrl;

    /**
     * 学员权限范围 0 公开 1 所关联数据自身权限范围  2 特定学员
     */
    private Byte authScope;

    /**
     * 特定目标学员列表
     */
    private String classroomTargetList;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 修改人id
     */
    private Integer updateId;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
}
