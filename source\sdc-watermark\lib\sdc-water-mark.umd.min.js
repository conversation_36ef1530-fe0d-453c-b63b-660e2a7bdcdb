(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e():"function"===typeof define&&define.amd?define([],e):"object"===typeof exports?exports["sdc-water-mark"]=e():t["sdc-water-mark"]=e()})("undefined"!==typeof self?self:this,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var s=e[n]={i:n,l:!1,exports:{}};return t[n].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var s in t)r.d(n,s,function(e){return t[e]}.bind(null,s));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s="fb15")}({fb15:function(t,e,r){"use strict";if(r.r(e),"undefined"!==typeof window){var n=window.document.currentScript,s=n&&n.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);s&&(r.p=s[1])}var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"component-wrapper"})},i=[],o={name:"sdcWaterMark",props:{targetId:{required:!1},targetClass:{required:!1},text:{type:String},canvasUserOptions:Object,wmUserOptions:Object,isManualInit:{type:Boolean,default:!1}},data(){return{watermarkId:"",url:"",$target:void 0,$wm:void 0,canvasOptions:{width:200,height:160,fillStyle:"rgba(12, 12, 12, 0.1)",font:"24px Microsoft Yahei",translate:{x:20,y:20},rotateDegree:39},wmOptions:{"z-index":99999},modifyCallback:null,isCloseModifyAlert:!1,destroyed:!1}},methods:{init(){this.targetId?(this.watermarkId=this.targetId+"_watermark_xx512",this.$target=document.getElementById(this.targetId)):this.targetClass&&(this.watermarkId=this.targetClass+"_watermark_xx512",this.$target=document.getElementsByClassName(this.targetClass)[0]),this.createCanvasOption(),this.createWmOption(),this.url=this.createCanvasDataUrl()},addWatermark(){this.$target&&(this.addWatermarkToTarget(),this.observeWaterMark())},createCanvasOption(){for(const t in this.canvasUserOptions)this.canvasOptions.hasOwnProperty(t)&&(this.canvasOptions[t]=this.canvasUserOptions[t])},createWmOption(){for(const t in this.wmUserOptions)this.wmOptions.hasOwnProperty(t)&&(this.wmOptions[t]=this.wmUserOptions[t])},createCanvasDataUrl(){const t=document.createElement("canvas");t.width=this.canvasOptions.width,t.height=this.canvasOptions.height;const e=t.getContext("2d");return e.fillStyle=this.canvasOptions.fillStyle,e.font=this.canvasOptions.font,e.translate(this.canvasOptions.translate.x,this.canvasOptions.translate.y),e.rotate(this.canvasOptions.rotateDegree*Math.PI/180),e.fillText(this.text,20,20),t.toDataURL("image/png")},addWatermarkToTarget(){const t=document.createElement("div");t.setAttribute("id",this.watermarkId),t.style.width=getComputedStyle(this.$target).width,t.style.height="100%",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style["pointer-events"]="none";for(const e in this.wmOptions)t.style[e]=this.wmOptions[e];t.style.background="url("+this.url+") repeat top left",this.$wm=t,this.$target.append(t)},observeWaterMark(){const t={attributes:!0,characterData:!0},e=new MutationObserver((t,e)=>{for(const r of t)e.disconnect(),this.$wm.parentNode.removeChild(this.$wm),"id"===r.attributeName&&(this.addWatermarkToTarget(),this.observeWaterMark())});e.observe(this.$wm,t);const r=new MutationObserver(t=>{for(const e of t)if("childList"===e.type&&e.removedNodes.length>0)for(const t of e.removedNodes)t.id!==this.watermarkId&&"watermark-warpper"!==t.id||(r.disconnect(),this.destroyed||(this.addWatermarkToTarget(),this.observeWaterMark()),this.isCloseModifyAlert?this.isCloseModifyAlert=!1:"watermark-warpper"===t.id&&setTimeout(()=>{location.reload()},1e3))}),n={childList:!0,subtree:!0},s=document.getElementById("videoBox");s?r.observe(s,n):r.observe(this.$target,n)},createWatermark(){this.destroyed=!1,this.init(),this.addWatermark()},refreshWatermark(){this.$wm?this.$wm.parentNode.removeChild(this.$wm):(this.init(),this.addWatermark())},destroyWatermark(){this.destroyed=!0,this.$wm.parentNode.removeChild(this.$wm)}},mounted(){this.isManualInit||(this.init(),this.addWatermark())}},d=o;function l(t,e,r,n,s,a,i,o){var d,l="function"===typeof t?t.options:t;if(e&&(l.render=e,l.staticRenderFns=r,l._compiled=!0),n&&(l.functional=!0),a&&(l._scopeId="data-v-"+a),i?(d=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),s&&s.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(i)},l._ssrRegister=d):s&&(d=o?function(){s.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:s),d)if(l.functional){l._injectStyles=d;var c=l.render;l.render=function(t,e){return d.call(e),c(t,e)}}else{var h=l.beforeCreate;l.beforeCreate=h?[].concat(h,d):[d]}return{exports:t,options:l}}var c=l(d,a,i,!1,null,null,null),h=c.exports;h.install=function(t){t.component(h.name,h)};var p=h;const u=[p],f=function(t){f.installed||u.map(e=>t.component(e.name,e))};"undefined"!==typeof window&&window.Vue&&f(window.Vue);var m={install:f,sdcWaterMark:p};e["default"]=m}})}));
//# sourceMappingURL=sdc-water-mark.umd.min.js.map