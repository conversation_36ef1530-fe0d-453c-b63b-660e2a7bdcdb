<template>
  <div class="sdc-dict-selector" :class="customClass">
    <sdc-select :value="value"  
      :tagsEllipsis="tagsEllipsis"
      :collapse-tags="collapseTags" :disabled="disabled"
      :placeholder="`${placeholder || $st('sdc.dictSelector.placeholder')}`" 
      :multiple="multiple" :filterable="filterable" clearable :size="size" :no-match-text="noMatchText"
      @change="handleSelected" @focus="handleFocus" :loading="loading" 
      @remove-tag="(tag) => $emit('remove-tag', tag)"
      @blur="(event) => $emit('blur', event)"
      @visible-change="(val) => $emit('visible-change', val)"
      @clear="$emit('clear')">
      <el-option v-for="(item, index) in options"
        :key="index" :label="item.label" :value="item.value"/>
    </sdc-select>
  </div>
</template>

<script>
  import sdcSelect from './select'
  import { DataType } from 'sdc-core'
  import { locale } from 'mixins'
  import DictionaryService from 'api/dictionary.service'

  export default {
    name: 'sdc-dict-selector',
    mixins: [locale],
    components: { sdcSelect },
    props: {
      customClass: {
        type: String,
        default: ''
      },
      promise: Promise,
      type: {
        type: [String, Number]
      },
      data: {
        type: Array,
        default: () => []
      },
      placeholder: {
        type: String,
        default: ''
      },
      size: {
        type: String
      },
      multiple: {
        type: Boolean,
        default: false
      },
      filterable: {
        type: Boolean,
        default: true
      },
      value: {
        required: true
      },
      exclude: {
        type: Array,
        default: () => []
      },
      labelMap: {
        type: String,
        default: ''
      },
      valueMap: {
        type: String,
        default: ''
      },
      collapseTags: {
        type: Boolean,
        default: false
      },
      clearable: {
        type: Boolean,
        default: false
      },
      noMatchText: {
        type: String
      },
      disabled: {
        type: Boolean,
        default: false
      },
      tagsLength: {
        type: Number,
        default: 13
      }
    },
    data() {
      return {
        options: [],
        originData: [],
        loading: false
      }
    },
    model: {
      prop: 'value',
      event: 'valueChange'
    },
    watch: {
      type(val, oldVal) {
        if (val !== oldVal) {
          this.getData()
          this.$emit('valueChange', this.multiple ? [] : '')
        }
      },
      value(newVal) {
        if (!this.isEmpty(newVal)) {
          this.getData()
        }
      }
    },
    computed: {
      tagsEllipsis() {
        const tagsLength = this.tagsLength < 1 ? 1 : this.tagsLength
        return { minLength: tagsLength, before: Math.ceil(tagsLength / 2), after: Math.floor(tagsLength / 2) }
      }
    },
    created() {
      (!this.isEmpty(this.value)) && this.getData()
    },
    methods: {
      normalizeData(data) {
        if (this.labelMap || this.valueMap) {
          data = data.map(item => ({
            label: item[this.labelMap || 'label'],
            value: item[this.valueMap || 'value']
          }))
        }
        return data
      },
      getData() {
        if (!DataType.isEmptyArray(this.data)) {
          this.options = this.normalizeData(this.data)
          return
        }
        const promise = this.promise || DictionaryService.getList(this.type)
        this.loading = true
        promise.then(res => {
          this.loading = false
          if (DataType.isArray(res)) {
            res = res.filter(item => !this.exclude.includes(item[this.labelMap || 'label']))
            this.options = this.normalizeData(res)
            this.originData = res
          }
        })
      },
      handleFocus(event) {
        this.getData()
        this.$emit('focus', event)
      },
      handleSelected(val) {
        const data = !DataType.isEmptyArray(this.data) ? this.data : this.originData
        const valueMap = this.valueMap || 'value'
        const selected = this.multiple ? data.filter(item => val.includes(item[valueMap])) : data.find(item => item[valueMap] === val)
        this.$emit('valueChange', val)
        this.$emit('change', selected)
      },
      isEmpty(value) {
        return value === '' || value === null || value === undefined || (Array.isArray(value) && value.length === 0)
      }
    }
  }
</script>
