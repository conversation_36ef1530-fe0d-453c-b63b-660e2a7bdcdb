package com.tencent.hr.knowservice.businessCommon.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2022/11/16
 * @version: 1.0
 */
@Service("auth")
@Slf4j
public class TargetPeopleServiceProxy {
    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${extapi.target-audience-service.host}")
    private String newHost;

    @Autowired
    BaseProxyService baseProxyService;

    @Value("${com.appSetting.appName}")
    private String appName;

    /**
     * 如果设置目标学员为空，默认是有权限的。
     * 如果设置的目标学员不为空，根据判断结果判断是否有权限
     * 会自动判断使用targetId还是rule
     * @param staffId
     * @param audIds
     * @return
     */
    public boolean checkRight(String staffId, String audIds) {
        if (StringUtils.isNotBlank(audIds)) {
            Boolean hasRight;
            if (audIds.matches("^[0-9]*$") || (audIds.contains(",") && audIds.split(",")[0].matches("^[0-9]*$"))) {
                hasRight = checkRightById(staffId, audIds);
            } else {
                //新的，通过rule规则
                hasRight = checkRightByRule(staffId, audIds);
            }
            return hasRight;
        }
        return true;
    }

    /**
     * 新目标学校验
     *
     * @param staffId   员工id
     * @param targetIds 目标学员集合id
     * @return 是否通过鉴权
     * @throws IOException io异常
     */
    public boolean checkRightById(String staffId, String targetIds) {
        final String api = "/api/ext/v1/audience/verify";
        //构建参数
        String[] targetIdArr = targetIds.split(",\\s+|;|,");
        // 去除targetIdArr数组中为空的值
        List<String> targetIdList = new ArrayList<>();
        for (String targetId : targetIdArr) {
            if (!StringUtils.isEmpty(targetId)) {
                targetIdList.add(targetId);
            }
        }
        targetIdArr = new String[targetIdList.size()];
        for (int i = 0; i < targetIdList.size(); i++) {
            targetIdArr[i] = targetIdList.get(i);
        }

        Map<String, Object> param = new HashMap<>();
        List<String> staffIds = new ArrayList<>();
        staffIds.add(staffId);
        param.put("staffIds", staffIds);
        param.put("audIds", targetIdArr);

        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        try {
            //发送请求
            String result = baseProxyService.postForApiWithHeaders(newHost, api, param, headers);
            //处理结果
            TransDTO<List<String>> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<List<String>>>() {
            });
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                StringBuilder targetIdSB = new StringBuilder();
                for (int i = 0; i < targetIdArr.length; i++) {
                    targetIdSB.append("index:" + i + "&value=" + targetIdArr[i]).append(",");
                }
                log.warn("鉴权服务返回错误,staffId={},targetId={},targetIdArr={},result={}", staffId, targetIds, targetIdSB.toString(), result);
                return false;
            }
            List<String> data = transDTO.getData();
            if (CollectionUtils.isEmpty(data)) {
                return false;
            }
            //应为是单个id，所以这里直接去取第一个就好
            return String.valueOf(staffId).equals(data.get(0));
        } catch (Exception e) {
            log.error("鉴权服务内部异常！", e);
            return false;
        }
    }

    /**
     * 通过规则查询目标学员
     *
     * @param staffId
     * @param rule
     * @return
     */
    public Boolean checkRightByRule(String staffId, String rule) {
        final String api = "/api/ext/v1/rule/verify";
        String url = newHost + api;
        //参数
        Map<String, Object> param = new HashMap<>();
        List<String> staffIds = new ArrayList<>();
        staffIds.add(staffId);
        param.put("staffIds", staffIds);
        param.put("rule", rule);
        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        //发送请求
        try {
            String result = baseProxyService.postForApiWithHeaders(newHost, api, param, headers);
            //处理结果
            TransDTO<List<String>> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<List<String>>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                log.warn("鉴权服务返回错误,staffId={},rule={},result={}", staffId, rule, result);
                return false;
            }
            List<String> data = transDTO.getData();
            if (CollectionUtils.isEmpty(data)) {
                return false;
            }
            return String.valueOf(staffId).equals(data.get(0));
        } catch (Exception e) {
            log.error("鉴权服务内部异常！", e);
            return false;
        }
    }

    /**
     * 修改黑白名单
     * @param audId
     * @param allowList
     * @param blockList
     * @param removeAllowList
     * @param removeBlockList
     * @return
     */
    public Boolean modifyStaffList(Integer audId, List<String> allowList, List<String> blockList, List<String> removeAllowList, List<String> removeBlockList) {
        final String api = "/api/ext/v1/audiences/import";
        String url = newHost + api;
        //参数
        Map<String, Object> param = new HashMap<>();

        param.put("audId", audId);
        param.put("allowList", allowList);
        param.put("blockList", blockList);
        param.put("removeAllowList", removeAllowList);
        param.put("removeBlockList", removeBlockList);
        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        //发送请求
        try {
            String result = baseProxyService.postForApiWithHeaders(newHost, api, param, headers);
            //处理结果
            TransDTO<List<String>> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<List<String>>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                log.warn("修改目标学员黑白名单失败,result={}", result);
                return false;
            }
            return transDTO.getSuccess();
        } catch (Exception e) {
            log.error("修改目标学员黑白名单服务内部异常！", e);
            return false;
        }
    }
    /**
     * 根据规则串获取statffName
     * @param rule
     * @return
     */
    public List<String> getTargetListByRule(String rule, Integer type) {
        List<String> datas = new ArrayList<>();
        if (StringUtils.isEmpty(rule)) {
            return datas;
        }
        ContextEntity current = GatewayContext.current();
        String staffId = String.valueOf(Integer.valueOf(current.getStaffId()));
        String staffName = current.getStaffName();
        //调用目标学员转化接口
        HttpHeaders esbHeader = HeaderSignUtil.getESBHeader(appId, appToken, staffId, staffName);
        String host = newHost + "/api/ext/v1/rule/staffs/details";
        String jsonData = "{\"rule\": \""+ rule +"\", \"type\": "+type+"}";
        String result = HttpUtil.sendPostByHttpClient(host, jsonData, esbHeader.toSingleValueMap());
        if (StringUtils.isNotBlank(result)) {
            try {
                Map map = JsonUtil.getMapByJson(result);
                datas.addAll((List) map.get("data"));
                return datas;
            } catch (Exception e) {
                return datas;
            }
        } else {
            return datas;
        }
    }

    /**
     * 根据规则串获取statffName
     * @param rule
     * @return
     */
    public List<String> getTargetListByRuleNoLogin(String rule, Integer type) {
        List<String> datas = new ArrayList<>();
        if (StringUtils.isEmpty(rule)) {
            return datas;
        }
        //调用目标学员转化接口
        HttpHeaders esbHeader = HeaderSignUtil.getESBHeader(appId, appToken, null, null);
        String host = newHost + "/api/ext/v1/rule/staffs/details";
        String jsonData = "{\"rule\": \""+ rule +"\", \"type\": "+type+"}";
        String result = HttpUtil.sendPostByHttpClient(host, jsonData, esbHeader.toSingleValueMap());
        if (StringUtils.isNotBlank(result)) {
            try {
                Map map = JsonUtil.getMapByJson(result);
                datas.addAll((List) map.get("data"));
                return datas;
            } catch (Exception e) {
                return datas;
            }
        } else {
            return datas;
        }
    }

    /**
     * 根据规则串获取statffName
     * @param targetId
     * @return
     */
    public List<String> getTargetListByTargetId(String targetId, Integer type) {
        List<String> datas = new ArrayList<>();
        if (StringUtils.isEmpty(targetId)) {
            return datas;
        }
        ContextEntity current = GatewayContext.current();
        String staffId = String.valueOf(Integer.valueOf(current.getStaffId()));
        String staffName = current.getStaffName();
        //调用目标学员转化接口
        HttpHeaders esbHeader = HeaderSignUtil.getESBHeader(appId, appToken, staffId, staffName);
        String host = newHost + "/api/ext/v1/audience/staffs/details";
        String jsonData = "{\"audIds\": [\""+ targetId +"\"], \"type\": "+type+"}";
        String result = HttpUtil.sendPostByHttpClient(host, jsonData, esbHeader.toSingleValueMap());
        if (StringUtils.isNotBlank(result)) {
            try {
                Map map = JsonUtil.getMapByJson(result);
                datas.addAll((List) map.get("data"));
                return datas;
            } catch (Exception e) {
                return datas;
            }
        } else {
            return datas;
        }
    }

    /**
     * 获取员工所在的目标学员集合
     * @param checkStaffId
     * @return
     */
    public List<String> getTargetListByStaffId(String checkStaffId) {
        List<String> datas = new ArrayList<>();

        ContextEntity current = GatewayContext.current();
        String staffId = String.valueOf(Integer.valueOf(current.getStaffId()));
        String staffName = current.getStaffName();

        if (checkStaffId == null) {
            checkStaffId = staffId;
        }

        //调用目标学员转化接口
        HttpHeaders esbHeader = HeaderSignUtil.getESBHeader(appId, appToken, staffId, staffName);

        //调用目标学员转化接口
        String host = newHost + "/api/ext/v1/staff/audiences";
        String jsonData = "{\"staffId\": \""+ checkStaffId +"\", \"appCode\": \""+appName+"\"}";
        String result = HttpUtil.sendPostByHttpClient(host, jsonData, esbHeader.toSingleValueMap());
        if (StringUtils.isNotBlank(result)) {
            try {
                Map map = JsonUtil.getMapByJson(result);
                datas.addAll((List) map.get("data"));
                return datas;
            } catch (Exception e) {
                return datas;
            }
        } else {
            return datas;
        }
    }
}
