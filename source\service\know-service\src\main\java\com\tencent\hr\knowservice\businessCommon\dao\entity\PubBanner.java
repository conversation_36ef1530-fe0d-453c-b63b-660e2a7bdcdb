package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * pub_banner
 * <AUTHOR>
@Data
public class PubBanner implements Serializable {
    /**
     * id
     */
    private String id;

    /**
     * 功能模块编码
     */
    private Integer actType;

    /**
     * 功能模块的单据id
     */
    private Integer actId;

    /**
     * Banner名称
     */
    private String bannerName;

    /**
     * 1 普通图片banner模式  2  跳转内容模块模式
     */
    private Integer bannerType;

    /**
     * banner图片的content_id
     */
    private String imgContentId;

    /**
     * banner链接地址
     */
    private String linkUrl;

    /**
     * 课程描述
     */
    private String decription;

    /**
     * banner背景色
     */
    private String bgColor;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 状态 （0 下架 1 上架）
     */
    private Integer status;

    /**
     * 展示顺序
     */
    private Integer orderNo;

    /**
     * 是否删除
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}