## Layout 布局
> 贡献者：miraclehe(何名宇)；jeeliu(刘志杰)；最近更新时间：2021-12-02；

外网和内网的统一布局组件，分为顶部区域(Header)和内容区域(Content)两部分，每部分都可以单独配置模块。

### 顶部区域(Header)

包含5个插槽：`header-logo`、`header-menus`、`header-links`、`header-icons`、`header-avatar`。
每块都可以独立配置，如果不配置则显示默认内容。也可指定需要渲染的区域`headerLayout`: `nav`、`logo`、`menus`、`links`、`icons`、`avatar`，
同时也可传入对应的数据`navbarMenus`、`avatarMenus`来渲染。

### 内容区域(Content)

包含2个插槽：`content-sidebar`、`content-main`。每块都可以独立配置，如果不配置则显示默认内容。
也可指定需要渲染的区域`contentLayout`: `sidebar`，同时也可传入对应的数据`sidebarbarMenus`来渲染。

### 菜单配置
菜单包含3部分：`navbar-menu(导航栏)`、`avatar-menu(头像，仅限于外网)`、`sidebar-menu(侧边栏)`。每一块的配置至少包含两部分：`map`、`data`。
其中map处理应用数据与框架字段映射，data来自实际的菜单数据，每个菜单项可配置：`url`、`text`、`icon`、`click`等。

### 基础用法

内网布局（包含logo,search,feedback,links,sidebar）

:::demo 使用 `scope` 属性来设置内网(oa,默认)或外网(oc), `navbarMenus`来设置指定的导航栏菜单，`sidebarMenus`来设置指定的侧边栏菜单，`headerLayout`来设置header布局可选区(['logo','search','feedback','links'])，`contentLayout`来设置content布局可选区(['sidebar'])。若要设置某个菜单禁止点击，请在data的属性添加`disabled：true`，参考下面代码。添加openeds属性可以设置默认展开的子菜单，openeds为包含菜单key的数组。
```html
<template>
  <sdc-layout :header-layout="['logo', 'search', 'feedback', 'links']" :openeds="openeds" :navbar-menus="navbarMenus" :sidebar-menus="sidebarMenus" @toggleCollapse="handleToggleCollapse" @menuContextmenu="handleMenuContextmenu"/>
</template>
<script>
  export default {
    data() {
      return {
        navbarMenus: {
          map: {
            url: 'link'
            // text: 'content'
          },
          data: [
            { link: 'https://www.qq.com/', text: '应用名称1' },
            { link: '/oa', text: '应用名称2' } // 如果在当前窗口打开，则配置target
          ]
        },
        openeds: ['tn-03'],
        sidebarMenus: {
          active: 'tn-01',
          uniqueOpened: true, // 是否只保持一个子菜单的展开，默认false
          map: {
            key: 'id',
            text: 'name',
            url: 'link',
            // level: 'level',
            // children: 'children',
            pid: 'parentId',
            root: '0'
          },
          data: [
            { id: 'tn-01', name: '办事大厅', link: 'http://hr.oa.com/', icon: 'el-icon-place', parentId: '0', level: 1 },
            { id: 'tn-02', name: '个人信息', link: 'http://hrstaff.oa.com/hr/HRStaff', icon: 'el-icon-user', parentId: '0', level: 1, disabled: true },
            {
              id: 'tn-03',
              name: '组织发展',
              icon: 'el-icon-coordinate',
              parentId: '0',
              level: 1,
              children: [
                { id: 'tn-03-02', name: '360度评估', link: 'http://yunassess.oa.com/yunassess/taskList', parentId: 'tn-03',icon:'el-icon-user', level: 2, disabled: true },
                { id: 'tn-03-03', name: '组织人才盘点', link: 'http://od.oa.com/organization/OrgPages/MyOrgTask.aspx', parentId: 'tn-03', level: 2 },
                { id: 'tn-03-04', name: '任职资格测算', link: 'http://od.oa.com/HRQualification/', parentId: 'tn-03', level: 2 }
              ]
            },
            {
              id: 'tn-04',
              name: '组织发展2',
              icon: 'el-icon-coordinate',
              parentId: '0',
              level: 1,
              children: [
                { id: 'tn-04-02', name: '360度评估', link: 'http://yunassess.oa.com/yunassess/taskList', parentId: 'tn-04', level: 2, disabled: true }
              ]
            }
          ]
        }
      }
    },
    methods:{
      handleToggleCollapse(collapse){
        console.log('sdc-layout toggleCollapse',collapse)
      },
      handleMenuContextmenu(event, menu) {
        console.log('handleMenuContextmenu', event, menu)
      }
    }
  }
</script>
```
:::

内网布局2（包含navMenu, logo,feedback,links）

:::demo `navData`来设置顶部左侧导航栏菜单，`headerLayout`来设置header布局可选区(['nav','logo', 'feedback', 'avatar'])。
```html
<template>
  <sdc-layout :header-layout="['nav', 'logo', 'feedback', 'avatar'] "  scope="oa" :sidebar-menus="navData"/>
</template>
<script>
  export default {
    data() {
      return {
        navData: {
          map: {
            key: 'id',
            text: 'name',
            url: 'link',
          },
          data: [
            { id: 'tn-01', name: '办事大厅', link: 'http://hr.oa.com/', icon: 'el-icon-place', parentId: '0', level: 1 },
            { id: 'tn-02', name: '个人信息', link: 'http://hrstaff.oa.com/hr/HRStaff', icon: 'el-icon-user', parentId: '0', level: 1 },
            {
              id: 'tn-03',
              name: '组织发展',
              parentId: '0',
              level: 1,
              children: [
                { id: 'tn-03-02', name: '360度评估', link: 'http://yunassess.oa.com/yunassess/taskList', parentId: 'tn-03',icon:'el-icon-user', level: 2 },
                { id: 'tn-03-03', name: '组织人才盘点', link: 'http://od.oa.com/organization/OrgPages/MyOrgTask.aspx', parentId: 'tn-03', level: 2 },
                { id: 'tn-03-04', name: '任职资格测算', link: 'http://od.oa.com/HRQualification/', parentId: 'tn-03', level: 2 }
              ]
            }
          ]
        }
      }
    }
  }
</script>
```
:::

外网布局（包含logo,menus,icons,avatar,sidebar）

:::demo 使用 `scope` 属性来设置内网(oa,默认)或外网(oc), `navbarMenus`来设置指定的导航栏菜单，`avatarMenus`来设置指定的头像菜单，`sidebarMenus`来设置指定的侧边栏菜单，`headerLayout`来设置header布局可选区(['logo','menus','icons','avatar'])，`contentLayout`来设置content布局可选区(['sidebar'])。
```html
<template>
  <sdc-layout scope="oc" :navbar-menus="navbarMenus" :avatar-menus="avatarMenus" :sidebar-menus="sidebarMenus"/>
</template>
<script>
  export default {
    data() {
      return {
        navbarMenus: {
          active: 'app1', // 默认选中项，值为data中的key
          map: {
            url: 'link'
            // text: 'content'
          },
          // maxMenuCount: 8, // 默认显示6个菜单项，多余项会合并成“更多”下拉菜单项，可通过maxMenuCount自定义菜单项个数
          // adaptive: true, // 根据页面宽度自适应改变展开显示的菜单项个数，此时maxMenuCount无效
          data: [
            { key: 'app1', link: 'https://www.qq.com/', text: '应用名称1' },
            { link: '/oa', text: '应用名称2' } // 如果在当前窗口打开，则配置target
          ]
        },
        avatarMenus: {
          name: 'test', // 头像名字
          url: 'http://km.oa.com/user/miraclehe',
          avatarUrl: 'examples/assets/img/avatar.png',
          map: {
            url: 'link',
            text: 'name'
          },
          data: [
            { name: '个人信息', type: 'info' },
            { name: '个人空间', link: 'http://test.app.oa.com/info', disabled: true },
            { name: '退出', divided: true, type: 'exit' } // 需要分隔一下
          ]
        },
        sidebarMenus: {
          active: 'tn-01',
          map: {
            key: 'id',
            text: 'name',
            url: 'link',
            // level: 'level',
            // children: 'children',
            pid: 'parentId',
            root: '0'
          },
          data: [
            { id: 'tn-01', name: '办事大厅', link: 'http://hr.oa.com/', icon: 'el-icon-place', parentId: '0', level: 1 },
            { id: 'tn-02', name: '个人信息', link: 'http://hrstaff.oa.com/hr/HRStaff', icon: 'el-icon-user', parentId: '0', level: 1 },
            {
              id: 'tn-03',
              name: '组织发展',
              icon: 'el-icon-coordinate',
              parentId: '0',
              level: 1,
              children: [
                { id: 'tn-03-01', name: '首页', link: 'http://od.oa.com/organization/default.aspx', parentId: 'tn-03', level: 2 },
                { id: 'tn-03-02', name: '360度评估', link: 'http://yunassess.oa.com/yunassess/taskList', parentId: 'tn-03', level: 2 },
                { id: 'tn-03-03', name: '组织人才盘点', link: 'http://od.oa.com/organization/OrgPages/MyOrgTask.aspx', parentId: 'tn-03', level: 2 },
                { id: 'tn-03-04', name: '任职资格测算', link: 'http://od.oa.com/HRQualification/', parentId: 'tn-03', level: 2 }
              ]
            }
          ]
        }
      }
    }
  }
</script>
```
:::

### Layout Attributes
| 参数            | 说明                     | 类型    | 可选值                                             | 默认值                                                                                          |
| --------------- | ------------------------ | ------- | -------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| scope           | 布局类型(内网、外网)     | string  | oa/oc                                              | oa                                                                                              |
| headerLayout    | Header可选显示区域       | array   | nav/logo/menus/search/feedback/ links/icons/avatar | 内网：['logo', 'search', 'feedback', 'links'], <br>外网: ['logo', 'menus', 'icons', 'avatar'] |
| contentLayout   | Content可选显示区域      | array   | sidebar                                            | ['sidebar']                                                                                     |
| navbarMenus     | 导航栏菜单配置           | object  | —                                                  | maxMenuCount: 6, adaptive: false |
| avatarMenus     | 头像菜单配置(仅限于外网) | object  | —                                                  | —                                                                                               |
| sidebarMenus    | 侧边栏菜单配置           | object  | —                                                  | —                                                                                               |
| sidebarCollapse | 侧边栏菜单是否默认折叠   | boolean | —                                                  | false                                                                                           |
| navData         | 顶部左侧菜单配置         | object  | —                                                  | —                                                                                               |
| openeds         | 配置默认打开的子菜单         | araay  | 数组里的值和sidebarMenus里data的key相同，参考第一个例子   | []     |

### Layout Events
| 事件名称       | 说明                             | 回调参数          |
| -------------- | -------------------------------- | ----------------- |
| search         | 当输入关键字回车时触发           | 输入关键字keyword |
| avatar         | 点击头像子菜单时触发(仅限于外网) | 子菜单类型type    |
| toggleCollapse | 左侧菜单切换展开折叠时回调       | 是否收起菜单      |
| menuContextmenu | 左侧菜单右键点击事件    | event 鼠标右击事件对象, menu 右击点的菜单项      |

### Layout Methods
| 事件名称        | 说明                    | 参数          |
| -------------- | -------------------------------- | ----------------- |
| changeCollapse | 修改左侧菜单折叠状态     | 折叠状态      |


### Layout Slots
| 名称            | 说明                       |
| --------------- | -------------------------- |
| header-logo     | 自定义logo                 |
| header-menus    | 自定义导航栏               |
| header-icons    | 自定义右侧图标(仅限于外网) |
| header-links    | 自定义右侧链接(仅限于内网) |
| header-avatar   | 自定义右侧头像(仅限于外网) |
| content-sidebar | 自定义侧边栏               |
| content-main    | 自定义主体内容区           |
