<template>
    <div id="trainingPosting" class="flex flex-column" @click.stop="hidePop">
        <tabHeader :active="1" @changeTab="changeTab" v-show="isShowTab"></tabHeader>
        <div class="part1 flex-1">
            <h3>发布帖子</h3>
            <section class="partTackTopic">
                <div>
                    <span class="titleName">参与话题</span>
                    <span class="lightKey" v-if="!form.topic_id" @click.stop="addTopic">选择话题</span>
                    <span class="lightKey" v-else  @click.stop="addTopic">{{ checkedTopicObj.topic_name }} <i class="el-icon-close" @click.stop="form.topic_id='';checkedTopicObj={}"></i></span>
                </div>
                <!-- <ul class="topicCheckedList flex flex-wrap">
                    <li v-for="(item, index) in checkedTopic" :key="index">{{ item }} <i class="el-icon-close" @click.stop="checkedTopic.splice(index, 1)"></i></li>
                </ul> -->
                <div>
                    <template v-if="checkedTopicObj.max_word_num">
                        <span>话题字数限制：</span><span class="lightKey">{{checkedTopicObj.mini_word_num}}-{{checkedTopicObj.max_word_num}}字</span>
                    </template>
                    <template v-if="checkedTopicObj.topic_temp_desc">
                        <span>话题填写模板及要求：</span><span class="lightKey" @click="isShowTemp=true">点击查看</span>
                    </template>
                </div>
            </section>
            <div class="postContent">
                <span class="place" v-if="!post_text">请输入帖子的文本内容</span>
                <!-- <textarea class="postWord" v-html="htm"  id="inputTextarea" @input="changeWord" placeholder="请输入帖子的文本内容" ></textarea> -->
                <div class="postWord" contenteditable="true" id="editable-div" @paste="pasteData($event)" @keydown="inputDown($event)" @keyup="inputUp($event)" @click.stop="linkCk($event)"></div>
                <span class="wordLimit">{{wordLen}}/8000</span>
            </div>
            <div class="insertData flex align-center">
                <label class="flex align-center imageData" for="uploadImg">
                    <img src="../assets/img/file_image.png" alt="">
                    <span>添加图片</span>
                </label>
                <div class="flex linkData">
                    <button @click.stop="insertLink" class="insertBtn flex align-center"><span></span>插入链接</button>
                    <article class="postLinkPopover" :style="{'position':linkDom?'fixed':'absolute','left': linkDom ? linkLeft:'0px','top':linkDom ? linkTop:'32px'}" v-show="visible"  @click.stop>
                        <el-form ref="form" label-width="78px">
                            <div>
                                <el-form-item label="显示文本：">
                                    <el-input class="linkTitleText" v-model="insertLinkObj.title" maxlength="20" show-word-limit size="small"  placeholder="请输入显示的文本"></el-input>
                                    <p class="iptDesc">文本为空时，默认显示为“网页链接”</p>
                                </el-form-item>
                            </div>
                            <div>
                                <el-form-item label="跳转链接：">
                                    <el-input v-model="insertLinkObj.href" size="small"  placeholder="请输入跳转的链接"></el-input>
                                    <p class="iptDesc" :style="{'color': insertLinkObj.href && !(/^(https?:\/\/)/.test(insertLinkObj.href))? '#D54941' : '#00000066'}">请输入以http://或https://开头的链接</p>
                                </el-form-item>
                            </div>
                        </el-form>
                        <footer>
                            <el-button size="mini" class="cancelBtn" @click.stop="hidePop()">取消</el-button>
                            <el-button type="primary" size="mini" :disabled="!insertLinkObj.href || !/^(https?:\/\/)/.test(insertLinkObj.href)" @click.stop="addLink();">确定</el-button>
                        </footer>
                    </article>
                </div>
            </div>
            <section class="imageBlock">
                <draggable class="selected-box flex flex-wrap" @change="changeOrder" v-model="imageList" :disabled="false" :options="{handle:'.item_img,.hoverDiv'}">
                    <div class="imageItem" v-for="(item, index) in imageList" :key="item.file_id">
                        <div class="imgPad">
                            <img class="item_img" @load="loadImg($event)" :src="item.file_url" alt="">
                        </div>
                        <span class="close" @click.stop="delImage(item,index)"><i class="el-icon-close"></i></span>
                        <div class="Mask" v-if="!item.file_url">
                            <div class="loading">
                                <img src="../assets/img/loading.gif" alt="">
                            </div>
                            <p class="progress">Loading...</p>
                        </div>
                        <div class="hoverDiv flex justify-center align-center" v-if="item.file_url">
                            <span><img src="../assets/img/eye.png" @click="imgView(index)" alt=""></span>|<span><img src="../assets/img/delete2.png" @click.stop="delImage(item,index)" alt=""></span>
                        </div>
                    </div>
                    <div class="addImage" v-if="imageList.length<9">
                        <input type="file" id="uploadImg" ref="upload" multiple @change="upload($event)" style="width: 100%;height: 100%;opacity: 0;position: absolute;left: 0;top: 0;">
                        <img src="../assets/img/<EMAIL>" alt="">
                        <p>点击上传图片</p>
                    </div>
                </draggable>
                <p class="imgDesc">最多支持上传9张图片，拖动图片可调整顺序</p>
            </section>
            <section class="postType" v-if="communityRole===2">
                <div class="titleName">帖子类型</div>
                <div>
                    <el-radio v-model="form.post_type" label="2">运营帖</el-radio>
                    <el-radio v-model="form.post_type" label="3">公告帖</el-radio>
                </div>
            </section>
        </div>
        <footer class="footer flex align-center justify-end">
            <contract :readAndAgree.sync="readAndAgree" :isBg="false" :env="env" v-if="showContract"/>
            <div class="flex align-center">
                <el-button size="small" @click.stop="changePage({path:'/user/training',name:'homePage',query:null});">取消</el-button>
                <el-button size="small" :class="{'is-disabled':!post_text.replace(/\s/g, '') || (!readAndAgree && showContract)}" type="primary" @click.stop="sendPost">发布帖子</el-button>
            </div>
        </footer>

        <el-dialog title="参与话题" width="600px" height="533px" id="topicDialog" :visible.sync="dialogFormVisible">
            <article class="topicList" @scroll="topicHandleScroll">
                <!-- <el-checkbox-group v-model="checkedTopic2" @change="handleCheckedTopicChange">
                    <el-checkbox v-for="item in topicList" :label="item.topic_name" :key="item.topic_name">
                        <div class="title">{{item.topic_name}}</div>
                        <div class="topicDesc" v-if="item.topic_intro">
                            {{ item.topic_intro }}
                        </div>
                    </el-checkbox>
                </el-checkbox-group> -->
                <el-radio-group v-model="form.topic_id">
                    <el-radio v-for="item in topicList" :key="item.topic_name" :label="item.topic_id">
                        <span class="title">{{item.topic_name}}</span>
                        <div class="topicDesc" v-if="item.topic_intro">{{ item.topic_intro }}</div>
                    </el-radio>
                </el-radio-group>
            </article>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click.stop="cancelTopic">取 消</el-button>
                <el-button size="small" type="primary" @click.stop="changeTopic">确 定</el-button>
            </div>
        </el-dialog>
        <imageView  :imgList="imgList" :imageIndex="imageIndex" :imgShow="imgShow" @close="close" />
        <topicTemp :isShowTemp.sync="isShowTemp" :title="checkedTopicObj.topic_name" :desc="checkedTopicObj.topic_temp_desc"/>
    </div>
</template>
<script>
import draggable from 'vuedraggable'
import tabHeader from './components/tabHeader.vue'
import uploadCOS from './uploadCOS.js'
import headers from './common/headers'
import imageView from './components/imageView.vue'
import contract from './components/contract.vue'
import topicTemp from './components/topicTemp.vue'
import { getTopicList, savePost, getPostDetails, editPost, postOperate, getTopicDetail } from '../service/index.js'
export default{
    name: 'trainingPosting',
    data() {
        return {
            htm: '<a href="www.baidu.com">链接</a>',
            post_id: '',
            // 上传图片列表
            imageList: [],
            // 话题列表
            topicList: [],
            // 选中的话题
            checkedTopic: [],
            checkedTopic2: [],
            dialogFormVisible: false,
            wordLen: 0,
            form: {
                post_word: '',
                post_resource: '',
                post_resource_type: 1,
                topic_id: '',
                post_type: '1',
                submit_count: 1
                // system_id: 201,
                // category_id: ''
            },
            topic_page_no:1,
            isUpload: false,
            topic_loading: false,
            topic_finished: false,
            isSave:false,
            insertLinkObj: {
                title: '',
                href: ''
            },
            visible: false,
            range: null,
            post_text: '',
            isIptFocus: false,
            linkDom: null,
            linkLeft: '10px',
            linkTop: '50px',
            isSubmit: false,
            readAndAgree: false,
            isShowTemp: false,
            // 选择的话题对象
            checkedTopicObj: {}
        }
    },
    mixins: [headers],
    components: {
        draggable,
        tabHeader,
        imageView,
        contract,
        topicTemp
    },
    created() {
        if (this.communityRole === 2) {
            this.form.post_type = '2'
        } else {
            this.form.post_type = '1'
        }
        // this.getHeaders(this.initData)
        this.initData()
    },
    mounted() {},
    methods: {
        initData(){
            if (!this.pageInfo) return
            let { post_id, topic_id } = this.pageInfo
            console.log(this.pageInfo, 'pageInfo')
            if (topic_id) {
                this.form.topic_id = topic_id
                this.getTopicDetail(topic_id)
            }
            this.getTopicList()
            if (post_id) {
                this.post_id = post_id
                getPostDetails({
                    params: {
                        post_id: post_id
                    },
                    headers: this.headersData
                }).then((res) => {
                    res.post_type = res.post_type.toString()
                    // res.post_word = res.post_word.replaceAll('<br/>', '\n')
                    this.form = res
                    this.form.submit_count = 1
                    if (res.post_resource) {
                        let arr = res.post_resource.split(',')
                        let arr2 = []
                        arr.forEach(item => {
                            arr2.push({
                                file_url: item,
                                status: 'success'
                            })
                        })
                        this.imageList = arr2
                    }
                    // if (res.community_post_topic_list && res.community_post_topic_list.length > 0) {
                    //     this.checkedTopic = this.checkedTopic2 = res.community_post_topic_list.map((ele) => ele.topic_name)
                    // }
                    document.getElementById('editable-div').innerHTML = res.post_word
                    this.post_text = document.getElementById('editable-div').textContent
                    var chineseCount = 0
                    var englishCount = 0
                    for (var i = 0; i < this.post_text.length; i++) {
                        var char = this.post_text[i]
                        if (/[\u4e00-\u9fa5]/.test(char)) {
                            // 中文字符
                            chineseCount += 2
                        } else {
                            // 非中文字符
                            englishCount += 1
                        }
                    }
                    this.wordLen = chineseCount + englishCount
                    if (this.pageInfo && this.pageInfo.type === 'revoke') {
                        this.postOperate(7, this.pageInfo.post_id)
                    }
                })
            }
        },
        // 获取详题详情
        getTopicDetail(id) {
            getTopicDetail({
                params: {
                    topic_id: id
                },
                headers: this.headersData
            }).then(res => {
                this.checkedTopicObj = res || {}
            })
        },
        // 取消话题
        cancelTopic() {
            this.form.topic_id = this.checkedTopicObj.topic_id
            this.dialogFormVisible = false
        },
        // 话题选择
        changeTopic() {
            this.checkedTopicObj = this.topicList.filter(item => item.topic_id === this.form.topic_id)[0]
            this.dialogFormVisible = false
        },
        // 帖子操作
        postOperate(opt_type, post_id) {
            return postOperate({
                params: {
                    opt_type,
                    post_id
                },
                headers: this.headersData
            }).then((res) => {
                console.log(res)
                return res
            })
        },
        // 添加链接
        insertLink() {
            var selection = window.getSelection()
            let focusNode = selection && selection.focusNode
            let offsetParent = focusNode && focusNode.offsetParent
            let parentElement = focusNode && focusNode.parentElement
            let parentElementOffsetParent = parentElement && parentElement.offsetParent
            if (offsetParent && offsetParent.className === 'postWord' || focusNode && focusNode.className === 'postWord' || parentElement && parentElement.className === 'postWord' || parentElementOffsetParent && parentElementOffsetParent.className === 'postWord') {
                var range = selection.getRangeAt(0)
                this.range = range
                this.isIptFocus = true
            } else {
                this.isIptFocus = false
            }
            this.insertLinkObj.href = ''
            this.insertLinkObj.title = ''
            this.linkDom = null
            // 手动触发键盘事件以触发监听事件
            var keyboardEvent = new KeyboardEvent('keyup', {
                bubbles: true,
                cancelable: true,
            })
            document.getElementById('editable-div').dispatchEvent(keyboardEvent)
            this.visible = !this.visible
            return false
        },
        // 插入链接
        addLink() {
            if (this.wordLen >= 8000 || (this.wordLen + this.insertLinkObj.title.length) > 8000) {
                this.linkDom = null
                this.$message({
                    customClass: 'post--el-message',
                    message: `字数已超出限制`,
                    iconClass: 'el-icon-warning'
                })
                return
            }
            var link = document.createElement('a')
                link.className = 'postLink'
                link.href = this.insertLinkObj.href
                link.target = '_blank'
                link.innerHTML = '<img src="https://contentcenter-1252291750.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2024/3/cab327c9-b1b6-42f8-a120-f84247f7a7f0.png" />' + (this.insertLinkObj.title || '网页链接')
            if (this.isIptFocus) {
                if (this.linkDom) {
                    this.linkDom.href = this.insertLinkObj.href
                    this.linkDom.innerHTML =  '<img src="https://contentcenter-1252291750.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2024/3/cab327c9-b1b6-42f8-a120-f84247f7a7f0.png" />' + (this.insertLinkObj.title || '网页链接')
                } else {
                    let range = this.range
                    if (range.endContainer.className === 'postLink' || range.endContainer.parentNode.className === 'postLink') {
                        document.getElementById('editable-div').appendChild(link)
                    } else {
                        range.deleteContents()
                        range.insertNode(link)
                    }
                }
            } else {
                if (this.linkDom) {
                    this.linkDom.href = this.insertLinkObj.href
                    this.linkDom.innerHTML =  '<img src="https://contentcenter-1252291750.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2024/3/cab327c9-b1b6-42f8-a120-f84247f7a7f0.png" />' + (this.insertLinkObj.title || '网页链接')
                } else {
                    document.getElementById('editable-div').appendChild(link)
                }
            }
            this.insertLinkObj.href = ''
            this.insertLinkObj.title = ''
            this.linkDom = null
            let dom = document.getElementById('editable-div')
            this.textContent = dom.textContent
            this.form.post_word = dom.innerHTML
            this.wordLen = this.getCharCount(dom.textContent)['count']
            this.visible = false
        },
        // 监听文本输入
        inputUp(event) {
            var textContent = event.target.textContent
            this.post_text = textContent
            this.form.post_word = event.target.innerHTML
            this.wordLen = this.getCharCount(textContent)['count']
        },
        // 监听文本删除
        inputDown(event){
            if (event.key === 'Backspace') {
                var selection = window.getSelection()
                var range = selection.getRangeAt(0)
                var node = range.startContainer
                // 获取父节点
                var parentNode = node.parentNode
                // 如果光标在链接内部，则删除整个链接
                if (parentNode.tagName === 'A' && parentNode.className === 'postLink' && parentNode.href) {
                    range.selectNode(parentNode)
                    range.deleteContents()
                    event.preventDefault()
                }
            } else {
                let html = ''
                if (this.wordLen>=8000) {
                    this.wordLen = 8000
                    html = event.target.innerHTML
                    event.target.innerHTML = html
                    event.target.blur()
                    event.preventDefault()
                }
                // console.log('html', html)
            }
            
        },
        // 粘帖事件
        pasteData(event) {
            event.preventDefault()
            let text = (event.originalEvent || event).clipboardData.getData('text/plain')
            document.execCommand('inserttext',false,text)
        },
        // 点击链接
        linkCk(e) {
            console.dir(e)
            // console.log(document.getElementsByClassName('postLinkPopover')[0])
            if (e.target.nodeName === 'A' && e.target.className === 'postLink' && e.target.href) {
                // window.open(e.target.href, '_blank')
                this.insertLinkObj.href = e.target.href
                this.insertLinkObj.title = e.target.innerText
                this.linkDom = e.target
                this.visible = true
                console.log(e.target.clientWidth)
                this.linkLeft = e.x - e.target.clientWidth + 'px'
                this.linkTop = e.y + 32 + 'px'
            } else {
                this.visible = false
                this.insertLinkObj.href = ''
                this.insertLinkObj.title = ''
                this.linkDom = null
            }
        },
        hidePop() {
            this.insertLinkObj.href = ''
            this.insertLinkObj.title = ''
            this.linkDom = null
            this.visible = false
        },
        // 图片预览
        imgView(index) {
            this.imageIndex = index
            this.imgShow = true
            this.imgList = this.imageList.map((item) => item.file_url)
        },
        changeTab(val){
            this.changePage({path:'/user/training',name:'homePage',query:{active:val}})
        },
        // 拖拽改变顺序后回调
        changeOrder(){},
        // 添加话题
        addTopic() {
            // this.checkedTopic2 = this.checkedTopic
            this.dialogFormVisible = true
            this.topic_page_no = 1
            this.topicList = []
            this.topic_finished = false
            this.getTopicList()
        },
        // 话题下拉加载
        topicHandleScroll(event){
            const { scrollTop, clientHeight, scrollHeight } = event.target
            if (this.topic_finished) return
            if (Math.ceil(scrollTop) + clientHeight >= scrollHeight - 10 && !this.topic_loading) {
                this.topic_page_no += 1
                this.getTopicList()
            }
        },
        // 获取话题列表
        getTopicList() {
            this.topic_loading = true
            getTopicList({ params: { 'page_no': this.topic_page_no, 'page_size': 10 }, headers: this.headersData }).then((res) => {
                this.topic_loading = false
                if (res.records.length < 10) {
                    this.topic_finished = true
                }
                this.topicList = [...this.topicList, ...res.records]
            }).catch(() => {
                this.topic_loading = true
            })
        },
        // 发布帖子
        async sendPost() {
            if (this.isSubmit) {
                return
            }
            if (!this.readAndAgree && this.showContract) {
                this.$message({
                    customClass: 'post--el-message',
                    message: `请勾选《腾讯学堂学习平台文明公约》`,
                    iconClass: 'el-icon-warning'
                })
                return
            }
            if (!this.form.post_word.replace(/\s/g, '') || !this.post_text) {
                this.$message({
                    customClass: 'post--el-message',
                    message: `帖子的文本内容不能为空`,
                    iconClass: 'el-icon-warning'
                })
                return
            }
            if (this.isUpload) {
                this.$message({
                    customClass: 'post--el-message',
                    message: `图片正在上传中`,
                    iconClass: 'el-icon-warning'
                })
                return
            }
            this.isSubmit = true
            // this.form.post_word = this.form.post_word.replaceAll('\n', '<br/>')
            this.form.post_resource = this.imageList.map(item => item.file_url).toString()
            // let arr = []
            // this.topicList.forEach((item) => {
            //     if (this.checkedTopic.includes(item.topic_name)) arr.push(item.topic_id)
            // })
            // this.form.topic_id = arr
            if (this.pageInfo && (this.pageInfo.type === 'revoke')) {
                this.post_id = ''
            }
            let serviceApi = this.post_id ? editPost : savePost
            if (this.post_id) this.form.post_id = this.post_id
            serviceApi({ params: this.form, headers: this.headersData }).then(() =>{
                this.readAndAgree = false
                this.isSubmit = false
                if (this.form.submit_count === 2 && this.communityRole === 1) {
                    this.$message({
                        customClass: 'post--el-message',
                        message: `提交成功，待管理员审核`,
                        iconClass: 'el-icon-success'
                    })
                    this.changePage({path:'/user/my_post',name:'myPost',query:null})
                } else {
                    this.$message({
                        customClass: 'post--el-message',
                        message: `发帖成功`,
                        iconClass: 'el-icon-success'
                    })
                    this.changePage({path:'/user/training',name:'homePage',query:null})
                }
            }).catch((err) => {
                this.isSubmit = false
                if (err.code === -50004) {
                    if (this.communityRole === 1) {
                        this.$confirm('发帖中包含敏感内容，请仔细检查，完成修改后再次发布；如果选择“继续提交”，帖子将在管理员通过审核后发布。', '温馨提醒', {
                            confirmButtonText: '修改内容',
                            cancelButtonText: '继续提交',
                            customClass: 'postDeleteBox'
                        }).then(() => {

                        }).catch(() => {
                            this.form.submit_count = 2
                            this.sendPost()
                        })
                    } else {
                        this.$confirm('发帖中包含敏感内容，请仔细检查，建议修改后再次发布。', '温馨提醒', {
                            confirmButtonText: '修改内容',
                            cancelButtonText: '仍要发布',
                            customClass: 'postDeleteBox'
                        }).then(() => {

                        }).catch(() => {
                            this.form.submit_count = 2
                            this.sendPost()
                        })
                    }
                    // this.$alert('发帖中包含敏感内容，请仔细检查，完成修改后再次发布', '温馨提醒', {
                    //     confirmButtonText: '修改内容',
                    //     customClass: 'postSensitiveBox',
                    //     callback: action => {
                    //         console.log(action)
                    //     }
                    // })
                }
            })
        },
        // 上传图片
        upload(event) {
            let that = this
            this.isUpload = true
            let files = event.target.files
            event.target.type = 'text'
            if (files.length > 9 || (this.imageList.length + files.length) > 9) {
                this.$message({
                    customClass: 'post--el-message',
                    message: '最多可上传9张图片',
                    iconClass: 'el-icon-warning'
                })
                event.target.type = 'file'
                return
            }
            for(let i =0 ; i< files.length; i++) {
                let obj = {
                    status : 'loadding'
                }
                this.imageList.push(obj)
            }
            console.log('files', files)
            let operateAuthUrl = ''
            let uploadSignUrl = ''
            let saveContentUrl = ''
            console.log('env', this.env)
            if (location.hostname.endsWith('.woa.com') || location.hostname.endsWith('.oa.com')) {
                if (this.env === 'production') {
                    operateAuthUrl = 'https://portal.learn.woa.com/training-portal-common/api/v1/portal/user/common/uploadOperateSignature'
                    uploadSignUrl = 'https://ntsgw.woa.com/api/sso/content-center/api/v1/content/file/uploadSignatures'
                    saveContentUrl = 'https://ntsgw.woa.com/api/sso/content-center/api/v1/content/save_contentinfo'
                } else {
                    operateAuthUrl = 'https://test-portal-learn.woa.com/training-portal-common/api/v1/portal/user/common/uploadOperateSignature'
                    uploadSignUrl = 'https://test-contentcenter.woa.com/api/sso/content-center/api/v1/content/file/uploadSignatures'
                    saveContentUrl = 'https://test-contentcenter.woa.com/api/sso/content-center/api/v1/content/save_contentinfo'
                }
            } else {
                if (this.env === 'production') {
                    operateAuthUrl = 'https://prejob-tencent.ihr.tencent-cloud.com/prejob/api/v1/user/content/uploadOperateSignature'
                    uploadSignUrl = 'https://prejob-tencent.ihr.tencent-cloud.com/prejob/api/content-center/api/v1/content/file/uploadSignatures'
                    saveContentUrl = 'https://prejob-tencent.ihr.tencent-cloud.com/prejob/api/content-center/api/v1/content/save_contentinfo'
                } else {
                    operateAuthUrl = 'https://prejob-tencentprejob.test-caagw.yunassess.com/prejob/api/v1/user/content/uploadOperateSignature'
                    uploadSignUrl = 'https://prejob-tencentprejob.test-caagw.yunassess.com/prejob/api/content-center/api/v1/content/file/uploadSignatures'
                    saveContentUrl = 'https://prejob-tencentprejob.test-caagw.yunassess.com/prejob/api/content-center/api/v1/content/save_contentinfo'
                }
            }
            uploadCOS({
                file: files,
                type: 0, // 0表示上传图片，1视频 2音频 3文档
                appId: 'QLearningService',
                isPublic: true,
                operateAuthUrl,
                uploadSignUrl,
                saveContentUrl,
                staffId: 0,
                staff_id: 0,
                staffName: this.headersData['community-staffname'],
                staff_name: this.headersData['community-staffname'],
                onSuccess(res) {
                    console.log('onSuccess', res)
                    let newArr = [...res]
                    let len = 0
                    that.imageList.forEach((item) => {
                        if (item.status === 'success') {
                            len +=1
                        }
                    })
                    newArr.forEach((item, index) =>{
                        that.imageList[index+len] = {
                            ...item,
                        }
                        that.imageList[index+len].status = 'success'
                    })
                    that.imageList = [...that.imageList]
                    event.target.type = 'file'
                    that.isUpload = false
                },
                onError(err) {
                    console.log('onError', err)
                    that.isUpload = false
                    that.$message({
                        customClass: 'post--el-message',
                        message: err,
                        iconClass: 'el-icon-warning'
                    })
                    event.target.type = 'file'
                    that.imageList.splice(that.imageList.length - files.length,files.length)
                },
                onProgress(info) {
                    console.log('onProgress', info)
                }
            })
        },
        // 删除图片
        delImage(item, index) {
            this.imageList.splice(index, 1)
        },
        // 选择话题
        handleCheckedTopicChange(value) {
            console.log(value)
        },
        // 获取字符数
        getCharCount(str) {
            var chineseCount = 0
            var englishCount = 0
            for (var i = 0; i < str.length; i++) {
                var char = str[i]
                if (/[\u4e00-\u9fa5]/.test(char)) {
                    // 中文字符
                    chineseCount += 2
                } else {
                    // 非中文字符
                    englishCount += 1
                }
            }
            var totalCharacters = chineseCount + englishCount
            // 超过限制，截断文本
            var truncatedText = ''
            if (totalCharacters > 8000) {
                var currentCharacterCount = 0
                for (var j = 0; j < str.length; j++) {
                    var currentChar = str[j]
                    if (/[\u4e00-\u9fa5]/.test(currentChar)) {
                        // 中文字符
                        if (currentCharacterCount + 2 <= 8000) {
                            truncatedText += currentChar
                            currentCharacterCount += 2
                        } else {
                            break
                        }
                        } else {
                        // 非中文字符
                        if (currentCharacterCount + 1 <= 8000) {
                            truncatedText += currentChar
                            currentCharacterCount += 1
                        } else {
                            break
                        }
                    }
                }
                totalCharacters = 8000
            }
            return {
                count: totalCharacters,
                text: truncatedText
            }
        },
        changeWord() {
            var textarea = document.getElementById('inputTextarea')
            var inputText = textarea.value
            var chineseCount = 0
            var englishCount = 0
            for (var i = 0; i < inputText.length; i++) {
                var char = inputText[i]
                if (/[\u4e00-\u9fa5]/.test(char)) {
                    // 中文字符
                    chineseCount += 2
                } else {
                    // 非中文字符
                    englishCount += 1
                }
            }
            var totalCharacters = chineseCount + englishCount
            if (totalCharacters > 8000) {
                // 超过限制，截断文本
                var truncatedText = ''
                var currentCharacterCount = 0
                for (var j = 0; j < inputText.length; j++) {
                    var currentChar = inputText[j]
                    if (/[\u4e00-\u9fa5]/.test(currentChar)) {
                        // 中文字符
                        if (currentCharacterCount + 2 <= 8000) {
                            truncatedText += currentChar
                            currentCharacterCount += 2
                        } else {
                            break
                        }
                        } else {
                        // 非中文字符
                        if (currentCharacterCount + 1 <= 8000) {
                            truncatedText += currentChar
                            currentCharacterCount += 1
                        } else {
                            break
                        }
                    }
                }
                textarea.value = truncatedText
                totalCharacters = 8000
            }
            this.form.post_word = textarea.value
            this.wordLen = totalCharacters
        }
    }
}
</script>
<style lang="less">
@import './common/style.less';
#trainingPosting{
    font-family: "PingFang SC";
    min-width: 1200px;
    height: calc(100% - 40px);
    overflow: auto;
    background: #fff;
    margin: 20px auto;
    border-radius: 4px;
    .part1 {
        padding: 20px 24px;
        overflow: auto;
        h3{
            font-weight: 600;
            line-height: 28px;
            font-size: 20px;
            color: #000000e6;
        }
    }
    .postContent{
        border: 1px solid var(--Gray-Gray4-, #DCDCDC);
        height: 200px;
        border-radius: 3px;
        margin-top: 16px;
        position: relative;
        .place{
            position: absolute;
            left: 5px;
            top: 5px;
            color: #00000066;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
        }
        .postWord{
            color: #000000e6;
            height: 165px;
            width: 100%;
            border: none;
            outline: none;
            line-height: 24px;
            font-size: 16px;
            margin-bottom: 20px;
            padding: 5px;
            position: relative;
            z-index: 3;
            cursor: pointer;
            overflow: auto;
            .postLink{
                color: #0052D9;
                text-decoration: none;
                margin-right: 8px;
                cursor: pointer;
                // display: inline-flex;
                // align-items: center;
                img{
                    width: 16px !important;
                    margin-right: 4px;
                }
            }
        }
        .wordLimit{
            position: absolute;
            right: 8px;
            bottom: 10px;
            color: #00000066;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
        }
    }
    .insertData{
        margin-top: 12px;
        .imageData{
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            color: #00000099;
            cursor: pointer;
            img{
                width: 16px;
                margin-right: 8px;
            }
        }
        .linkData{
            margin-left: 24px;
            // color: #366EF4;
            color: #00000099;
            font-size: 14px;
            font-weight: 400;
            font-style: normal;
            line-height: 22px;
            position: relative;
            button.insertBtn{
                outline: none;
                border: none;
                background: none;
                height: auto;
                line-height: inherit;
                cursor: pointer;
                color: #00000099;
                span{
                    display: inline-block;
                    height: 16px;
                    width: 16px;
                    margin-right: 8px;
                    background: url('../assets/img/link2.png');
                    background-size: 100% 100%;
                }
                &:hover{
                    color: #366EF4;
                    span{
                        background: url('../assets/img/link.png');
                        background-size: 100% 100%;
                    }
                }
            }
            .postLinkPopover{
                width: 380px;
                background: #fff;
                position: absolute;
                left: 0;
                top: 32px;
                font-family: "PingFang SC";
                padding: 16px;
                box-shadow: 0 3px 14px 2px #0000000d, 0 8px 10px 1px #0000000f, 0 5px 5px -3px #0000001a;
                border-radius: 6px;
                z-index: 10;
                &::after{
                    content: '';
                    position: absolute;
                    left:10px ;
                    top: -6px;
                    width: 8.69px;
                    height: 8.69px;
                    transform: rotate(-45deg);
                    position: absolute;
                    left: 41px;
                    bottom: -6.29px;
                    border-top: 0.5px solid var(--Gray-Gray4-, #DCDCDC);
                    border-right: 0.5px solid var(--Gray-Gray4-, #DCDCDC);
                    background: var(--Gray-White, #FFF);
                }
                .el-form-item{
                    margin-bottom: 16px;
                }
                .el-form-item__label{
                    padding: 0;
                    color: #000000e6;
                    font-size: 14px;
                    font-weight: 400;
                    // line-height: 22px;
                }
                .el-input__inner{
                    border-radius: 3px;
                }
                .linkTitleText{
                    .el-input__inner{
                        padding-right: 50px;
                    }
                    .el-input__suffix{
                        right: 8px;
                    }
                }
                .iptDesc{
                    color: #00000066;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                }
                footer{
                    text-align: right;
                    margin: 0;
                    button{
                        width: 40px;
                        height: 24px;
                        border-radius: 3px;
                        padding: 2px 8px;
                        font-size: 12px;
                        font-weight: 400;
                        font-style: normal;
                        color: #000000e6;
                        background: var(--Gray-Gray3-, #E7E7E7);
                        border: none;
                    }
                    button.el-button--primary{
                        color: #fff;
                        background: var(--Brand-Brand7-Normal, #0052D9);
                    }
                    button.el-button--primary.is-disabled{
                        background: var(--Brand-Brand3-Disabled, #B5C7FF);
                    }
                }
            }
        }
    }
    .imageBlock{
        margin-top: 12px;
        min-height: 100px;
        .imageItem{
            margin-right: 12px;
            margin-bottom: 8px;
            width: 120px;
            height: 120px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            background: var(--Gray-Gray2, #EEE);
            div.imgPad{
                width: 104px;
                height: 104px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .close{
                position: absolute;
                right: 0;
                top: 0;
                width: 27px;
                height: 27px;
                background: var(--text-icon-font-gy-340, #00000066);
                border-bottom-left-radius: 8px;
                color: #fff;
                text-align: center;
                line-height: 26px;
                cursor: pointer;
            }
            .Mask {
                position: absolute;
                top: 0;
                left: 0;
                z-index: 12;
                width: 100%;
                height: 100%;
                background: var(--text-icon-font-gy-260, #00000099);
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .loading{
                    img{
                        width: 26px;
                        height: 26px;
                    }
                }
                .progress {
                    margin-top: 8px;
                    color: #ffffff;
                    font-family: "PingFang SC";
                    font-size: 16.2px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 27px;
                }
            }
            .hoverDiv{
                display: none;
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                background: rgba(0,0,0, .6);
                color: #FFFFFF;
                img{
                    width: 16px;
                    margin: 0 16px;
                    cursor: pointer;
                }
            }
            &:hover{
                .hoverDiv{
                    display: flex;
                }
                .close{
                    display: none;
                }
            }
        }
        .addImage{
            margin-right: 12px;
            width: 120px;
            height: 120px;
            position: relative;
            background: var(--Gray-Gray2, #EEE);
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            img{
                width: 24px;
                margin-top: 33px;
                margin-bottom: 10px;
            }
            p{
                color: #00000066;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
            }
        }
        .imgDesc{
            margin-top: 10px;
            color: #00000099;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
        }
    }
    .partTackTopic{
        margin-top: 16px;
        .titleName{
            display: inline-block;
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
        }
        .lightKey{
            margin-left: 12px;
            font-size: 14px;
            font-weight: 400;
            color: #0052d9;
            cursor: pointer;
        }
        .topicCheckedList{
            margin-top: 12px;
            li{
                padding: 2px 8px;
                margin-right: 12px;
                margin-bottom: 8px;
                background: var(--Brand-Brand1-Light, #F2F3FF);
                border-radius: 3px;
                line-height: 20px;
                font-weight: 400;
                font-size: 12px;
                color: #0052d9;
                i{
                    margin-left: 8px;
                    cursor: pointer;
                }
            }
        }
    }
    .postType{
        margin-top: 18px;
        .titleName{
            display: inline-block;
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
            margin-bottom: 12px;
        }
        .el-radio__label{
            font-size: 14px;
            line-height: 22px;
            font-weight: 400;
        }
        .el-radio__input.is-checked+.el-radio__label{
            color: #000000e6;
        }
    }
    .footer{
        border-top: 1px solid #EEEEEE;
        padding: 14px 20px;
        button{
            width: 100px;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            height: 32px;
        }
        button.el-button--default{
            color: #000000e6;
        }
        .el-button+.el-button{
            margin-left: 20px;
        }
    }
}
#topicDialog{
    .el-dialog{
        border-radius: 6px;
    }
    .el-dialog__header{
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333333;
        font-family: "PingFang SC";
        font-size: 16px;
        font-weight: 600;
        padding: 20px 24px 20px 32px;
        border-bottom: 1px solid #F2F2F2;
        .el-dialog__title{
            font-size: 16px;
            color: #333333;
        }
        .el-dialog__headerbtn{
            right: 24px;
            top: 21px;
        }
        i{
            font-size: 19px;
            font-weight: 600;
        }
    }
    .el-dialog__body{
        padding: 16px 32px;
    }
    .topicList{
        height: 356px;
        overflow: auto;
        .el-radio{
            display: block;
            margin-bottom: 16px;
        }
        .title{
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
        }
        .topicDesc{
            margin-top: 4px;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            color: #00000099;
            font-family: "PingFang SC";
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: break-spaces;
        }
        // .el-checkbox{
        //     display: flex;
        //     position: relative;
        //     border-bottom: 0.5px solid var(--Gray-Gray3, #E7E7E7);
        //     padding: 16px 0;
        //     margin-left: 30px;
        //     margin-right: 0;
        //     .el-checkbox__input{
        //         position: relative;
        //         left: -30px;
        //         margin-top: 5px;
        //     }
        //     .el-checkbox__label{
        //         padding-left: 0;
        //         margin-left: -12px;
        //     }
        //     .el-checkbox__input.is-checked+.el-checkbox__label{
        //         color: #333 !important;
        //     }
            
        //     .el-checkbox__input.is-checked .el-checkbox__inner{
        //         background-color: #0052D9;
        //     }
        //     .el-checkbox__inner{
        //         width: 16px;
        //         height: 16px;
        //         border-radius: 3px;
        //     }
        //     .el-checkbox__inner::after{
        //         border-color: #fff;
        //     }
        // }
    }
    .el-dialog__footer{
        padding: 24px 32px;
        .dialog-footer{
            button{
                width: 100px;
                font-size: 14px;
                font-weight: 400;
                height: 32px;
            }
            .el-button+.el-button{
                margin-left: 20px;
            }
            
        }
    }
}


</style>
