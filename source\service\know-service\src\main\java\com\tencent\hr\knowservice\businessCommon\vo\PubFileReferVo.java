package com.tencent.hr.knowservice.businessCommon.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 引用详情
 * <AUTHOR>
 */
@Data
public class PubFileReferVo {
    /**
     * 课程id
     */
    private String courseId;
    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程状态
     */
    private String courseStatus;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 课程状态名称
     */
    private String courseStatusName;

    /**
     * 引用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createdAt;

    /**
     * 引用人
     */
    private String creatorName;
}
