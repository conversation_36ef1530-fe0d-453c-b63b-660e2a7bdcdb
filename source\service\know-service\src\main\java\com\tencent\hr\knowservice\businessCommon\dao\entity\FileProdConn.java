package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * file_prod_conn
 *
 *
 *
 */
@Data
public class FileProdConn implements Serializable {
    /**
     * 产品关联Id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**
     * 产品类型(11 图文)
     */
    private Integer prodType;

    /**
     * 产品id
     */
    private Integer prodId;

    /**
     * 关联产品类型
     *
     */
    private Integer connProdType;
    /**
     * 关联产品类型名称
     */
    private String connProdTypeName;
    /**
     * 关联产品ModuleId
     */
    private Integer connProdModuleId;
    /**
     * 关联产品ModuleName
     */
    private String connProdModuleName;


    /**
     * 关联产品id
     *
     *
     */
    private Integer connProdId;
    /**
     * 关联产品单据id
     *
     *
     */
    private String connProdItemId;
    /**
     * 观看数量
     */
    private Integer viewCount;
    /**
     * 评分
     */
    private Double avgScore;
    /**
     * 关联产品创建时间
     */
    private Date connProdCreatedTime;
    /**
     * 排序号
     */
    private Integer orderNo;

    /**
     * 使用场景： 默认为空：是目前的使用场景-延伸学习； 1 - 课前延伸学习 （后续需要添加，请写注释）
     */
    private Integer usageScenario;
    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    /**
     * 1：加密，0：未加密
     */
    private Integer encrypted;

    /**
     * 1：置顶 0：非置顶
     */
    private Integer toped;

    /**
     * 1：站内内容 2:自定义内容
     */
    private Integer contentType;

    /**
     * 置顶时间
     */
    private Date topedTime;

    /**
     * 产品名称
     */
    private String connProdName;

    /**
     * 跳转链接
     */
    private String connProdUrl;

    /**
     * 封面图id
     */
    private String connProdCoverId;

    /**
     * 可参与人群
     */
    private String targetPeople;


    private static final long serialVersionUID = 1L;

    public void SetCreatAndUpdate(Integer staffId, String staffName){
        setCreatorId(staffId);
        setCreatorName(staffName);
        setCreatedAt(new Date());
        setUpdateId(staffId);
        setUpdateName(staffName);
        setUpdatedAt(new Date());
        setEnabled(true);
    }

    public void SetCreat(Integer staffId, String staffName){
        setCreatorId(staffId);
        setCreatorName(staffName);
        setCreatedAt(new Date());
        setEnabled(true);
    }

    public void SetUpdate(Integer staffId, String staffName){
        setUpdateId(staffId);
        setUpdateName(staffName);
        setUpdatedAt(new Date());
    }
}
