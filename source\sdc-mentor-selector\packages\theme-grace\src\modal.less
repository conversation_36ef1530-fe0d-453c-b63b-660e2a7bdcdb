@import "./vars.less";
@import "./svg-icon.less";

.sdc-modal {
  position: fixed;
  top: 15%;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  transition: all .5s;
  &.modal-scroll {
    top: 20%;
    .modal-dialog,
    &.slide-enter-active {
      top: 20%;
    }
  }
  &.slide-enter-active {
    top: 15%;
  }
  &.slide-enter,
  &.slide-leave-active {
    top: -100%;
  }
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: @color-black;
    z-index: 9999;
    filter: alpha(Opacity=75);
    -moz-opacity: .75;
    opacity: .75;
  }
  .modal-dialog {
    position: absolute;
    top: 15%;
    left: 50%;
    height: auto;
    border-radius: 6px;
    overflow-y: hidden;
    z-index: 10000;
    transform: translate(-50%, -40%);
    background-color: @color-bg-white;
    .modal-header,
    .modal-footer {
      padding: 0 20px;
      background-color: @color-bg-white;
      //box-shadow: 0 0 10px rgba(0,0,0,.1);
    }
    .modal-header {
      display: block;
      position: relative;
      height: 50px;
      line-height: 50px;
      font-size: @font-14;
      border-bottom: 1px solid @color-bd-light;
      .title {
        color: @color-text-dark;
        font-size: @font-16;
        font-weight: bold;
      }
      .close {
        position: absolute;
        display: inline-block;
        top: 50%;
        transform: translateY(-50%);
        right: 20px;
        cursor: pointer;
      }
    }
    .modal-body {
      overflow-y: auto;
      padding: 10px 20px;
      color: @color-text-black;
    }
    .modal-footer {
      height: 60px;
      line-height: 50px;
      text-align: right;
      margin-top: -10px;
      .btn-group {
        button {
          min-width: 80px;
          padding: 10px 15px;
          font-size: 12px;
          border-radius: 3px;
          margin-right: 5px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}
