package com.tencent.hr.knowservice.businessCommon.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * pub_ql_dict_item
 * <AUTHOR>
@Data
public class PubQlDictItem implements Serializable {
    /**
     * 字典项Id
     */
    private Integer dictItemId;

    private String dictItemKey;

    /**
     * 字典项名
     */
    private String dictItemName;

    /**
     * 字典项值
     */
    private String dictItemValue;

    /**
     * 序号
     */
    private Byte orderNo;

    /**
     * 备注
     */
    private String memo;

    /**
     * 父级字典项的Id
     */
    private Integer pid;

    /**
     * 节点全路径
     */
    private String fullPath;

    /**
     * 是否系统字段能否修改
     */
    private Boolean isSystemDict;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}
