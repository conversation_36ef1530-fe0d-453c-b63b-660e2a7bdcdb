## 国际化
> 贡献者：miraclehe(何名宇)；最近更新时间：2020-10-13；

SDC WebUI 组件内部默认使用中文，若希望使用其他语言，则需要进行多语言设置。以中文为例，在 main.js 中：

### 配置国际化目录locales
```
└─locales 所有国际化配置(位于src目录下)
   ├─lang          所有语言配置
   │  ├─en.js      英文配置
   │  ├─zh.js      中文配置
   │  └─index.js   所有语言出口配置
   └─i18n.js       i18n出口配置
```
然后将i18n挂接到Vue实例上即可(main.js)
```js
import i18n from './locales/i18n'

new Vue({
  ...,
  i18n,
  el: '#app',
  render: (h) => h(App)
})
```
如果使用sdc-cli脚手架构建，挂接到render方法即可(main.js)
```
import { render } from '@tencent/sdc-vue'
import i18n from './locales/i18n'

export default render({
 i18n,
 ...
})
```
到此为止，所有的国际化配置已准备完毕！
- 以下是国际化配置的示例代码:
```js
// en.js
import { mergeLangs } from '@tencent/sdc-vue'
import { ele, sdc } from 'plugins'

export default mergeLangs({
  message: {
    hello: 'Hello, SDC WebUI'
  },
  // more config
}, ele.langs.en, sdc.langs.en)
```
```js
// zh.js
import { mergeLangs } from '@tencent/sdc-vue'
import { ele, sdc } from 'plugins'

export default mergeLangs({
  default: true, // 配置默认语言
  message: {
    hello: '你好, SDC WebUI'
  },
  // 更多配置
}, ele.langs.zh, sdc.langs.zh)
```
```js
// index.js
import zh from './zh'
import en from './en'

export default { zh, en }
```
```js
// i18n.js
import { getI18nLocale } from '@tencent/sdc-vue'
import { ele, sdc } from 'plugins'
import lang from './lang'

export default getI18nLocale(lang, ele.locale, sdc.locale)
```
- 如何在vue模板和js代码中使用国际化
```
<template>
  <p v-text="$t('message.hello')"></p>
</template>
```
```js
const hello = this.$t('message.hello')
console.log(hello)
```
