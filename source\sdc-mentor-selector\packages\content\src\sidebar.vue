<template>
  <div class="sdc-sidebar" :class="{'sidebar-collapse':collapse}">
    <div class="sidebar-wrapper">
      <el-menu :default-active="current" :collapse="collapse"
        :default-openeds="menus.defaultOpeneds || openeds" :collapse-transition="false" :unique-opened="menus.uniqueOpened" :style="contentStyle">
        <sdc-sidebar-menu :data="menuList" :collapse="collapse" @menuContextmenu="handleMenuContextmenu"/>
      </el-menu>
      <div class="toggle-sidebar" :class="{'toggle-collapse':collapse}" ref="toggle-sidebar">
        <i class="toggle-icon" :class="toggleClass" @click="toggleCollapse"></i>
      </div>
    </div>
  </div>
</template>

<script>
  import { locale, calc, sidebar } from 'mixins'
  import SdcSidebarMenu from './menu'

  export default {
    name: 'sdc-sidebar',
    mixins: [locale, calc, sidebar],
    inject: {
      openeds: {
        default: []
      }
    },
    props: {
      scope: {
        type: String,
        default: 'oa'
      },
      menus: {
        type: Object,
        default() {
          return {}
        }
      },
      sidebarCollapse: {
        type: Boolean,
        default: false
      },
      current: [Number, String]
    },
    data() {
      return {
        collapse: this.sidebarCollapse
      }
    },
    computed: {
      toggleClass() {
        return this.collapse ? 'unfold' : 'fold'
      }
    },
    mounted() {
      this.$bus.$on('changeCollapse', (collapse) => {
        this.toggleCollapse(collapse)
      })
    },
    methods: {
      calculateHeight(clientHeight) {
         return clientHeight - 60 - this.$refs['toggle-sidebar'].offsetHeight
      },
      toggleCollapse() {
        this.collapse = !this.collapse
        this.$emit('toggle', this.collapse)
      },
      handleMenuContextmenu(event, menu) {
        this.$emit('menuContextmenu', event, menu)
      }
    },
    components: {
      SdcSidebarMenu
    }
  }
</script>
