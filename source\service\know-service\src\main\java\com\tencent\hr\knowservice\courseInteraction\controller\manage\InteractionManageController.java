package com.tencent.hr.knowservice.courseInteraction.controller.manage;

import com.alibaba.excel.EasyExcel;
import com.tencent.hr.base.dto.TransDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveInDto;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveInfoDto;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractivePageDto;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveRecordInfoDto;
import com.tencent.hr.knowservice.courseInteraction.service.InteractionService;
import com.tencent.hr.knowservice.courseInteraction.service.InteractiveRecordService;
import com.tencent.hr.knowservice.courseInteraction.vo.InteractiveRecordExcelVo;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/10/10
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/courseInteraction/manage/interaction")
public class InteractionManageController {
    @Autowired
    InteractionService interactionService;

    @Autowired
    InteractiveRecordService recordService;

    /**
     * 保存互动配置
     * @param interactiveInDTO
     * @return
     */
    @PostMapping("/add-course-interaction")
    public TransDTO addInteractive(@RequestBody InteractiveInDto interactiveInDTO){
        String id = interactionService.saveInteractive(interactiveInDTO);
        return new TransDTO().withData(id).withCode(HttpStatus.SC_OK).withSuccess(true);
    }

    /**
     * 修改互动配置
     * @param interactiveInDTO
     * @return
     */
    @PostMapping("/update-course-interaction")
    public TransDTO updateInteractive(@RequestBody InteractiveInDto interactiveInDTO){
        interactionService.updateInteractive(interactiveInDTO);
        return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true);
    }

    /**
     * 获取互动配置详情
     * @param interactiveId
     * @return
     */
    @GetMapping("/{id}/get-course-interaction")
    public TransDTO<InteractiveInfoDto> getInteractiveInfo(@PathVariable("id") String id,
                                                               @RequestParam("interactive_id") String interactiveId){
        InteractiveInfoDto infoDto = interactionService.getInteractiveInfo(id,interactiveId);
        return new TransDTO<InteractiveInfoDto>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(infoDto);
    }

    /**
     * 互动配置-删除
     * @param id
     * @param interactiveId
     * @return
     */
        @DeleteMapping("/{id}/delete-course-interaction")
    public TransDTO deleteInteractive(@PathVariable("id") String id,
                                      @RequestParam("interactive_id") String interactiveId){
        interactionService.deleteInteractive(id,interactiveId);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK);
    }

    /**
     * 互动配置分页
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/get-interactive-page")
    public TransDTO<IPage<InteractivePageDto>> getInteractivePage(@RequestParam("act_type") Integer actType,
                                                                  @RequestParam("course_id") String courseId,
                                                                  @RequestParam("current") Integer current,
                                                                  @RequestParam("size") Integer size){
        IPage<InteractivePageDto> page = interactionService.getInteractivePage(actType,courseId,current,size);
        return new TransDTO<IPage<InteractivePageDto>>().withCode(HttpStatus.SC_OK).withSuccess(true).withData(page);
    }

    /**
     *  互动记录分页查询
     * @param current 当前页
     * @param size 每页大小
     * @return
     */
    @GetMapping("get-interactive-record")
    public TransDTO getInteractiveRecord(@RequestParam("current") Integer current,
                                         @RequestParam("size")Integer size,
                                         @RequestParam("course_id") String courseId,
                                         @RequestParam("act_type") Integer actType){
        return recordService.getInteractiveRecordPage(current, size,actType,courseId);
    }

    /**
     * 获取互动记录详情
     * @param id
     * @return
     */
    @GetMapping("get-interactive-record-info/{id}")
    public TransDTO getInteractiveRecords(@PathVariable("id") String id){
        return recordService.getInteractiveRecords(id);
    }

    /**
     * 互动记录全量导出
     * @param response
     * @return
     * @throws UnsupportedEncodingException
     */
    @PostMapping("export-interactive-record")
    public void exportInteractiveRecord(@RequestParam("current") Integer current,
                                        @RequestParam("size")Integer size,
                                        @RequestParam("course_id") String courseId,
                                        @RequestParam("act_type") Integer actType,
                                        HttpServletResponse response) throws IOException {
        String fileName = URLEncoder.encode("互动记录", "UTF-8");
        // 导出
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<InteractiveRecordExcelVo> list = recordService.getInteractiveRecordList(current,size,actType,courseId);
        if (list == null){
            list = new ArrayList<>();
        }
        // 使用 try-with-resources 语句确保 ServletOutputStream 在写入完成后自动关闭资源
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, InteractiveRecordExcelVo.class)
                    .sheet("互动记录")
                    .doWrite(list);
        }
    }

    /**
     * 获取互动记录详情
     * @param id
     * @return
     */
    @GetMapping("/get-interactive-record-info")
    public TransDTO getInteractiveRecord(@RequestParam("id")Integer id) throws IOException {
        InteractiveRecordInfoDto interactiveRecord = recordService.getInteractiveRecord(id);
        return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true).withData(interactiveRecord);
    }

    /**
     * 更新互动配置开启状态
     * @param status 0-关闭 1-开启
     * @return
     */
    @PutMapping("/update-interactive-status")
    public TransDTO updateInteractiveStatus(@RequestParam("act_type") Integer actType,
                                            @RequestParam("course_id") String courseId,
                                            @RequestParam("status") boolean status){
        interactionService.updateInteractiveStatus(actType,courseId,status);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK);
    }

    /**
     * 更新互动配置开启状态
     * @return
     */
    @PutMapping("/update-progress-bar")
    public TransDTO updateProgressBar(@RequestParam("act_type") Integer actType,
                                      @RequestParam("course_id") String courseId,
                                      @RequestParam("status") boolean status){
        interactionService.updateProgressBar(actType,courseId,status);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK);
    }
}
