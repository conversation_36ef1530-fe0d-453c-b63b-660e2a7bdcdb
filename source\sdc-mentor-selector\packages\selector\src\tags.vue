<template>
  <transition-group mode="out-in" @after-leave="$emit('change')">
    <el-tooltip v-for="item in getSelectedItems(getSelected())" :key="item._key" :content="item._text" placement="top" :disabled="item._text.length <= item._tags.minLength && !showLastLevels">
      <el-tag :size="size || 'medium'" :closable="!getDisabled()" type="info" @close="handleDeleteTag(item)" class="tag">
        <span>{{showFullTag ? (showLastLevels ? item._lastText : item._text) : textEllipsis(showLastLevels ? item._lastText : item._text, item._tags) }}</span>
      </el-tag>
    </el-tooltip>
  </transition-group>
</template>

<script>
  import { textEllipsis } from 'sdc-webui/src/utils/main'
  import { map } from 'mixins'

  export default {
    name: 'selector-tags',
    inject: ['size', 'getDisabled', 'change', 'nodeKey', 'getSelected', 'getCurrentItem', 'showLastLevels', 'showFullTag'],
    mixins: [map],
    methods: {
      textEllipsis,
      handleDeleteTag(tag) {
        const items = this.getSelected()
        const index = items.findIndex(item => item[this.nodeKey] === tag[this.nodeKey])
        if (index > -1) {
          const selected = items.slice()
          selected.splice(index, 1)
          this.change(selected)
        }
      }
    }
  }
</script>
