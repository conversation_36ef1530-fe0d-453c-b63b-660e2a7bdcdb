<template>
  <div :class="['sdc-header', `sdc-header-${this.scope}`]">
    <div class="header-inner" v-if="!headerLayout || !headerLayout.length">
      <slot/>
    </div>
    <div class="header-inner" v-else>
      <div class="header-left" :class="{'has-nav':has('nav')}">
        <div class="nav" v-if="has('nav')">
          <sdc-nav-menu :scope="scope" :menuData="navData">
            <div class="menu-icon"></div>
            <span>{{$st('sdc.layout.navMenu')}}</span>
          </sdc-nav-menu>
        </div>
        <div class="logo" v-if="has('logo')">
          <slot name="logo" >
            <sdc-link :to="this.scope == 'oa' ? 'http://hr.oa.com' : 'https://assistant-wxvendor.ihr.tencent-cloud.com/'" :custom-class="`logo-icon logo-${this.scope}`"/>
          </slot>
        </div>
      </div>
      <div class="header-center">
        <div class="menu-list">
          <slot name="menus" v-if="has('menus')">
            <sdc-navbar :menus="menus" v-if="showMenuList"/>
          </slot>
        </div>
      </div>
      <div class="header-right">
        <div class="header-right-inner">
          <sdc-search v-if="has('search')"/>
          <sdc-feedback v-if="has('feedback')"/>
          <slot name="links" v-if="has('links')">
            <sdc-link :text="$st('sdc.layout.links.exit')"/>
          </slot>
          <slot name="icons" v-if="has('icons')">
            <sdc-icons/>
          </slot>
          <slot name="avatar" v-if="has('avatar')">
            <sdc-avatar :avatar="avatar"/>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { layout, locale } from 'mixins'
  import SdcLink from 'packages/link'
  import SdcNavMenu from './navmenu'
  import SdcNavbar from './navbar'
  import SdcSearch from './search'
  import SdcFeedback from './feedback'
  import SdcIcons from './icons'
  import SdcAvatar from 'packages/avatar'

  export default {
    name: 'sdc-header',
    mixins: [layout, locale],
    props: {
      scope: {
        type: String,
        default: 'oa'
      },
      menus: {
        type: Object,
        default: () => ({ active: '', map: {}, data: [], maxMenuCount: 6, adaptive: false })
      },
      avatar: {
        type: Object,
        default() {
          return {}
        }
      },
      navData: {
        type: Object,
        default() {
          return null
        }
      }
    },
    computed: {
      showMenuList() {
        return this.menus.data && this.menus.data.length > 0
      },
      headerLayout() {
        if (this.layout && this.layout.length > 0) {
          return this.layout
        }
        let defaultLayout = []
        if (this.scope === 'oa') {
          defaultLayout = ['logo', 'search', 'feedback', 'links']
        } else if (this.scope === 'oc') {
          defaultLayout = ['logo', 'menus', 'icons', 'avatar']
        }
        return defaultLayout
      }
    },
    created() {
      this.$bus.$on('search', keyword => this.$emit('search', keyword))
      this.$bus.$on('avatar', type => this.$emit('avatar', type))
    },
    methods: {
      has (key) {
        return this.headerLayout && this.headerLayout.some(item => item === key)
      }
    },
    components: {
      SdcLink,
      SdcNavMenu,
      SdcNavbar,
      SdcSearch,
      SdcFeedback,
      SdcIcons,
      SdcAvatar
    }
  }
</script>
