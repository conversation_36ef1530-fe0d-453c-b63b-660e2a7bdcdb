package com.tencent.hr.knowservice.businessCommon.dto.classRoom;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/22/14:56
 * @version: 1.0
 */
@Data
public class ActClassDTO {
    /**
     * 班级Id
     */
    private Integer classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 所属课程Id
     */
    private Integer courseId;

    /**
     * 班级所属系统
     */
    private String systemName;

    /**
     * 开班城市
     */
    private String city;

    /**
     * 班级名称日期
     */
    private Date classDate;

    /**
     * 班级级别
     */
    private Integer classLevel;

    /**
     * 是否开放给目标学员自由报名
     */
    private Boolean canRegisted;

    /**
     * 是否报名需要直接上司审批
     */
    private Boolean needAppovel;

    /**
     * 课时(课程已包含)
     */
    private Long estDur;

    /**
     * 班主任
     */
    private Integer headTeacher;

    /**
     * 班主任姓名
     */
    private String headTeacherName;

    /**
     * 内部讲师姓名（冗余）
     */
    private String innerTeacherNames;

    /**
     * 外部讲师姓名（冗余）
     */
    private String outerTeacherNames;

    /**
     * 目标学员
     */
    private String targetIds;

    /**
     * 内容类型
     */
    private Integer pdiLevel;

    /**
     * 内容类型名称
     */
    private String pdiLevelName;

    /**
     * 内容类型(课程已包含)
     */
    private Integer pdiSubLevel;

    /**
     * 内容类型名称(课程已包含)
     */
    private String pdiSubLevelName;

    /**
     * 最多开班人数
     */
    private Integer maxStudentCount;

    /**
     * 预计开始时间
     */
    private Date startTime;

    /**
     * 预计结束时间
     */
    private Date endTime;

    /**
     * 报名截止时间
     */
    private Date registLastDate;

    /**
     * 注销截止时间
     */
    private Date cancelLastDate;

    /**
     * 所属组织单元
     */
    private Integer deptId;

    /**
     * 培训地点
     */
    private String location;

    /**
     * 是否外购班(0 否 1 是)
     */
    private Boolean isOutsourcing;

    /**
     * 是否系列班
     */
    private Boolean isSeriesClass;

    /**
     * 补充说明
     */
    private String remark;

    /**
     * 附加信息
     */
    private String ext;

    /**
     * 总反馈分
     */
    private BigDecimal totalScore;

    /**
     * 组织反馈分
     */
    private BigDecimal organizeScore;

    /**
     * 讲师反馈分
     */
    private BigDecimal lecturerScore;

    /**
     * 课程反馈分
     */
    private BigDecimal courseScore;

    /**
     * 反馈分是否自动计算
     */
    private Byte isScoreAutoCalculate;

    /**
     * 反馈分修改人Id
     */
    private Integer scoreUpdatedStaffId;

    /**
     * 反馈分修改人姓名
     */
    private String scoreUpdatedStaffName;

    /**
     * 反馈分修改时间
     */
    private Date scoreUpdateTime;

    /**
     * 是否取消（ 0：正常 1： 取消）
     */
    private Boolean cancel;

    /**
     * 原计划开始时间
     */
    private Date oldStartTime;

    /**
     * 原计划结束时间
     */
    private Date oldEndTime;

    /**
     * 老系统id
     */
    private Integer oldSyncId;

    /**
     * 签到次数
     */
    private Integer signCount;

    /**
     * 授课形式 1 线下面试 2 在线授课 3 网络研讨会
     */
    private Byte teachingType;

    /**
     * 腾讯会议id
     */
    private Integer meetinginfoId;

    /**
     * 主持人类型，多选用分号分隔 1 班主任 2 讲师
     */
    private String hostType;

    /**
     * 拒绝加入会议的级别 1 拒绝外部人员加入 2 拒绝非目标学员加入 3 拒绝未报名的学员加入
     */
    private Byte refuseJoinType;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;


    /**
     * 会议id
     */
    private String meetingId;

    /**
     * 会议主题
     */
    private String subject;

    /**
     * 会议号
     */
    private String meetingCode;

    /**
     * 会议地址
     */
    private String meetingUrl;
}
