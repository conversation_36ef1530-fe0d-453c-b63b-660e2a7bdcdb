package com.tencent.hr.knowservice.businessCommon.proxy;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.exam.ExamSearchDto;
import com.tencent.hr.knowservice.graphic.dto.ContentSearchDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name="exam-evaluation",url="${project.exam-evaluation-host}")
public interface ExamServiceApi {

    /**
     * 获取考试列表
     *
     * @param searchDto
     * @return
     */
    @PostMapping("/api/v1/extapi/exam/exam/list")
    public TransDTO getExamList(@RequestBody ExamSearchDto searchDto);


    /**
     * 获取考试信息
     * @param examId
     * @return
     */
    @PostMapping("/api/v1/extapi/exam/{examId}")
    public TransDTO getExamInfo(@PathVariable("examId") String examId);

    /**
     * 获取练习列表
     *
     * @param searchDto
     * @return
     */
    @PostMapping("/api/v1/extapi/practice/list")
    public TransDTO getPracticeList(@RequestBody ExamSearchDto searchDto);

    /**
     * 获取考试结果
     *
     * @param examId     考试主键id
     * @param staffNames 考生姓名
     * @param startTime  考生考试时间前置区间
     * @param endTime    考生考试前置区间
     * @return
     */
    @PostMapping("/api/v1/extapi/testing/exam_results_list")
    public TransDTO getExamResultList(@RequestParam("exam_id") String examId,
                                      @RequestParam("staff_names") String staffNames,
                                      @RequestParam("start_time") String startTime,
                                      @RequestParam("end_time") String endTime);
}
