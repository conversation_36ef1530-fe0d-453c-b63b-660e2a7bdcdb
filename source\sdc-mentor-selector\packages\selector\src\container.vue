<template>
  <div class="selector-container selector-container-tutor-costomer"
       :class="[textarea ? 'selector-container--textarea' : 'selector-container--normal',
                size && !textarea ? 'selector-container--' + size : '',
                focus ? 'selector-container--focus' : '',
                getDisabled() ? 'is-disabled' : '']"
       v-loading="pasteLoading" element-loading-spinner="el-icon-loading">
       <!-- @click="$refs.input.focus()" -->
       <div class="container-inner">
      <slot></slot>
      <el-autocomplete
        v-model="queryText" ref="input"
        :disabled="getDisabled()"
        :trigger-on-focus="false"
        :fetch-suggestions="getSuggetions"
        :debounce="500"
        :value-key="valueKey"
        :readonly="readonly"
        :size="inputSize"
        :placeholder="textPlaceholder"
        :popper-class="`sdc-${mode}-selector-popper`"
        @focus="handlefocus"
        @blur="handleblur"
        @select="handleSelect"
        @paste.native="handlePaste"
        @keydown.delete.native="handleDelete"
        @keyup.enter.native="handleEnter"
      >
        <template slot-scope="{ item }">
          <p class="dropdown--empty" v-if="item.empty">{{item.message}}</p>
          <slot v-else name="item" :data="{option:item,keyword:queryText}"/>
        </template>
        <i slot="suffix" v-if="suffixIcon" :class="suffixIcon" @click.stop="handleClear"></i>
      </el-autocomplete>
    </div>
    <div class="empty-trip-customer" v-if="focus && queryText && searchEmptyText && requireWrongNum === 0">{{ searchEmptyText }}</div>
    <div v-loading="wrongLoaading" class="empty-trip-customer" v-else-if="focus && queryText && requireWrongNum > 0 && requireWrongNum < 3">无法获取当前操作人权限范围，<el-button class="refresh-btn" :loading="false" type="text" @click="refresh">请点击刷新重试</el-button></div>
  </div>
</template>

<script>
  import { DataType } from 'sdc-core'
  import { locale } from 'mixins'
  import { isNotLogin } from 'sdc-webui/src/utils/main'
  import Toast from 'packages/toast'

  export default {
    name: 'selector-container',
    inject: ['getWrongLoaading', 'operatorRightCheck', 'getWrongNum', 'getPlatform', 'getTypeId', 'getOperatorRightMap', 'getDisabledMentorList', 'onSelectedCheck', 'textarea', 'multiple', 'getDisabled', 'search', 'mode', 'size', 'valueKey', 'placeholder', 'pasteable', 'change', 'checkItemExist', 'getDataList', 'getPasteResult', 'getSelected', 'queryParams', 'getRange', 'filterKey', 'filterValue'],
    mixins: [locale],
    data() {
      return {
        queryText: '',
        pasteLoading: false,
        focus: false,
        callback: null,
        isEnter: false,
        searchEmptyText: '' // 搜索为空时的提示语
      }
    },
    computed: {
      readonly() {
        if (!this.search) return true
        return this.getSelected().length === 1 && !this.multiple
      },
      inputSize() {
        return this.size === 'small' ? 'mini' : 'small'
      },
      textPlaceholder: {
        get() {
          if (this.getSelected().length) {
            return ''
          }
          return this.placeholder
        },
        set() {
        }
      },
      suffixIcon() {
        let icon = this.multiple ? 'el-input__icon el-icon-circle-close' : ''
        if (!this.getSelected().length || this.getDisabled()) {
          icon = ''
        }
        return icon
      },
      platform() {
        return this.getPlatform() || ''
      },
      typeId() {
        return this.getTypeId() || ''
      },
      operatorRightMap() {
        return this.getOperatorRightMap() || []
      },
      requireWrongNum() { // 权限接口错误的次数 3次后不提示刷新 身份默认是"运营人员"
        return this.getWrongNum() || 0
      },
      wrongLoaading() { // 权限接口报错时的loading
        return this.getWrongLoaading() || false
      }
    },
    watch: {
      readonly(newVal) {
        if (newVal && this.queryText) {
          this.queryText = ''
          this.callback([])
        }
      }
    },
    methods: {
      handlefocus() {
        this.focus = true
      },
      handleblur() {
        setTimeout(() => {
          this.focus = false
          this.queryText = ''
          this.searchEmptyText = ''
        }, 300)
      },
      refresh() {
        this.operatorRightCheck()
      },
      getSuggetions(query, callback) {
        if (this.isEnter) {
          this.callback([])
          return
        } 
        if (!this.callback) this.callback = callback
        // 人才透视系统没传"操作人身份"参数时
        if ([1, '1'].includes(this.platform) && !this.typeId) {
          Toast(`人才透视系统需要提供'操作人身份'参数`)
          this.callback([])
          return
        }
        // "非人才透视系统"选择了"员工本人"不让选，只有"人才透视系统"才能选"员工本人"
        if (String(this.platform) !== '1' && this.typeId === '3') {
          Toast(`人才透视系统才能传'操作人身份'为'员工本人'`)
          this.callback([])
          return
        }
        // 权限接口报错时不让搜索
        if (this.requireWrongNum) {
          this.callback([])
          return
        }
        if (!DataType.isFunction(this.getDataList) || query === '') {
          const res = []
          callback(res)
        }
        this.getDataList(query, { ...this.queryParams, ...this.getRange() }).then(async res => {
          if (DataType.isEmptyArray(res)) {
            // res = [{ empty: true, message: '暂无人员数据' }]
            res = []
          } else {
            const isNumber = !isNaN(parseFloat(query)) && isFinite(query)
            // 员工选择器筛选名称时
            if (this.mode === 'staff' && !isNumber && res.length > 1) {
              // 需要精准查询一次
              const equalRes = await this.getDataList(query, { ...this.queryParams, ...this.getRange(), count: 1, likeMode: 'equal' })
              if (!DataType.isEmptyArray(equalRes)) {
                const index = res.findIndex(item => item[this.valueKey] === equalRes[0][this.valueKey])
                // 判断是否已存在第一次查询结果中
                if (index > -1) {
                  res.unshift(res.splice(index, 1)[0])
                } else {
                  res.unshift(equalRes[0])
                }
                // 最多只展示十条数据
                if (res.length > 10) {
                  res.length = 10
                }
              }
            }
          }
          // 过滤掉已经被禁用的导师
          res = res.filter(item => !this.getDisabledMentorList().includes(item.StaffID))
          if (this.filterValue && this.filterValue.length > 0) {
            // 存在过滤字段
            res = res.filter(item => {
              return !(this.filterKey in item) || this.filterValue.indexOf(item[this.filterKey]) > -1
            })
          }
          if (DataType.isEmptyArray(res)) {
            // res = [{ empty: true, message: '暂无人员数据' }]
            res = []
            // 搜索为空时的提示语
            if (['4', '5'].includes(this.typeId)) {
              this.searchEmptyText = '暂无人员数据，请检查输入信息是否正确'
            } else if (['3'].includes(this.typeId)) {
              this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择非部门员工担任导师，请点击右侧按钮通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
            } else {
              this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择管理组织以外的员工担任导师，请点击右侧按钮通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
            }
          } else {
            this.searchEmptyText = ''
          }
          callback(res)
        }).catch(res => {
          // res = [{ empty: true, message: isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed') }]
          if (isNotLogin(res)) {
            res = [{ empty: true, message: this.$st('sdc.selector.notLogin') }]
          } else {
            res = []
          }
          // 搜索报错时的提示语
          if (['4', '5'].includes(this.typeId)) {
            this.searchEmptyText = '暂无人员数据，请检查输入信息是否正确'
          } else if (['3'].includes(this.typeId)) {
            this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择非部门员工担任导师，请点击右侧按钮通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
          } else {
            this.searchEmptyText = '请检查输入信息是否正确，当前不支持选择管理组织以外的员工担任导师，请点击右侧按钮通过组织树选择兼岗员工，如仍有疑问可咨询小T(8008)'
          }
          callback(res)
        })
      },
      handleSelect(data) {
        if (data.empty) {
          return
        }
        if (!this.multiple) {
          // 发请求校验风险
          this.onSelectedCheck(data)
          // this.change([data])
        } else if (this.multiple && (!DataType.isFunction(this.checkItemExist) || !this.getSelected().find(this.checkItemExist(data)))) {
          this.change([...this.getSelected(), data])
        } else {
          Toast(this.$st('sdc.selector.exist'))
        }
        this.queryText = ''
      },
      handleDelete(event) {
        if (event.keyCode === 8 && !this.queryText) {
          const selected = this.getSelected().slice(0, this.getSelected().length - 1)
          this.change(selected)
        }
      },
      handlePaste(event) {
        if (!this.pasteable || !DataType.isFunction(this.getPasteResult)) return
        if (this.getSelected().length > 0 && !this.multiple) return
        let text = event.clipboardData.getData('text')
        // 多个走粘贴自动选中返回数据，单个时走输入模式
        text = text.replace(/[\r\n]/g, ';')
        if (text.indexOf(';') !== -1) {
          event.preventDefault()
          text = text.split(';').filter(item => !!item).join(';')
          if (text.split(';').length > 500) {
            Toast(this.$st('sdc.selector.limitOut'), 2000)
            return
          }
          const restore = this.textPlaceholder
          this.pasteLoading = true
          this.textPlaceholder = text
          this.getPasteResult(text, { ...this.queryParams, ...this.getRange() }).then(res => {
            if (DataType.isEmptyArray(res)) {
              Toast(this.$st('sdc.selector.noMatch'), 2000)
            } else {
              if (!this.multiple) res.splice(1)
              const data = res.filter(item => {
                return !DataType.isFunction(this.checkItemExist) || !this.getSelected().find(this.checkItemExist(item))
              })
              if (data.length !== 0) {
                this.change([...this.getSelected(), ...data])
              } else {
                Toast(this.$st('sdc.selector.exist'))
              }
            }
            this.pasteLoading = false
            this.textPlaceholder = restore
          }).catch(res => {
            Toast(isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed'), 2000)
            this.pasteLoading = false
            this.textPlaceholder = restore
          })
        }
      },
      handleEnter() {
        if (this.$refs.input.suggestions && this.$refs.input.suggestions.length > 0) {
          this.handleSelect(this.$refs.input.suggestions[0])
          // 隐藏提示
          this.$refs.input.suggestions = []
          this.$refs.input.highlightedIndex = -1
          this.isEnter = true
          setTimeout(() => {
            this.isEnter = false
          }, 500)
        }
      },
      handleClear() {
        this.change([])
      }
    }
  }
</script>

<style lang="less" scoped>
.selector-container-tutor-costomer {
  position: relative;
  // border-right: 1px solid #dcdcdc !important;
  // padding-left: 0 !important;
  // .tags {
  //   padding-right: 4px !important;
  //   /deep/.tag.el-tag.el-tag--info {
  //     background-color: #eee !important;
  //     border-color: #eee !important;
  //     color: #000000e6 !important;
  //     font-size: 12px !important;
  //     .el-tag__close {
  //       color: #000000e6 !important;
  //     }
  //   }
  // }
  .empty-trip-customer {
    position: absolute;
    top: 44px;
    left: 0;
    padding: 13px 16px;
    width: calc(100% + 36px);
    color: #00000099;
    font-size: 14px;
    line-height: 22px;
    border-radius: 6px;
    border: 0.5px solid #DCDCDC;
    box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a, 0 8px 10px -5px #00000014;
    background: #fff;
    z-index: 999;
    .refresh-btn {
      color: #0052D9;
      border: none;
      padding: 0;
      line-height: 22px;
    }
  }
}
</style>
