package com.tencent.hr.knowservice.businessCommon.constans;

/**
 * @description:
 * @author: shi<PERSON><PERSON>
 * @createDate: 2023/10/27
 * @version: 1.0
 */
public enum V8CacheKeyEnum {
    /**
     * V8 用户端的缓存值
     * QlearningV8:NetCourse:{courseId}
     */
    NET_COURSE_INFO_USER("QlearningV8:NetCourse:");

    /**
     * 缓存key值
     */
    private String keyName;

    V8CacheKeyEnum(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyName() {
        return keyName;
    }
}
