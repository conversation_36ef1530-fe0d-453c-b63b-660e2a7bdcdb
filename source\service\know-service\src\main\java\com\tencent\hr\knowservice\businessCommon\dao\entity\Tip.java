package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * tip
 *
 */
@Data
public class Tip implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 类型id 1:面授课 2：网络课 18:新图文
     */
    private Integer actType;

    /**
     * 类型名称 
     */
    private String actTypeName;

    /**
     * 0 开启 1 关闭
     */
    private Integer tipStatus;

    /**
     * 是否有效 0：无效 1：有效
     */
    private Boolean enable;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 修改人id
     */
    private Integer updateId;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 修改时间
     */
    private Date updatedAt;

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public void setCreatorAndUpdate(Integer staffId,String staffName){
        this.enable = true;
        this.creatorId = staffId;
        this.creatorName = staffName;
        this.createdAt = new Date();
        setUpdate(staffId,staffName);
    }

    public void setUpdate(Integer staffId, String staffName) {
        this.updateId = staffId;
        this.updateName = staffName;
        this.updatedAt = new Date();
    }

}