package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.Tip;
import org.springframework.stereotype.Repository;

@Repository
public interface TipMapper extends BaseMapper<Tip> {
    int deleteByPrimaryKey(Integer id);

    int insert(Tip record);

    int insertSelective(Tip record);

    Tip selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(Tip record);

    int updateByPrimaryKey(Tip record);
}