package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActEmpFavorites;

public interface ActEmpFavoritesMapper extends BaseMapper<ActEmpFavorites> {
    int deleteByPrimaryKey(Integer id);

    int insert(ActEmpFavorites record);

    int insertSelective(ActEmpFavorites record);

    ActEmpFavorites selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ActEmpFavorites record);

    int updateByPrimaryKey(ActEmpFavorites record);
}