<template>
  <SdcCascader ref="cascader" :getData="getData" :value.sync="internalValue" v-bind="$attrs" v-on="$listeners" class="sdc-staff-subtype-selector"/>
</template>

<script>
import SdcCascader from 'packages/cascader'
import staffSubtypeService from 'api/staff-subtype.service'
  export default {
    name: 'sdc-staff-subtype-selector',
    components: { SdcCascader },
    props: {
      promise: Promise,
      value: {
        require: true
      }
    },
    computed: {
      internalValue: {
        get() {
          return this.value
        },
        set(newValue) {
          this.$emit('input', newValue)
        }
      }
    },
    methods: {
      async getData() {
        try {
          const result = this.promise ? await this.promise : await staffSubtypeService.getList()
          return result 
        } catch (error) {
          return [] 
        }
      },
      getCheckedNodes(leafOnly = false) {
        return this.$refs.cascader.getCheckedNodes(leafOnly)
      },
      clearSelected() {
        this.$refs.cascader.clearSelected()
      }
    }
  }
</script>
