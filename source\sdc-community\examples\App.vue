<template>
  <div id="app">
    <sdc-community :staffId="staffId" :isShowTab="isShowTab" :pageName="pageName" :pageInfo="pageInfo" :communityRole="1" :env="env" :headersData="headersData" :showContract="true" @changePage="changePage"></sdc-community>
  </div>
</template>

<script>
// import api from '../packages/comment/utils/api'

export default {
  name: 'App',
  data() {
    return {
      // params: {},
      // actId: 1169,
      isShowTab: true,
      pageInfo: {},
      pageName: 'homePage',
      headersData:{
        'community-categoryid': "909643934531584",
        'community-phone': '0e1bce988a081f5f9d4effa33e9077c8',
        'community-staffid': '209543',
        'community-staffname': 'v_shoujunhe',
        'community-systemid': 201
      },
      staffId: 83326,
      env: 'test'
    }
  },
  created() {
    // api
    //   .getDetail('/api/courselist/api/cl/', this.actId, {
    //     scrollTarget: '#app',
    //     share_staff_id: '',
    //     share_staff_name: '',
    //     from: 'CourseList'
    //   })
    //   .then(() => {
    //     // this.params = {
    //     //   env: 'dev',
    //     //   userName: 'vincentyqwu',
    //     //   actId: this.actId,
    //     //   appId: 'A9BiosXihR0h46ThNsAX',
    //     //   maxHeight: '1000px'
    //     // }
    //     this.params = {
    //       env: 'dev',
    //       userName: 'vincentyqwu',
    //       actId: this.actId,
    //       appId: 'A9BiosXihR0h46ThNsAX',
    //       scrollTarget: '#app'
    //     }
    //   })
  },
  methods:{
    changePage(obj) {
      this.pageName = obj.name
      this.pageInfo = obj.query
    }
  }
}
</script>

<style>
html,
body {
  height: 100%;
  padding: 0;
  margin: 0;
  background: #f6f8fa;
  box-sizing: border-box;
}
#app {
  /* padding: 50px; */
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  font-family: PingFang SC, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* text-align: center; */
  color: #2c3e50;
}
</style>
