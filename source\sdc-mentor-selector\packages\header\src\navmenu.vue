<template>
  <div class="sdc-nav-menu">
    <el-menu mode="horizontal" menu-trigger="hover">
      <el-submenu index="1" popper-class="sdc-nav-menu-popper">
        <slot slot="title">
          <i class="el-icon-menu"></i>
        </slot>
        <nav-menu-item :data="menus"/>
      </el-submenu>
    </el-menu>
  </div>
</template>

<script>
  import { getMapItem } from 'sdc-webui/src/utils/main'
  import NavMenuItem from './navmenu-item'

  const defaultData = {
        "menuTypeList": [
            {
                "id": 3,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 3,
                "dictName": "自助服务",
                "dictValue": null,
                "roleId": null,
                "sort": 2,
                "new": false
            },
            {
                "id": 5,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 5,
                "dictName": "招聘选拔",
                "dictValue": "http://zhaopin.oa.com/resume/default.aspx",
                "roleId": null,
                "sort": 3,
                "new": false
            },
            {
                "id": 7,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 7,
                "dictName": "学习成长",
                "dictValue": "http://learn.oa.com/qlearning7/default.htm",
                "roleId": null,
                "sort": 4,
                "new": false
            },
            {
                "id": 6,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 6,
                "dictName": "评估管理",
                "dictValue": "http://assess.oa.com/assess",
                "roleId": null,
                "sort": 5,
                "new": false
            },
            {
                "id": 4,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 4,
                "dictName": "薪酬福利",
                "dictValue": "https://pay.woa.com/staff/sbc/salary",
                "roleId": null,
                "sort": 6,
                "new": false
            },
            {
                "id": 2,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 2,
                "dictName": "组织发展",
                "dictValue": "http://od.oa.com/organization/default.aspx",
                "roleId": null,
                "sort": 7,
                "new": false
            },
            {
                "id": 8,
                "dictTypeId": 1,
                "dictTypeName": null,
                "dictId": 8,
                "dictName": "人事管理",
                "dictValue": "http://hm.oa.com/hrmove/",
                "roleId": null,
                "sort": 8,
                "new": false
            }
        ],
        "menuGroupMap": {
            "2": [
                {
                    "id": 311,
                    "name": "360度评估",
                    "typeId": 2,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://yunassess.oa.com/",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 1
                }
            ],
            "3": [
                {
                    "id": 41,
                    "name": "我要休假",
                    "typeId": 3,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://holiday.oa.com/flow/apply",
                    "mobileUrl": "https://oa.m.tencent.com/an:MHoliday/html/index.html",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 1
                },
                {
                    "id": 42,
                    "name": "加班申请",
                    "typeId": 3,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://ot.oa.com/overtime/holidayotapply",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 3
                },
                {
                    "id": 43,
                    "name": "腾讯阳光平台",
                    "typeId": 3,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://yangguang.oa.com/#/Main/Home",
                    "mobileUrl": "https://oa.m.tencent.com/an:Sunshine/sunshineMobile/index.html",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 7
                }
            ],
            "4": [
                {
                    "id": 24,
                    "name": "鹅民公社",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://flex.oa.com/",
                    "mobileUrl": "https://fm.m.tencent.com/an:flex/",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 0
                },
                {
                    "id": 23,
                    "name": "工资单查询",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://pay.woa.com/staff/sbc/salary",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 1
                },
                {
                    "id": 26,
                    "name": "商保理赔",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://ecomm.generalichina.com/ebnsp/#/tencent",
                    "mobileUrl": "https://fm.m.tencent.com/an:mhr/portal/insurance.php",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 2
                },
                {
                    "id": 31,
                    "name": "业务体验",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://tiyan.oa.com",
                    "mobileUrl": "https://oa.m.tencent.com/an:tiyan/",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 3
                },
                {
                    "id": 28,
                    "name": "安居计划",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://tx.fuli.oa.com/housingplan/My/ApplyUser",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 4
                },
                {
                    "id": 27,
                    "name": "礼金申请",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://cost.oa.com/costv5web/default.aspx",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 5
                },
                {
                    "id": 30,
                    "name": "Q爸妈俱乐部",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://km.oa.com/group/23048",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 6
                },
                {
                    "id": 310,
                    "name": "年末激励查询",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://pay.woa.com/staff/sbc/incentive/bonus",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 8
                },
                {
                    "id": 37,
                    "name": "调薪记录查询",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://pay.woa.com/staff/sbc/salary/salary-change",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 9
                },
                {
                    "id": 39,
                    "name": "证券账户维护",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://pay.woa.com/staff/sbc/account",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 10
                },
                {
                    "id": 32,
                    "name": "长期激励查询",
                    "typeId": 4,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://pay.woa.com/staff/lti/index",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 11
                }
            ],
            "5": [
                {
                    "id": 305,
                    "name": "ATS",
                    "typeId": 5,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://ats.oa.com/",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 0
                },
                {
                  "id": 49,
                  "name": "社招伯乐推荐",
                  "typeId": 5,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "https://bole.woa.com/bole",
                  "mobileUrl": "https://fm.m.tencent.com/an:mhr/bole/?isbole=1",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 1
                },
                {
                  "id": 56,
                  "name": "校招伯乐推荐",
                  "typeId": 5,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://campus.oa.com/index.html#/bole",
                  "mobileUrl": "https://oa.m.tencent.com/an:campusmobile/interviewhelpermobile/bole",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 2
                },
                {
                    "id": 50,
                    "name": "活水平台",
                    "typeId": 5,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://huoshui.oa.com/huoshui/page/Home/index",
                    "mobileUrl": "https://oa.m.tencent.com/an:huoshui/mhuoshui/page/Home/index",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 2
                }
            ],
            "6": [
                {
                  "id": 57,
                  "name": "绩效评估",
                  "typeId": 6,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://tps.oa.com/assess/Index",
                  "mobileUrl": "https://oa.m.tencent.com/an:tpsroot/massess/ToDoList",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 1
                },
                {
                  "id": 58,
                  "name": "目标管理",
                  "typeId": 6,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://goal.oa.com/goal",
                  "mobileUrl": "https://oa.m.tencent.com/an:tpsroot/mgoal/",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 2
                },
                {
                  "id": 59,
                  "name": "全面反馈",
                  "typeId": 6,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://tps.oa.com/ca/Index",
                  "mobileUrl": "",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 5
                },
                {
                  "id": 318,
                  "name": "职级评定",
                  "typeId": 6,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://hrppe.oa.com/Home/My/PPEDeptPending",
                  "mobileUrl": "",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 6
                },
                {
                  "id": 61,
                  "name": "试用期转正",
                  "typeId": 6,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://formal.oa.com/formal/V2/My/FMPendingAssess.aspx",
                  "mobileUrl": "",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 7
                }
            ],
            "7": [
                {
                  "id": 62,
                  "name": "Q-learning",
                  "typeId": 7,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "http://portal.learn.oa.com/user/home",
                  "mobileUrl": "https://oa.m.tencent.com/an:qlearning/mobile/facelist",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 1
                },
                {
                    "id": 64,
                    "name": "行家",
                    "typeId": 7,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://hangjia.oa.com/",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 2
                },
                {
                    "id": 63,
                    "name": "通道标准",
                    "typeId": 7,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://portal.learn.oa.com/user/special?page_id=414&lang=zh",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 3
                },
                {
                    "id": 65,
                    "name": "IDP课程",
                    "typeId": 7,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://v8.learn.oa.com/user/idp",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 4
                },
                {
                    "id": 66,
                    "name": "讲师申请",
                    "typeId": 7,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://learn.oa.com/MS_Lecture/Apply/Lecture",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 5
                }
            ],
            "8": [
                {
                    "id": 312,
                    "name": "HR权限申请",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://hrright.oa.com/home",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 2
                },
                {
                    "id": 313,
                    "name": "HR数据提取",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://tbi.oa.com/dataextract/order",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 3
                },
                {
                    "id": 82,
                    "name": "发起离职",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://hm.oa.com/hrmovedimission/Request/DimissionApply.aspx",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 6
                },
                {
                    "id": 83,
                    "name": "发起调动",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://hm.oa.com/HRMoveTransfer",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 7
                },
                {
                    "id": 84,
                    "name": "外包入场",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://wbgl.oa.com/HRMoveOSRegister",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 9
                },
                {
                    "id": 86,
                    "name": "小工种入场",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "http://hmnew.oa.com/HRMoveLabor/pcpage/hrmove",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 10
                },
                {
                  "id": 87,
                  "name": "申诉平台",
                  "typeId": 8,
                  "typeName": null,
                  "roleId": 0,
                  "pcUrl": "https://shensu.woa.com/",
                  "mobileUrl": "https://oa.m.tencent.com/an:HRAppealMobile/HRAppeal/mobilepage/home",
                  "backgroundImg": "",
                  "mark": 0,
                  "sort": 16
                },
                {
                    "id": 88,
                    "name": "保护系统",
                    "typeId": 8,
                    "typeName": null,
                    "roleId": 0,
                    "pcUrl": "https://talentprotection.woa.com/sp/Todo",
                    "mobileUrl": "",
                    "backgroundImg": "",
                    "mark": 0,
                    "sort": 12
                }
            ]
        }
    }
  export default {
    name: 'sdc-nav-menu',
    props: {
      scope: {
        type: String,
        required: true
      },
      menuData: {
        type: Object,
        default() {
          return null
        }
      }
    },
    computed: {
      menus() {
        let menuCofig = this.menuData
        if (!menuCofig && this.scope === 'oa') {
          menuCofig = this.dataCovert(defaultData)
        }
        const { map = {}, data = [] } = (menuCofig || {})
        return this.getMenuList(map, data)
      }
    },
    methods: {
      getMenuList(map, data) {
        if (!data) {
          return []
        }
        return data.map(item => {
          const menuItem = getMapItem(item, map, 'key', 'text', 'url', 'icon')
          const children = item[map.children] || item.children
          if (!!children && children.length > 0) {
            menuItem.children = this.getMenuList(map, children)
          }
          return menuItem
        })
      },
      dataCovert(data) {
        const result = {
          map: {},
          data: []
        }

        // 分类
        result.data = (data.menuTypeList || []).map(item => {
          const menu = {
            key: 'm' + item.id,
            text: item.dictName,
            url: item.dictValue
            // icon: 'el-icon-caret-right'
          }
          // 子菜单
          if (data.menuGroupMap[item.id]) {
            menu.children = (data.menuGroupMap[item.id] || []).map(subItem => {
              return {
                key: `${menu.key}-${subItem.id}`,
                text: subItem.name,
                url: subItem.pcUrl || subItem.mobileUrl
              }
            })
          }
          return menu
        })
        return result
      }
    },
    components: {
      NavMenuItem
    }
  }
</script>
