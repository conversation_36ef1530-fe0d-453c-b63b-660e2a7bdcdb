<template>
  <sdc-modal ref="modal" custom-class="sdc-prompt" :title="title || $st('sdc.confirm.title')" width="400px"
             @ok="handleConfirm" @cancel="handleCancel" @close="handleCancel(true)">
    <p slot="body" class="content">{{message || $st('sdc.confirm.message')}}</p>
  </sdc-modal>
</template>

<script>
  import { DataType } from 'sdc-core'
  import { modal, locale } from 'mixins'
  import SdcModal from 'packages/modal'

  export default {
    name: 'sdc-prompt',
    mixins: [modal, locale],
    data() {
      return {
        title: '',
        message: '',
        confirm: null,
        cancel: null
      }
    },
    methods: {
      handleConfirm() {
        DataType.isFunction(this.confirm) && this.confirm()
      },
      handleCancel(close = false) {
        const handler = close ? (this.close || this.cancel) : this.cancel
        DataType.isFunction(handler) && handler()
        this.hide()
        document.body.removeChild(this.$refs.modal.$el)
      }
    },
    components: {
      SdcModal
    }
  }
</script>
