<template>
  <fragment>
    <sdc-selector :custom-class="`sdc-staff-selector ${selectClass}`" pasteable mode="staff" ref="selector">
      <template slot-scope="{data}" slot="selector-item">
        <div class="selector-item" :key="data.option[selectorMap.staffID]">
          <img class="item-avatar" :src="avatarUrl" v-set-img="data.option[selectorMap.avatar]"/>
          <span class="item-name" v-html="highlight(data.option[selectorMap.staffName],data.keyword)"></span>
          <span class="item-former-name" v-if="data.option.IsFormerName"></span>
        </div>
      </template>
    </sdc-selector>
    <sdc-selector-modal ref="modal" :data="selected"/>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { selector, highlight, locale } from 'mixins'
  import StaffService from 'api/staff.service'
  import SdcSelector from 'packages/selector'
  import SdcSelectorModal from './modal'
  import SetImg from 'directives/set-img'

  export default {
    name: 'sdc-staff-selector',
    mixins: [selector, highlight, locale],
    directives: {
      setImg: SetImg
    },
    props: {
      icon: {
        type: String,
        default: 'staff'
      },
      includeDimission: {
        type: Boolean,
        default: false
      },
      includeOnBoarding: {
        type: Boolean,
        default: false
      },
      includePartTimePost: {
        type: Boolean,
        default: false
      },
      useFormerNameSearch: {
        type: Boolean,
        default: false
      },
      getDataList: {
        type: Function,
        default: StaffService.getDataList
      },
      getPasteResult: {
        type: Function,
        default: StaffService.getPasteResult
      },
      getTreeData: {
        type: Function,
        default: StaffService.getTreeData
      },
      getChildrenData: {
        type: Function,
        default: StaffService.getChildrenData
      }
    },
    data() {
      const selectorMap = {
        staffID: 'StaffID',
        staffName: 'StaffName',
        engName: 'EngName',
        unitID: 'UnitID',
        unitName: 'UnitName',
        unitFullName: 'UnitFullName',
        avatar: 'Avatar',
        type: {
          staff: 'staff',
          unit: 'unit'
        }
      }
      return {
        valueKey: selectorMap.staffName,
        nodeKey: selectorMap.staffID,
        selectedText: this.$st('sdc.staffSelector.selected'),
        totalText: this.$st('sdc.staffSelector.total'),
        modalProps: {
          title: this.$st('sdc.staffSelector.title')
        },
        treeProps: {
          isLeaf: 'isLeaf'
        },
        selectorProps: { ...{ staffID: 'StaffID', staffName: 'StaffName', avatar: 'Avatar', engName: 'EngName', unitID: 'UnitID', unitName: 'UnitName', unitFullName: 'UnitFullName' }, ...this.props },
        selectorMap,
        queryParams: {
          includeDimission: this.includeDimission,
          includeOnBoarding: this.includeOnBoarding,
          includePartTimePost: this.includePartTimePost,
          useFormerNameSearch: this.useFormerNameSearch
        }
      }
    },
    computed: {
      avatarUrl() {
        return require('packages/theme-grace/img/avatar.gif')
      }
    },
    methods: {
      getCurrentItem(item) {
        return {
          key: item[this.nodeKey] || 0,
          text: item[this.valueKey] || '',
          tags: { minLength: 14, before: 8, after: 5 },
          modal: { minLength: 21, before: 9, after: 11 }
        }
      }
    },
    components: {
      Fragment,
      SdcSelector,
      SdcSelectorModal
    }
  }
</script>
