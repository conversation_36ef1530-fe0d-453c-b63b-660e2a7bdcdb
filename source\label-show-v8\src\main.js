import Vue from 'vue'
import App from './App.vue'

// 引入sdc-label-show组件
import sdcLabelShow from '@tencent/sdc-label-show'
import '@tencent/sdc-label-show/lib/sdc-label-show.css'
// import sdcLabelShow from '@tencent/sdc-label-show-test'
// import '@tencent/sdc-label-show-test/lib/sdc-label-show-test.css'
Vue.use(sdcLabelShow)

Vue.config.productionTip = false

/*********模板引用可以直接new*********/
new Vue({
  render: h => h(App),
}).$mount('#labelShow')
/*********************************/

/********单页面需要在组件上new******/
let LabelObj = {
  App,
  Vue
}
window.LabelShow = LabelObj
/********************************/