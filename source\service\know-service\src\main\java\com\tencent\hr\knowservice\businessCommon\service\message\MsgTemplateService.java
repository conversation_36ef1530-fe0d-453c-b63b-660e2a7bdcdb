package com.tencent.hr.knowservice.businessCommon.service.message;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.MsgTemplate;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.MsgTemplateMapper;

import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.springframework.util.ObjectUtils;


import java.util.*;


/**
 * 消息模板逻辑层
 */
@Service
@Slf4j
public class MsgTemplateService {
    @Autowired
    MsgTemplateMapper msgTemplateMapper;


    @Autowired
    RedisUtil redis;


    public Integer updateCommonMsgTemplate(MsgTemplate msgTemplate) {
        Integer templateId = msgTemplate.getTemplateId();
        if (null == templateId) {
            throw new RuntimeException("模板id为空");
        }
        MsgTemplate template = msgTemplateMapper
                .selectOne(new QueryWrapper<MsgTemplate>().eq("template_id", templateId).eq("enabled", 1));
        int updateNums = 0;
        if (!ObjectUtils.isEmpty(template)) {
            BeanUtils.copyProperties(msgTemplate, template);
            template.setUpdateId(GatewayContext.get().getStaffId());
            template.setUpdateName(GatewayContext.get().getStaffName());
            template.setUpdatedAt(new Date());
            updateNums = msgTemplateMapper.updateByPrimaryKey(template);
        } else {
            throw new RuntimeException("无对应此id的模板");
        }
        return updateNums;
    }

    public MsgTemplate getCommonMsgTemplate(String moduleName, String templateType, Integer actType) {
        String key = CommonCacheKeyEnum.MsgTemplate.getKeyName() + actType + moduleName + templateType;
        MsgTemplate msgTemplate = (MsgTemplate) redis.get(key);
        if (ObjectUtils.isEmpty(msgTemplate)) {
            QueryWrapper<MsgTemplate> wrapper = new QueryWrapper<>();
            QueryWrapper<MsgTemplate> eq = wrapper.eq("template_type", templateType).eq("module_name", moduleName)
                    .eq("act_type", actType).eq("enabled", 1);
            msgTemplate = msgTemplateMapper.selectList(eq).get(0);
            if (ObjectUtils.isEmpty(msgTemplate)) {
                throw new RuntimeException("无此对应渠道下的模板");
            }
            redis.set(key, msgTemplate, 30 * 60L);
        }
        return msgTemplate;
    }

}
