## @tencent/sdc-cli
[![](https://img.shields.io/badge/@tencent/sdc--cli-v1.0.0-green.svg)](http://git.code.oa.com/SDCFront/sdc-cli.git) <br/>
> 贡献者：miraclehe(何名宇)；最近更新时间：2020-10-14；

基于SDC前端框架创建Vue工程化项目

### 用法
- 安装
```bash
tnpm install -g @tencent/sdc-cli
```

- 通过**sdc**命令行一键下载项目脚手架
```bash
sdc init (也可使用别名sdc i)
```
- 然后会依次提示您输入项目名称，描述，作者等信息(不输入将采用默认值)
- 选择项目类型为**web|mob|mfe**，回车开始下载项目模板
- 其中web基于sdc-webui模板，mob基于sdc-mobui模板[<font color=#f81d22>开发中，不建议使用</font>]，mfe基于微前端模板
- 当出现**项目创建成功**表示项目脚手架已下载成功
- 按照操作指引安装依赖，运行项目即可
- 接下来，就开始你的快乐编程之旅把!

- *提示：如果你忘记修改项目名称，描述等信息，可直接下载完成后修改package.json中的name,title,author,description以及repository.url*
