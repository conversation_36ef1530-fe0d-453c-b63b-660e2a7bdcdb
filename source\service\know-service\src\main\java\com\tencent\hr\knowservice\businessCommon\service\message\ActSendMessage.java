package com.tencent.hr.knowservice.businessCommon.service.message;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.message.*;
import com.tencent.hr.knowservice.businessCommon.proxy.MessageServiceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ActSendMessage implements ActSendMessageApi {

    @Autowired
    private MessageServiceApi serviceApi;


    @Override
    public TransDTO sendMessage(MailMessage msg) {
        return serviceApi.sendMail(msg);
    }

    @Override
    public TransDTO sendMessage(TipsMessage msg) {
        return serviceApi.sendTips(msg);
    }

    @Override
    public TransDTO sendMessage(SmsMessage msg) {
        return serviceApi.sendSMS(msg);
    }

    @Override
    public TransDTO sendMessage(BotMessage msg) {
        return serviceApi.sendBot(msg);
    }

    @Override
    public TransDTO sendMessage(HrAssistantMessage msg) {
        return serviceApi.sendHrAssistant(msg);
    }

    @Override
    public TransDTO sendMessage(CalendarMessage msg) {
        return serviceApi.sendCalendar(msg);
    }

}
