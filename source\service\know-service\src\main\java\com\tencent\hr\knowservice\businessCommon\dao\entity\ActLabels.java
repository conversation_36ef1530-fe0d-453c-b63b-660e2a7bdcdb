package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程标签表
 * @TableName act_labels
 */
@Data
@TableName(value ="act_labels")
public class ActLabels implements Serializable {
    /**
     * 标签Id
     */
    @TableId(type = IdType.AUTO)
    private Integer lableId;

    /**
     * 名称
     */
    private String name;

    /**
     * 使用次数
     */
    private Integer usedCount;

    /**
     * 
     */
    private Integer syncId;

    /**
     * 业务类型
     */
    private Integer actType;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}