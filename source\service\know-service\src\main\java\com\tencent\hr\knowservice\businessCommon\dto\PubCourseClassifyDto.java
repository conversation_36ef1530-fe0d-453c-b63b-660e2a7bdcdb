package com.tencent.hr.knowservice.businessCommon.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.List;

@Data
public class PubCourseClassifyDto {

    /**
     * 分类Id
     */
    @TableId(type = IdType.AUTO)
    private Integer classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 业务类型
     */
    private Integer actType;

    /**
     * 父级id
     */
    private Integer pid;

    /**
     * 全路径
     */
    private String fullPath;

    /**
     * 全路径名称
     */
    private String fullName;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 子类
     */
    private List<PubCourseClassifyDto> child;
}
