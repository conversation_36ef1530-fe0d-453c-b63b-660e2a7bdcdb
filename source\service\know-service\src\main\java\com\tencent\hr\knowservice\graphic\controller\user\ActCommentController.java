package com.tencent.hr.knowservice.graphic.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.constans.LRSVerbs;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActComment;
import com.tencent.hr.knowservice.businessCommon.dto.TransCustomDTO;
import com.tencent.hr.knowservice.businessCommon.dto.common.*;
import com.tencent.hr.knowservice.businessCommon.dto.xapi.TXAgent;
import com.tencent.hr.knowservice.businessCommon.proxy.AdapterServiceApi;
import com.tencent.hr.knowservice.businessCommon.service.CommentService;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.entity.Graphic;
import com.tencent.hr.knowservice.graphic.dto.AuthorsDTO;
import com.tencent.hr.knowservice.graphic.service.GraphicCommentService;
import com.tencent.hr.knowservice.graphic.service.GraphicService;
import com.tencent.hr.knowservice.graphic.service.LRSService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 图文评论功能
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/graphic/user/graphic-comment")
public class ActCommentController {
    @Autowired
    private GraphicCommentService graphicCommentService;

    @Autowired
    private GraphicService graphicService;

    @Autowired
    LRSService lrsService;


    @Autowired
    private CommentService commentService;
    /**
     * 新增评论
     *
     * @return
     */
    @PostMapping("/add")
    public TransCustomDTO addComment(@RequestBody ActCommentDto actCommentDto) {
        String graphicIdStr = actCommentDto.getActId();
        Graphic graphic = graphicService.getGraphicByIdEnable(Integer.valueOf(graphicIdStr));
        if (null == graphic){
            throw new LogicException("当前图文不存在！");
        }
        ContextEntity current = GatewayContext.current();
        actCommentDto.setLoginStaffId(Integer.valueOf(current.getStaffId()));
        actCommentDto.setLoginStaffName(current.getStaffName());
        actCommentDto.setActType(ActTypeEnum.GRAPHIC.getActType());
        TransCustomDTO dto = new TransCustomDTO();

            Integer commentId = commentService.addComment(actCommentDto);
            //添加积分
            if (commentId != null) {
                //图文表评论数字段加一
                graphicCommentService.updateCommentCount(actCommentDto.getActId(),true);
                dto.setData(commentId);
                //更新积分
                List<TXAgent> txAgents = new ArrayList<>();
                List<AuthorsDTO> authorsDTOs = JsonUtil.getListByJson(graphic.getAuthors(), AuthorsDTO.class);
                if (CollectionUtils.isNotEmpty(authorsDTOs)){
                    for (AuthorsDTO authorsDTO : authorsDTOs){
                        TXAgent authorTxAgent = new TXAgent(String.valueOf(authorsDTO.getStaffid()), authorsDTO.getStaffname(), "author");
                        txAgents.add(authorTxAgent);
                    }
                }
                //计算字数
                HashMap<String,String> resultExtension = new HashMap<>();
                resultExtension.put("contentLength",String.valueOf(com.tencent.hr.knowservice.utils.StringUtils.contentNumber(actCommentDto.getContent())));
                String credit = lrsService.addLrsRecord(String.valueOf(current.getStaffId()), current.getStaffName(), LRSVerbs.comment, graphicIdStr, graphic.getGraphicName(), resultExtension,txAgents);
                if (StringUtils.isNotBlank(credit)){
                    dto.setRemark(credit);
                }
            }

        dto.setCode(HttpStatus.SC_OK);
        dto.setSuccess(true);
        return dto;
    }

    /**
     * 获取评论列表
     *
     * @param actId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/get_comments")
    public TransDTO getComments(@RequestParam("act_id") String actId,
                                @RequestParam(value = "pid", required = false) Integer pid,
                                @RequestParam(value = "staff_name", required = false) String staffName,
                                @RequestParam(value = "content", required = false) String content,
                                @RequestParam(value = "type", required = false) Integer type,
                                @RequestParam(value = "order_type", required = false) Integer orderType,
                                @RequestParam("page_no") int pageNo,
                                @RequestParam("page_size") int pageSize) {

        ActCommentDto actCommentDto = new ActCommentDto();
        ContextEntity current = GatewayContext.current();
        actCommentDto.setActId(actId);
        actCommentDto.setContent(content);
        actCommentDto.setStaffName(staffName);
        actCommentDto.setPid(pid);
        actCommentDto.setType(type);
        actCommentDto.setActType(ActTypeEnum.GRAPHIC.getActType());
        actCommentDto.setPageNo(pageNo);
        actCommentDto.setPageSize(pageSize);
        actCommentDto.setOrderType(orderType);

        Integer staffId = Integer.valueOf(current.getStaffId());
        actCommentDto.setLoginStaffId(staffId);
        actCommentDto.setLoginStaffName(current.getStaffName());
        if (StringUtils.isBlank(actId)) {
            return new TransDTO().withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("图文id不能为空！");
        }
        Graphic graphic = graphicService.getGraphicById(Integer.valueOf(actId));
        if (graphic == null){
            return new TransDTO().withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("图文不存在");
        }
        if (!graphic.getCreatorId().equals(staffId)) {
            //不是作者只能查看显示的
            actCommentDto.setShow(1);
        }
        actCommentDto.setAuthorId(graphic.getCreatorId());
        CommentPageDto<ActCommentResDto> commentPageDto= commentService.getComments(actCommentDto);
        if (staffId.intValue() == graphic.getCreatorId().intValue()) {
            commentPageDto.setHasShow(false);
        }
        commentPageDto.setAuthorId(Collections.singletonList(graphic.getCreatorId().toString()));
        commentPageDto.setAuthorName(Collections.singletonList(graphic.getCreatorName()));
        commentPageDto.setName(graphic.getGraphicName());

        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withData(commentPageDto);
    }

    /**
     * 评论删除
     *
     * @param cId
     * @return
     */
    @PostMapping("/delete/{cId}")
    public TransDTO deleteComment(@PathVariable String cId) {
        if (cId == null){
            throw new LogicException("删除评论id不能为空");
        }
        TransDTO dto = new TransDTO();

            ContextEntity current = GatewayContext.current();
            ActDeleteCommentDto actCommentDto = new ActDeleteCommentDto();
            actCommentDto.setLoginStaffId(Integer.valueOf(current.getStaffId()));
            actCommentDto.setLoginStaffName(current.getStaffName());
            actCommentDto.setId(Integer.valueOf(cId));
            ActComment actComment = commentService.deleteComment(actCommentDto);
            if (actComment != null) {
                //评论数量减一
                graphicCommentService.updateCommentCount(actComment.getActId(),false);
            }

        dto.withSuccess(true).withCode(HttpStatus.SC_OK);
        dto.setData(null);
        return dto;
    }

    /**
     * 评论隐藏
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/show")
    public TransDTO showComment(@RequestBody ActShowCommentDto actCommentDto) {
        ContextEntity current = GatewayContext.current();
        actCommentDto.setLoginStaffId(Integer.valueOf(current.getStaffId()));
        actCommentDto.setLoginStaffName(current.getStaffName());
        commentService.showComment(actCommentDto);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withData(true).withMessage("操作成功！");
    }

    /**
     * 评论点赞
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/like")
    public TransDTO likeComment(@RequestBody ActPraisedCommentDto actCommentDto) {
        actCommentDto.setActType(ActTypeEnum.GRAPHIC.getActType());
        ContextEntity current = GatewayContext.current();
        actCommentDto.setLoginStaffId(Integer.valueOf(current.getStaffId()));
        actCommentDto.setLoginStaffName(current.getStaffName());
        commentService.praisedComment(actCommentDto);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withData(true).withMessage("操作成功！");
    }

    /**
     * 评论置顶
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/sticky")
    public TransDTO stickyComment(@RequestBody ActStickyCommentDto actCommentDto) {
        ContextEntity current = GatewayContext.current();
        actCommentDto.setLoginStaffId(Integer.valueOf(current.getStaffId()));
        actCommentDto.setLoginStaffName(current.getStaffName());
        commentService.stickyComment(actCommentDto);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withData(true).withMessage("操作成功！");
    }

    /**
     * 获取评论数量
     * @param actId
     * @param actType
     * @return
     */
    @GetMapping("get_comment_count")
    public TransDTO getCommentCount(@RequestParam("act_id") String actId, @RequestParam("act_type") Integer actType) {
        TransDTO dto = new TransDTO<>();
        long count;

            count = commentService.getCommentCount(actId, actType);

        return dto.withCode(HttpStatus.SC_OK).withData(count).withSuccess(true).withMessage("操作成功！");
    }

}
