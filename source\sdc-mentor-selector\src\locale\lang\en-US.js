export default {
  sdc: {
    loading: 'Loading...',
    toast: 'Toast Message',
    table: {
      caption: 'List',
      select: 'Select',
      number: 'No.',
      action: 'Action'
    },
    modal: {
      ok: 'OK',
      cancel: 'Cancel'
    },
    alert: {
      title: 'Information',
      message: 'Success!'
    },
    confirm: {
      title: 'Delete Confirmation',
      message: 'This will delete the data permanently, continue?'
    },
    queryPanel: {
      query: 'Query',
      reset: 'Reset'
    },
    selector: {
      select: 'Select',
      modal: {
        ok: 'Ok',
        cancel: 'Cancel'
      },
      exist: 'The item is existed, select other！',
      noMatch: 'No matching data',
      failed: 'Failed to get data',
      notLogin: 'Not logged in',
      limitOut: 'The number of pasting cannot exceed 500'
    },
    dictSelector: {
      placeholder: 'Select'
    },
    manageSubjectSelector: {
      placeholder: 'Select',
      total: '$count total items'
    },
    unitSelector: {
      title: 'Select Unit',
      selected: '$count Units is Selected',
      total: '$count total items'
    },
    staffSelector: {
      title: 'Select Staff',
      selected: '$count Staffs is Selected',
      total: '$count total items'
    },
    postSelector: {
      title: 'Select Post',
      selected: '$count Posts is Selected',
      total: '$count total items'
    },
    postCascader: {
      total: '$count total items'
    },
    positionLevel: {
      total: '$count total items'
    },
    layout: {
      navMenu: 'Navigation',
      navbar: {
        more: 'More'
      },
      search: {
        placeholder: 'Search People, Material, Guideline...'
      },
      links: {
        feedback: 'Suggestion & Feedback',
        exit: 'Exit'
      },
      icons: {
        setting: 'Setting',
        message: 'Message'
      },
      backtop: 'Back Top'
    },
    exception: {
      error: {
        401: 'You don\'t have the authority to access this page!',
        404: 'Sorry, the page has wrong!'
      },
      back: 'Back',
      or: 'or',
      home: 'Home',
      apply: 'Apply Authority',
      contactUs: 'Contact Us: 8008（HR hotline）'
    }
  }
}
