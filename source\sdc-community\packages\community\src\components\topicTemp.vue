<template>
    <div id="topicTemp" ref="tempDialog" v-if="isShowTemp" @mousedown="startDrag($event)">
        <header class="tempHead">
            <span class="title">{{title}}</span>
            <span @click.stop="closeTemp"><i class="el-icon-close"></i></span>
        </header>
        <section class="tempDesc" v-html="desc"></section>
    </div>
</template>
<script>
export default {
    name: 'topicTemp',
    props: {
        title: {
            type: String,
            default: '标题'
        },
        desc: {
            type: String,
            default: '内容'
        },
        isShowTemp: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isDragging: false,
            offsetX: 0,
            offsetY: 0
        }
    },
    methods: {
        startDrag(event) {
            this.isDragging = true
            this.offsetX = event.clientX - this.$refs.tempDialog.offsetLeft
            this.offsetY = event.clientY - this.$refs.tempDialog.offsetTop
            document.addEventListener('mousemove', this.drap)
            document.addEventListener('mouseup', this.stopDrap)
        },
        drap(event) {
            if(this.isDragging) {
                this.$refs.tempDialog.style.left = event.clientX - this.offsetX + 'px'
                this.$refs.tempDialog.style.top = event.clientY - this.offsetY + 'px'
            }
        },
        stopDrap() {
            this.isDragging = false
            document.removeEventListener('mousemove', this.drap)
            document.removeEventListener('mouseup', this.stopDrap)
        },
        closeTemp() {
            this.$emit('update:isShowTemp', false)
        }
    }
}
</script>
<style lang='less' scoped>
#topicTemp{
    position: fixed;
    right: 20px;
    top: 100px;
    width: 600px;
    height: 800px;
    box-shadow: 0 0  5px #999;
    overflow: hidden;
    z-index: 100;
    background: #fff;
    font-family: "PingFang SC";
    .tempHead{
        height: 40px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #ddd;
        i{
            cursor: pointer;
        }
    }
    .tempDesc{
        padding: 16px 20px;
    }
}
</style>
