package com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class ConfigurationsOfSelect {
    /**
     * 某一个时间点的互动配置唯一标识（支持同一个时间点，多个不同的配置）
     */
    private String interactiveId;
    /**
     * 互动时间点
     */
    private Integer activeTime;
    /**
     * 互动简介
     */
    private String introduction;
    /**
     * 互动英文
     */
    private String introductionEn;
    /**
     * 互动简介提示
     */
    private String continueStudyingTips;
    /**
     * 互动简介提示（英文）
     */
    private String continueStudyingTipsEn;
    /**
     * 互动标题
     */
    private String title;
    /**
     * 互动标题（英文）
     */
    private String titleEn;

    /**
     * 选择类型互动的具体内容值
     */
    private List<SelectContent> selectContent;

    private boolean enabled;
    private String creatorId;
    private String creatorName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String createdAt;
    private String updaterId;
    private String updaterName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String updatedAt;
}
