package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * prod_file_read_records_summary
 *
 */
@Data
public class ProdFileReadRecordsSummary implements Serializable {
    /**
     */
    private Integer id;

    /**
     * 学员Id
     */
    private Integer staffId;

    /**
     * 文档产品Id
     */
    private Integer fileProdId;

    /**
     * 阅读时间
     */
    private Date readTime;

    /**
     * 阅读次数
     */
    private Integer viewCount;

    /**
     * 1:已好评;0:未好评
     */
    private Integer goodStatus;

    /**
     * 1:已差评;0:未差评
     */
    private Integer badStatus;

    /**
     * 1:已收藏;0:未收藏
     */
    private Integer favStatus;

    /**
     * 状态(0 未删除1 已删除)
     */
    private Integer enabled;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;


    /**
     * 点赞数
     */
    @TableField(exist = false)
    private Integer praiseCount;

}