package com.tencent.hr.knowservice.businessCommon.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/09/20/11:31
 * @version: 1.0
 */
@Data
public class ExtendContentAddDTO {

    /**
     * 关联的延申学习课程
     */
    @NotNull(message = "conn_products is null")
    private List<ExtendProductInDTO> connProducts;

    /**
     * 课程id
     */
    @NotNull(message = "prod_id is null")
    private Integer prodId;

    /**
     * 课程类型
     */
    @NotNull(message = "prod_type is null")
    private Integer prodType;

    /**
     * 使用场景，默认为空，1：课前延申学习
     */
    private Integer usageScenario;

    private Integer staffId;

    private String staffName;
}
