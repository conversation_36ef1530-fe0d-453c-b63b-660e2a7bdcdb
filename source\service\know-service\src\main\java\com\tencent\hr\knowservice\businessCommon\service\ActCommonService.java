package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.ActCommonMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ActCommonService {


    @Resource
    private ActCommonMapper actCommonMapper;

    public Object getFileProdList(String actType, String title, String prodId, String prodType, int pageNo, int pageSize) {


        IPage page = new Page(pageNo, pageSize);
        IPage prod = actCommonMapper.findFileProdByTitle(page, title);
        return prod;
    }
}
