package com.tencent.hr.knowservice.businessCommon.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: vincentyqwu
 * @createDate: 2022/6/19/
 * @version: 1.0
 */
@Data
public class  MessageBase {
    /**
     * 消息标题
     */
    private String title;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 消息接收人
     */
    private String receiver;
    /**
     * 消息发送人
     */
    private String sender;
    /**
     * 是否需要发送
     */
    private Boolean canSend;
    /**
     * 定时发送时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date prepareSendTime;
    /**
     * 业务单据id
     */
    private String actId;
    /**
     * 业务类型
     */
    private Integer actType;
    /**
     * 计划发送人数
     */
    private int totalCount;
    /**
     * 实际发送人数
     */
    private int sendCount;
    /**
     * 实际发送时长
     */
    private float sendTotalTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 邮件创建人id
     */
    private String creatorId;
    /**
     * 邮件创建人姓名
     */
    private String creatorName;

}
