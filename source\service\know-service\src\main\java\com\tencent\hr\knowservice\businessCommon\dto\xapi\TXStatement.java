package com.tencent.hr.knowservice.businessCommon.dto.xapi;

import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;

/**
 * 调用LRS - Xapi的主类
 */
@Data
@JsonNaming
public class TXStatement {

    /**
     * 用户
     */
    private TXAgent actor;

    /**
     * 动作
     */
    private TXVerb verb;

    /**
     * 事件
     */
    private TXActivity object;

    /**
     * 结果
     */
    private TXResult result;

    /**
     * 环境信息
     */
    private TXContext context;

    /**
     * 记录ID
     */
    private String id;

    /**
     * 存储时间
     */
    private Date storeTime;

    /**
     * 时间产生时间
     */
    private Date createTime;

    /**
     * 应用ID。多租户情况下，可以使用 APPID + 租户ID
     */
    private String appId;

    /**
     * 删除标志
     */
    private Integer enabled;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    public TXStatement(String id){
        super();
        this.id = id;
    }

    public TXStatement(){
        super();
    }

    public void init(){
        this.verb = new TXVerb();
        this.actor = new TXAgent();
        this.object = new TXActivity();
        this.context = new TXContext();
        this.result = new TXResult();

        this.actor.setExtensions(new HashMap<>());
        this.object.setExtensions(new HashMap<>());
        this.result.setExtensions(new HashMap<>());
        this.context.setExtensions(new HashMap<>());
    }

}
