package com.tencent.hr.knowservice.graphic.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.LRSVerbs;
import com.tencent.hr.knowservice.businessCommon.dto.TransCustomDTO;
import com.tencent.hr.knowservice.businessCommon.dto.xapi.TXAgent;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.entity.Graphic;
import com.tencent.hr.knowservice.graphic.dto.GraphicDetailsDTO;
import com.tencent.hr.knowservice.graphic.dto.credit.CreditResDto;
import com.tencent.hr.knowservice.graphic.service.GraphicService;
import com.tencent.hr.knowservice.graphic.service.LRSService;
import com.tencent.hr.knowservice.graphic.service.PraiseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.security.auth.message.AuthException;
import java.util.ArrayList;
import java.util.List;

/**
 * 点赞功能
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/graphic/user/praise")
public class ActPraiseController {

    @Autowired
    PraiseService praiseService;

    @Autowired
    GraphicService graphicService;

    @Autowired
    LRSService lrsService;

    /**
     * 判断是否点赞
     * @param graphicId
     * @return
     * @throws AuthException
     */
    @GetMapping("/check-praised")
    public TransDTO checkPraised(@RequestParam("graphic_id") Integer graphicId) throws AuthException {
        Integer staffId= Integer.valueOf(GatewayContext.current().getStaffId());
        TransDTO dto = new TransDTO<>().withSuccess(true).withCode(HttpStatus.SC_OK);
        //获取数据
        Boolean result = praiseService.checkPraised(graphicId,staffId);
        dto.withData(result);
        return dto;
    }

    /**
     * 用户点赞
     * 点赞的积分上报由V8进行上报，但是不计算点赞数量，和收藏有所不同
     * @param graphicId
     * @return
     */
    @GetMapping("/add-praise")
    public TransDTO<CreditResDto> addPraise(@RequestParam("graphic_id") Integer graphicId){
        Integer staffId= Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
        Graphic graphic = graphicService.getGraphicByIdEnable(graphicId);
        if (null == graphic){
            throw new LogicException("当前图文不存在！");
        }
//        CreditResDto creditResDto = new CreditResDto();
        CreditResDto data = praiseService.addPraise(graphicId,staffId,staffName);
//        if (result){
//            //更新积分
//            List<TXAgent> txAgents = new ArrayList<>();
//            TXAgent txAgent = new TXAgent(String.valueOf(graphic.getCreatorId()), graphic.getCreatorName(), "author");
//            txAgents.add(txAgent);
//            String credit = lrsService.addLrsRecord(String.valueOf(staffId), staffName, LRSVerbs.praise, String.valueOf(graphicId), graphic.getGraphicName(), null,txAgents);
//            if (StringUtils.isNotBlank(credit)){
//                creditResDto.setCredit(credit);
//            }
//        }
//        creditResDto.setData(data);
        return new TransDTO<CreditResDto>().withSuccess(true).withCode(org.apache.commons.httpclient.HttpStatus.SC_OK).withData(data);
    }

    /**
     * 取消点赞
     * 取消点赞的行为上报由V8进行上报
     * @param graphicId
     * @return
     * @throws AuthException
     */
    @GetMapping("/delete-praise")
    public TransDTO deletePraise(@RequestParam("graphic_id") Integer graphicId) throws AuthException {

        Integer staffId= Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
        //获取数据
        Boolean result = praiseService.deletePraise(graphicId,staffId);
        //上报取消点赞到lrs
//        if (result){
//            Graphic graphic = graphicService.getGraphicByIdEnable(graphicId);
//            if (null == graphic){
//                throw new LogicException("当前图文不存在！");
//            }
//            //更新积分
//            List<TXAgent> txAgents = new ArrayList<>();
//            TXAgent txAgent = new TXAgent(String.valueOf(graphic.getCreatorId()), graphic.getCreatorName(), "author");
//            txAgents.add(txAgent);
//            //取消点赞行为上报到lrs
//            lrsService.addLrsRecord(String.valueOf(staffId), staffName, LRSVerbs.cancel_praise, String.valueOf(graphicId), graphic.getGraphicName(), null,txAgents);
//        }
        return new TransDTO<>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(result);
    }
}
