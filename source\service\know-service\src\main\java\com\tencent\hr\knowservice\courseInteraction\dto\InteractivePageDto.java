package com.tencent.hr.knowservice.courseInteraction.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/10/13/14:32
 * @version: 1.0
 */
@Data
public class InteractivePageDto {

    /**
     * 互动规则id
     */
    private String _id;

    /**
     * 互动配置id
     */
    private String interactiveId;

    /**
     * 题目id
     */
    private String questionId;

    /**
     * 互动时间
     */
    @NotNull(message = "互动时间不能为空")
    private Integer activeTime;

    /**
     * 互动标题
     */
    @NotBlank(message = "互动标题不能为空")
    private String title;
    /**
     * 互动标题英文版
     */
    private String titleEn;

    /**
     * 题目类型 CHOOSE--选择；VOTE--投票
     */
    private String activeType;

    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String createdAt;
}
