package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.tencent.hr.knowservice.businessCommon.dao.entity.PubQlDictItem;
import org.springframework.stereotype.Repository;

@Repository
public interface PubQlDictItemMapper {
    int deleteByPrimaryKey(Integer dictItemId);

    int insert(PubQlDictItem record);

    int insertSelective(PubQlDictItem record);

    PubQlDictItem selectByPrimaryKey(Integer dictItemId);

    int updateByPrimaryKeySelective(PubQlDictItem record);

    int updateByPrimaryKey(PubQlDictItem record);

    String findValByItemKey(String dictItemKey);

    String findValByItemKeyAndCourseId(String dictItemKey, String courseId);
}
