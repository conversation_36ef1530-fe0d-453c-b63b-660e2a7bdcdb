<template>
  <div class="sdc-content" :class="customClass">
    <div class="page-nav" v-if="showMenu && has('sidebar')" :class="collapseClass">
      <slot name="sidebar">
        <sdc-sidebar :scope="scope" :sidebarCollapse="sidebarCollapse" :current="activeIndex" :menus="menus" @toggle="handleToggleMenu" @menuContextmenu="handleMenuContextmenu"/>
      </slot>
    </div>
    <div class="page-content">
      <slot name="main">
        <sdc-router-view :nested="true"/>
      </slot>
    </div>
    <el-backtop class="sdc-back-top" target=".page-content .page-container" :bottom="100">
      <el-tooltip :content="$st('sdc.layout.backtop')" placement="top" effect="light" popper-class="sdc-backtop-popper">
        <i class="el-icon-top"></i>
      </el-tooltip>
    </el-backtop>
  </div>
</template>

<script>
  import { classes, layout, locale, sidebar } from 'mixins'
  import SdcRouterView from 'packages/router-view'
  import SdcSidebar from './sidebar'

  export default {
    name: 'sdc-content',
    mixins: [classes, layout, locale, sidebar],
    props: {
      scope: {
        type: String,
        default: 'oa'
      },
      menus: {
        type: Object,
        default() {
          return {}
        }
      },
      showMenu: {
        type: Boolean,
        default: true
      },
      sidebarCollapse: {
        type: Boolean,
        default: false
      }
    },
    components: {
      SdcRouterView,
      SdcSidebar
    },
    methods: {
      changeCollapse(collapse) {
        this.$bus.$emit('changeCollapse', collapse)
      },
      handleMenuContextmenu(event, menu) {
        this.$emit('menuContextmenu', event, menu)
      }
    }
  }
</script>
