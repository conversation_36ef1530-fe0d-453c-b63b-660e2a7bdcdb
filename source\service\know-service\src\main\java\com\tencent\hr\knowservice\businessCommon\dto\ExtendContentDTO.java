package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class  ExtendContentDTO {

    private Integer id;
//    /**
//     * 产品类型 (18 新图文)
//     */
//    @NotNull(message = "产品类型不能为空")
//    private Integer prodType;
//
//    /**
//     * 产品id
//     */
//    @NotNull(message = "图文id不能为空")
//    private Integer prodId;

    /**
     * 关联产品类型
     *
     */
    @NotNull(message = "关联课程类型不能为空")
    private Integer connProdModuleId;
    /**
     * 关联产品类型名称
     *
     */
    @NotBlank(message = "关联课程类型名称不能为空")
    private String connProdModuleName;
    /**
     * 关联产品id
     */
    private String connProdItemId;
    /**
     * 1：站内内容 2:自定义内容
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;
    /**
     * 产品名称
     */
    @NotBlank(message = "内容名称不能为空")
    private String connProdName;

    /**
     * 跳转链接
     */
    @NotBlank(message = "链接不能为空")
    private String connProdUrl;

    /**
     * 封面图id
     */
    private String connProdCoverId;
    /**
     * 观看数
     */
    private Integer viewCount;
    /**
     * 评分
     */
    private Double avgScore;
    /**
     * 关联产品创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
    private Date connProdCreatedTime;
    /**
     * 可参与人群
     */
    private String targetPeople;
}
