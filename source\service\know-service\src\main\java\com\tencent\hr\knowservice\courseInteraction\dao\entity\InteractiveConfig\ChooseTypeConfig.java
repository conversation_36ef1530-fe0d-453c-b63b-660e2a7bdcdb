package com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description:
 * @author: shi<PERSON><PERSON>
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class ChooseTypeConfig {
    /**
     * 类型：single/multi
     */
    private String type;

    /**
     * 完成条件
     */
    private String completionConditions;
}
