package com.tencent.hr.knowservice.businessCommon.constans;


public enum ModuleIdEnum {

    NET_COURSE(1, "netCourse", "网络课"),
    FACE_COURSE(2, "faceCourse", "面授课"),
    LIVE(3, "live", "直播"),
    ACTIVITY(4, "activity", "活动"),
    MARKER(5, "marker", "码客"),
    HANG_JIA(6, "hangjia", "行家"),
    LITTLE_CASE(7, "case", "案例"),
    NOTE(8, "note", "笔记"),
    ARTICLE(9, "article", "图文"),
    MOOC(10, "mooc", "慕课"),
    EXAM(11, "exam", "考试"),
    SPECIAL(12, "special", "专区"),
    VIDEO_COLLECTION(13, "videoCollection", "视频集锦"),
    TOPIC(14, "topic", "话题"),
    COURSE_LIST(15, "courselist", "课单"),
    DOCUMENTATION(16, "document", "文档"),
    OTHER(99, "other", "外部链接"),
    UNKNOWN(-1000, "UNKNOWN", "未知");
    private Integer moduleId;

    private String moduleName;

    private String text;

    ModuleIdEnum(Integer id, String name, String text) {
        this.moduleId = id;
        this.moduleName = name;
        this.text = text;
    }

    public Integer getModuleId() {
        return moduleId;
    }

    public void setModuleId(Integer moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static Integer getModuleId(String text) {
        ModuleIdEnum[] moduleIdEnums = ModuleIdEnum.values();
        for (ModuleIdEnum item : moduleIdEnums) {
            if (item.getText().equals(text)) {
                return item.getModuleId();
            }
        }
        return ModuleIdEnum.UNKNOWN.getModuleId();
    }

    public static String getModuleName(Integer moduleId) {
        ModuleIdEnum[] moduleIdEnums = ModuleIdEnum.values();
        for (ModuleIdEnum item : moduleIdEnums) {
            if (item.getModuleId().equals(moduleId)) {
                return item.getModuleName();
            }
        }
        return ModuleIdEnum.UNKNOWN.getModuleName();
    }

    public static Integer getModuleIdByModuleName(String moduleName) {
        if ("fileProd".equals(moduleName)) {
            return DOCUMENTATION.getModuleId();
        }
        ModuleIdEnum[] moduleIdEnums = ModuleIdEnum.values();
        for (ModuleIdEnum item : moduleIdEnums) {
            if (item.getModuleName().equals(moduleName)) {
                return item.getModuleId();
            }
        }
        return ModuleIdEnum.UNKNOWN.getModuleId();
    }

    public static ModuleIdEnum getModuleEnum(Integer moduleId) {

        if (null == moduleId) {
            return null;
        }

        for (ModuleIdEnum v : ModuleIdEnum.values()) {
            if (v.getModuleId().equals(moduleId)) {
                return v;
            }
        }

        return null;
    }
}
