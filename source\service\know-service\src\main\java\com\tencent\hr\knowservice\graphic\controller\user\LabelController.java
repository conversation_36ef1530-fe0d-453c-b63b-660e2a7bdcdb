package com.tencent.hr.knowservice.graphic.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.proxy.AdapterServiceApi;
import com.tencent.hr.knowservice.graphic.dto.LabelRecommendConditionDTO;
import com.tencent.hr.knowservice.graphic.service.GraphicService;
import com.tencent.hr.knowservice.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.security.auth.message.AuthException;
import javax.validation.Valid;
import java.util.Map;

/**
 * 标签管理
 */
@RestController
@RequestMapping("/api/graphic/user/lable/")
@Slf4j
public class LabelController {
    @Autowired
    AdapterServiceApi adapterServiceApi;

    @Autowired
    GraphicService graphicService;

    @Value("${extapi.hr-ai-recruit-center.host}")
    String recruitCenterHost;

//    /**
//     * 获取分类下的标签
//     * @param classifyFullPath
//     * @return
//     * @throws AuthException
//     */
//    @GetMapping("getlabels")
//    public String getLabels(@RequestParam("classify_full_path") String classifyFullPath){
//        return adapterServiceApi.getLabels(classifyFullPath, ActTypeEnum.GRAPHIC.getActType().toString());
//    }

    /**
     * 保存用户标签
     * @param labelName
     * @return
     * @throws AuthException
     */
    @GetMapping("savelabel")
    public String saveLabel(@RequestParam("label_name") String labelName){
//        String actType = ActTypeEnum.GRAPHIC.getActType().toString();
//        return adapterServiceApi.saveLabel(labelName,null, actType);
        String actType = ActTypeEnum.GRAPHIC.getActType().toString();
        labelName = labelName.trim();
        String tem = "";
        String res = "";
        for (int i = 0; i < labelName.length() ; i++) {
            if(labelName.charAt(i) !=',' && labelName.charAt(i) !=' ' && labelName.charAt(i) !='，'){
                tem+=labelName.charAt(i);
                if(i == labelName.length() - 1){
                    adapterServiceApi.saveLabel(tem,null, actType);
                }
            }else{
                res = adapterServiceApi.saveLabel(tem,null, actType);
                tem = "";
            }
        }
        return res;
    }

    /**
     * 匹配标签
     * @param name
     * @return
     * @throws AuthException
     */
    @GetMapping("auto_com_labels")
    public String autoComLabels(@RequestParam(value = "name") String name,
                                @RequestParam(value = "count") String count){
        return adapterServiceApi.autoComLabels(name, ActTypeEnum.GRAPHIC.getActType().toString(),count);
    }

}
