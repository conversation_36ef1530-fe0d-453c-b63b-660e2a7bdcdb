package com.tencent.hr.knowservice.graphic.constant;

/**
 * 缓存值
 * <AUTHOR>
 */
public enum CacheKeyEnum {
    /**
     *
     */
    ExtendContentList("common:extend-content:list:"),
    /**
     * 图文基本信息缓存前缀值
     */
    GraphicBasicINfo("graphic:graphic-info:basic:"),
    /**
     * 图文浏览缓存key
     */
    ViewGraphic("graphic:view:view-graphic"),

    /**
     * 图文新建、发布白名单缓存key
     */
    SUPER_ADMIN("graphic:superAdmin")
    ;



    /**
     * 缓存key值
     */
    private String keyName;

    CacheKeyEnum(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyName() {
        return keyName;
    }
}
