package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.tencent.hr.knowservice.businessCommon.proxy.ContentServiceApi;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dto.ContentSearchDto;
import com.tencent.hr.knowservice.businessCommon.service.ContentCenterService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RequestMapping("/api/businessCommon/common/content")
@Api(tags = {"内容资源"})
@RestController
@Slf4j
public class ContentController {
    @Autowired
    ContentCenterService contentCenterService;

    @Autowired
    ContentServiceApi contentServiceApi;

    /**
     * 获取内容中心操作签名
     * @param contentId
     * @param operate
     * @return
     */
    @GetMapping("/operatesignature")
    public String getOperateSignature(@RequestParam(value = "content_id", required = false) String contentId,
                                                    @RequestParam(value = "operate", required = true) String operate){
        return contentCenterService.getOperateSignature(contentId,operate);
    }

    /**
     * 从内容（portal-content）模块搜索内容
     * @param searchDto
     * @return
     */
    @PostMapping("/search")
    public String searchContentByGropMatrix(@RequestBody ContentSearchDto searchDto){
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        searchDto.setLoginStaffId(staffId);
        List<String> moduleIds=new ArrayList<>();
        if(searchDto.getModuleId()==null){
            moduleIds.add("1");
            moduleIds.add("2");
            moduleIds.add("3");
            moduleIds.add("4");
            moduleIds.add("7");
            moduleIds.add("8");
            moduleIds.add("9");
            moduleIds.add("16");
        }
        else {
            moduleIds.add(searchDto.getModuleId().toString());
        }
        searchDto.setModuleIds(moduleIds);
        return contentServiceApi.searchContentByGropMatrix(searchDto);
    }


    /**
     * 从内容（portal-content）模块搜索内容
     * @param searchDto
     * @return
     */
    @PostMapping("/mooc-search")
    public String moocSearchContentByGropMatrix(@RequestBody ContentSearchDto searchDto){
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        searchDto.setLoginStaffId(staffId);
        List<String> moduleIds=new ArrayList<>();
        if(searchDto.getModuleId()==null){
            moduleIds.add("1");
            moduleIds.add("8");
            moduleIds.add("16");
        }
        else {
            moduleIds.add(searchDto.getModuleId().toString());
        }
        searchDto.setModuleIds(moduleIds);
        return contentServiceApi.searchContentByGropMatrix(searchDto);
    }

    /**
     * 获取内容预览信息
     * @param contentId
     * @return
     */
    @GetMapping("/previewInfo")
    public String getPreviewInfo(@RequestParam(value = "content_id") String contentId, HttpServletRequest httpServletRequest){
        String userAgent = httpServletRequest.getHeader("User-Agent");
        return contentCenterService.getPreviewInfo(contentId,userAgent);
    }

    /**
     * 获取文件源码地址，用于下载
     * @param contentId
     * @return
     */
    @GetMapping("/urlForDownload")
    public String getUrlForDownload(@RequestParam(value = "content_id", required = true) String contentId){
        return contentCenterService.getContentUrlInfo(contentId);
    }
}
