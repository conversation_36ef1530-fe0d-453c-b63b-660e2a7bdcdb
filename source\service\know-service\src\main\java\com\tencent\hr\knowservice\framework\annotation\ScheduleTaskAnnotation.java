package com.tencent.hr.knowservice.framework.annotation;

import java.lang.annotation.*;

/**
 * @description: 定时任务的任务描述
 * @author: vincentyqwu
 * @createDate: 2020/6/12
 * @version: 1.0
 */


@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ScheduleTaskAnnotation {

    /**
     * 接口对应的appid
     * @return
     */
    String appId() default "";

    /**
     * 对应的模块Id
     * @return
     */
    String moduleId() default  "";

    /**
     * 对应的模块名
     * @return
     */

    String moduleName() default  "";

    /**
     * 对应的任务名称
     * @return
     */
    String taskName() default  "";

}
