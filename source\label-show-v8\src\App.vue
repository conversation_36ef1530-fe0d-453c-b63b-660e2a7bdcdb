<template>
  <div id="app">
    <sdc-label-show
      ref="sdcLabelShow"
      class="label-main"
      :actType="actType"
      :courseId="courseId"
      :labelNodeEnv="labelNodeEnv"
      :showBurialPoint="showBurialPoint"
      :dtTagsFn="dtTagsFn"
      :debug="debug"
      :isPreview="isPreview"
      :previewLbael="previewLbael"
    />
  </div>
</template>

<script>
export default {
  name: 'App',
  data () {
    return {
      LabelObj: {},
      dtTagsFn: () => {} // 埋点方法
    }
  },
  computed: {
    actType() {
      return this.LabelObj.actType || ''
    },
    courseId() {
      return this.LabelObj.courseId || ''
    },
    showBurialPoint() { // 是否需要埋点 为true时 dtTagsFn必须传
      return this.LabelObj.showBurialPoint || false
    },
    labelNodeEnv() {
      return this.LabelObj.labelNodeEnv || 'production'
    },
    debug() {
      return this.LabelObj.debug || false
    },
    isPreview() { // 是否是预览功能 预览功能只展示标签，不能订阅等操作
      return this.LabelObj.isPreview || false
    },
    previewLbael() { // 预览时，标签从外面传进来，无需从接口获取
      return this.LabelObj.previewLbael || []
    }
  },
  created () {
    window.addEventListener('updateLabelList', this.updateLabelList)
    if (typeof window !== 'undefined' && typeof window.LabelShow !== 'object') {
      window.LabelShow = {}
    }
    let parentNode = this.$root.$el.id
    if (parentNode === 'showLabel') {
      this.LabelObj = window.LabelShow
    } else {
      if (typeof window.LabelShow[parentNode] !== 'object') {
        window.LabelShow[parentNode] = {}
      }
      this.LabelObj = window.LabelShow[parentNode]
    }
    // 埋点方法
    this.dtTagsFn = this.LabelObj.dtTagsFn
  },
  methods: {
    updateLabelList(list) {
      this.$refs.sdcLabelShow && this.$refs.sdcLabelShow.getLabelList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
