package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.knowservice.businessCommon.dto.ExtendContentAddDTO;
import com.tencent.hr.knowservice.businessCommon.dto.ExtendDelDTO;
import com.tencent.hr.knowservice.businessCommon.dto.ExtendSetTopDTO;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/09/20/16:12
 * @version: 1.0
 */
@Service
public class FaceClassProxyService extends BaseProxyService{
    @Value("${extapi.Qlearning-V8.host}")
    private String host;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    private final String appInfoId = "6wUzvEAICYl6bDyBLqdP";

    private final String appInfoKey = "9mEKjVVhcGq8IFifzKCR";

    /**
     * 配置延申学习
     * @param addDTO
     * @return
     */
    public String courseConn(ExtendContentAddDTO addDTO){
        String api = "/api/ext-interface/recommend/course-conn";
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        Map<String,Object> params = new HashMap<>();
        params.put("timestamp",timestamp);
        params.put("appid", appInfoId);
        params.put("prod_id", addDTO.getProdId());
        params.put("prod_type", addDTO.getProdType());

        String signature = "";
        try {
            signature = QlearningApiService.getQLSignature(params, appInfoKey);
        } catch (Exception e) {
            throw new LogicException("QL签名错误" + e.getMessage());
        }

        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        String staffName = current.getStaffName();

        params.put("conn_products", addDTO.getConnProducts());
        params.put("signature", signature);
        params.put("staff_id", staffId);
        params.put("staff_name", staffName);

        HttpHeaders header = getESBHeader(appId, appToken, GatewayContext.current().getStaffId(), GatewayContext.current().getStaffName());
        String jsonBody = JsonUtils.objectToJson(params);
        return  postJsonStrWithHeaders(host,api,jsonBody,header);
    }

    /**
     * 删除延申学习
     * @param extendDelDTO
     * @return
     */
    public String deleteConn(ExtendDelDTO extendDelDTO){
        String api = "/api/ext-interface/recommend/delete-conn";
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        Map<String,Object> params = new HashMap<>();
        params.put("timestamp",timestamp);
        params.put("appid", appInfoId);
        params.put("id", extendDelDTO.getId());

        String signature = "";
        try {
            signature = QlearningApiService.getQLSignature(params, appInfoKey);
        } catch (Exception e) {
            throw new LogicException("QL签名错误" + e.getMessage());
        }

        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        String staffName = current.getStaffName();

        params.put("signature", signature);
        params.put("staff_id", staffId);
        params.put("staff_name", staffName);

        HttpHeaders header = getESBHeader(appId, appToken, GatewayContext.current().getStaffId(), GatewayContext.current().getStaffName());
        String jsonBody = JsonUtils.objectToJson(params);
        return  postJsonStrWithHeaders(host,api,jsonBody,header);
    }

    /**
     * 设置课程置顶
     * @param extendSetTopDTO
     * @return
     */
    public String courseConnSetTop(ExtendSetTopDTO extendSetTopDTO){
        String api = "/api/ext-interface/recommend/course-conn-set-top";
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        Map<String,Object> params = new HashMap<>();
        params.put("timestamp",timestamp);
        params.put("appid", appInfoId);
        params.put("id", extendSetTopDTO.getId());
        params.put("toped", extendSetTopDTO.getToped());

        String signature = "";
        try {
            signature = QlearningApiService.getQLSignature(params, appInfoKey);
        } catch (Exception e) {
            throw new LogicException("QL签名错误" + e.getMessage());
        }

        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        String staffName = current.getStaffName();

        params.put("signature", signature);
        params.put("staff_id", staffId);
        params.put("staff_name", staffName);

        HttpHeaders header = getESBHeader(appId, appToken, GatewayContext.current().getStaffId(), GatewayContext.current().getStaffName());
        String jsonBody = JsonUtils.objectToJson(params);
        return  postJsonStrWithHeaders(host,api,jsonBody,header);
    }
}
