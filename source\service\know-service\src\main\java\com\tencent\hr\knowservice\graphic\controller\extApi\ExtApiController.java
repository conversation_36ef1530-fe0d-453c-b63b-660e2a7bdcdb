package com.tencent.hr.knowservice.graphic.controller.extApi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.graphic.dto.MyGraphicAccessRecordsPageDTO;
import com.tencent.hr.knowservice.graphic.dto.extapi.ModuleIdAndItemIdDTO;
import com.tencent.hr.knowservice.graphic.dto.extapi.RelationCourseGraphicDTO;
import com.tencent.hr.knowservice.graphic.dto.extapi.RelationGraphicByCoursesDTO;
import com.tencent.hr.knowservice.graphic.service.GraphicService;
import com.tencent.hr.knowservice.graphic.dto.extapi.RelationGraphicDTO;
import com.tencent.hr.knowservice.graphic.service.MyService;
import com.tencent.hr.knowservice.netcourse.dto.NetCourseLearnRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @description: 对外接口
 * @author: shizhouwang
 * @createDate: 2022/12/9
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/ext/graphic")
public class ExtApiController {

    @Autowired
    GraphicService graphicService;

    @Autowired
    MyService myService;

    /**
     * 获取课程所关联的图文
     *
     * @param moduleId
     * @param itemId
     */
    @GetMapping("/get_relation_graphic")
    public TransDTO<IPage<RelationGraphicDTO>> getRelationGraphic(@RequestParam("module_id") Integer moduleId,
                                                                  @RequestParam("item_id") String itemId,
                                                                  @RequestParam(value = "order_by_desc", required = false) String orderByDesc,
                                                                  @RequestParam("page_no") Integer pageNo,
                                                                  @RequestParam("page_size") Integer pageSize
    ) {
        TransDTO<IPage<RelationGraphicDTO>> dto = new TransDTO<>();
        IPage<RelationGraphicDTO> page = graphicService.getRelationGraphic(moduleId, itemId,orderByDesc, pageNo, pageSize);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(page);
    }

    /**
     * 获取用户的浏览记录
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/my_graphic_access_records")
    public TransDTO<IPage<MyGraphicAccessRecordsPageDTO>> getMyGraphicAccessRecords(@RequestParam("page_no") Integer pageNo, @RequestParam("page_size") Integer pageSize) {
        IPage<MyGraphicAccessRecordsPageDTO> page = myService.getMyGraphicAccessRecords(pageNo, pageSize);
        return new TransDTO<IPage<MyGraphicAccessRecordsPageDTO>>().withSuccess(true).withCode(HttpStatus.SC_OK).withData(page);
    }

    /**
     * 通过fromType获取图文列表
     * @param fromType
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/get_graphic_by_from_type")
    public TransDTO<IPage<RelationGraphicDTO>> getGraphicByFromType(@RequestParam("from_type") String fromType,
                                                                    @RequestParam(value = "staff_id",required = false) Integer staffId,
                                                                    @RequestParam("page_no") Integer pageNo,
                                                                    @RequestParam("page_size") Integer pageSize){
        TransDTO<IPage<RelationGraphicDTO>> dto = new TransDTO<>();
        IPage<RelationGraphicDTO> page = graphicService.getGraphicByFromType(fromType, staffId,pageNo, pageSize);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(page);
    }
    /**
     * 获取多个课程所关联的图文
     *
     */
    @PostMapping("/get_multiple_courses_relation_graphic")
    public TransDTO<List<RelationGraphicByCoursesDTO>> getMultipleCoursesRelationGraphic(@RequestBody List<ModuleIdAndItemIdDTO> moduleIdAndItemIdDTOS
    ) {
        TransDTO<List<RelationGraphicByCoursesDTO>> dto = new TransDTO<>();
        List<RelationGraphicByCoursesDTO> page = graphicService.getMultipleCoursesRelationGraphic(moduleIdAndItemIdDTOS);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(page);
    }
    /**
     * 获取某个时间段的关联本站内容的原创笔记
     * @param beginTime
     * @param endTime
     * @return
     */
    @GetMapping("/get_relation_course_graphic")
    public TransDTO<List<RelationCourseGraphicDTO>> getRelationCourseGraphic(@RequestParam(value = "begin_time") String beginTime,
                                                                           @RequestParam(value = "end_time")String endTime
    ){
       List<RelationCourseGraphicDTO> data = graphicService.getRelationCourseGraphic(beginTime,endTime);

       return new TransDTO<List<RelationCourseGraphicDTO>>().withCode(HttpStatus.SC_OK).withSuccess(true).withData(data);
    }
}
