package com.tencent.hr.knowservice.framework.config;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置mybatisplus初始化配置
 * <AUTHOR>
 *
 */
@Configuration
public class MybaisPlusConfig {

	/**
	 * 用于支持分页查询
	 * @return
	 */
	@Bean
	public PaginationInterceptor paginationInterceptor () {
		PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
		// 设置最大单页限制数量，默认 500 条，-1 不受限制
		paginationInterceptor.setLimit(-1);
		paginationInterceptor.setCountSqlParser(new JsqlParserCountOptimize(true));
		return paginationInterceptor;
	}
}
