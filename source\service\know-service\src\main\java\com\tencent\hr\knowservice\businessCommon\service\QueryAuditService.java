package com.tencent.hr.knowservice.businessCommon.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.hr.knowservice.businessCommon.dto.QueryAuditDto;
import com.tencent.hr.knowservice.framework.service.AsyncService;
import com.tencent.hr.knowservice.utils.DeviceUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.JsonUtils;
import io.swagger.models.auth.In;
import jodd.introspector.Fields;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.function.Consumer;


@Service
public class QueryAuditService {

    @Value("${extapi.hrseclog}")
    private String hrseclogHost;

    @Value("${com.appSetting.appName}")
    private String appName;

    @Value("${com.appSetting.appId}")
    private String appId;


    @Autowired
    private AsyncService asyncService;
    @Data
    static class AuditDtaResultDto {

        private String key;

        private String value;

    }
    @Data
    static class AuditResultDto {

        private Integer code;

        private String msg;

        private List<AuditDtaResultDto> data;
    }

    /**
     * 安全设计日志
     * @param staffId
     * @param staffName
     * @param size
     * @param path 文件下载地址
     */
    public QueryAuditDto queryAudit(Integer staffId, String staffName, int size, String path, String Fields) {
        String url = hrseclogHost + "/api/SecurityLog/QueryAudit";

        HashMap<String, Object> params = new HashMap<>();
        params.put("AppKey", appId);
        params.put("AppName", appName);
        params.put("ModuleName", "mooc");
        params.put("StaffID", staffId);
        params.put("StaffName", staffName);
        params.put("FieldList", Fields);
        params.put("RowCount", size);

        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("key", "部门");
        hashMap.put("value", "部门");
        params.put("BusinessData", Arrays.asList(hashMap));
        params.put("ExtraMsg1", null);
        params.put("ExtraMsg2", null);
        params.put("ExtraMsg3", null);
        params.put("ExtraMsg4", null);
        params.put("ExtraMsg5", null);
        params.put("ExtraMsg6", null);
        params.put("ExtraMsg9", null);
        params.put("ExtraMsg8", null);
        params.put("ExtraMsg9", path); //下载地址
        //下载地址

        String json = JsonUtils.objectToJson(params);
        String result = HttpUtil.sendPostByHttpClient(url, json, null);

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            AuditResultDto resultDto = objectMapper.readValue(result, AuditResultDto.class);
            QueryAuditDto queryAuditDto = new QueryAuditDto();
            queryAuditDto.setCode(resultDto.getCode());
            queryAuditDto.setMsg(resultDto.getMsg());
            if (CollectionUtils.isNotEmpty(resultDto.getData())) {
                resultDto.getData().forEach(o -> {if (StringUtils.equals(o.getKey(), "pc")) {queryAuditDto.setPc(o.getValue());}
                    if (StringUtils.equals(o.getKey(), "mobile")) {queryAuditDto.setMobile(o.getValue());}});
            }
            return queryAuditDto;
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    /**
     * 安全设计日志
     * @param staffId
     * @param staffName
     * @param size
     * @param path 文件下载地址
     */
    public boolean queryAudit(Integer staffId, String staffName, int size, String path, String fields, HttpServletRequest request, HttpServletResponse response, Consumer<Integer> consumer) throws IOException {
        QueryAuditDto queryAuditDto = queryAudit(staffId, staffName, size, path, fields);
        if (queryAuditDto != null) {
            if (consumer != null) {
                asyncService.run(() ->{
                    consumer.accept(queryAuditDto.getCode());
                });
            }
            if (queryAuditDto.getCode() != null && queryAuditDto.getCode() == 0) {
                return true;
            } else {
                boolean mobile = DeviceUtil.isMobile(request);
                URL url = new URL(mobile ? queryAuditDto.getMobile() : queryAuditDto.getPc());
                String securityLog= url.getFile();
                response.sendRedirect("https://test-hrseclog.woa.com" + securityLog);
                return false;
            }
        } else {
            return false;
        }
    }
}
