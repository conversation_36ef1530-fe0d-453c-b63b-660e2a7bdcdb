package com.tencent.hr.knowservice.courseInteraction.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.SelectContent;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/10/13/12:53
 * @version: 1.0
 */
@Data
public class InteractiveInfoDto {
    /**
     * 唯一互动规则id
     */
    private String _id;

    /**
     * 课程类型
     */
    private Integer actType;

    /**
     * 课程类型名称
     */
    private String actTypeName;

    /**
     * 课程ig
     */
    private String courseId;

    /**
     * 某一个时间点的互动配置唯一标识（支持同一个时间点，多个不同的配置）
     */
    private String interactiveId;

    /**
     * 互动时间点
     */
    private Integer activeTime;
    /**
     * 互动简介
     */
    private String introduction;
    /**
     * 互动英文
     */
    private String introductionEn;
    /**
     * 互动简介提示
     */
    private String continueStudyingTips;
    /**
     * 互动简介提示（英文）
     */
    private String continueStudyingTipsEn;
    /**
     * 互动标题
     */
    private String title;
    /**
     * 互动标题（英文）
     */
    private String titleEn;

    /**
     * 选择类型互动的具体内容值
     */
    private List<SelectContent> selectContent;

    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String createdAt;
}
