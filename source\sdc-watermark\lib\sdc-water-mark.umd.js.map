{"version": 3, "sources": ["webpack://sdc-water-mark/webpack/universalModuleDefinition", "webpack://sdc-water-mark/webpack/bootstrap", "webpack://sdc-water-mark/./node_modules/@vue/cli-service/lib/commands/build/setPublicPath.js", "webpack://sdc-water-mark/./packages/componentPage/src/index.vue?76eb", "webpack://sdc-water-mark/packages/componentPage/src/index.vue", "webpack://sdc-water-mark/./packages/componentPage/src/index.vue?2f53", "webpack://sdc-water-mark/./node_modules/vue-loader/lib/runtime/componentNormalizer.js", "webpack://sdc-water-mark/./packages/componentPage/src/index.vue", "webpack://sdc-water-mark/./packages/componentPage/index.js", "webpack://sdc-water-mark/./packages/index.js", "webpack://sdc-water-mark/./node_modules/@vue/cli-service/lib/commands/build/entry-lib.js"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFA;;AAEA;AACA;AACA,MAAM,KAAuC,EAAE,yBAQ5C;;AAEH;AACA;AACA,IAAI,qBAAuB;AAC3B;AACA;;AAEA;AACe,sDAAI;;;ACrBnB,+BAA+B,6BAA6B,iBAAiB,gCAAgC;AAC7G;AACA;;;;;;;ACIe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;AClQkL,CAAgB,uGAAG,EAAC,C;;ACAvM;;AAEA;AACA;AACA;;AAEe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AC/FmF;AAC3B;AACL;;;AAGnD;AAC6F;AAC7F,gBAAgB,kBAAU;AAC1B,EAAE,wCAAM;AACR,EAAE,MAAM;AACR,EAAE,eAAe;AACjB;AACA;AACA;AACA;;AAEA;;AAEe,uE;;AClBf;AAC0C;AAC1C;AACA,iBAAY;AACZ,kBAAkB,iBAAY,OAAO,iBAAY;AACjD;AACA;AACe,mE;;ACPf;AACA;;AAE2C;;AAE3C;AACA,EAAE,aAAY;AACd;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEe;AACf;AACA,EAAE,2BAAY;AACd,CAAC;;;;ACtBuB;AACA;AACT,yFAAG;AACI", "file": "sdc-water-mark.umd.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"sdc-water-mark\"] = factory();\n\telse\n\t\troot[\"sdc-water-mark\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"fb15\");\n", "// This file is imported into lib/wc client bundles.\n\nif (typeof window !== 'undefined') {\n  var currentScript = window.document.currentScript\n  if (process.env.NEED_CURRENTSCRIPT_POLYFILL) {\n    var getCurrentScript = require('@soda/get-current-script')\n    currentScript = getCurrentScript()\n\n    // for backward compatibility, because previously we directly included the polyfill\n    if (!('currentScript' in document)) {\n      Object.defineProperty(document, 'currentScript', { get: getCurrentScript })\n    }\n  }\n\n  var src = currentScript && currentScript.src.match(/(.+\\/)[^/]+\\.js(\\?.*)?$/)\n  if (src) {\n    __webpack_public_path__ = src[1] // eslint-disable-line\n  }\n}\n\n// Indicate to webpack that this file can be concatenated\nexport default null\n", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"component-wrapper\"})\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "/* eslint-disable no-prototype-builtins */\r\n<template>\r\n  <div class=\"component-wrapper\"></div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'sdcWaterMark',\r\n  props: {\r\n    // 被覆盖水印的目标元素的id\r\n    targetId: {\r\n      required: false\r\n    },\r\n    // 被覆盖水印的目标元素的class\r\n    targetClass: {\r\n      required: false\r\n    },\r\n    // 水印文字\r\n    text: {\r\n      type: String\r\n    },\r\n    // 水印图案的用户配置项\r\n    canvasUserOptions: Object,\r\n    // 水印元素的用户配置项\r\n    wmUserOptions: Object,\r\n    // 是否手动初始化\r\n    // 因为水印元素依赖于目标元素，如果目标元素没有渲染完成，那么水印元素不能正常工作，所以用户可根据实际情况进行手动初始化\r\n    isManualInit: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 水印元素的id\r\n      watermarkId: '',\r\n      // 水印图案，由canvas生成的url\r\n      url: '',\r\n      // 被覆盖水印的目标元素\r\n      $target: undefined,\r\n      // 水印元素\r\n      $wm: undefined,\r\n      // 水印图案的配置项\r\n      canvasOptions: {\r\n        width: 200,\r\n        height: 160,\r\n        fillStyle: 'rgba(12, 12, 12, 0.1)',\r\n        font: '24px Microsoft Yahei',\r\n        translate: {\r\n          x: 20,\r\n          y: 20\r\n        },\r\n        rotateDegree: 39\r\n      },\r\n      // 水印元素的配置项\r\n      wmOptions: {\r\n        'z-index': 99999\r\n      },\r\n      modifyCallback: null,\r\n      // 是否关闭监控告警\r\n      isCloseModifyAlert: false,\r\n      destroyed: false\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * [init 进行一些初始化操作]\r\n     * @return {[type]} [description]\r\n     */\r\n    init() {\r\n      if (this.targetId) {\r\n        // 生成水印元素的id，用于监控该元素是否被删除\r\n        // 后缀是为了增强id随机性\r\n        this.watermarkId = this.targetId + '_watermark_xx512'\r\n        // 获取到目标元素\r\n        this.$target = document.getElementById(this.targetId)\r\n      } else if (this.targetClass) {\r\n        this.watermarkId = this.targetClass + '_watermark_xx512'\r\n        this.$target = document.getElementsByClassName(this.targetClass)[0]\r\n      }\r\n      // 生成水印图案的配置项\r\n      this.createCanvasOption()\r\n      // 生成水印元素的配置项\r\n      this.createWmOption()\r\n      // 生成水印图案的url\r\n      this.url = this.createCanvasDataUrl()\r\n    },\r\n\r\n    /**\r\n     * [addWatermark 添加水印]\r\n     */\r\n    addWatermark() {\r\n      if (this.$target) {\r\n        this.addWatermarkToTarget()\r\n        this.observeWaterMark()\r\n      }\r\n    },\r\n\r\n    /**\r\n     * [createCanvasOption 根据用户传入的参数，生成水印图案的配置项]\r\n     * @return {[type]} [description]\r\n     */\r\n    createCanvasOption() {\r\n      for (const key in this.canvasUserOptions) {\r\n        // eslint-disable-next-line no-prototype-builtins\r\n        if (this.canvasOptions.hasOwnProperty(key)) {\r\n          this.canvasOptions[key] = this.canvasUserOptions[key]\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * [createWmOption 根据用户传入的参数，生成水印元素的配置项]\r\n     * @return {[type]} [description]\r\n     */\r\n    createWmOption() {\r\n      for (const key in this.wmUserOptions) {\r\n        // eslint-disable-next-line no-prototype-builtins\r\n        if (this.wmOptions.hasOwnProperty(key)) {\r\n          this.wmOptions[key] = this.wmUserOptions[key]\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * [createCanvasDataUrl 生成水印图案的url]\r\n     * @return {[type]} [description]\r\n     */\r\n    createCanvasDataUrl() {\r\n      // 创建canvas\r\n      const canvas = document.createElement('canvas')\r\n      canvas.width = this.canvasOptions.width\r\n      canvas.height = this.canvasOptions.height\r\n      const ctx = canvas.getContext('2d')\r\n      ctx.fillStyle = this.canvasOptions.fillStyle\r\n      ctx.font = this.canvasOptions.font\r\n      ctx.translate(this.canvasOptions.translate.x, this.canvasOptions.translate.y)\r\n      ctx.rotate(this.canvasOptions.rotateDegree * Math.PI / 180)\r\n      ctx.fillText(this.text, 20, 20)\r\n      return canvas.toDataURL('image/png')\r\n    },\r\n\r\n    /**\r\n     * [addWatermarkToTarget 在目标元素上面添加水印层，这种方式没有直接修改目标元素的background，这样可以单独操纵水印元素]\r\n     */\r\n    addWatermarkToTarget() {\r\n      // 创建水印覆盖目标元素\r\n      const $wm = document.createElement('div')\r\n      $wm.setAttribute('id', this.watermarkId)\r\n      // $wm.style.width = '100%'\r\n      $wm.style.width = getComputedStyle(this.$target).width\r\n      // 注意：此处不能使用$wm.height('100%');那样只会渲染一屏\r\n      $wm.style.height = '100%'\r\n      // $wm.style.height = getComputedStyle(this.$target).height\r\n      $wm.style.position = 'absolute'\r\n      $wm.style.top = '0px'\r\n      $wm.style.left = '0px'\r\n      $wm.style['pointer-events'] = 'none'\r\n      for (const key in this.wmOptions) {\r\n        $wm.style[key] = this.wmOptions[key]\r\n      }\r\n      // this.url = this.createCanvasDataUrl();\r\n      $wm.style.background = 'url(' + this.url + ') repeat top left'\r\n      this.$wm = $wm\r\n      this.$target.append($wm)\r\n    },\r\n\r\n    /**\r\n     * [observeWaterMark 监控水印元素，从两方面防止被修改：1.属性被修改，2.元素被删除]\r\n     * @param  {[type]} $wm      [水印元素]\r\n     * @param  {[type]} selector [被添加水印的目标元素的选择器]\r\n     * @param  {[type]} url      [水印的url]\r\n     * @return {[type]}          [description]\r\n     */\r\n    observeWaterMark() {\r\n      const obConfig = {\r\n        attributes: true,\r\n        characterData: true\r\n      }\r\n      // 增加监控，防止水印被修改\r\n      const observer = new MutationObserver((mutations, observer) => {\r\n        for (const m of mutations) {\r\n          // 先取消监听，避免死循环\r\n          observer.disconnect()\r\n          // 此处用了一点小技巧：直接删除$wm元素，删除动作会引发下面的监控，进而重新生成元素\r\n          this.$wm.parentNode.removeChild(this.$wm)\r\n          // 如果用户删除或者修改了id，那么下面n.id==this.watermarkId将会为flase，那么不能重新渲染水印元素，所以此处要对这一情况单独处理\r\n          if (m.attributeName === 'id') {\r\n            this.addWatermarkToTarget()\r\n            this.observeWaterMark()\r\n          }\r\n        }\r\n      })\r\n      observer.observe(this.$wm, obConfig)\r\n\r\n      // 进一步加强监控，防止元素被删除\r\n      // 因为\r\n      const pObserver = new MutationObserver((mutations) => {\r\n        for (const m of mutations) {\r\n          if (m.type === 'childList' && m.removedNodes.length > 0) {\r\n            for (const n of m.removedNodes) {\r\n              if (n.id === this.watermarkId || n.id === 'watermark-warpper') {\r\n                pObserver.disconnect()\r\n                // 如果是代码内删除的水印则不重新生成水印\r\n                if (!this.destroyed) {\r\n                  this.addWatermarkToTarget()\r\n                  this.observeWaterMark()\r\n                }\r\n                if (!this.isCloseModifyAlert) {\r\n                  // 如果是删除了父元素\r\n                  if (n.id === 'watermark-warpper') {\r\n                    setTimeout(() => {\r\n                      location.reload()\r\n                    }, 1000)\r\n                  }\r\n                } else {\r\n                  this.isCloseModifyAlert = false\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      })\r\n      const pObConfig = {\r\n        childList: true,\r\n        subtree: true\r\n      }\r\n      const videoBox = document.getElementById('videoBox')\r\n      if (videoBox) {\r\n        pObserver.observe(videoBox, pObConfig)\r\n      } else {\r\n        pObserver.observe(this.$target, pObConfig)\r\n      }\r\n    },\r\n    createWatermark() {\r\n      this.destroyed = false\r\n      this.init()\r\n      this.addWatermark()\r\n    },\r\n    refreshWatermark() {\r\n      if (this.$wm) {\r\n        this.$wm.parentNode.removeChild(this.$wm)\r\n      } else {\r\n        this.init()\r\n        this.addWatermark()\r\n      }\r\n    },\r\n    destroyWatermark() {\r\n      this.destroyed = true\r\n      this.$wm.parentNode.removeChild(this.$wm)\r\n    }\r\n  },\r\n  mounted() {\r\n    if (!this.isManualInit) {\r\n      this.init()\r\n      this.addWatermark()\r\n    }\r\n  }\r\n}\r\n</script>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent(\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier /* server only */,\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options =\n    typeof scriptExports === 'function' ? scriptExports.options : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) {\n    // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n          injectStyles.call(\n            this,\n            (options.functional ? this.parent : this).$root.$options.shadowRoot\n          )\n        }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection(h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=4211fb62\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "// 导入组件  组件必须声明name\r\nimport sdcWaterMark from './src/index.vue'\r\n// 为组件提供install安装方法  供按需引入\r\nsdcWaterMark.install = function(Vue) {\r\n    Vue.component(sdcWaterMark.name, sdcWaterMark)\r\n}\r\n// 导出组件\r\nexport default sdcWaterMark", "// 整个包的入口\r\n// 统一导出\r\n\r\nimport sdcWaterMark from \"./componentPage\";\r\n \r\nconst components = [\r\n  sdcWaterMark\r\n]\r\n// 定义install方法 接收Vue作为参数，如果使用use注册插件，那么所有的组件都会被注册\r\nconst install = function (Vue) {\r\n  if (install.installed) return;\r\n  components.map(component => Vue.component(component.name, component))\r\n}\r\n\r\n// auto install\r\nif (typeof window !== 'undefined' && window.Vue) {\r\n  install(window.Vue);\r\n}\r\n\r\nexport default {\r\n  install,\r\n  sdcWaterMark\r\n}\r\n\r\n", "import './setPublicPath'\nimport mod from '~entry'\nexport default mod\nexport * from '~entry'\n"], "sourceRoot": ""}