package com.tencent.hr.knowservice.businessCommon.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * act_activity
 *
 *
 */
@Data
public class ActActivity implements Serializable {
    /**
     * 活动Id
     */
    private Integer activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动所属系统
     */
    private String systemName;

    /**
     * 活动简介
     */
    private String brief;

    /**
     * 活动详情
     */
    private String description;

    /**
     * 举办城市
     */
    private String city;

    /**
     * 活动地点
     */
    private String location;

    /**
     * 活动日期
     */
    private Date activityDate;

    /**
     * 活动图片
     */
    private String photoUrl;

    /**
     * 活动级别
     */
    private Integer activityLevel;

    /**
     * 是否开放给目标学员自由报名
     */
    private Integer canRegisted;

    /**
     * 是否报名需要直接上司审批
     */
    private Integer needAppovel;

    /**
     * 活动时长
     */
    private Long estDur;

    /**
     * 活动负责人Id
     */
    private Integer headTeacherId;

    /**
     * 活动负责人姓名
     */
    private String headTeacherName;

    /**
     * 目标学员
     */
    private String targetIds;

    /**
     * 内容类型
     */
    private Integer pdiSubLevel;

    /**
     * 是否有报名人数限制
     */
    private Integer isLimitStudentCount;

    /**
     * 最多报名人数
     */
    private Integer maxStudentCount;

    /**
     * 报名满额后是否可以等待队列
     */
    private Integer allowWaitingList;

    /**
     * 预计开始时间
     */
    private Date startTime;

    /**
     * 预计结束时间
     */
    private Date endTime;

    /**
     * 报名开始时间
     */
    private Date registStartDate;

    /**
     * 报名截止时间
     */
    private Date registLastDate;

    /**
     * 注销截止时间
     */
    private Date cancelLastDate;

    /**
     * 所属组织单元
     */
    private Integer deptId;

    /**
     * 内部分享人姓名（冗余）
     */
    private String innerTeacher;

    /**
     * 外部分享人姓名
     */
    private String outerTeacher;

    /**
     * 补充说明
     */
    private String remark;

    /**
     * 附加信息
     */
    private String ext;

    /**
     * 总反馈分
     */
    private BigDecimal totalScore;

    /**
     * 组织反馈分
     */
    private BigDecimal organizeScore;

    /**
     */
    private BigDecimal activityScore;

    /**
     * 讲师反馈分
     */
    private BigDecimal lecturerScore;

    /**
     * 反馈分是否自动计算
     */
    private Integer isScoreAutoCalculate;

    /**
     * 反馈分修改人Id
     */
    private Integer scoreUpdatedStaffId;

    /**
     * 反馈分修改人姓名
     */
    private String scoreUpdatedStaffName;

    /**
     * 反馈分修改时间
     */
    private Date scoreUpdateTime;

    /**
     * 是否取消（ 0：正常 1： 取消）
     */
    private Integer cancel;

    /**
     * 签到次数
     */
    private Integer signCount;

    /**
     * 授课形式 1 线下面试 2 在线授课
     */
    private Integer teachingType;

    /**
     * 腾讯会议id
     */
    private Integer meetinginfoId;

    /**
     * 主持人类型，多选用分号分隔 1 班主任 2 讲师
     */
    private String hostType;

    /**
     * 拒绝加入会议的级别 1 拒绝外部人员加入 2 拒绝非目标学员加入 3 拒绝未报名的学员加入
     */
    private Integer refuseJoinType;

    /**
     * 是否显示推荐
     */
    private Integer isShowRecommend;

    /**
     * 老系统id
     */
    private Integer oldSyncId;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    /**
     *
     * @mbg.generated Mon Aug 21 12:18:42 CST 2023
     */
    private static final long serialVersionUID = 1L;


}