package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActComment;
import com.tencent.hr.knowservice.businessCommon.dto.common.*;
import com.tencent.hr.knowservice.businessCommon.service.MultiLang.MultiLanguageService;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.mapper.ActCommentMapper;
import com.tencent.hr.knowservice.mooc.dao.mapper.MoocCourseAdminsMapper;
import com.tencent.hr.knowservice.mooc.dto.moocCourse.MoocCourseAdminsDto;
import com.tencent.hr.knowservice.proFile.dao.mapper.ProdFileSummaryMapper;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评论
 */
@Slf4j
@Service
public class CommentService {

    private Set<String> existKeys = Collections.synchronizedSet(new HashSet<String>());

    @Resource
    private ActCommentMapper commentMapper;

    @Resource
    ProdFileSummaryMapper prodFileSummaryMapper;

    @Autowired
    private ActPraiseService actPraiseService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    MultiLanguageService multiLangService;

    @Resource
    private MoocCourseAdminsMapper moocCourseAdminsMapper;

    /**
     * 新增评论
     *
     * @param actCommentDto
     */
    public Integer addComment(ActCommentDto actCommentDto) {
        if (StringUtils.isBlank(actCommentDto.getContent())) {
            throw new RuntimeException("评论内容不能为空！");
        }
        if (null == actCommentDto.getActType()){
            throw new RuntimeException("课程类型不能为空！");
        }
        if (StringUtils.isBlank(actCommentDto.getActId())){
            throw new RuntimeException("课程id不能为空！");
        }

        if (actCommentDto.getPid() != null){
            ActComment replyComment = commentMapper.findActCommentById(String.valueOf(actCommentDto.getPid()));
            if (replyComment == null){
                throw new LogicException(multiLangService.getApiRecourseText("Api_Mooc_Error_CommentNotExisit","评论不存在!"));
            }
        }

        Date date = new Date();
        ActComment actComment = new ActComment();
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        String staffName = current.getStaffName();
        actComment.setActId(actCommentDto.getActId());
        actComment.setActAuthorId(actCommentDto.getAuthorId());
        actComment.setAppId(actCommentDto.getAppId());
        actComment.setFromMobile(actCommentDto.getFromMobile());
        actComment.setContent(actCommentDto.getContent());
        actComment.setActType(actCommentDto.getActType());
        actComment.setPid(actCommentDto.getPid());
        actComment.setStaffId(staffId);
        actComment.setEmpName(staffName);
        actComment.setReplyStaffId(actCommentDto.getReplyStaffId());
        actComment.setReplyStaffName(actCommentDto.getReplyStaffName());
        if (StringUtils.isNotBlank(actCommentDto.getStaffName())) {
            actComment.setEmpName(actCommentDto.getStaffName());
        }
        actComment.setCreatedAt(date);
        actComment.setCreatorId(staffId);
        actComment.setCreatorName(staffName);
        actComment.setUpdatedAt(date);
        actComment.setUpdateId(staffId);
        actComment.setUpdateName(staffName);
        actComment.setNeedShow((byte) 1);
        actComment.setReplyCount(0);
        actComment.setPraiseCount(0);
        actComment.setEnabled((byte) 1);
        commentMapper.insert(actComment);
        //处理回复的评论
        if (actCommentDto.getPid() != null) {
            ActComment updateActComment = new ActComment();
            updateActComment.setId(actComment.getId());
            updateActComment.setFullPath(actCommentDto.getPid() + "," + actComment.getId());
            commentMapper.updateByPrimaryKeySelective(updateActComment);
        }
        cleanCache(actComment.getId());
        //同步增加评论数据
        sumCommentCount(actCommentDto,true);
        return actComment.getId();
    }

    /**
     * 通用计算评论数，每一个类型自己补充
     * @param actCommentDto
     * @param hasAdd
     */
    private void sumCommentCount(ActCommentDto actCommentDto,boolean hasAdd) {
        int actType = actCommentDto.getActType();
        //文档数据增加
        if (actType == 10) {
            prodFileSummaryMapper.sumCommentCount(actCommentDto.getActId(),hasAdd);
        }
    }

    /**
     * 移除缓存
     */
    synchronized private void cleanCache(Integer commentId) {
        if (commentId != null) {
            try {
                ActComment actComment = commentMapper.selectByPrimaryKey(commentId);
                int hash = Objects.hash(actComment.getActType(), actComment.getActId());
                String hashCode = String.valueOf(hash);
                if (redisUtil.hasKey(hashCode)) {
                    redisUtil.del(hashCode);
                }
            } catch (Exception e) {
                log.error("移除key异常：{}",e);
            }
        }
    }

    /**
     * 获取评论列表
     *
     * @param actCommentDto
     */
    public CommentPageDto<ActCommentResDto> getComments(ActCommentDto actCommentDto) {
        if (actCommentDto.getPageNo() <= 0) {
            actCommentDto.setPageNo(1);
        }
        if (actCommentDto.getPageSize() <= 0) {
            actCommentDto.setPageSize(20);
        }
        String key = String.valueOf(actCommentDto.hashCode());
        if (redisUtil.hasKey(key)) {
            List<ActCommentResDto> actComments = (List<ActCommentResDto>) redisUtil.get(key);
            return searchComment(actCommentDto, actComments);
        }
        List<ActCommentResDto> comments = commentMapper.getComments(actCommentDto);
        //所有点赞数据
        ArrayList<ActPraiseDto> actPraiseList = actPraiseService
                .checkPraisedList(String.valueOf(actCommentDto.getActType()), actCommentDto.getActId(), 1);
        Map<Integer, Long> countObj = actPraiseList.stream()
                .collect(Collectors.groupingBy(ActPraiseDto::getInteractId, Collectors.counting()));
        comments.forEach(actComment -> {
            if (countObj.containsKey(actComment.getId())) {
                actComment.setPraiseCount(countObj.get(actComment.getId()));
            }
        });
        //个人点赞数据
        ArrayList<ActPraiseDto> actPraiseDtos = actPraiseService
                .checkPraisedBatch(String.valueOf(actCommentDto.getActType()), actCommentDto.getActId(), 1);
        List<Object> objects = actPraiseDtos.stream().map(o ->
                o.getInteractId()
        ).collect(Collectors.toList());
        comments.forEach(actComment -> {
            actComment.setIsLike(objects.contains(actComment.getId()));
            //回复里的点赞
            if (actComment.getReplyComments() != null && actComment.getReplyComments().size() > 0) {
                actComment.getReplyComments().forEach(replyItem->{
                    replyItem.setIsLike(objects.contains(replyItem.getId()));
                });
            }
        });
        //缓存时长60秒
        redisUtil.set(key, comments, 300);
        existKeys.add(key);
        return searchComment(actCommentDto, comments);
    }

    /**
     * 评论排序
     *
     * @param actCommentDto
     * @param comments
     * @return
     */
    private List<ActCommentResDto> commentOrder(ActCommentDto actCommentDto, List<ActCommentResDto> comments) {
        //排序 时间排序
        if (actCommentDto.getOrderType() != null && actCommentDto.getOrderType() == 1) {
            comments = comments.stream().sorted(Comparator.comparing(ActCommentResDto::getCreatedAt).reversed())
                    .collect(Collectors.toList());
        }
        //排序 点赞排序
        if (actCommentDto.getOrderType() != null && actCommentDto.getOrderType() == 2) {
            comments = comments.stream().sorted(Comparator.comparing(ActCommentResDto::getPraiseCount).reversed())
                    .collect(Collectors.toList());
        }
        //排序 评论最多
        if (actCommentDto.getOrderType() != null && actCommentDto.getOrderType() == 3) {
            comments = comments.stream().sorted(Comparator.comparing(ActCommentResDto::getReplyCount).reversed())
                    .collect(Collectors.toList());
        }
        return comments;
    }

    /**
     * 过滤评论
     *
     * @param actCommentDto
     * @param comments
     * @return
     */
    private List<ActCommentResDto> filterComment(ActCommentDto actCommentDto, List<ActCommentResDto> comments) {
        //返回复数据
        if (actCommentDto.getPid() != null) {
            ActCommentResDto resDto = comments.stream().filter(o -> o.getId().equals(actCommentDto.getPid()))
                    .findFirst().orElse(new ActCommentResDto());
            comments =
                    CollectionUtils.isEmpty(resDto.getReplyComments()) ? new ArrayList<>() : resDto.getReplyComments();
        }
        //全部不传，0-置顶、1-隐藏、2-显示。
        if (actCommentDto.getType() != null && actCommentDto.getType() == 0) {
            comments = comments.stream().filter(o -> o.getUpPass() != null && o.getUpPass() == 1)
                    .collect(Collectors.toList());
        }
        if (actCommentDto.getType() != null && actCommentDto.getType() == 1) {
            comments = comments.stream().filter(o -> o.getNeedShow() != null && o.getNeedShow() == 0)
                    .collect(Collectors.toList());
        }
        if (actCommentDto.getType() != null && actCommentDto.getType() == 2) {
            comments = comments.stream().filter(o -> o.getNeedShow() != null && o.getNeedShow() == 1)
                    .collect(Collectors.toList());
        }
        //不是作者或者管理员看不到隐藏的评论
        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        MoocCourseAdminsDto adminsDto = moocCourseAdminsMapper.findAdminById(actCommentDto.getActId(),staffId);

        if (actCommentDto.getShow() != null && adminsDto == null) {
            comments = comments.stream().filter(o -> o.getNeedShow().intValue() == actCommentDto.getShow())
                    .collect(Collectors.toList());
        }
        //搜索
        if (StringUtils.isNotBlank(actCommentDto.getStaffName())) {
            comments = comments.stream().filter(o -> o.getCreatorName().startsWith(actCommentDto.getStaffName())).collect(Collectors.toList());
        }
        //内容搜索
        if (StringUtils.isNotBlank(actCommentDto.getContent())) {
            comments = comments.stream().filter(o -> o.getContent().contains(actCommentDto.getContent())).collect(Collectors.toList());
        }

        return comments;
    }

    /**
     * 评论分组
     *
     * @param actCommentDto
     * @param comments
     * @return
     */
    private List<ActCommentResDto> commentGroupBy(ActCommentDto actCommentDto, List<ActCommentResDto> comments) {
        if (CollectionUtils.isEmpty(comments)) {
            return comments;
        }
        //我的>置顶>作者>其他人
        ArrayList commentList = new ArrayList();

        //我的评论
        List<ActCommentResDto> myComments = comments.stream()
                .filter(o -> o.getStaffId().equals(actCommentDto.getLoginStaffId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(myComments)) {
            comments.removeAll(myComments);
            myComments = commentOrder(actCommentDto, myComments);
            commentList.addAll(myComments);
        }
        //置顶的评论
        List<ActCommentResDto> upComments = comments.stream()
                .filter(o -> o.getUpPass() != null && o.getUpPass().equals((byte) 1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(upComments)) {
            comments.removeAll(upComments);
            upComments = commentOrder(actCommentDto, upComments);
            commentList.addAll(upComments);
        }

        //作者的评论
        List<ActCommentResDto> authorComments = comments.stream()
                .filter(o -> o.getStaffId().equals(actCommentDto.getAuthorId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(authorComments)) {
            comments.removeAll(authorComments);
            authorComments = commentOrder(actCommentDto, authorComments);
            commentList.addAll(authorComments);
        }
        //其他的评论
        comments = commentOrder(actCommentDto, comments);
        commentList.addAll(comments);

        return commentList;
    }

    /**
     * 搜索评
     *
     * @param actCommentDto
     * @param comments
     * @return
     */
    private CommentPageDto<ActCommentResDto> searchComment(ActCommentDto actCommentDto, List<ActCommentResDto> comments) {
        //不是作者或者管理员看不到隐藏的评论
        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        MoocCourseAdminsDto adminsDto = moocCourseAdminsMapper.findAdminById(actCommentDto.getActId(),staffId);
        Integer needShow = 1;
        if (adminsDto != null){
            needShow = null;
        }

        if (CollectionUtils.isEmpty(comments)) {
            CommentPageDto commentPageDto = new CommentPageDto(actCommentDto.getPageNo(), actCommentDto.getPageSize(), false);
            commentPageDto.setCommentCount(commentMapper.getCommentCount(actCommentDto.getActId(), actCommentDto.getActType(),needShow));
            commentPageDto.setHasShow(commentMapper.getNeedShowCount(actCommentDto) > 0 ? true : false);
            return commentPageDto;
        }
        //过滤评论
        List<ActCommentResDto> commentList = filterComment(actCommentDto, comments);

        if (actCommentDto.getPid() != null) {
            commentList = commentList.stream().sorted(Comparator.comparing(ActCommentResDto::getCreatedAt))
                    .collect(Collectors.toList());
        } else {
            //评论分组
            commentList = commentGroupBy(actCommentDto, commentList);
        }
        //评论分页
        CommentPageDto<ActCommentResDto> page = new CommentPageDto<>(actCommentDto.getPageNo(),
                actCommentDto.getPageSize(), false);
        int skipNum = actCommentDto.getPageSize() * (actCommentDto.getPageNo() - 1);
        List<ActCommentResDto> actComments = commentList.stream().skip(skipNum).limit(actCommentDto.getPageSize())
                .collect(Collectors.toList());
        page.setCurrent(actCommentDto.getPageNo());
        page.setTotal(commentList.size());
        page.setPages((commentList.size() + actCommentDto.getPageSize()) / actCommentDto.getPageSize());
        page.setRecords(actComments);
        //统计评论
        if (actCommentDto.getPid() == null) {
            page.setCommentCount(commentMapper.getCommentCount(actCommentDto.getActId(), actCommentDto.getActType(),needShow));
            page.setHasShow(commentMapper.getNeedShowCount(actCommentDto) > 0 ? true : false);
        }
        return page;
    }

    /**
     * 删除评论
     *
     * @param actCommentDto
     */
    @Transactional(rollbackFor = Exception.class)
    public ActComment deleteComment(ActDeleteCommentDto actCommentDto) {
        if (actCommentDto.getId() == null) {
            throw new RuntimeException(multiLangService.getApiRecourseText("Api_Mooc_Error_idNotNull","id不能为空!"));
        }
        ActComment actComment = commentMapper.findActCommentById(String.valueOf(actCommentDto.getId()));
        if (actComment != null) {
            Date date = new Date();
            actComment.setEnabled((byte) 0);
            actComment.setUpdatedAt(date);
            ContextEntity current = GatewayContext.current();
            Integer staffId = Integer.valueOf(current.getStaffId());
            String staffName = current.getStaffName();
            actComment.setUpdateId(staffId);
            actComment.setUpdateName(staffName);
            commentMapper.updateByPrimaryKeySelective(actComment);
            List<ActComment> actComments = commentMapper.findActCommentByPid(String.valueOf(actComment.getId()));
            //删除回复
            if (CollectionUtils.isNotEmpty(actComments)) {
                actComments.forEach(comment -> {
                    comment.setEnabled((byte) 0);
                    comment.setUpdatedAt(date);
                    comment.setUpdateId(staffId);
                    comment.setUpdateName(staffName);
                    commentMapper.updateByPrimaryKeySelective(comment);
                });
            }
            ActCommentDto sumActCommentDto = new ActCommentDto();
            sumActCommentDto.setActId(actComment.getActId());
            sumActCommentDto.setActType(actComment.getActType());
            sumCommentCount(sumActCommentDto,false);
        } else {
            throw new LogicException(multiLangService.getApiRecourseText("Api_Mooc_Error_CommentNotExisit","评论不存在!"));
        }
        cleanCache(actComment.getId());
        return actComment;
    }

    /**
     * 隐藏/显示评论
     *
     * @param actCommentDto
     */
    public void showComment(ActShowCommentDto actCommentDto) {
        if (actCommentDto.getId() == null) {
            throw new RuntimeException("id不能为空!");
        }
        Date date = new Date();
        ActComment actComment = new ActComment();
        actComment.setId(actCommentDto.getId());
        actComment.setNeedShow((byte) (actCommentDto.getShow() > 0 ? 1 : 0));
        actComment.setUpdatedAt(date);
        actComment.setUpdateId(actCommentDto.getLoginStaffId());
        actComment.setUpdateName(actCommentDto.getLoginStaffName());
        cleanCache(actCommentDto.getId());
        //判断隐藏评论置顶状态为0
        if (actComment.getNeedShow().equals((byte) 0)) {
            actComment.setUpPass((byte) 0);
        }
        commentMapper.updateNeedShowPrimaryKey(actComment);
        cleanCache(actComment.getId());
    }

    /**
     * 评论点赞
     *
     * @param actPraisedCommentDto
     */
    public void praisedComment(ActPraisedCommentDto actPraisedCommentDto) {
        if (actPraisedCommentDto.getId() == null) {
            throw new RuntimeException(multiLangService.getApiRecourseText("Api_Mooc_Error_idNotNull","id不能为空!"));
        }
        //判断是否点赞
        try {
            ActPraiseDto praiseDto = actPraiseService
                    .checkPraised(String.valueOf(actPraisedCommentDto.getActType()), actPraisedCommentDto.getActId(),
                            actPraisedCommentDto.getId(), 1);
            if (praiseDto != null) {
                //取消点赞
                boolean state = actPraiseService.deletePraise(String.valueOf(actPraisedCommentDto.getActType()),
                        actPraisedCommentDto.getActId(), actPraisedCommentDto.getId(), 1);
                if (state) {
                    cleanCache(actPraisedCommentDto.getId());
                    commentMapper.subLikeCount(actPraisedCommentDto.getId());
                } else {
                    throw new RuntimeException(multiLangService.getApiRecourseText("Api_Mooc_Error_CancelFaild","取消失败!"));
                }
            } else {
                //点赞
                boolean state = actPraiseService
                        .addPraise(String.valueOf(actPraisedCommentDto.getActType()), actPraisedCommentDto.getActId(),
                                actPraisedCommentDto.getId(), 1);
                if (state) {
                    cleanCache(actPraisedCommentDto.getId());
                    commentMapper.addLikeCount(actPraisedCommentDto.getId());
                } else {
                    throw new RuntimeException(multiLangService.getApiRecourseText("Api_Mooc_Error_PraiseFaild","点赞失败!"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        cleanCache(actPraisedCommentDto.getId());
    }

    /**
     * 评论置顶
     *
     * @param actCommentDto
     */
    public void stickyComment(ActStickyCommentDto actCommentDto) {
        if (actCommentDto.getId() == null) {
            throw new RuntimeException(multiLangService.getApiRecourseText("Api_Mooc_Error_idNotNull","id不能为空!"));
        }
        ActComment comment = commentMapper.selectByPrimaryKey(actCommentDto.getId());
        if (comment == null) {
            throw new RuntimeException(multiLangService.getApiRecourseText("Api_Mooc_Error_CommentNotExisit","评论不存在！"));
        }

        if (comment.getNeedShow() != null && comment.getNeedShow().equals((byte) 0)) {
            throw new RuntimeException(multiLangService.getApiRecourseText("Api_NetCourse_CannotTop","隐藏的评论不能置顶!"));
        }
        Date date = new Date();
        ActComment actComment = new ActComment();
        actComment.setId(actCommentDto.getId());
        actComment.setUpPass((byte) (actCommentDto.getSticky() > 0 ? 1 : 0));
        actComment.setStickTime(date);
        actComment.setUpdatedAt(date);
        actComment.setUpdateId(actCommentDto.getLoginStaffId());
        actComment.setUpdateName(actCommentDto.getLoginStaffName());
        cleanCache(actCommentDto.getId());
        commentMapper.updateUpPassAndStickTimePrimaryKey(actComment);
    }

    /**
     * 获取评论数据
     *
     * @param actId
     * @param actType
     * @return
     */
    public long getCommentCount(String actId, Integer actType) {
        if (actId == null) {
            throw new RuntimeException("id不能为空");
        }
        if (actType == null) {
            throw new RuntimeException("actType不能为空");
        }
        return commentMapper.getCommentCount(actId, actType,null);
    }

    /**
     * 获取评论用户数
     *
     * @param actId
     * @param actType
     * @return
     */
    public int getCommentUserCount(String actId, Integer actType) {
        if (actId == null) {
            throw new RuntimeException("id不能为空");
        }
        if (actType == null) {
            throw new RuntimeException("actType不能为空");
        }
        return commentMapper.getCommentUserCount(actId, actType);
    }

    public String findActCommentById(Integer cId) {
        ActComment comment = commentMapper.findActCommentById(String.valueOf(cId));
        if (comment != null) {
            return comment.getActId();
        }
        return null;
    }

    /**
     * 获取评论列表
     *
     * @param appId
     * @param actId
     * @param actType
     * @param staffId
     * @param empName
     * @param PageNo
     * @param PageSize
     * @return
     */
    public Page<ActCommentResDto> getUserComments(String appId, String actId, Integer actType, Integer staffId,
                                                  String empName, Integer PageNo, Integer PageSize) {
        String key = String.valueOf(Objects.hash(appId, actId, actType, staffId, PageNo, PageSize));
        if (redisUtil.hasKey(key)) {
            Page<ActCommentResDto> CommentsCache = (Page<ActCommentResDto>) redisUtil.get(key);
            return CommentsCache;
        }

        IPage<ActCommentResDto> page = new Page<>(PageNo, PageSize);
        Page<ActCommentResDto> Comments = commentMapper.getCommentsById(appId, actId, actType, staffId, empName, page);

        //缓存时长60秒
        redisUtil.set(key, Comments, 300);
        existKeys.add(key);
        return Comments;
    }


    /**
     * 获取我的评论列表
     *
     * @param queryDto
     */
    public Page<ActComment> getMyComments(ActMyCommentQueryDto queryDto) {

        if (queryDto.getPageNo() <= 0) {
            queryDto.setPageNo(1);
        }
        if (queryDto.getPageSize() <= 0) {
            queryDto.setPageSize(20);
        }

        if (null == queryDto.getStaffId() && (null == queryDto.getActIdList() || queryDto.getActIdList().size() == 0)) {
            throw new RuntimeException("参数错误");
        }

        return commentMapper
                .getMyComments(new Page(queryDto.getPageNo(), queryDto.getPageSize()), queryDto.getAppId(),
                        queryDto.getActType(),
                        queryDto.getStaffId(), queryDto.getActIdList());

    }

    /**
     * 获取评论实体
     * @param cId
     * @return
     */
    public ActComment findCommentById(Integer cId) {
        return commentMapper.findActCommentById(String.valueOf(cId));
    }
}
