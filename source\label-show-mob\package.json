{"name": "@tencent/sdc-label-show-mob", "version": "0.0.17", "description": "sdc label show mob", "main": "lib/sdc-label-show-mob.common.js", "files": ["lib", "packages"], "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service build --target lib  --dest lib packages/index.js", "pub": "npm publish && npm version patch"}, "style": "lib/sdc-addlabel-uicss", "dependencies": {"@babel/polyfill": "^7.8.7", "@popperjs/core": "^2.11.8", "@tencent/autotracker-beacon-oa": "^4.3.9", "@tencent/sdc-sub-label-manage-test": "^0.0.3", "amfe-flexible": "^2.2.1", "core-js": "^3.6.5", "driver.js": "^1.3.1", "element-ui": "2.13.0", "vue": "^2.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.6", "@vue/cli-plugin-eslint": "~4.5.6", "@vue/cli-service": "~4.5.6", "axios": "^0.21.0", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}