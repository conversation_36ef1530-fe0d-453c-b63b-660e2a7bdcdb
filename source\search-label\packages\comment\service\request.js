/** axios封装
 * 请求拦截、相应拦截、错误统一处理
 */
import axios from 'axios'
import { Message } from 'element-ui'
const service = axios.create({
  // baseURL,
  timeout: 15000,
  withCredentials: true
})
// 请求拦截器
service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.error(error)
  })

// 响应拦截器
service.interceptors.response.use(
  response => {
    if (response.status === 200) {
      if (response.data.code === 200) {
        return Promise.resolve(response.data)
      } else if (response.data.code === 403) {
        Message({
          type: 'error',
          message: '没有权限访问此站',
          customClass: 'comment-message',
          showClose: true,
          duration: 2000
        })
      } else {

        Message({
          type: 'error',
          message: response.data.message || '请求错误',
          showClose: true,
          customClass: 'comment-message',
          duration: 2000
        })
        return Promise.reject(response.data)
      }
    }
  },
  // 服务异常的情况
  error => {
    if (error.code === 'ECONNABORTED' && (error.message.indexOf('timeout') > -1)) {
      Message({
        type: 'error',
        message: '网络连接超时',
        showClose: true,
        customClass: 'comment-message',
        duration: 2000
      })
    } else if (error.response.status) {
      switch (error.response.status) {
        // 403 无权限
        case 403:
          Message({
            type: 'error',
            message: '没有权限访问此站',
            showClose: true,
            customClass: 'comment-message',
            duration: 2000
          })
          break
          // 404 请求不存在
        case 404:
          Message({
            type: 'error',
            message: '网络请求不存在',
            showClose: true,
            customClass: 'comment-message',
            duration: 2000
          })
          break
          // 500 服务异常，请联系管理员
        case 500:
          Message({
            type: 'error',
            message: '服务异常，请联系管理员',
            showClose: true,
            customClass: 'comment-message',
            duration: 2000
          })
          break
          // 其他错误，直接抛出错误提示
        default:
          Message({
            type: 'error',
            message: error.response.data.message || '服务异常，请联系管理员',
            showClose: true,
            customClass: 'comment-message',
            duration: 2000
          })
      }
      return Promise.reject(error.response)
    }
  }
)

export default service