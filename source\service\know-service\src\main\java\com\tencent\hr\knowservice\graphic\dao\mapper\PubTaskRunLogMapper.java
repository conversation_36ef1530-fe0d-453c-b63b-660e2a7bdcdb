package com.tencent.hr.knowservice.graphic.dao.mapper;


import com.tencent.hr.knowservice.graphic.dao.entity.PubTaskRunLog;

public interface PubTaskRunLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PubTaskRunLog record);

    int insertSelective(PubTaskRunLog record);

    PubTaskRunLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PubTaskRunLog record);

    int updateByPrimaryKey(PubTaskRunLog record);

    PubTaskRunLog getLastSuccessRunLog(String appId,String moduleId,String taskName);

    int deleteMonthLogs(Integer taskId);
}