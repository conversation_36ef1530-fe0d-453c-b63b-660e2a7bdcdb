package com.tencent.hr.knowservice.businessCommon.constans;


/**
 * 积分系统对应类型枚举
 */
public enum LRSObjectTypeEnum {
    NetCourse("qlearning","NetCourse","网络课",1),
    FaceCourse("qlearning","FaceCourse","面授课",2),
    CourseList("qlearning","CourseList","课单",15),
    Activity("qlearning","Activity","活动",4),
    Live("qlearning","Live","直播",5),
    Thesis("qlearning","Thesis","论文",9),
    Article("qlearning","Article","图文",6),
    SeriesCourse("qlearning","SeriesCourse","系列课",7),
    FileProd("qlearning","FileProd","文档",10),
    Classes("qlearning","Classes","班级",3),
    Graphic("qlearning","Graphic","图文",18),
    Note("note","Note","笔记",17),
    Case("case","Case","案例",16),
    Topic("hangjia","Topic","主题",19),
    ;
    private String appId;
    private String objectType;
    private String objectTypeName;
    private Integer actType;

    LRSObjectTypeEnum(String appId, String objectType, String objectTypeName, Integer actType) {
        this.appId = appId;
        this.objectType = objectType;
        this.objectTypeName = objectTypeName;
        this.actType = actType;
    }

    public static LRSObjectTypeEnum  getObjectTypeEnumByActType(Integer actType){
        if (actType == null){
            return null;
        }
        for (LRSObjectTypeEnum value : LRSObjectTypeEnum.values()) {
            if (actType.equals(value.getActType())){
                return value;
            }
        }
        return null;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }

    public String getObjectTypeName() {
        return objectTypeName;
    }

    public void setObjectTypeName(String objectTypeName) {
        this.objectTypeName = objectTypeName;
    }

    public Integer getActType() {
        return actType;
    }

    public void setActType(Integer actType) {
        this.actType = actType;
    }
}
