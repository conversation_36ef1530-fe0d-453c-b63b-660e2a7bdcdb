# test

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### 访问项目
http://test.woa.com:8080 (端口号根据实际情况替换)

### 关于插件 打包时需要修改package.json文中里面的name、version、main的值
```
测试环境
name: "@tencent/sdc-addlabel-test1"
version: "0.15.7" (改成最新的版本+1)
main: "lib/sdc-addlabel-test1.common.js"
```

```
生产环境
name: "@tencent/sdc-addlabel"
version: "0.15.5" (改成最新的版本+1)
main: "lib/sdc-addlabel.common.js"
```
### npm仓库地址：https://mirrors.tencent.com/#/private/npm
