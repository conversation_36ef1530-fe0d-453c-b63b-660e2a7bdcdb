package com.tencent.hr.knowservice.businessCommon.dto.xapi;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.HashMap;
import lombok.Data;

@Data
@JsonNaming
public class TXAgent {

    private String id;

    private String objectType;

    private String objectTypeName;

    private String name;

    private String openID;

    private HashMap<String, String> extensions;

    public TXAgent(){
        super();
    }

    public TXAgent(String id, String name, String objectType){
        this.id = id;
        this.name = name;
        this.objectType = objectType;
    }

}
