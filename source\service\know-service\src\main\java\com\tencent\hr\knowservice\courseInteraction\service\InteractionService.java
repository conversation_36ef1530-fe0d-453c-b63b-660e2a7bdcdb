package com.tencent.hr.knowservice.courseInteraction.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.common.util.StringUtil;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.constans.V8CacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.service.V8DataTransferService;
import com.tencent.hr.knowservice.courseInteraction.constans.CacheKeyEnum;
import com.tencent.hr.knowservice.courseInteraction.constans.InteractionConstants;
import com.tencent.hr.knowservice.courseInteraction.dao.InteractiveDao;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.ConfigurationsOfSelect;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.InteractiveConfig;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.Options;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.SelectContent;
import com.tencent.hr.knowservice.courseInteraction.dto.*;
import com.tencent.hr.knowservice.courseInteraction.dto.condition.ConInteractiveConfig;
import com.tencent.hr.knowservice.framework.advice.exception.AuthException;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.netcourse.dao.entity.ActNetCourse;
import com.tencent.hr.knowservice.netcourse.dao.mapper.ActNetCourseMapper;
import com.tencent.hr.knowservice.netcourse.service.ActNetCourseService;
import com.tencent.hr.knowservice.netcourse.service.AuthCheckService;
import com.tencent.hr.knowservice.utils.CommonUtils;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/10/10
 * @version: 1.0
 */
@Service
@Slf4j
public class InteractionService {

    @Autowired
    InteractiveDao interactiveDao;

    @Autowired
    ActNetCourseMapper actNetCourseMapper;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    V8DataTransferService v8DataTransferService;

    @Autowired
    AuthCheckService netCourseAuthCheckService;

    @Autowired
    ActNetCourseService actNetCourseService;

    /**
     * 统一权限校验
     * @param courseId
     * @param actType
     */
    public void AuthCheck(String courseId, Integer actType){
        if (ActTypeEnum.NET_COURSE.getActType().equals(actType)){
            boolean hasPermission = netCourseAuthCheckService.checkManageRight(Integer.valueOf(courseId));
            if (!hasPermission) {
                throw new AuthException("您暂时没有访问该页面的权限！");
            }
        }else {
            throw new LogicException("当前课程暂时不支持互动配置");
        }
    }

    /**
     * 获取某一个课程的互动配置信息
     *
     * @param courseId 课程id
     * @param actType  课程类型
     * @return
     */
    public InteractiveConfigOutDto getBaseInteractionConfig(String courseId, Integer actType) {
        if (ActTypeEnum.NET_COURSE.getActType().equals(actType)){
            boolean enableInteractive = getNetCourseInteractiveInUse(courseId);
            //判断是否开启数据库
            if (!enableInteractive){
                log.error("当前课程暂未开启互动配置项目，课程id={}",courseId);
                return null;
            }
        }else {
            log.error("当前课程暂时不支持互动配置，课程id={}",courseId);
            return null;
        }
        //互动配置课程缓存
        String redisKey = CacheKeyEnum.UserInteractiveInfo.getKey() + actType + ":" + courseId;
        Object redisObj = redisUtil.get(redisKey);
        if (null != redisObj) {
            return (InteractiveConfigOutDto) redisObj;
        } else {
            //获取mongo中的数据
            ConInteractiveConfig conInteractiveConfig = new ConInteractiveConfig();
            conInteractiveConfig.setCourseId(courseId);
            conInteractiveConfig.setActType(actType);
            InteractiveConfig dataInDb = interactiveDao.getInteractionConfigByCondition(conInteractiveConfig);
            //数据不存在
            if (null == dataInDb) {
                return null;
            }
            //数据装配
            InteractiveConfigOutDto data = new InteractiveConfigOutDto();
            data.set_id(dataInDb.get_id())
                    .setActType(dataInDb.getActType())
                    .setCourseId(dataInDb.getCourseId());
            //互动配置数据的map
            List<ConfigurationsOfSelectOutDto> configurationsOfSelectOutDtoData = new ArrayList<>();
            //数据处理
            for (ConfigurationsOfSelect configuration : dataInDb.getConfigurationsOfSelect()) {
                //1、判断正确答案是否返回(选择题在完成条件是正确时才返回正确答案到前端，否则全部不返回正确答案)
                List<SelectContent> selectContents = configuration.getSelectContent();
                for (SelectContent selectContent : selectContents) {
                    if (InteractionConstants.selectActiveType.CHOOSE.getValue().equals(selectContent.getActiveType())) {
                        if (!InteractionConstants.completionCondition.CORRECT.getValue().equals(selectContent.getChooseTypeConfig().getCompletionConditions())) {
                            selectContent.setCorrectAnswer(null);
                            selectContent.setCorrectAnswerEn(null);
                        }
                    }
                }
                ConfigurationsOfSelectOutDto configurationsOfSelectOutDto = new ConfigurationsOfSelectOutDto();
                BeanUtils.copyProperties(configuration, configurationsOfSelectOutDto);
                configurationsOfSelectOutDto.setSelectContent(configuration.getSelectContent());
                configurationsOfSelectOutDtoData.add(configurationsOfSelectOutDto);
            }
            data.setConfigurationsOfSelect(configurationsOfSelectOutDtoData);
            redisUtil.set(redisKey, data, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
            return data;
        }
    }

    /**
     * 保存互动配置
     *
     * @param interactiveInDTO
     * @return
     */
    public String saveInteractive(InteractiveInDto interactiveInDTO) {
        //权限校验
        AuthCheck(interactiveInDTO.getCourseId(),interactiveInDTO.getActType());
        //数据基础校验
        checkBaseInteractive(interactiveInDTO);
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        String date = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        //判断课程是否存在
        if (ActTypeEnum.NET_COURSE.getActType().equals(interactiveInDTO.getActType())){
            ActNetCourse netCourse = getBaseNetCourse(interactiveInDTO.getCourseId());
            if (null == netCourse) {
                throw new LogicException("该课程不存在");
            }
            //判断互动时间点不能大于课程时长
            if(interactiveInDTO.getActiveTime() > netCourse.getEstDur() * 60){
                throw new LogicException("互动时间点不能大于课程时长");
            }
        }else {
            throw new LogicException("当前课程暂时不支持互动配置");
        }
        //判断当前课程是否有互动规则
        ConInteractiveConfig condition = new ConInteractiveConfig();
        condition.setActType(interactiveInDTO.getActType());
        condition.setCourseId(interactiveInDTO.getCourseId());
        InteractiveConfig interactiveConfig = interactiveDao.getInteractionConfigByCondition(condition);
        if (null == interactiveConfig) {
            //新建互动规则
            InteractiveConfig entity = new InteractiveConfig();
            ActTypeEnum actTypeEnum = ActTypeEnum.getActTypeEnum(interactiveInDTO.getActType());
            entity.setActType(actTypeEnum.getActType());
            entity.setActTypeName(actTypeEnum.getText());
            entity.setCourseId(interactiveInDTO.getCourseId());
            entity.setEnabled(true);
            entity.setCreatorId(staffId);
            entity.setCreatorName(staffName);
            entity.setCreatedAt(date);
            entity.setUpdaterId(staffId);
            entity.setUpdaterName(staffName);
            entity.setUpdatedAt(date);
            interactiveDao.saveInteractiveConfig(entity);
            interactiveConfig = entity;
            delInteractiveCache(interactiveInDTO.getActType(),interactiveInDTO.getCourseId(),interactiveConfig.get_id());
        }
        //新建互动项
        ConfigurationsOfSelect select = new ConfigurationsOfSelect();
        select.setInteractiveId(InteractionConstants.prefixId.ACTIVE_PREFIX.getValue() + CommonUtils.generateShortUUID());
        select.setActiveTime(interactiveInDTO.getActiveTime());
        select.setIntroduction(interactiveInDTO.getIntroduction());
        select.setIntroductionEn(interactiveInDTO.getIntroductionEn());
        select.setContinueStudyingTips(interactiveInDTO.getContinueStudyingTips());
        select.setContinueStudyingTipsEn(interactiveInDTO.getContinueStudyingTipsEn());
        select.setTitle(interactiveInDTO.getTitle());
        select.setTitleEn(interactiveInDTO.getTitleEn());
        //新建试题信息
        List<SelectContent> selectContents = interactiveInDTO.getSelectContent();
        for (SelectContent selectContent : selectContents) {
            selectContent.setQuestionId(InteractionConstants.prefixId.QUESTION_PREFIX.getValue() +CommonUtils.generateShortUUID());
        }
        select.setSelectContent(selectContents);
        select.setEnabled(true);
        select.setCreatorId(staffId);
        select.setCreatorName(staffName);
        select.setCreatedAt(date);
        select.setUpdaterId(staffId);
        select.setUpdaterName(staffName);
        select.setUpdatedAt(date);
        List<ConfigurationsOfSelect> configurationsOfSelects = interactiveConfig.getConfigurationsOfSelect();
        if (configurationsOfSelects == null) {
            configurationsOfSelects = new ArrayList<>();
        }
        List<Integer> times = configurationsOfSelects.stream().map(ConfigurationsOfSelect::getActiveTime).collect(Collectors.toList());
        if (times.contains(interactiveInDTO.getActiveTime())) {
            throw new LogicException("互动时间点重复");
        }
        //互动配置项添加到互动规则中
        configurationsOfSelects.add(select);
        //根据互动时间点进行排序
        configurationsOfSelects = configurationsOfSelects.stream().sorted(Comparator.comparing(ConfigurationsOfSelect::getActiveTime)).collect(Collectors.toList());
        interactiveConfig.setConfigurationsOfSelect(configurationsOfSelects);
        //更新整个互动规则
        interactiveDao.saveInteractiveConfig(interactiveConfig);
        delInteractiveCache(interactiveInDTO.getActType(),interactiveInDTO.getCourseId(),interactiveConfig.get_id());
        return select.getInteractiveId();
    }

    /**
     * 修改互动配置
     *
     * @param interactiveInDTO
     * @return
     */
    public void updateInteractive(InteractiveInDto interactiveInDTO) {
        if(StringUtil.isBlank(interactiveInDTO.get_id())){
            throw new LogicException("互动规则唯一id不能为空");
        }
        AuthCheck(interactiveInDTO.getCourseId(),interactiveInDTO.getActType());
        checkBaseInteractive(interactiveInDTO);
        //判断课程是否存在
        ActNetCourse netCourse = getBaseNetCourse(interactiveInDTO.getCourseId());
        if (null == netCourse) {
            throw new LogicException("该课程不存在");
        }
        delInteractiveCache(interactiveInDTO.getActType(), interactiveInDTO.getCourseId(), interactiveInDTO.get_id());
        //判断当前课程是否有互动规则
        InteractiveConfig interactionConfig = interactiveDao.getInteractionConfigById(interactiveInDTO.get_id());
        if (null == interactionConfig) {
            throw new LogicException("互动规则不存在");
        }
        //校验该配置项是否存在
        List<ConfigurationsOfSelect> configurationsOfSelects = interactionConfig.getConfigurationsOfSelect();
        //防止空指针
        if (CollectionUtils.isEmpty(configurationsOfSelects)) {
            configurationsOfSelects = new ArrayList<>();
        }
        List<ConfigurationsOfSelect> selects = configurationsOfSelects.stream().filter(item -> item.getInteractiveId().equals(interactiveInDTO.getInteractiveId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selects)) {
            throw new LogicException("该互动配置不存在");
        }
        //要修改的配置项
        ConfigurationsOfSelect select = selects.get(0);
        select.setTitle(interactiveInDTO.getTitle());
        select.setTitleEn(interactiveInDTO.getTitleEn());
        select.setContinueStudyingTips(interactiveInDTO.getContinueStudyingTips());
        select.setContinueStudyingTipsEn(interactiveInDTO.getContinueStudyingTipsEn());
        select.setIntroduction(interactiveInDTO.getIntroduction());
        select.setIntroductionEn(interactiveInDTO.getIntroductionEn());
        if (!select.getActiveTime().equals(interactiveInDTO.getActiveTime())) {
            //修改了时间，判断是否重复
            List<Integer> times = configurationsOfSelects.stream().map(ConfigurationsOfSelect::getActiveTime).collect(Collectors.toList());
            if (times.contains(interactiveInDTO.getActiveTime())) {
                throw new LogicException("互动时间点重复");
            }
            select.setActiveTime(interactiveInDTO.getActiveTime());
        }
        //判断互动时间点不能大于课程时长
        if(interactiveInDTO.getActiveTime() > netCourse.getEstDur() * 60){
            throw new LogicException("互动时间点不能大于课程时长");
        }

        List<String> updateQuestionIds = new ArrayList<>();
        List<SelectContent> updateContents = interactiveInDTO.getSelectContent();
        for (SelectContent updateContent : updateContents) {
            if (StringUtil.isBlank(updateContent.getQuestionId())) {
                updateContent.setQuestionId(InteractionConstants.prefixId.QUESTION_PREFIX.getValue()+CommonUtils.generateShortUUID());
            } else {
                updateQuestionIds.add(updateContent.getQuestionId());
            }
        }
        //由于试题id是由前端传递，这里对试题id做校验，判断是否有非法的试题id
        List<SelectContent> selectContent = select.getSelectContent();
        List<String> questionIds = selectContent.stream().map(SelectContent::getQuestionId).collect(Collectors.toList());
        Set<String> set = new HashSet<>(updateQuestionIds);
        if (set.size() < updateQuestionIds.size()) {
            throw new LogicException("存在重复试题id");
        }
        updateQuestionIds.removeAll(questionIds);
        if (CollectionUtils.isNotEmpty(updateQuestionIds)) {
            throw new LogicException("非法试题id【" + StringUtil.join(updateQuestionIds.toArray()) + "】，请检查");
        }
        //将前端传入的所有试题覆盖db的试题
        select.setSelectContent(updateContents);
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        interactionConfig.setUpdaterId(staffId);
        interactionConfig.setUpdaterName(staffName);
        interactionConfig.setUpdatedAt(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        //根据互动时间点进行排序
        configurationsOfSelects = configurationsOfSelects.stream().sorted(Comparator.comparing(ConfigurationsOfSelect::getActiveTime)).collect(Collectors.toList());
        interactionConfig.setConfigurationsOfSelect(configurationsOfSelects);
        interactiveDao.saveInteractiveConfig(interactionConfig);
        delInteractiveCache(interactiveInDTO.getActType(), interactiveInDTO.getCourseId(), interactiveInDTO.get_id());
    }

    /**
     * 获取互动配置详情
     *
     * @param id            互动规则唯一id
     * @param interactiveId 互动配置唯一id
     * @return
     */
    public InteractiveInfoDto getInteractiveInfo(String id, String interactiveId) {
        InteractiveConfig interactionConfig = interactiveDao.getInteractionConfigById(id);
        if (null == interactionConfig) {
            throw new LogicException("互动规则不存在");
        }
        AuthCheck(interactionConfig.getCourseId(),interactionConfig.getActType());
        if (CollectionUtils.isEmpty(interactionConfig.getConfigurationsOfSelect())) {
            return null;
        }
        List<ConfigurationsOfSelect> selects = interactionConfig.getConfigurationsOfSelect().stream().filter(item -> item.getInteractiveId().equals(interactiveId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(selects)) {
            return null;
        }
        ConfigurationsOfSelect select = selects.get(0);
        //将数据库对象转换为输出对象
        InteractiveInfoDto infoDto = new InteractiveInfoDto();
        infoDto.set_id(id);
        infoDto.setActType(interactionConfig.getActType());
        infoDto.setActTypeName(interactionConfig.getActTypeName());
        infoDto.setCourseId(interactionConfig.getCourseId());
        infoDto.setInteractiveId(select.getInteractiveId());
        infoDto.setActiveTime(select.getActiveTime());
        infoDto.setIntroduction(select.getIntroduction());
        infoDto.setIntroductionEn(select.getIntroductionEn());
        infoDto.setContinueStudyingTips(select.getContinueStudyingTips());
        infoDto.setContinueStudyingTipsEn(select.getContinueStudyingTipsEn());
        infoDto.setTitle(select.getTitle());
        infoDto.setTitleEn(select.getTitleEn());
        infoDto.setSelectContent(select.getSelectContent());
        infoDto.setCreatorId(select.getCreatorId());
        infoDto.setCreatorName(select.getCreatorName());
        infoDto.setCreatedAt(select.getCreatedAt());
        return infoDto;
    }

    /**
     * 删除互动配置
     *
     * @param id
     * @param interactiveId
     */
    public void deleteInteractive(String id, String interactiveId) {
        InteractiveConfig interactionConfig = interactiveDao.getInteractionConfigById(id);
        if (null == interactionConfig) {
            throw new LogicException("互动规则不存在");
        }
        AuthCheck(interactionConfig.getCourseId(),interactionConfig.getActType());
        delInteractiveCache(interactionConfig.getActType(), interactionConfig.getCourseId(), id);
        if (CollectionUtils.isEmpty(interactionConfig.getConfigurationsOfSelect())) {
            return;
        }
        //从互动规则配置项中删除对应id的互动配置项
        Iterator<ConfigurationsOfSelect> iterator = interactionConfig.getConfigurationsOfSelect().iterator();
        while (iterator.hasNext()) {
            ConfigurationsOfSelect select = iterator.next();
            if (select.getInteractiveId().equals(interactiveId)) {
                iterator.remove();
                break;
            }
        }
        interactiveDao.saveInteractiveConfig(interactionConfig);
        delInteractiveCache(interactionConfig.getActType(), interactionConfig.getCourseId(), id);
    }

    /**
     * 获取互动配置项分页
     *
     * @param actType
     * @param courseId
     * @param current
     * @param size
     * @return
     */
    public IPage<InteractivePageDto> getInteractivePage(Integer actType, String courseId, Integer current, Integer size) {
        AuthCheck(courseId,actType);
        ConInteractiveConfig condition = new ConInteractiveConfig();
        condition.setCourseId(courseId);
        condition.setActType(actType);
        InteractiveConfig interactiveConfig = interactiveDao.getInteractionConfigByCondition(condition);
        List<InteractivePageDto> records = new ArrayList<>();
        IPage<InteractivePageDto> page = new Page<>(current, size);
        int total = 0;
        if(interactiveConfig != null){
            //获取该配置规则下的所有配置项
            List<ConfigurationsOfSelect> selects = interactiveConfig.getConfigurationsOfSelect();
            //防止空指针
            if (selects == null) {
                selects = new ArrayList<>();
            }
            total = selects.size();
            //进行分页
            selects = selects.stream().skip((long) (current - 1) * size).limit(size).collect(Collectors.toList());

            //转换成输出对象
            for (ConfigurationsOfSelect select : selects) {
                InteractivePageDto record = new InteractivePageDto();
                record.set_id(interactiveConfig.get_id());
                record.setInteractiveId(select.getInteractiveId());
                //由于现在一个配置项只能配置一个试题，所以默认取值为第一个
                record.setQuestionId(select.getSelectContent().get(0).getQuestionId());
                record.setActiveTime(select.getActiveTime());
                record.setTitle(select.getTitle());
                record.setTitleEn(select.getTitleEn());
                record.setActiveType(select.getSelectContent().get(0).getActiveType());
                record.setCreatorId(select.getCreatorId());
                record.setCreatorName(select.getCreatorName());
                record.setCreatedAt(select.getCreatedAt());
                records.add(record);
            }
        }
        page.setTotal(total);
        page.setRecords(records);
        return page;
    }

    /**
     * 课程互动基本校验
     *
     * @param interactiveInDTO
     */
    private void checkBaseInteractive(InteractiveInDto interactiveInDTO) {
        if (null == ActTypeEnum.getActTypeEnum(interactiveInDTO.getActType())) {
            throw new LogicException("课程类型错误！");
        }
        if(!ActTypeEnum.FACE_COURSE.getActType().equals(interactiveInDTO.getActType()) && !ActTypeEnum.NET_COURSE.getActType().equals(interactiveInDTO.getActType())
            &&!ActTypeEnum.LIVE.getActType().equals(interactiveInDTO.getActType()) && !ActTypeEnum.MOOC.getActType().equals(interactiveInDTO.getActType())){
            throw new LogicException("只有视频类型课程才支持互动功能！");
        }
        if (StringUtil.isBlank(interactiveInDTO.getCourseId())) {
            throw new LogicException("课程id不能为空！");
        }
        if (StringUtil.isBlank(interactiveInDTO.getTitle()) && StringUtil.isBlank(interactiveInDTO.getTitleEn())) {
            throw new LogicException("互动标题不能为空");
        }
        if (null == interactiveInDTO.getActiveTime()) {
            throw new LogicException("互动时间点不能为空");
        }
        if (interactiveInDTO.getActiveTime() < 0) {
            throw new LogicException("互动时间点不能小于0");
        }
        if (CollectionUtils.isEmpty(interactiveInDTO.getSelectContent())) {
            throw new LogicException("互动内容不能为空");
        }
        //校验题目信息
        for (SelectContent selectContent : interactiveInDTO.getSelectContent()) {
            //校验题型
            if (StringUtil.isBlank(selectContent.getActiveType())) {
                throw new LogicException("题目类型不能为空");
            }
            if (!selectContent.getActiveType().equals(InteractionConstants.selectActiveType.CHOOSE.getValue()) && !selectContent.getActiveType().equals(InteractionConstants.selectActiveType.VOTE.getValue())) {
                throw new LogicException("题目类型异常");
            }
            //校验题目选项内容
            List<Options> options = selectContent.getOptions();
            if (CollectionUtils.isEmpty(options)) {
                throw new LogicException("选项不能为空");
            }
            for (Options option : options) {
                if (option == null) {
                    throw new LogicException("选项不能为空");
                }
                if (StringUtil.isBlank(option.getOptionValue())) {
                    throw new LogicException("选项值不能为空");
                }
                if (StringUtil.isBlank(option.getOptionText()) && StringUtil.isBlank(option.getOptionTextEn())) {
                    throw new LogicException("选项内容不能为空");
                }
            }
            //校验不同题型的配置
            if (InteractionConstants.selectActiveType.CHOOSE.getValue().equals(selectContent.getActiveType())) {
                if (null == selectContent.getChooseTypeConfig()) {
                    throw new LogicException("选择题配置不能为空");
                }
                if (StringUtil.isBlank(selectContent.getChooseTypeConfig().getType())) {
                    throw new LogicException("选择题类型不能为空");
                }
                if (!InteractionConstants.selectActiveType.SINGLE.getValue().equals(selectContent.getChooseTypeConfig().getType()) &&
                        !InteractionConstants.selectActiveType.MULTI.getValue().equals(selectContent.getChooseTypeConfig().getType())) {
                    throw new LogicException("选择题类型异常");
                }
                if (InteractionConstants.selectActiveType.MULTI.getValue().equals(selectContent.getChooseTypeConfig().getType()) && options.size() < 2) {
                    throw new LogicException("多选题至少两个选项");
                }
                if (StringUtil.isBlank(selectContent.getChooseTypeConfig().getCompletionConditions())) {
                    throw new LogicException("继续学习条件不能为空");
                }
                if (!InteractionConstants.completionCondition.CHOOSE.getValue().equals(selectContent.getChooseTypeConfig().getCompletionConditions()) &&
                        !InteractionConstants.completionCondition.CORRECT.getValue().equals(selectContent.getChooseTypeConfig().getCompletionConditions())) {
                    throw new LogicException("继续学习条件异常");
                }
                //校验答案
                if (CollectionUtils.isEmpty(selectContent.getCorrectAnswer())) {
                    throw new LogicException("选择题答案不能为空");
                }
                if (InteractionConstants.selectActiveType.SINGLE.getValue().equals(selectContent.getChooseTypeConfig().getType()) && selectContent.getCorrectAnswer().size() != 1) {
                    throw new LogicException("单选题有且只有一个正确选项");
                }
                if (InteractionConstants.selectActiveType.MULTI.getValue().equals(selectContent.getChooseTypeConfig().getType()) && selectContent.getCorrectAnswer().size() < 2) {
                    throw new LogicException("多选题至少两个正确选项");
                }

            }
            if (InteractionConstants.selectActiveType.VOTE.getValue().equals(selectContent.getActiveType())) {
                if (null == selectContent.getVoteTypeConfig()) {
                    throw new LogicException("投票题型配置不能为空");
                }
                if(null == selectContent.getVoteTypeConfig().getCompletionConditions()){
                    throw new LogicException("完成条件未选");
                }

                if (!InteractionConstants.completionCondition.CHOOSE.getValue().equals(selectContent.getVoteTypeConfig().getCompletionConditions())) {
                    throw new LogicException("继续学习条件异常");
                }
            }

            if (StringUtil.isBlank(selectContent.getQuestionText()) && StringUtil.isBlank(selectContent.getQuestionTextEn())) {
                throw new LogicException("题目题干不能为空");
            }
        }
    }

    /**
     * 删除用户端互动配置缓存
     *
     * @param actType
     * @param courseId
     */
    private void delInteractiveCache(Integer actType, String courseId, String id) {
        //删除互动缓存 -- 用户版本
        String redisKeyUser = CacheKeyEnum.UserInteractiveInfo.getKey() + actType + ":" + courseId;
        redisUtil.del(redisKeyUser);

        //删除互动缓存 -- 基础版本
        String redisKeyBase = CacheKeyEnum.BaseInteractiveInfo.getKey() + id;
        redisUtil.del(redisKeyBase);
    }

    /**
     * 获取网络课的基本信息
     *
     * @param courseId
     * @return
     */
    private ActNetCourse getBaseNetCourse(String courseId) {
        QueryWrapper<ActNetCourse> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("net_course_id", courseId);
        queryWrapper.isNull("deleted_at");
        return actNetCourseMapper.selectOne(queryWrapper);
    }

    /**
     * 获取网络课是否开启互动配置
     *
     * @param courseId
     * @return
     */
    private boolean getNetCourseInteractiveInUse(String courseId) {
        String redisKey = CacheKeyEnum.V8NetCourseInteractiveInfo.getKey()+courseId;
        Boolean redisVal = (Boolean) redisUtil.get(redisKey);
        //数据库获取数据
        if (null == redisVal){
            QueryWrapper<ActNetCourse> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("net_course_id,enable_interactive");
            queryWrapper.eq("net_course_id", courseId);
            queryWrapper.isNull("deleted_at");
            ActNetCourse actNetCourse = actNetCourseMapper.selectOne(queryWrapper);
            redisVal = BooleanUtils.isTrue(actNetCourse.getEnableInteractive());
            redisUtil.set(redisKey,redisVal,Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
        }
        return redisVal;
    }

    /**
     * 修改互动配置开启状态
     * @param actType
     * @param courseId
     * @param status 0-关闭 1-开启
     */
    public void updateInteractiveStatus(Integer actType, String courseId, boolean status) {
        AuthCheck(courseId,actType);
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        Date nowDate = new Date();
        String date = DateFormatUtils.format(nowDate, "yyyy-MM-dd HH:mm:ss");
        //判断课程是否存在
        ActNetCourse netCourse = getBaseNetCourse(courseId);
        if (null == netCourse) {
            throw new LogicException("该课程不存在");
        }
        //更新V8的数据库
        ActNetCourse updateNetCourse = new ActNetCourse();
        updateNetCourse.setNetCourseId(Integer.valueOf(courseId));
        updateNetCourse.setEnableInteractive(status);
        updateNetCourse.setUpdateId(Integer.valueOf(staffId));
        updateNetCourse.setUpdateName(staffName);
        updateNetCourse.setUpdatedAt(nowDate);
        if (status){
            //开启互动配置的时候，默认会限制滚动条拖动
            updateNetCourse.setLimitProgressBar(true);
        }
        actNetCourseMapper.updateByPrimaryKeySelective(updateNetCourse);
        //删除网络课的缓存。因为互动配置都在新网课生效，所以只用删除新网课的缓存即可。V8缓存不用处理
        actNetCourseService.deleteCourseInfoCache(Integer.valueOf(courseId));
        //删除自定义的互动配置缓存，这是互动配置自己的
        String enableInteractiveKey = CacheKeyEnum.V8NetCourseInteractiveInfo.getKey()+courseId;
        redisUtil.del(enableInteractiveKey);
    }

    /**
     * 控制进度条拖动开关
     * @param courseId
     * @param status
     */
    public void updateProgressBar(Integer actType,String courseId, boolean status) {
        AuthCheck(courseId,actType);
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        Date nowDate = new Date();
        //判断课程是否存在
        ActNetCourse netCourse = getBaseNetCourse(courseId);
        if (null == netCourse) {
            throw new LogicException("该课程不存在");
        }
        //更新V8的数据库
        ActNetCourse updateNetCourse = new ActNetCourse();
        updateNetCourse.setNetCourseId(Integer.valueOf(courseId));
        updateNetCourse.setLimitProgressBar(status);
        updateNetCourse.setUpdateId(Integer.valueOf(staffId));
        updateNetCourse.setUpdateName(staffName);
        updateNetCourse.setUpdatedAt(nowDate);
        actNetCourseMapper.updateByPrimaryKeySelective(updateNetCourse);
        //删除网络课的缓存。因为互动配置都在新网课生效，所以只用删除新网课的缓存即可。V8缓存不用处理
        actNetCourseService.deleteCourseInfoCache(Integer.valueOf(courseId));
    }
}
