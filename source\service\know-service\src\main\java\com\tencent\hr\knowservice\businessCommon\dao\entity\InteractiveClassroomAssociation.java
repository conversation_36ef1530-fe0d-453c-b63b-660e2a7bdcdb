package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * Interactive_classroom_association
 * <AUTHOR>
@Data
public class InteractiveClassroomAssociation implements Serializable {
    /**
     * id
     */
    @TableId(value="id",type= IdType.AUTO)
    private Integer id;

    /**
     * 课程id--数据类型唯一标识，比如课程id，活动id
     */
    private String itemId;

    /**
     * 数据类型
     */
    private Integer actType;

    /**
     * 数据类型名称
     */
    private String actTypeName;

    /**
     * 互动课堂状态 0关闭 1开启
     */
    private Byte classroomStatus;

    /**
     * 互动课堂id
     */
    private String classroomId;

    /**
     * 互动课堂用户地址
     */
    private String classroomUserUrl;

    /**
     * 学员权限范围 0 公开 1 所关联数据自身权限范围  2 特定学员
     */
    private Byte authScope;

    /**
     * 特定目标学员列表
     */
    private String classroomTargetList;

    /**
     * 是否有效 0：无效 1：有效
     */
    private Boolean enable;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 修改人id
     */
    private Integer updateId;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}