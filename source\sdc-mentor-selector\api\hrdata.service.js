import { DataHttp } from 'sdc-core'

export default class HRDataService {
  // 判断是否是生产环境
  static isProd() {
    var env = window.SDC_BUILD_ENV || 'prod'
    // 判断路径是否传参sdcuiEnvironment
    const query = location.href.split('?')[1] || ''
    if (query) {
      const queryItem = query.split('&').find(item => item.includes('sdcuiEnvironment='))
      if (queryItem) {
        env = queryItem.split('=')[1]
      }
    }
    if (!['production', 'prd', 'prod'].includes(env)) {
      return false
    }
    return true
  }

  static http = DataHttp.getInstance({
    multi: true,
    axios: DataHttp.create({
      baseURL: HRDataService.getBaseUrl(),
      withCredentials: true,
      retry: 0,
      headers: { 'X-Requested-With': 'XMLHttpRequest' }
    }),
    map: { result: 'data', success: res => res }
  })

  static getData(params) {
    return HRDataService.http.get('/api/sso/hr-middletier-hrdata-querybaseinfo/api/data', { params })
  }

  static getBaseUrl() {
    const oa = location.host.includes('woa.com') ? 'woa' : 'oa'
    const protocol = location.protocol || 'https:'
    const domain = window.SDC_DATA_DOMAIN || `${protocol}//${HRDataService.isProd() ? '' : 'dev-'}ntsgw.${oa}.com`
    return domain
  }
}
