package com.tencent.hr.knowservice.framework.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class ActLabelRecommendDto {
    /**
     * 标题
     */
//    @NotBlank(message = "标题不能为空")
    private String title;
    /**
     * 内容
     */
//    @NotBlank(message = "内容不能为空")
    private String content;
    /**
     * 需要推荐标签数据
     */
//    @Min(value = 1,message = "数量必须大于0")
    private int num;

}
