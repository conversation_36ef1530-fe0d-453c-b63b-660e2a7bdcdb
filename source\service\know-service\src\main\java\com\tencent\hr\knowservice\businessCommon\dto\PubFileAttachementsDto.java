package com.tencent.hr.knowservice.businessCommon.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PubFileAttachementsDto {

    /**
     * 素材id
     */
    private Integer fileId;

    /**
     * 附属信息类型（Audio 音频 Caption 字幕 Image 截图提示 PlayList 不同码率视频 M-Video 移动端自适应播放列表）
     */
    private String attachementType;

    /**
     *
     */
    private String url;

    /**
     *
     */
    private String contentId;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 显示标题
     */
    private String title;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 生成方式（1 用户上传  2  程序语音识别）
     */
    private Integer generateType;

}
