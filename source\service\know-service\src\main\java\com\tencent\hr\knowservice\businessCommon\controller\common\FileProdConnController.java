package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.dto.*;
import com.tencent.hr.knowservice.businessCommon.service.FileProdConnService;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 延伸课程
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/businessCommon/common/fileProdConn")
public class FileProdConnController {
    @Autowired
    FileProdConnService fileProdConnService;
    /**
     * 获取延伸课程列表
     *
     * @param actId id
     * @param actType 类型
     * @return
     */
    @GetMapping("/query_extend_content_page")
    public TransDTO<List<ExtendContentOutPageDTO>> getExtendContentPage(@RequestParam("act_id") Integer actId,
                                                                        @RequestParam("act_type") Integer actType,
                                                                        @RequestParam(value = "scenario_type",required = false) Integer scenarioType) {
        TransDTO<List<ExtendContentOutPageDTO>> dto = new TransDTO<>();
        if (actId == null) {
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("id不能为空");
        }
        if (actType == null || ActTypeEnum.getActTypeName(actType).equals(ActTypeEnum.UNKNOWN.getActTypeName())){
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("actType类型数据错误");
        }
        List<ExtendContentOutPageDTO> dtos = fileProdConnService.getExtendContentPage(actId,actType,scenarioType);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(dtos);
    }

    /**
     * 获取所有的延伸课程(有最新的平均分和播放量)
     *
     * @param actId id
     * @param actType 类型
     * @return
     */
    @GetMapping("/get_extend_content_list")
    public TransDTO<List<ExtendContentOutPageDTO>> getExtentContentList(@RequestParam("act_id") Integer actId,
                                                                        @RequestParam("act_type") Integer actType,
                                                                        @RequestParam(value = "scenario_type",required = false) Integer scenarioType) {
        TransDTO<List<ExtendContentOutPageDTO>> dto = new TransDTO<>();
        if (actId == null) {
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("id不能为空");
        }
        if (actType == null || ActTypeEnum.getActTypeName(actType).equals(ActTypeEnum.UNKNOWN.getActTypeName())){
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("actType类型数据错误");
        }
        List<ExtendContentOutPageDTO> list = fileProdConnService.getExtentContentList(actId,actType,scenarioType);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(list);
    }

    /**
     * 配置延申学习
     * @param extendContentAddDTO
     * @return
     */
    @PostMapping("/course_conn")
    public TransDTO courseConn(@RequestBody ExtendContentAddDTO extendContentAddDTO){
        String s = fileProdConnService.courseConn(extendContentAddDTO);
        ExtendResultDTO resultDTO = JsonUtils.jsonToBean(s, new TypeReference<ExtendResultDTO>() {});
        TransDTO transDTO = new TransDTO<>();
        if(resultDTO.getSuccess() == 1){
            transDTO.withSuccess(true).withCode(200).withData(resultDTO.getData());
        }else {
            transDTO.withSuccess(false).withCode(500).withMessage(resultDTO.getMessagedetail()).withData(resultDTO.getData());
        }
        return transDTO;
    }

    /**
     * 删除延申学习
     * @param extendDelDTO
     * @return
     */
    @PostMapping("/delete-conn")
    public TransDTO deleteConn(@RequestBody ExtendDelDTO extendDelDTO){
        String s = fileProdConnService.deleteConn(extendDelDTO);
        ExtendResultDTO resultDTO = JsonUtils.jsonToBean(s, new TypeReference<ExtendResultDTO>() {});
        TransDTO transDTO = new TransDTO<>();
        if(resultDTO.getSuccess() == 1){
            transDTO.withSuccess(true).withCode(200).withData(resultDTO.getData());
        }else {
            transDTO.withSuccess(false).withCode(500).withMessage(resultDTO.getMessagedetail()).withData(resultDTO.getData());
        }
        return transDTO;
    }

    /**
     * 设置延申学习置顶
     * @param extendSetTopDTO
     * @return
     */
    @PostMapping("/course-conn-set-top")
    public TransDTO courseConnSetTop(@RequestBody ExtendSetTopDTO extendSetTopDTO){
        String s = fileProdConnService.courseConnSetTop(extendSetTopDTO);
        ExtendResultDTO resultDTO = JsonUtils.jsonToBean(s, new TypeReference<ExtendResultDTO>() {});
        TransDTO transDTO = new TransDTO<>();
        if(resultDTO.getSuccess() == 1){
            transDTO.withSuccess(true).withCode(200).withData(resultDTO.getData());
        }else {
            transDTO.withSuccess(false).withCode(500).withMessage(resultDTO.getMessagedetail()).withData(resultDTO.getData());
        }
        return transDTO;
    }

}
