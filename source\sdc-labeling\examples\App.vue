<template>
  <div id="app">
    <div class="top">
      <el-input v-model="courseId" placeholder="请输入网课act_id"></el-input>
      <el-button @click="getCourseInfo(false)">获取课程详情(用户端)</el-button>
    </div>
    <sdc-labeling
      v-model="form.course_labels"
      :labelNodeEnv="labelNodeEnv"
      :recommend="{
        title: form.course_title,
        desc: form.course_desc
      }"
    />
  </div>
</template>

<script>
import axios from 'axios'
export default {
  name: 'App',
  data() {
    return {
      form: {
        course_labels: [],
        course_title: '',
        course_desc: ''
      },
      courseId: '10805',
      urlInfo: {
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test'
    }
  },
  computed: {
    commonUrl() {
      return this.urlInfo[this.labelNodeEnv]
    }
  },
  mounted() {
    this.getCourseInfo(false)
  },
  methods: {
    async getCourseInfo(isAdmin) {
      // isAdmin:是否在管理端使用
      let url = `${this.commonUrl}/training/api/netcourse/user/courseinfo/get-course-info?act_id=${this.courseId}`
      if (isAdmin) {
        url = `${this.commonUrl}/training/api/netcourse/manage/courseinfo/${this.courseId}`
      }
      let res1 = await axios({
        url: url,
        method: 'get',
        withCredentials: true
      })
      const { data, status, statusText} = res1
      if (status === 200 && data.code === 200) {
        const { course_labels, course_name, course_desc} = data.data
        this.form.course_labels = course_labels
        this.form.course_title = course_name
        this.form.course_desc = course_desc
      } else {
        throw new Error(statusText)
      }
    }
  }
}
</script>

<style  lang="less" scoped>
#app {
  .top {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f5f5f5;
    .el-input {
      width: 200px;
      margin-right: 20px;
    }
  }
}
</style>
