package com.tencent.hr.knowservice.businessCommon.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tencent.hr.common.util.StringUtil;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ClassRoomProxyInDTO;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ClassRoomResultDTO;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ClassroomImportProxyDTO;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;


/**
 * @description: 互动课堂服务应用层
 * @author: liqiang
 * @createDate: 2023/08/21/17:00
 * @version: 1.0
 */
@Service
@Slf4j
public class ClassRoomProxyService extends BaseProxyService {

    @Value("${extapi.classroom.host}")
    private String host;

    /**
     * 创建互动课程
     *
     * @param classRoomProxyInDTO
     * @return
     */
    public String createLesson(ClassRoomProxyInDTO classRoomProxyInDTO) {
        final String api = "/external/create-lesson";
        String jsonBody = JsonUtils.objectToJson(classRoomProxyInDTO);
        String result = postJsonStrWithHeaders2(host, api, jsonBody, new HttpHeaders());
        if (StringUtil.isBlank(result)) {
            log.error("请求互动课堂创建课程时返回为空，请求地址={}，请求参数={}", host + api, jsonBody);
            throw new LogicException("调用互动课堂接口失败！");
        }
        ClassRoomResultDTO roomResultDTO =null;
        try {
            roomResultDTO = JsonUtils.jsonToBean(result, new TypeReference<ClassRoomResultDTO>() {});
        }catch (Exception e){
            log.error("请求互动课堂创建课程时转换失败，请求地址={}，请求参数={}，返回结果={}",host+api,jsonBody,result);
            throw new LogicException("调用互动课堂接口失败！");
        }
        if(roomResultDTO.getState() != 1){
            log.error("请求互动课堂创建课程失败，请求地址={}，请求参数={}，返回结果={}",host+api,jsonBody,result);
            throw new LogicException("调用互动课堂接口失败！"+roomResultDTO.getMsg());
        }
        return roomResultDTO.getCode();
    }

    /**
     * 更新互动课堂 未开课code会变
     * @param classRoomProxyInDTO
     * @return
     */
    public String updateLesson(ClassRoomProxyInDTO classRoomProxyInDTO){
        final String api = "/external/save-lesson";
        String jsonBody = JsonUtils.objectToJson(classRoomProxyInDTO);
        String result = postJsonStrWithHeaders2(host, api, jsonBody, new HttpHeaders());
        if (StringUtil.isBlank(result)) {
            log.error("请求互动课堂修改课程时返回为空，请求地址={}，请求参数={}", host + api, jsonBody);
            throw new LogicException("调用互动课堂接口失败！");
        }
        ClassRoomResultDTO roomResultDTO =null;
        try {
            roomResultDTO = JsonUtils.jsonToBean(result, new TypeReference<ClassRoomResultDTO>() {});
        }catch (Exception e){
            log.error("请求互动课堂修改课程时转换失败，请求地址={}，请求参数={}，返回结果={}",host+api,jsonBody,result);
            throw new LogicException("调用互动课堂接口失败！");
        }
        if(roomResultDTO.getState() != 1){
            log.error("请求互动课堂修改课程失败，请求地址={}，请求参数={}，返回结果={}",host+api,jsonBody,result);
            throw new LogicException("调用互动课堂接口失败！"+roomResultDTO.getMsg());
        }
        return roomResultDTO.getCode();
    }

    /**
     * 推送课程全量学员
     * @param importDto
     */
    public void pushAllStudent(ClassroomImportProxyDTO importDto) {
        final String api = "/external/import-user";
        String jsonBody = JsonUtils.objectToJson(importDto);
        String result = postJsonStrWithHeaders2(host, api, jsonBody, new HttpHeaders());
        if (StringUtil.isBlank(result)) {
            log.error("请求互动课堂推送全量学员数据时返回为空，请求地址={}，请求参数={}", host + api, jsonBody);
            throw new LogicException("调用互动课堂接口失败！");
        }
        ClassRoomResultDTO roomResultDTO =null;
        try {
            roomResultDTO = JsonUtils.jsonToBean(result, new TypeReference<ClassRoomResultDTO>() {});
        }catch (Exception e){
            log.error("请求互动课堂推送全量学员数据时转换失败，请求地址={}，请求参数={}，返回结果={}",host+api,jsonBody,result);
            throw new LogicException("调用互动课堂接口失败！");
        }
        if(roomResultDTO == null || roomResultDTO.getState() != 1){
            log.error("请求互动课堂推送全量学员数据失败，请求地址={}，请求参数={}，返回结果={}",host+api,jsonBody,result);
            throw new LogicException("调用互动课堂接口失败！"+roomResultDTO.getMsg());
        }
    }
}
