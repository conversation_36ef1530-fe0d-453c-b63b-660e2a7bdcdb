<template>
  <fragment>
    <sdc-selector :custom-class="`sdc-unit-selector ${selectClass}`" mode="unit" ref="selector">
      <template slot-scope="{data}" slot="selector-item">
        <div class="selector-item">
          <span class="item-name" v-html="highlight(data.option[selectorMap.unitFullName],data.keyword)"></span>
        </div>
      </template>
    </sdc-selector>
    <sdc-selector-modal ref="modal" :data="selected" :unitID="unitID" :isLimitUnitExpand="isLimitUnitExpand"/>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { selector, highlight, locale } from 'mixins'
  import UnitService from 'api/unit.service'
  import SdcSelector from 'packages/selector'
  import SdcSelectorModal from './modal'

  export default {
    name: 'sdc-unit-selector',
    mixins: [selector, highlight, locale],
    props: {
      unitID: {
        type: [Array, Number],
        default: 0
      },
      icon: {
        type: String,
        default: 'unit'
      },
      getDataList: {
        type: Function,
        default: UnitService.getDataList
      },
      getTreeData: {
        type: Function,
        default: UnitService.getTreeData
      },
      showLastLevels: {
        type: Boolean,
        default: true
      },
      filterEnableFlag: {
        type: Boolean,
        default: true
      },
      includeVirtualUnit: {
        type: Boolean,
        default: false
      },
      containUnitIDPath: {
        type: Boolean,
        default: false
      },
      containUnitLocationCode: {
        type: Boolean,
        default: false
      },
      includeUnitSortIDs: {
        type: Array,
        default: () => []
      },
      isLimitUnitExpand: {
        type: Boolean,
        default: true
      }
    },
    data() {
      const selectorMap = {
        unitID: 'UnitID',
        unitName: 'UnitName',
        unitFullName: 'UnitFullName',
        unitSortID: 'UnitSortID',
        unitOwnershipTypeId: 'UnitOwnershipTypeId',
        unitOwnershipTypeNameCn: 'UnitOwnershipTypeNameCn',
        unitOwnershipTypeNameEn: 'UnitOwnershipTypeNameEn'
      }
      if (this.containUnitIDPath) {
        selectorMap.unitIDPath = 'UnitIDPath'
      }
      if (this.containUnitLocationCode) {
        selectorMap.UnitLocationCode = 'UnitLocationCode'
      }
      if (this.unitSortId > 0) {
        selectorMap.unitSortID = 'UnitSortID'
      }
      return {
        valueKey: selectorMap.unitFullName,
        lastValueKey: selectorMap.unitName,
        nodeKey: selectorMap.unitID,
        filterKey: selectorMap.unitSortID,
        selectedText: this.$st('sdc.unitSelector.selected'),
        totalText: this.$st('sdc.unitSelector.total'),
        modalProps: {
          title: this.$st('sdc.unitSelector.title')
        },
        treeProps: {
          isLeaf: 'isLeaf',
          label: selectorMap.unitName || 'label'
        },
        selectorProps: { ...selectorMap, ...this.props },
        selectorMap,
        queryParams: {
          filterEnableFlag: this.filterEnableFlag,
          includeVirtualUnit: this.includeVirtualUnit,
          LocationString: this.containUnitIDPath,
          LocationCode: this.containUnitLocationCode,
          includeUnitSortIDs: this.includeUnitSortIDs,
          unitID: this.unitID
        },
        filterValue: this.includeUnitSortIDs
      }
    },
    methods: {
      getCurrentItem(item) {
        return {
          key: item[this.nodeKey] || 0,
          text: item[this.valueKey] || '',
          lastText: item[this.lastValueKey] || '',
          tags: { minLength: 17, before: 8, after: 8 },
          modal: { minLength: 21, before: 9, after: 11 }
        }
      }
    },
    components: {
      Fragment,
      SdcSelector,
      SdcSelectorModal
    }
  }
</script>
