package com.tencent.hr.knowservice.courseInteraction.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.StringUtil;
import com.tencent.hr.knowservice.courseInteraction.constans.InteractionConstants;
import com.tencent.hr.knowservice.courseInteraction.dao.InteractiveDao;
import com.tencent.hr.knowservice.courseInteraction.dao.InteractiveRecordDetails;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.InteractiveConfig;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.SelectContent;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveRecordContentEntity;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveRecordEntity;
import com.tencent.hr.knowservice.courseInteraction.dao.mapper.InteractiveRecordMapper;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveRecordInfoDto;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveSubmitDto;
import com.tencent.hr.knowservice.courseInteraction.service.InteractionService;
import com.tencent.hr.knowservice.courseInteraction.service.InteractiveRecordContentService;
import com.tencent.hr.knowservice.courseInteraction.service.InteractiveRecordService;
import com.tencent.hr.knowservice.courseInteraction.vo.InteractiveRecordExcelVo;
import com.tencent.hr.knowservice.courseInteraction.vo.InteractiveRecordVo;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.framework.service.AsyncService;
import com.tencent.hr.knowservice.utils.JsonUtils2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("interactiveRecordService")
public class InteractiveRecordServiceImpl extends ServiceImpl<InteractiveRecordMapper, InteractiveRecordEntity> implements InteractiveRecordService {

    @Autowired
    InteractiveDao interactiveDao;

    @Autowired
    InteractiveRecordMapper recordMapper;

    @Autowired
    InteractiveRecordContentService contentService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    InteractionService interactionService;
    /**
     * 每次存储多少条
     */
    private static final Integer bitchSize = 100;
    private static ObjectMapper objectMapper = new ObjectMapper();


    /**
     * 互动提交
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public TransDTO saveInteractionRecord(InteractiveSubmitDto dto) {
        ContextEntity current = GatewayContext.current();
        InteractionSubmit(dto, current);
        return new TransDTO<>().withCode(HttpStatus.SC_OK).withMessage("提交完成");
    }

    /**
     * 互动提交
     *
     * @param dto
     * @return
     */
    public void InteractionSubmit(InteractiveSubmitDto dto, ContextEntity current) {
        // 获取配置信息
        InteractiveConfig data = interactiveDao.getInteractionConfigById(dto.getInteractiveConfigId());
        // 获取互动记录
        List<InteractiveRecordEntity> recordList = getInteractiveRecordEntities(dto, data, current);
        // 批量保存
        this.saveBatch(recordList, bitchSize);
        asyncService.run(() -> {
            try {
                //  获取互动记录相关信息
                List<InteractiveRecordContentEntity> contentList = getInteractiveRecordContentEntities(dto, data, recordList, current);
                // 保存互动记录相关信息
                contentService.saveBatch(contentList, bitchSize);
            } catch (Exception e) {
                log.error("互动记录存储出错, 参数信息:{}:错误信息:{}", dto.toString(), e);
            }
        });
    }

    /**
     * 获取互动记录相关信息
     *
     * @param dto
     * @param data
     * @param recordList
     * @param current
     * @return
     */
    private List<InteractiveRecordContentEntity> getInteractiveRecordContentEntities(InteractiveSubmitDto dto, InteractiveConfig data, List<InteractiveRecordEntity> recordList, ContextEntity current) {
        List<InteractiveRecordContentEntity> contentList = new ArrayList<>();
        dto.getAnswers().stream().flatMap(answer -> {
            assert data != null;
            return data.getConfigurationsOfSelect().stream().flatMap(configurations ->
                    configurations.getSelectContent().stream().map(content -> {
                        // 找出当前项对应的题目
                        if (dto.getInteractiveId().equals(configurations.getInteractiveId()) && answer.getQuestionId().equals(content.getQuestionId())) {
                            Optional<InteractiveRecordEntity> optionalRecordEntity = recordList.stream()
                                    .filter(recordEntity -> recordEntity.getQuestionId().equals(content.getQuestionId()) && recordEntity.getInteractiveId().equals(configurations.getInteractiveId()) && recordEntity.getCreatorId().toString().equals(current.getStaffId()))
                                    .findFirst();
                            if (optionalRecordEntity.isPresent()) {
                                InteractiveRecordEntity recordEntity = optionalRecordEntity.get();
                                InteractiveRecordContentEntity record = new InteractiveRecordContentEntity();
                                record.setRecordId(recordEntity.getId());
                                record.setInteractiveConfigId(dto.getInteractiveConfigId());
                                record.setInteractiveId(configurations.getInteractiveId());
                                record.setIntroduction(configurations.getIntroduction());
                                record.setIntroductionEn(configurations.getIntroductionEn());
                                record.setSelectContent(selectContentToJson(configurations.getSelectContent(), recordEntity));
                                record.setActiveTime(Integer.valueOf(configurations.getActiveTime()));
                                record.setEnabled(true);
                                record.setCreatorId(Integer.valueOf(current.getStaffId()));
                                record.setCreatorName(current.getStaffName());
                                record.setCreatedAt(new Date());
                                record.setUpdateId(Integer.valueOf(current.getStaffId()));
                                record.setUpdateName(current.getStaffName());
                                record.setUpdatedAt(new Date());
                                return record;
                            }
                        }
                        return null;
                    })
            );
        }).filter(Objects::nonNull).forEach(contentList::add);
        return contentList;
    }

    /**
     * 获取互动记录
     *
     * @param dto
     * @param data
     * @param current
     * @return
     */
    private List<InteractiveRecordEntity> getInteractiveRecordEntities(InteractiveSubmitDto dto, InteractiveConfig data, ContextEntity current) {
        return dto.getAnswers().stream().flatMap(answer ->
                {
                    assert data != null;
                    return data.getConfigurationsOfSelect().stream().flatMap(configurations ->
                            configurations.getSelectContent().stream().map(content -> {
                                if (dto.getInteractiveId().equals(configurations.getInteractiveId()) && answer.getQuestionId().equals(content.getQuestionId())) {
                                    InteractiveRecordEntity record = new InteractiveRecordEntity();
                                    record.setRecordId(dto.getRecordId());
                                    record.setInteractiveId(dto.getInteractiveId());
                                    record.setInteractiveConfigId(dto.getInteractiveConfigId());
                                    record.setQuestionId(answer.getQuestionId());
                                    record.setActType(data.getActType());
                                    record.setActTypeName(data.getActTypeName());
                                    record.setCourseId(data.getCourseId());
                                    record.setActiveType(content.getActiveType());
                                    record.setCompletionConditions(content.getChooseTypeConfig() != null ? content.getChooseTypeConfig().getCompletionConditions() : null);
                                    record.setActiveAnswer(answer.getActiveAnswer());
                                    if (content.getActiveType().equalsIgnoreCase(InteractionConstants.selectActiveType.CHOOSE.getValue())) {
                                        if (answer.getActiveAnswer() != null) {
                                            record.setEnabledCorrect(correctingQuestion(content.getCorrectAnswerEn() == null ? content.getCorrectAnswer() : content.getCorrectAnswerEn(), answer.getActiveAnswer()));
                                        } else {
                                            record.setEnabledCorrect(false);
                                        }
                                    }
                                    record.setScore(answer.getScore());
                                    record.setCreatorId(Integer.valueOf(current.getStaffId()));
                                    record.setCreatorName(current.getStaffName());
                                    record.setCreatedAt(new Date());
                                    record.setUpdateId(Integer.valueOf(current.getStaffId()));
                                    record.setUpdateName(current.getStaffName());
                                    record.setUpdatedAt(new Date());
                                    record.setEnabled(true);
                                    return record;
                                }
                                return null;
                            })
                    );
                }
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 判断试题是否正确
     *
     * @param answers
     * @param activeAnswer
     * @return
     */
    private boolean correctingQuestion(List<String> answers, String activeAnswer) {
        if (StringUtil.isBlank(activeAnswer)) {
            return false;
        }
        List<String> userAnswer = Arrays.stream(activeAnswer.split(",")).collect(Collectors.toList());
        Collections.sort(answers);
        Collections.sort(userAnswer);
        if (answers.equals(userAnswer)) {
            return true;
        }
        return false;
    }

    /**
     * 互动记录分页查询
     *
     * @param current
     * @param size
     * @return
     */
    @Override
    public TransDTO getInteractiveRecordPage(int current, int size, Integer actType, String courseId) {
        interactionService.AuthCheck(courseId,actType);
        Page<InteractiveRecordEntity> page = getInteractiveRecordEntityPage(current, size, actType, courseId);
        List<InteractiveRecordVo> list = convertToInteractiveRecordVoList(page.getRecords());
        Page<InteractiveRecordVo> res = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setRecords(list);
        return new TransDTO<>().withCode(HttpStatus.SC_OK).withData(res);
    }

    /**
     * 查询需要导出的全量数据
     *
     * @return
     */
    @Override
    public List<InteractiveRecordExcelVo> getInteractiveRecordList(int current, int size, Integer actType, String courseId) {
        interactionService.AuthCheck(courseId,actType);
        return convertToInteractiveRecordExcelList(getInteractiveRecordEntityPage(current, size, actType, courseId).getRecords());
    }

    /**
     * 获取互动记录
     *
     * @param current
     * @param size
     * @return
     */
    private Page<InteractiveRecordEntity> getInteractiveRecordEntityPage(int current, int size, Integer actType, String courseId) {
        QueryWrapper<InteractiveRecordEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("enabled", 1);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("course_id", courseId);
        queryWrapper.orderByDesc("created_at");
        return recordMapper.selectPage(new Page<>(current, size), queryWrapper);
    }


    /**
     * 获取需要导出的互动记录
     *
     * @param records
     * @return
     */
    private List<InteractiveRecordExcelVo> convertToInteractiveRecordExcelList(List<InteractiveRecordEntity> records) {
        if (records == null || records.isEmpty()) {
            return null;
        }
        List<Integer> recordIds = records.stream().map(InteractiveRecordEntity::getId).collect(Collectors.toList());
        List<InteractiveRecordContentEntity> contents = contentService.list(new QueryWrapper<InteractiveRecordContentEntity>().lambda().in(InteractiveRecordContentEntity::getRecordId, recordIds).eq(InteractiveRecordContentEntity::getEnabled, true));
        AtomicInteger order = new AtomicInteger(1);
        return records.stream().map(record -> {
            InteractiveRecordExcelVo vo = new InteractiveRecordExcelVo();
            vo.setOrder(order.getAndIncrement());
            BeanUtils.copyProperties(record, vo);
            vo.setEnabledCorrect(record.getEnabledCorrect() == null ? "" : (record.getEnabledCorrect() ? "是" : "否"));
            vo.setActiveType(record.getActiveType().equalsIgnoreCase(InteractionConstants.selectActiveType.CHOOSE.getValue()) ? "选择" : "投票");
            InteractiveRecordContentEntity contentEntity = contents.stream().filter(content -> content.getRecordId().equals(record.getId())).findFirst().orElse(null);
            if (contentEntity != null) {
                try {
                    vo.setTitle(getQuestionText(contentEntity).getQuestionText());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
//                List<ConfigurationsOfSelect> config = interactiveDao.getInteractionTitleById(contentEntity.getInteractiveConfigId()).getConfigurationsOfSelect();
//                for (ConfigurationsOfSelect configurations : config) {
//                    if (contentEntity.getInteractiveId().equals(configurations.getInteractiveId())) {
//                        vo.setTitle(configurations.getTitle());
//                    }
//                }
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 活动记录数据处理
     *
     * @param records 互动记录数据
     * @return
     */
    private List<InteractiveRecordVo> convertToInteractiveRecordVoList(List<InteractiveRecordEntity> records) {
        List<Integer> recordIds = records.stream().map(InteractiveRecordEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordIds)) {
            return new ArrayList<>();
        }
        //获取所有记录切片内容
        List<InteractiveRecordContentEntity> contents = contentService.list(new QueryWrapper<InteractiveRecordContentEntity>().lambda().in(InteractiveRecordContentEntity::getRecordId, recordIds).eq(InteractiveRecordContentEntity::getEnabled, true));

        return records.stream().map(record -> {
            //匹配该记录对应的切片内容
            InteractiveRecordContentEntity contentEntity = contents.stream().filter(content -> content.getRecordId().equals(record.getId())).findFirst().orElse(null);
            InteractiveRecordVo vo = new InteractiveRecordVo();
            BeanUtils.copyProperties(record, vo);
            if (contentEntity != null) {
                try {
                    SelectContent selectContent = getQuestionText(contentEntity);
                    vo.setTitle(selectContent.getQuestionText());
                    vo.setTitleEn(selectContent.getQuestionTextEn());
                } catch (IOException e) {
                    log.error("互动记录数据处理出错:{}", e);
                }
                contentEntity.setSelectContent(null);
                vo.setIntroduction(contentEntity.getIntroduction());
                vo.setIntroductionEn(contentEntity.getIntroductionEn());
                vo.setSelectContent(contentEntity.getSelectContent());
                vo.setActiveTime(contentEntity.getActiveTime());
//                List<ConfigurationsOfSelect> config = interactiveDao.getInteractionTitleById(contentEntity.getInteractiveConfigId()).getConfigurationsOfSelect();
//                for (ConfigurationsOfSelect configurations : config) {
//                    if (contentEntity.getInteractiveId().equals(configurations.getInteractiveId())) {
//                        vo.setTitle(configurations.getTitle());
//                        vo.setTitleEn(configurations.getTitleEn());
//                    }
//                }
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取标题
     *
     * @param contentEntity
     * @return
     * @throws IOException
     */
    private static SelectContent getQuestionText(InteractiveRecordContentEntity contentEntity) throws IOException {
        return objectMapper.readValue(contentEntity.getSelectContent(), SelectContent.class);
//        List<SelectContent> selectContents = JsonUtils2.jsonToBean(contentEntity.getSelectContent(), new TypeReference<List<SelectContent>>() {});
//        //当前一个互动只能配置一个试题，其标题就是这个试题的题干
//        assert selectContents != null;
//        return selectContents.get(0);
    }

    /**
     * 数组转化成json
     *
     * @param list
     * @return
     */
    public String selectContentToJson(List<SelectContent> list, InteractiveRecordEntity record) {
        SelectContent selectedContent = null;
        for (SelectContent content : list) {
            if (content.getQuestionId().equals(record.getQuestionId())) {
                selectedContent = content;
                break;
            }
        }
        if (selectedContent != null) {
            try {
                return objectMapper.writeValueAsString(selectedContent);
            } catch (JsonProcessingException e) {
                log.error("互动提交 SelectContent 数据转化失败！:{}", e);
            }
        }
        return null;
    }

    /**
     * 获取互动记录详情
     *
     * @param id
     * @return
     */
    @Override
    public InteractiveRecordInfoDto getInteractiveRecord(Integer id) throws IOException {
        InteractiveRecordEntity interactiveRecord = recordMapper.selectById(id);
        if (null == interactiveRecord) {
            throw new LogicException("该记录不存在！");
        }
        interactionService.AuthCheck(interactiveRecord.getCourseId(),interactiveRecord.getActType());
        InteractiveRecordInfoDto result = new InteractiveRecordInfoDto();
        BeanUtils.copyProperties(interactiveRecord, result);

        //装配试题内容
        QueryWrapper<InteractiveRecordContentEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enabled", 1);
        queryWrapper.eq("record_id", id);
        InteractiveRecordContentEntity recordContent = contentService.getOne(queryWrapper);
        if (recordContent != null) {
//            List<SelectContent> selectContents = JsonUtils2.jsonToBean(recordContent.getSelectContent(), new TypeReference<List<SelectContent>>() {});
//            SelectContent selectContent = selectContents.stream().filter(item -> item.getQuestionId().equals(interactiveRecord.getQuestionId())).findFirst().orElse(null);
            SelectContent selectContent = JsonUtils2.jsonToBean(recordContent.getSelectContent(), new TypeReference<SelectContent>() {});
            result.setSelectContent(selectContent);
            result.setTitle(selectContent.getQuestionText());
            result.setTitleEn(selectContent.getQuestionTextEn());
            result.setIntroduction(recordContent.getIntroduction());
            result.setIntroductionEn(recordContent.getIntroductionEn());
        }

        return result;
    }

    @Override
    public TransDTO getInteractiveRecords(String id) {
        InteractiveRecordDetails details = recordMapper.getInteractiveRecords(id);
        return new TransDTO().withData(details).withCode(HttpStatus.SC_OK);
    }
}
