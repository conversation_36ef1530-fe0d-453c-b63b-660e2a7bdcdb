<template>
  <div class="app">
    <div class="main" id="main">
        测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水
        印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面
        测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水
        印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面
        测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水
        印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面
        测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水
        印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面
        测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水
        印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面
        测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水
        印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面测试水印页面
    </div>
    <sdcWaterMark
      class="sdc-mark"
      ref="watermark"
      :targetId="watermark.targetId"
      :text="watermark.textContent"
      :canvasUserOptions="watermark.canvasUserOpt"
      :wmUserOptions="watermark.wmUserOpt"
      :isManualInit="false"
    />
  </div>
</template>
<script>
export default {
  name: 'App',
  data() {
    return {
      watermark: {
        targetId: 'main', // 水印目标元素id
        textContent: 'v_cxiqchen',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '40px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      }
    }
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.app {
  #main {
    margin: 0 auto;
    width: 1000px;
    height: 1000px;
    background-color: bisque;
    position: relative; // 注意：定位一定要加
    margin-top: 50px;
    color: #999999;
  }
}
</style>
