package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PubLabelInfoDto extends PubLabelBasicDto {

    /**
     * 课程id
     */
    private String courseId;
    /**
     * 类型 2网课 15课单
     */
    private Integer actType;
    /**
     * 标签关联课程时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date joinTime;
    /**
     * 分类id
     */
    private Integer categoryId;
    /**
     * 是否是热门标签，手动运营。1-是，0-不是
     */
    private Integer labelHotEnable;
    /**
     * 分类全名
     */
    private String categoryFullName;

}
