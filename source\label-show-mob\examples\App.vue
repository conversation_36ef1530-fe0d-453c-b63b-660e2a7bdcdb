<template>
  <div id="app">
    <div class="top">
      <el-input v-model="courseId" placeholder="请输入网课act_id"></el-input>
      <el-button @click="getCourseInfo(true)">获取课程详情(管理端:默认)</el-button>
      <el-button @click="getCourseInfo(false)">获取课程详情(用户端)</el-button>
    </div>
    <div class="sdc-main">
      <div class="label-name">标签：</div>
      <!-- v-model="form.course_labels" -->
      <sdc-label-show-mob class="label-main"
        :actType="actType"
        :courseId="courseId"
        :labelNodeEnv="labelNodeEnv"
        :showAll="false"
        :isH5="true"
        :isMock="false"
        :isPreview="isPreview"
        :previewLbael="previewLbael"
        />
    </div>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  name: 'App',
  data() {
    return {
      isAdmin: true, // 是否在管理端使用
      form: {
        course_labels: []
      },
      // courseId: '10805',
      courseId: '10769',
      urlInfo: {
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      labelNodeEnv: 'test', // 模拟测试环境
      courseInfo: {
        page: ''
      },
      isPreview: true, // 是否是预览功能
      previewLbael: [] // 预览时显示的标签 传入到组件里面显示
    }
  },
  computed: {
    commonUrl() {
      return this.urlInfo[this.labelNodeEnv]
    },
    actType() {
      let act_type = null
      if (this.form.course_labels) {
        act_type = this.form.course_labels[0] ? this.form.course_labels[0].act_type : null
      }
      return act_type || '2'
    }
  },
  methods: {
    async getCourseInfo(isAdmin) {
      this.isAdmin = isAdmin
      let url = `${this.commonUrl}/training/api/netcourse/user/courseinfo/get-course-info?act_id=${this.courseId}`
      if (isAdmin) {
        url = `${this.commonUrl}/training/api/netcourse/manage/courseinfo/${this.courseId}`
      }
      let res1 = await axios({
        url: url,
        method: 'get',
        withCredentials: true
      })
      const { data, status, statusText} = res1
      if (status === 200 && data.code === 200) {
        const { course_labels, course_name } = data.data
        this.form.course_labels = course_labels
        this.courseInfo.page = course_name
      } else {
        throw new Error(statusText)
      }
    }
  },
  mounted() {
    this.getCourseInfo(true)
  }
}
</script>

<style lang="less" scoped>
#app {
  .top {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f5f5f5;
    .el-input {
    }
    .el-button {
      margin: 10px 0 0 0;
    }
  }
  .sdc-main {
    display: flex;
    flex-direction: column;
    padding: 10px;
    // border: 1px solid red;
    .label-name {
      height: 25px;
      line-height: 25px;
      font-size: 16px;
      margin-bottom: 10px;
    }
    .label-main {
      flex: 1;
    }
  }
}
</style>
