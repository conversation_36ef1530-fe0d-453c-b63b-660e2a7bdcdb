<template>
  <div class="label-select-component">
    <div class="cascader-component">
      <div
        class="cascader-select"
        :class="[{ focus: isShowDropdown }]"
        @click.stop="togglePopper(true)"
      >
        <div class="select-input">
          <p class="tag" v-for="item in selectedLabels" :key="item.label_id">
            <span>{{ item.label_name }}</span
            ><i class="tag-close" @click.stop="deleteLabel(item)">×</i>
          </p>
          <input
            type="text"
            v-model.trim="inputVal"
            spellcheck="false"
            maxlength="20"
            :placeholder="placeholder"
            @input="search"
          />
          <!-- @keydown="keydownEvent" -->
        </div>
        <div class="clear" v-if="showClear">
          <span class="icon" @click.stop="clear">×</span>
        </div>
      </div>
      <div
        class="cascader-dropdown"
        v-show="isShowDropdown"
        @click.stop="togglePopper(true)"
      >
        <!-- 联级面板 -->
        <div class="cascader-panel" v-if="!inputVal">
          <template v-for="(item, index) in labelPanelOptions">
            <div
              class="cascader-list"
              :key="item.title"
              v-if="item.data.length"
              :data-panel="item.title"
              @scroll="scrollEvent"
            >
              <template v-if="item.title !== '四级分类'">
                <p
                  class="cascader-node"
                  :class="[{ active: item.active === subItem.category_id }]"
                  v-for="(subItem, i) in item.data"
                  :key="i"
                  @click="changeCategory(index, subItem)"
                >
                  {{ subItem.category_name }}
                  <span
                    class="arrow"
                    :class="[
                      { 'arrow-active': item.active === subItem.category_id }
                    ]"
                  ></span>
                </p>
              </template>
              <template v-if="item.title === '四级分类'">
                <el-checkbox
                  class="cascader-node"
                  :class="[{ active: isChecked(subItem.label_id) }]"
                  v-for="subItem in item.data"
                  :key="subItem.label_id"
                  :value="isChecked(subItem.label_id)"
                  @change="changeSelectedLabel(subItem)"
                >
                  <span class="cascader-node">{{ subItem.label_name }}</span>
                </el-checkbox>
              </template>
            </div>
          </template>
        </div>
        <!-- 搜索面板 -->
        <div class="search-panel" v-if="inputVal">
          <template v-if="searchList.length">
            <el-checkbox
              class="search-item"
              v-for="item in searchList"
              :key="item.label_id"
              :label="item"
              :value="isChecked(item.label_id)"
              @change="changeSelectedLabel(item)"
            >
              <span class="label" v-html="item.label_full_name"></span>
            </el-checkbox>
          </template>
          <template v-else>
            <p class="search-empty">暂无数据</p>
          </template>
        </div>
      </div>
    </div>
    <div class="recommend" v-if="showRecommend">
      <el-tooltip placement="bottom-start">
        <span class="tip"><i class="el-icon-warning-outline"></i>推荐标签</span>
        <div class="tip-popup" slot="content">
          <slot name="tip">
            <!-- <p>1.一个项目最多可添加{{maxNum}}个标签</p> -->
            <p>1.单个标签字符最多不超过20字符 (10个汉字/20个英文字母或数字)</p>
            <p>2.建议优先从官方标签库和推荐标签中选择，避免重复/无关标签</p>
          </slot>
        </div>
      </el-tooltip>
      <div class="recommend-label">
        <span
          class="tag"
          v-for="item in recommendList"
          :key="item.label_id"
          @click="changeSelectedLabel(item, 'recommend')"
        >
          {{ item.label_name }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
// const httpHost = process.env.VUE_APP_PORTAL_HOST_WOA
const debounce = (fn, wait = 200) => {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this)
    }, wait)
  }
}
export default {
  name: 'sdc-search-label',
  props: {
    // 双向绑定值
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择标签，支持多标签筛选'
    },
    // 禁止回车创建
    disableCreate: {
      type: Boolean,
      default: true
    },
    // 是否有推荐（课程名称-title、描述-desc）
    recommend: {
      type: Object,
      default () {
        return null
      }
    },
    // 最多选择数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 可清空
    clearable: {
      type: Boolean,
      default: true
    },
    showLabelList: {
      type: Array,
      default: () => []
    },
    labelNodeEnv: {
      type: String,
      default: 'production'
    }
  },
  data () {
    return {
      inputVal: '',
      searchList: [],
      recommendList: [],
      labelOptions: [],
      categoryTree: [
        { title: '一级分类', data: [], active: 0 },
        { title: '二级分类', data: [], active: 0 },
        { title: '三级分类', data: [], active: 0 },
        { title: '四级分类', data: [], active: 0 }
      ],
      selectedLabels: [], // 已选择标签
      isShowDropdown: false, // 显示下拉弹窗
      urlInfo: {
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      page: {
        current: 1,
        size: 20,
        total: 0,
        isRequesting: false
      }
    }
  },
  computed: {
    // 联级面板数据
    labelPanelOptions () {
      this.categoryTree.forEach((item, index) => {
        let levelData = []
        if (index > 0) {
          // 取上一级active值的sub_categories数据
          const _data = this.categoryTree[index - 1].data
          const _active = this.categoryTree[index - 1].active
          let _i = _data.findIndex(c => c.category_id === _active)
          if (_i >= 0) {
            levelData =
              this.categoryTree[index - 1].data[_i].sub_categories || []
          } else {
            levelData = []
          }
        } else {
          levelData = this.labelOptions
        }
        item.data = levelData
      })
      return this.categoryTree
    },
    // 显示清空按钮
    showClear () {
      return this.clearable && this.selectedLabels.length
    },
    isChecked () {
      return label_id => {
        const _index = this.selectedLabels.findIndex(
          item => item.label_id === label_id
        )
        return _index > -1
      }
    },
    // 是否显示底部推荐
    showRecommend () {
      return !!this.recommend
    },
    commonUrl () {
      return location.hostname.endsWith('.woa.com')
        ? this.urlInfo[this.labelNodeEnv]
        : this.urlInfo[this.labelNodeEnv]
    }
  },
  watch: {
    value: {
      handler (newVal) {
        this.selectedLabels = newVal
      },
      immediate: true
    },
    'recommend.title' () {
      this.getRecommendLabels()
    },
    'recommend.desc' () {
      this.getRecommendLabels()
    },
    selectedLabels (newVal) {
      this.$emit('input', newVal)
      this.$emit('getSelectedLabelList', newVal)
    }
  },
  mounted () {
    this.getLabelCategory()
    document.addEventListener('click', this.hideDropdown)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.hideDropdown)
  },
  methods: {
    getLabelCategory () {
      axios
        .get(
          `${this.commonUrl}/training/api/label/user/category/category_tree`,
          {
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            let list = []
            if (this.showLabelList.length > 0) {
              list = res.data.data.filter(item => {
                return (
                  item.sub_categories.length &&
                  this.showLabelList.includes(item.category_name)
                )
              })
            } else {
              list = res.data.data.filter(item => {
                return item.sub_categories.length
              })
            }
            this.labelOptions = list
          }
        })
    },
    getLabelLeaf (id) {
      this.page.isRequesting = true
      axios
        .get(
          `${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params: {
              page_no: this.page.current,
              page_size: this.page.size,
              category_id: id,
              label_type: '5',
              order_by:'level'
            },
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            this.labelPanelOptions[3].data = this.labelPanelOptions[3].data.concat(
              res.data.data.records || []
            )
            this.page.total = res.data.data.total
          }
        })
        .finally(() => {
          this.page.isRequesting = false
        })
    },
    // 三级叶子节点滚动加载更多
    scrollEvent (event) {
      const ele = event.target
      const isGap = ele.scrollHeight - ele.clientHeight - ele.scrollTop <= 40
      const isLeafPanel = ele.dataset.panel === '四级分类'
      if (isGap && isLeafPanel) {
        const isOverTotal =
          this.labelPanelOptions[3].data.length >= this.page.total
        if (this.page.isRequesting || isOverTotal) {
          return
        }
        this.page.current += 1
        this.getLabelLeaf(this.labelPanelOptions[1].active)
      }
    },
    // 推荐标签
    getRecommendLabels: debounce(function () {
      if (!this.showRecommend) {
        return
      }
      axios
        .post(
          `${this.commonUrl}/training/api/businessCommon/manage/label/get_recommend_labels_v2`,
          {
            object: 'online_course',
            title: (this.recommend && this.recommend.title) || '',
            content: (this.recommend && this.recommend.desc) || ''
            // num: this.maxNum
          },
          {
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            this.recommendList = res.data.data.map(item => {
              return {
                ...item,
                label_name: item.name
              }
            })
          }
        })
    }, 1500),
    // 搜索
    getSearchList: debounce(function () {
      axios
        .get(
          // `${this.commonUrl}/training/api/label/user/labelinfo/label_match`,
         `${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params: {
              page_no: 1,
              page_size: 6,
              search_name: this.inputVal,
              label_type: '5',
              order_by: 'level'
              // name: this.inputVal,
              // showCount: 6,
            },
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            if (res.data.data.records) {
              const highlightedArray = res.data.data.records.map(obj => {
                const highlightedName = `<font style="font-size: 14px;margin-right: 8px;" color="#000000">${obj.label_name.replace(
                  new RegExp(this.escapeRegExp(this.inputVal), 'g'),
                  '<font color="#0052D9">$&</font>'
                )}</font>`
                const highlightedCategory = `<font style="font-size: 12px;" color="#153000000">${obj.category_full_name.replace(
                  new RegExp(this.escapeRegExp(this.inputVal), 'g'),
                  '<font color="#0052D9">$&</font>'
                )}</font>`
                const labelFullName = `${highlightedName}   ${highlightedCategory}`
                return { ...obj, label_full_name: labelFullName }
              })
              console.log(highlightedArray)
              this.searchList = highlightedArray || []
            }
            // this.searchList = res.data.data || []
          }
        })
    }, 500),
    // 搜索
    search () {
      if (!this.inputVal) {
        return
      }
      const reg = /[^a-zA-Z0-9\u4e00-\u9fa5]/g
      if (reg.test(this.inputVal)) {
        this.inputVal = this.inputVal.replace(reg, '')
        return
      }
      this.getSearchList()
    },
    escapeRegExp (string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    },
    // 键盘回车事件创建自定义标签
    // keydownEvent (event) {
    //   // 如果禁止创建或输入值不存在或者不是Enter键或刚已经创建则不执行
    //   if (
    //     this.disableCreate ||
    //     !this.inputVal ||
    //     event.key !== 'Enter' ||
    //     event.code !== 'Enter'
    //   ) {
    //     return
    //   }
    //   this.changeSelectedLabel(
    //     {
    //       label_name: this.inputVal,
    //       label_id: 0,
    //       label_type: 2
    //     },
    //     'create'
    //   )
    // },
    changeCategory (level, label) {
      this.labelPanelOptions[level].active = label.category_id
      if (level === 2 || label.sub_categories.length === 0) {
        this.page.current = 1
        this.labelPanelOptions[3].data = []
        this.getLabelLeaf(label.category_id)
      }
    },
    // 当前标签添加或删除管理
    changeSelectedLabel (label, type) {
      const _index = this.selectedLabels.findIndex(item => {
        return (
          item.label_id === label.label_id &&
          item.label_name === label.label_name
        )
      })
      if (_index === -1) {
        // 如果不存在且数量大于限制则无法添加
        // if (this.maxNum && this.selectedLabels.length >= this.maxNum) {
        //   this.$message.warning(`最多可添加${this.maxNum}标签`)
        //   return
        // }
        this.selectedLabels.push(label)
        if (type === 'create') {
          this.inputVal = ''
        }
      } else {
        // 点击推荐如果存在则不删除
        if (type === 'recommend' || type === 'create') {
          // this.$message.warning(`标签已${type === 'recommend' ? '选择' : '创建'}~`)
          return
        }
        this.selectedLabels.splice(_index, 1)
      }
    },
    // 显示隐藏面板
    togglePopper (visible) {
      const isDef = visible !== undefined && visible !== null
      this.isShowDropdown = isDef ? visible : !this.isShowDropdown
    },
    // 下拉框隐藏事件
    hideDropdown () {
      this.isShowDropdown = false
      this.inputVal = ''
      this.searchList = []
      this.$emit('hideDropdwon', this.selectedLabels)
    },
    deleteLabel (label) {
      const _index = this.selectedLabels.findIndex(item => {
        return item.label_id === label.label_id
      })
      this.selectedLabels.splice(_index, 1)
    },
    // 清空标签
    clear () {
      this.selectedLabels = []
    }
  }
}
</script>

<style lang="less" scoped>
.label-select-component {
  width: 100%;
  .cascader-component {
    position: relative;
  }
  .cascader-select {
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    &.focus {
      border: 1px solid #0052d9ff;
    }
    .select-input {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .tag {
        max-width: 100%;
        height: 24px;
        background: #e7e7e7;
        border-radius: 0 12px 12px 0;
        box-sizing: border-box;
        color: rgba(0, 0, 0, 0.9);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-flex;
        align-items: center;
        margin: 3px 0 3px 4px;
        padding: 0 8px;
        .tag-close {
          width: 16px;
          height: 16px;
          color: rgba(0, 0, 0, 0.4);
          font-size: 14px;
          font-weight: bold;
          font-style: normal;
          line-height: 16px;
          text-align: center;
          margin-left: 2px;
          cursor: pointer;
        }
      }
      & > input {
        flex: 1;
        height: 30px;
        min-width: 60px;
        padding: 2px 8px;
        color: #606266;
        font-size: 14px;
        border: 0;
        outline: none;
        box-sizing: border-box;
        &::placeholder {
          color: rgba(0, 0, 0, 0.4);
          font-size: 13px;
        }
      }
    }
    .clear {
      margin-right: 8px;
      & > span {
        width: 14px;
        height: 14px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 50%;
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        line-height: 13px;
        display: flex;
        justify-content: center;
        // align-items: center;
        cursor: pointer;
      }
    }
  }
  .recommend {
    margin-top: 6px;
    line-height: 24px;
    display: flex;
    .tip {
      color: #666;
      font-size: 12px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      & > i {
        // width: 14px;
        // height: 14px;
        // background: url('data:image/png;base64,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') no-repeat center / 100% 100%;
        margin-right: 5px;
        font-size: 14px;
      }
    }
    .recommend-label {
      margin-left: 16px;
      .tag {
        max-width: 100%;
        height: 24px;
        background: #e7e7e7;
        border-radius: 4px;
        box-sizing: border-box;
        color: rgba(0, 0, 0, 0.9);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-flex;
        align-items: center;
        margin: 0 4px;
        padding: 0 8px;
        cursor: pointer;
      }
    }
  }
  .cascader-dropdown {
    // max-width: 100%;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 10px;
    background: #ffffffff;
    box-shadow: 0 3px 14px 2px rgba(0, 0, 0, 0.1),
      0 8px 10px 1px rgba(0, 0, 0, 0.06), 0 5px 5px -3px rgba(0, 0, 0, 0.05);
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 6px;
    z-index: 10;
    .cascader-panel {
      border-radius: 4px;
      display: flex;
      .cascader-list {
        min-width: 180px;
        max-height: 320px;
        // overflow: hidden;
        overflow-y: auto;
        padding: 8px;
        padding-right: 25px;
        &::-webkit-scrollbar {
          width: 3px;
          height: 5px;
        }
        &::-webkit-scrollbar-track {
          background: #fff;
        }
        &::-webkit-scrollbar-thumb {
          background: #c9cdd4;
        }
        &:not(:last-of-type) {
          border-right: 1px solid #e7e7e7;
        }
      }
      .cascader-node {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        margin: 0;
        position: relative;
        &:hover {
          color: #0054e1;
          background: #ecf2fe;
        }
        &.active {
          border-radius: 4px;
          background: #ecf2fe;
          margin: 3px 0;
          color: #0052d9;
        }
        &:hover .arrow {
          border-top: 1px solid #0054e1;
          border-right: 1px solid #0054e1;
        }
        .arrow {
          position: absolute;
          right: 20px;
          top: 50%;
          width: 7px;
          height: 7px;
          border-top: 1px solid #000000;
          border-right: 1px solid #000000;
          transform: translateY(-50%) rotate(45deg);
        }
        .arrow-active {
          border-top: 1px solid #0054e1;
          border-right: 1px solid #0054e1;
        }
      }
    }
    .search-panel {
      min-width: 220px;
      padding: 8px;
      .search-item {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        &:hover {
          background: #ecf2fe;
          padding-right: 0;
        }
        &.active {
          background: #ecf2fe;
          color: #0052d9;
        }
      }
      .search-empty {
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
