package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PubFilePageDto {
    private Integer current;
    private Integer size;

    /**
     * 文件类型
     */
    @JsonProperty("fileType")
    private List<String> fileType;

    /**
     * 文件名称
     */
    @JsonProperty("fileName")
    private String fileName;

    /**
     * 上传人
     */
    @JsonProperty("creatorName")
    private String creatorName;

    @JsonProperty("sort_order")
    private String sort_order;

    @JsonProperty("sort_by")
    private String sort_by;

    /**
     * 来源
     */
    @JsonProperty("contentType")
    private String contentType;

    /**
     * 转码状态
     */
    private List<Integer> status;

    /**
     * 存储位置 1-云资源；2-内网资源
     */
    @JsonProperty("storageLocation")
    private Integer storageLocation;

    /**
     * 上传开始时间
     */
    @JsonProperty("startToCloudTime")
    private Date startToCloudTime;

    /**
     * 上传结束时间
     */
    @JsonProperty("endToCloudTime")
    private Date endToCloudTime;
}
