package com.tencent.hr.knowservice.businessCommon.dto.exam;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ExamSearchDto {
    /**
     * 应用编码
     */
    @JsonProperty("app_id")
    String appId;

    /**
     * 租户id
     */
    @JsonProperty("tenant_code")
    String tenantCode;

    /**
     * 分类id
     */
    @JsonProperty("category_id")
    String categoryId;

    /**
     * 	顶级分类id
     */
    @JsonProperty("top_parent_category_id")
    String topParentCategoryId;
    /**
     * 考试名称
     */
    @JsonProperty("exam_name")
    String examName;
    /**
     * 	考试状态 0-未开始 1-进行中 2-已结束
     */
    @JsonProperty("exam_status")
    String examStatus;

    /**
     * 考试开始时间
     */
    @JsonProperty("exam_start_time")
    String 	examStartTime;

    /**
     * 考试结束时间
     */
    @JsonProperty("exam_end_time")
    String examEndTime;

    /**
     * 	页码
     */
    @JsonProperty("page_index")
    String pageIndex;

    /**
     * 	每页数量
     */
    @JsonProperty("page_size")
    String pageSize;
}
