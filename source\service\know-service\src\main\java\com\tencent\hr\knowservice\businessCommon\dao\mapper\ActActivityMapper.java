package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActActivity;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ActActivityInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ActActivityMapper extends BaseMapper<ActActivity> {
    int deleteByPrimaryKey(Integer activityId);

    int insert(ActActivity record);

    int insertSelective(ActActivity record);

    ActActivity selectByPrimaryKey(Integer activityId);

    int updateByPrimaryKeySelective(ActActivity record);

    int updateByPrimaryKey(ActActivity record);

    ActActivityInfoDTO findActivityById(@Param("activityId") Integer activityId);
}