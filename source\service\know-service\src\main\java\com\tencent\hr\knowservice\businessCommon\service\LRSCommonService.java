package com.tencent.hr.knowservice.businessCommon.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.LRSVerbs;
import com.tencent.hr.knowservice.businessCommon.dto.xapi.*;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @description: 积分服务
 * @author: shi<PERSON>wang
 * @createDate: 2022/11/24
 * @version: 1.0
 */
@Service
@Slf4j
public class LRSCommonService extends BaseProxyService {
    @Value("${com.appSetting.appName}")
    private String appName;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${extapi.credit-service.host}")
    private String host;

    private String objectType = "agent";


    /**
     * 上报LRS消息，并返回积分结果
     *
     * @param staffId         当前操作人 id
     * @param staffName       当前操作人名字
     * @param verb            动作枚举
     * @param itemId          操作对象id
     * @param itemName        操作对象名字
     * @param ObjectType      课程类型英文名
     * @param ObjectTypeName  课程类型中文名
     * @param resultExtension 结果信息。可以为null
     * @return
     */
    public String addLrsRecord(String staffId, String staffName, LRSVerbs verb, String itemId, String itemName, String ObjectType, String ObjectTypeName, HashMap<String, String> resultExtension, List<TXAgent> authors) {
        TXStatement txStatement = new TXStatement();
        txStatement.init();
        //当前用身份
        txStatement.getActor().setObjectType(objectType);
        txStatement.getActor().setId(staffId);
        txStatement.getActor().setName(staffName);
        //行为动作
        txStatement.getVerb().setId(verb.getType());
        txStatement.getVerb().setName(verb.getName());
        txStatement.getVerb().setObjectType(verb.getSort());
        //操作对象
        txStatement.getObject().setObjectType(ObjectType);
        txStatement.getObject().setObjectTypeName(ObjectTypeName);
        txStatement.getObject().setId(itemId);
        txStatement.getObject().setName(itemName);
        //result扩展字段
        if (MapUtils.isNotEmpty(resultExtension)) {
            txStatement.getResult().setExtensions(resultExtension);
        }

        //返回积分结果
        HashMap<String, String> contextExtension = new HashMap<>();
        contextExtension.put("pointResultSync", "1");
        txStatement.getContext().setExtensions(contextExtension);
        //作者信息，可能是多个
        txStatement.getContext().setActorRefs(authors);

        //其他信息
        txStatement.setCreateTime(new Date());
        txStatement.setAppId(appName);

        String jsonStr = JsonUtils.objectToJson(txStatement);
        //调用接口发送信息
        String api = "/api/v1/behavior/user/xapi/recordqueue";
        HttpHeaders headers = super.getESBHeader(appId, appToken, staffId, staffName);
        String result = super.postJsonStrWithHeaders(host, api, jsonStr, headers);
        log.info("上报积分请求参数={}，返回结果{}", jsonStr, result);

        if (StringUtils.isNotBlank(result)) {
            TransDTO<XApiResultDto> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<XApiResultDto>>() {
            });
            if (null != transDTO && transDTO.getSuccess() && null != transDTO.getData()) {
                List<RuleRetData> data = transDTO.getData().getBiz();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (RuleRetData item : data) {
                        if (LRSVerbs.add_excellent.equals(verb)) {
                            if (item.getStaffName().equals(authors.get(0).getName())) {
                                return item.getCreditPoint().toString();
                            }
                        } else if (item.getStaffName().equals(staffName)) {
                            return item.getCreditPoint().toString();
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 创建或者更新上报信息
     * @param sharerId
     * @param sharerName
     * @param verb
     * @param itemId
     * @param itemName
     * @param txResult
     * @param context
     * @param createTime
     * @param objType
     * @param objTypeName
     * @return
     */
    public String addAndUpdateLrsRecord(String currentUserId, String currentUserName, LRSVerbs verb, String itemId, String itemName, TXResult txResult,TXContext context,Date createTime,String objType,String objTypeName) {
        //信息
        TXStatement txStatement = new TXStatement();
        txStatement.init();
        //当前用户的身份
        txStatement.getActor().setObjectType(objectType);
        txStatement.getActor().setId(currentUserId);
        txStatement.getActor().setName(currentUserName);
        //行为动作
        txStatement.getVerb().setId(verb.getType());
        txStatement.getVerb().setName(verb.getName());
        txStatement.getVerb().setObjectType(verb.getSort());
        //操作对象
        txStatement.getObject().setObjectType(objType);
        txStatement.getObject().setObjectTypeName(objTypeName);
        txStatement.getObject().setId(itemId);
        txStatement.getObject().setName(itemName);
        //result扩展字段
        txStatement.setResult(txResult);

        //返回积分结果
        txStatement.setContext(context);

        //其他信息
        txStatement.setCreateTime(createTime);
        txStatement.setAppId(appName);

        String jsonStr = JsonUtils.objectToJson(txStatement);
        //调用接口发送信息
        String api = "/api/v1/behavior/user/xapi/recordqueue";
        HttpHeaders headers = super.getESBHeader(appId, appToken, currentUserId, currentUserName);
        String result = super.postJsonStrWithHeaders(host, api, jsonStr, headers);
        log.info("上报积分请求参数={}，返回结果{}", jsonStr, result);

        if (StringUtils.isNotBlank(result)) {
            TransDTO<XApiResultDto> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<XApiResultDto>>() {
            });
            if (null != transDTO && transDTO.getSuccess() && null != transDTO.getData()) {
                return transDTO.getData().getId();
            }
        }
        return null;
    }


    /**
     * 上报LRS消息，并返回积分结果
     *
     * @param staffId         当前操作人 id
     * @param staffName       当前操作人名字
     * @param verb            动作枚举
     * @param itemId          操作对象id
     * @param itemName        操作对象名字
     * @param ObjectType      课程类型英文名
     * @param ObjectTypeName  课程类型中文名
     * @param res 结果信息。可以为null
     * @return
     */
    public String addNetCourseRecord(String staffId, String staffName, LRSVerbs verb, String itemId, String itemName, String ObjectType, String ObjectTypeName, TXResult res,  TXContext context) {
        TXStatement txStatement = new TXStatement();
        txStatement.init();
        //当前用身份
        txStatement.getActor().setObjectType(objectType);
        txStatement.getActor().setId(staffId);
        txStatement.getActor().setName(staffName);
        //行为动作
        txStatement.getVerb().setId(verb.getType());
        txStatement.getVerb().setName(verb.getName());
        txStatement.getVerb().setObjectType(verb.getSort());
        //操作对象
        txStatement.getObject().setObjectType(ObjectType);
        txStatement.getObject().setObjectTypeName(ObjectTypeName);
        txStatement.getObject().setId(itemId);
        txStatement.getObject().setName(itemName);
        //result扩展字段
        if (res != null) {
            txStatement.setResult(res);
        }

        //返回积分结果
        HashMap<String, String> contextExtension = new HashMap<>();
//        contextExtension.put("pointResultSync", "1");
        txStatement.setContext(context);
        txStatement.getContext().setExtensions(contextExtension);


        //其他信息
        txStatement.setCreateTime(new Date());
        txStatement.setAppId(appName);

        String jsonStr = JsonUtils.objectToJson(txStatement);
        //调用接口发送信息
        String api = "/api/v1/behavior/user/xapi/recordqueue";
        HttpHeaders headers = super.getESBHeader(appId, appToken, staffId, staffName);
        String result = super.postJsonStrWithHeaders(host, api, jsonStr, headers);
        log.info("上报积分请求参数={}，返回结果{}", jsonStr, result);

        if (StringUtils.isNotBlank(result)) {
            TransDTO<XApiResultDto> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<XApiResultDto>>() {
            });
            if (null != transDTO && transDTO.getSuccess() && null != transDTO.getData()) {
                List<RuleRetData> data = transDTO.getData().getBiz();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (RuleRetData item : data) {
                        if (LRSVerbs.add_excellent.equals(verb)) {
                            if (item.getStaffName().equals(context.getActorRefs().get(0).getName())) {
                                return item.getCreditPoint().toString();
                            }
                        } else if (item.getStaffName().equals(staffName)) {
                            return item.getCreditPoint().toString();
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     *
     *
     * @param staffId         当前操作人 id
     * @param staffName       当前操作人名字
     * @param verb            动作枚举
     * @param itemId          操作对象id
     * @param itemName        操作对象名字
     * @param ObjectType      课程类型英文名
     * @param ObjectTypeName  课程类型中文名
     * @param res 结果信息。可以为null
     * @return
     */
    public String addOrUpdateNetCourseRecord(String staffId, String staffName, LRSVerbs verb, String itemId, String itemName, String ObjectType, String ObjectTypeName, TXResult res,  TXContext context) {
        TXStatement txStatement = new TXStatement();
        txStatement.init();
        //当前用身份
        txStatement.getActor().setObjectType(objectType);
        txStatement.getActor().setId(staffId);
        txStatement.getActor().setName(staffName);
        //行为动作
        txStatement.getVerb().setId(verb.getType());
        txStatement.getVerb().setName(verb.getName());
        txStatement.getVerb().setObjectType(verb.getSort());
        //操作对象
        txStatement.getObject().setObjectType(ObjectType);
        txStatement.getObject().setObjectTypeName(ObjectTypeName);
        txStatement.getObject().setId(itemId);
        txStatement.getObject().setName(itemName);
        //result扩展字段
        if (res != null) {
            txStatement.setResult(res);
        }

        //返回积分结果
        HashMap<String, String> contextExtension = new HashMap<>();
//        contextExtension.put("pointResultSync", "1");
        txStatement.setContext(context);
        txStatement.getContext().setExtensions(contextExtension);


        //其他信息
        txStatement.setCreateTime(new Date());
        txStatement.setAppId(appName);

        String jsonStr = JsonUtils.objectToJson(txStatement);
        //调用接口发送信息
        String api = "/api/v1/behavior/ext/sync/update";
        HttpHeaders headers = super.getESBHeader(appId, appToken, staffId, staffName);
        String result = super.postJsonStrWithHeaders(host, api, jsonStr, headers);
        log.info("上报积分请求参数={}，返回结果{}", jsonStr, result);

        if (StringUtils.isNotBlank(result)) {
            TransDTO<XApiResultDto> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<XApiResultDto>>() {
            });
            if (null != transDTO && transDTO.getSuccess() && null != transDTO.getData()) {
                List<RuleRetData> data = transDTO.getData().getBiz();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (RuleRetData item : data) {
                        if (LRSVerbs.add_excellent.equals(verb)) {
                            if (item.getStaffName().equals(context.getActorRefs().get(0).getName())) {
                                return item.getCreditPoint().toString();
                            }
                        } else if (item.getStaffName().equals(staffName)) {
                            return item.getCreditPoint().toString();
                        }
                    }
                }
            }
        }
        return null;
    }

//    {
//        "actor": {
//        "id": "196925",                                                                  -- 当前学员的staffid
//        "objectType": "agent",
//                "name": "kelvinyye(叶胜强)"                                                -- 当前学员的staffname
//    },
//        "verb": {
//        "id": "study",
//                "sort": "study",
//                "name": "学习",
//                "objectType": "study"
//    },
//        "object": {
//        "id": "17282",                                                                               -- 任务id
//        "name": "一封邮件带来的拖库事件案例",                                   -- 任务名称
//        "objectType": "video",                                                                  -- 资源类型
//        "extensions": {
//            "courseName": "FiT安全及质量建设系列课程（持续更新）",  -- 培训项目的名称
//            "courseId": "320"                                                                        -- 培训项目id
//        }
//    },
//        "result": {
//        "completion": true,                                           -- 任务是否完成
//        "duration": {
//            "startTime": "2023-08-07 21:19:39",   -- 本次学习的开始时间
//            "endTime": "2023-08-07 21:20:40",     -- 本次学习的结束时间
//            "length": 60,                         -- 课程的总时长，如果是本身没有时长就传0
//            "targetLength": 60,                                        -- 任务的完成条件中，要完成该任务需要看多少时间，如果是随课程的完成状态改字段就传0
//            "realLength": 57600                                        -- 时间学习的时长
//        }
//    },
//        "context": {
//        "oriRecordId": "2607922",                                -- 本次学习行为的记录id
//        "extensions": {
//            "moocVersion":"2"                                                -- 新mooc上报需要传改字段，用于区分新老mooc的行为
//        }
//    },
//        "id": "759ed7c698bb4135bf2d2d83470f44dc",
//            "storeTime": "2023-08-07 21:36:44",
//            "createTime": "2023-08-07 21:19:39",
//            "appId": "mooc",
//            "enabled": 1,
//            "version": "2.0.0"
//    }

    /**
     * 上报LRS
     *
     * @param appId
     * @param verb
     * @param stuStaffId
     * @param stuStaffName
     * @param taskId
     * @param recordId
     * @param resourceType
     * @param taskName
     * @param taskStatus
     * @param startTime
     * @param endTime
     * @param realLength
     * @param targetLength
     * @param courseId
     * @param courseName
     * @param authors
     * @return
     */
    public String sendLRSRequest(String appId, LRSVerbs verb, Integer stuStaffId, String stuStaffName, Integer taskId, Integer recordId, String resourceType, String taskName, boolean taskStatus, Date startTime, Date endTime, Integer length, Integer realLength, Integer targetLength, String courseId, String courseName, List<TXAgent> authors, boolean isMobile) {
        HashMap<String, Object> hashMap = new HashMap<>();
        HashMap<String, Object> actorMap = new HashMap<>();
        actorMap.put("objectType", objectType);
        actorMap.put("id", stuStaffId);
        actorMap.put("name", stuStaffName);
        hashMap.put("actor", actorMap);

        HashMap<String, Object> verbMap = new HashMap<>();
        verbMap.put("id", verb.getType());
        verbMap.put("sort", verb.getSort());
        verbMap.put("name", verb.getName());
        verbMap.put("objectType", verb.getSort());
        hashMap.put("verb", verbMap);

        HashMap<String, Object> objectMap = new HashMap<>();
        objectMap.put("id", taskId);
        objectMap.put("name", taskName);
        objectMap.put("objectType", resourceType);

        HashMap<String, Object> objectExtMap = new HashMap<>();
        objectExtMap.put("courseId", courseId);
        objectExtMap.put("courseName", courseName);
        objectMap.put("extensions", objectExtMap);
        hashMap.put("object", objectMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("completion", taskStatus);
        HashMap<String, Object> durationMap = new HashMap<>();
        durationMap.put("startTime", startTime);
        durationMap.put("endTime", endTime);
        durationMap.put("length", length);
        durationMap.put("targetLength", targetLength);
        durationMap.put("realLength", realLength);
        durationMap.put("duration", durationMap);
        hashMap.put("result", resultMap);

        HashMap<String, Object> contextMap = new HashMap<>();
        contextMap.put("oriRecordId", recordId);
        HashMap<String, Object> contextExtMap = new HashMap<>();
        contextExtMap.put("moocVersion", "2");
//        contextExtMap.put("pointResultSync", "1");
        contextMap.put("extensions", contextExtMap);
        contextMap.put("actorRefs", authors);
        contextMap.put("platform", isMobile ? "mobile" : "PC");

        hashMap.put("context", contextMap);

        hashMap.put("appId", appId);
        hashMap.put("enabled", 1);
        hashMap.put("createTime", new Date());

        String jsonStr = JsonUtils.objectToJson(hashMap);
        //调用接口发送信息
        String api = "/api/v1/behavior/user/xapi/recordqueue";
        HttpHeaders headers = super.getESBHeader(this.appId, appToken, String.valueOf(stuStaffId), stuStaffName);
        String result = super.postJsonStrWithHeaders(host, api, jsonStr, headers);
        log.info("上报积分请求参数={}，返回结果{}", jsonStr, result);

        if (StringUtils.isNotBlank(result)) {
            TransDTO<XApiResultDto> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<XApiResultDto>>() {
            });
            if (null != transDTO && transDTO.getSuccess() && null != transDTO.getData()) {
                List<RuleRetData> data = transDTO.getData().getBiz();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (RuleRetData item : data) {
                        if (LRSVerbs.add_excellent.equals(verb)) {
                            if (item.getStaffName().equals(authors.get(0).getName())) {
                                return item.getCreditPoint().toString();
                            }
                        } else if (item.getStaffName().equals(stuStaffName)) {
                            return item.getCreditPoint().toString();
                        }
                    }
                }
            }
        }
        return null;
    }


    /**
     * TODO 后面再改为通用
     * 缺漏数据上报地址
     * @param appId
     * @param verb
     * @param stuStaffId
     * @param stuStaffName
     * @param taskId
     * @param recordId
     * @param resourceType
     * @param taskName
     * @param taskStatus
     * @param startTime
     * @param endTime
     * @param length
     * @param realLength
     * @param targetLength
     * @param courseId
     * @param courseName
     * @param authors
     * @param isMobile
     * @return
     */
    public String sendLRSRequestUpdateApi(String appId, LRSVerbs verb, Integer stuStaffId, String stuStaffName, Integer taskId, Integer recordId, String resourceType, String taskName, boolean taskStatus, Date startTime, Date endTime, Integer length, Integer realLength, Integer targetLength, String courseId, String courseName, List<TXAgent> authors, boolean isMobile) {
        HashMap<String, Object> hashMap = new HashMap<>();
        HashMap<String, Object> actorMap = new HashMap<>();
        actorMap.put("objectType", objectType);
        actorMap.put("id", stuStaffId);
        actorMap.put("name", stuStaffName);
        hashMap.put("actor", actorMap);

        HashMap<String, Object> verbMap = new HashMap<>();
        verbMap.put("id", verb.getType());
        verbMap.put("sort", verb.getSort());
        verbMap.put("name", verb.getName());
        verbMap.put("objectType", verb.getSort());
        hashMap.put("verb", verbMap);

        HashMap<String, Object> objectMap = new HashMap<>();
        objectMap.put("id", taskId);
        objectMap.put("name", taskName);
        objectMap.put("objectType", resourceType);

        HashMap<String, Object> objectExtMap = new HashMap<>();
        objectExtMap.put("courseId", courseId);
        objectExtMap.put("courseName", courseName);
        objectMap.put("extensions", objectExtMap);
        hashMap.put("object", objectMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("completion", taskStatus);
        HashMap<String, Object> durationMap = new HashMap<>();
        durationMap.put("startTime", startTime);
        durationMap.put("endTime", endTime);
        durationMap.put("length", length);
        durationMap.put("targetLength", targetLength);
        durationMap.put("realLength", realLength);
        resultMap.put("duration", durationMap);
        hashMap.put("result", resultMap);

        HashMap<String, Object> contextMap = new HashMap<>();
        contextMap.put("oriRecordId", recordId);
        HashMap<String, Object> contextExtMap = new HashMap<>();
        contextExtMap.put("moocVersion", "2");
        contextMap.put("extensions", contextExtMap);
        contextMap.put("actorRefs", authors);
        contextMap.put("platform", isMobile ? "mobile" : "PC");

        hashMap.put("context", contextMap);

        hashMap.put("appId", appId);
        hashMap.put("enabled", 1);
        hashMap.put("createTime", new Date());

        String jsonStr = JsonUtils.objectToJson(hashMap);
        //调用接口发送信息
        String api = "/api/v1/behavior/ext/sync/update";
        HttpHeaders headers = super.getESBHeader(this.appId, appToken, String.valueOf(stuStaffId), stuStaffName);
        String result = super.postJsonStrWithHeaders(host, api, jsonStr, headers);
        log.info("上报积分请求参数={}，返回结果{}", jsonStr, result);

        if (StringUtils.isNotBlank(result)) {
            TransDTO<XApiResultDto> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<XApiResultDto>>() {
            });
            if (null != transDTO && transDTO.getSuccess() && null != transDTO.getData()) {
                List<RuleRetData> data = transDTO.getData().getBiz();
                if (CollectionUtils.isNotEmpty(data)) {
                    for (RuleRetData item : data) {
                        if (LRSVerbs.add_excellent.equals(verb)) {
                            if (item.getStaffName().equals(authors.get(0).getName())) {
                                return item.getCreditPoint().toString();
                            }
                        } else if (item.getStaffName().equals(stuStaffName)) {
                            return item.getCreditPoint().toString();
                        }
                    }
                }
            }
        }
        return null;
    }
}
