package com.tencent.hr.knowservice.businessCommon.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PubNoticeStatusDto {
    /**
     * 公告ID
     */
    @TableId(type = IdType.AUTO)
    private Integer noticeId;
    /**
     * 状态 0未发布 1已发布 2已取消
     */
    private String status;
    /**
     * 计划发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preparePublishTime;

    private static final long serialVersionUID = 1L;
}
