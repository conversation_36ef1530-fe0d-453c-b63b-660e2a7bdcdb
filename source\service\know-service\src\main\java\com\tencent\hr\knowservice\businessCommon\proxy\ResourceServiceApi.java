package com.tencent.hr.knowservice.businessCommon.proxy;

import com.tencent.hr.base.dto.TransDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;



@FeignClient(name="content-center",url="http://demo.ntsgw.oa.com/api/sso|pub|esb/content-center")
public interface ResourceServiceApi {

    /**
     * 查看或者下载文件
     * @param
     * @return
     */
    @GetMapping("/api/v1/content/operatesignature")
    public TransDTO getSmartCover(@RequestParam(value = "content_id" ,required = false) String title,
                                  @RequestParam(value = "operate" ,required = true)  String operate) ;

    /**
     * 获取文件下载地址
     * @param
     * @return
     */
    @GetMapping("/api/v1/content/{contentId}/url")
    TransDTO getContentData(@PathVariable String contentId) ;
}
