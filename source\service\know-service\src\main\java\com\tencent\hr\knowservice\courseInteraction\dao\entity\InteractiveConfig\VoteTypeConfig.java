package com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description:
 * @author: shi<PERSON><PERSON>
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class VoteTypeConfig {
    /**
     * 最多选择数量
     */
    private int canMaxVoteNums;
    private String completionConditions;
}
