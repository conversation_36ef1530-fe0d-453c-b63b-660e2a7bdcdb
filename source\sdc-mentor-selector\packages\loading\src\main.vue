<template>
  <div class="sdc-loading">
    <transition name="fade">
      <div class="mask" v-show="visible">
        <div class="spinner">
          <i></i>
          <i></i>
          <i></i>
          <i></i>
          <i></i>
          <i></i>
          <i></i>
          <i></i>
        </div>
        <div class="text">{{message || $st('sdc.loading')}}</div>
      </div>
    </transition>
  </div>
</template>

<script>
  import { locale } from 'mixins'

  export default {
    name: 'sdc-loading',
    mixins: [locale],
    data: () => ({
      visible: false,
      message: ''
    })
  }
</script>
