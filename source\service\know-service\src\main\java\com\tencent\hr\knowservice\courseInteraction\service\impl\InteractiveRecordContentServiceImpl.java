package com.tencent.hr.knowservice.courseInteraction.service.impl;

import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveRecordContentEntity;
import com.tencent.hr.knowservice.courseInteraction.dao.mapper.InteractiveRecordContentMapper;
import com.tencent.hr.knowservice.courseInteraction.service.InteractiveRecordContentService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * <AUTHOR>
 */
@Service("interactiveRecordContentService")
public class InteractiveRecordContentServiceImpl extends ServiceImpl<InteractiveRecordContentMapper, InteractiveRecordContentEntity> implements InteractiveRecordContentService {


}