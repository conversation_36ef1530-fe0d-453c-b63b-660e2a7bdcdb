## Header 页面头部
> 贡献者：cxyxhhuang(黄鑫杰)；jeel<PERSON>(刘志杰)；最近更新时间：2022-05-27

header组件是页面的头部，是layout的组件的一部分，可以单独使用header组件，包含oa、oc两种布局。

### 基础用法

内网布局默认包含logo, search, feedback, links四个模块。

:::demo 使用 `scope` 属性来设置内网(oa,默认)或外网(oc)
```html
<template>
  <sdc-header scope="oa"></sdc-header>
</template>
```
:::

外网布局默认包含logo, menus, icons, avatar四个模块。

:::demo 使用 `menus`来设置指定的导航栏菜单，avatar来设置指定的头像菜单
```html
<template>
  <sdc-header :menus="menus" scope="oc" :avatar="avatar"></sdc-header>
</template>
<script>
  export default {
    data() {
      return {
        menus: {
          active: 'app1', // 默认选中项，值为data中的key
          map: {
            url: 'link'
          },
          // maxMenuCount: 8, // 默认显示6个菜单项，多余项会合并成“更多”下拉菜单项，可通过maxMenuCount自定义菜单项个数
          // adaptive: true, // 根据页面宽度自适应改变展开显示的菜单项个数，此时maxMenuCount无效
          data: [ // 显示menus数组
            { key: 'app1', link: 'https://assistant-wxvendor.ihr.tencent-cloud.com/', text: '应用名称1' },
            { link: 'https://assistant-wxvendor.ihr.tencent-cloud.com/', text: '应用名称2' } // 如果在当前窗口打开，则配置target
          ]
        },
        avatar: {
          url: 'http://hrstaff.oa.com/hr/HRStaff', // 点击头像跳转链接
          avatarUrl: 'examples/assets/img/avatar1.png', // 显示的头像URL
          map: {
            url: 'link',
            text: 'name'
          },
          data: [
            { name: '个人信息', link: 'http://test.app.oa.com/info', type: 'info' },
            { name: '个人空间', link: 'http://test.app.oa.com/info', disabled: true },
            { name: '退出', divided: true, type: 'exit' } // 需要分隔一下
          ]
        }
      }
    }
  }
</script>
```
:::

除了默认的两种布局，也可以自定义头部显示的内容，使用`layout`属性，可选值为nav/logo/search/feedback/links/icons/avatar

:::demo 使用 `layout`来设置要显示的模块，layout为数组类型
```html
<template>
  <sdc-header :layout="['logo','avatar']"></sdc-header>
</template>
```
:::

如果想自定义一些内容，可以使用内置的slot

:::demo slot主要包括logo、menus、icons、link、avatar这五个
```html
<template>
  <sdc-header scope="oc">
    <div slot="logo" class="logo-text ellipsis">
      SDC-WEBUI
    </div>
    <div class="header-icons" slot="icons">
      <i class="el-icon-setting"></i> 设置
    </div>
    <div class="header-avatar" slot="avatar">
      <!-- 这里可重新定制头像以及头像下拉菜单-->
      <sdc-avatar :avatar="avatar"></sdc-avatar>
    </div>
  </sdc-header>
</template>
<script>
export default {
  data() {
    return {
      avatar: {
        map: {},
        data: [
          { text: '退出', type: 'logout', url: 'http://www.baidu.com', target: '_self' }
        ]
      }
    }
  }
}
</script>
```
:::

### Header Attributes
| 参数            | 说明                     | 类型    | 可选值                                             | 默认值                                                                                          |
| --------------- | ------------------------ | ------- | -------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| scope           | 布局类型(内网、外网)     | string  | oa/oc                                              | oa                                                                                              |
| layout    | Header可选显示区域       | array   | logo/search/feedback/links/icons/avatar | 内网：['logo', 'search', 'feedback', 'links']，外网: ['logo', 'menus', 'icons', 'avatar'] |
| menus     | 导航栏菜单配置           | object  | —                                                  | maxMenuCount: 6, adaptive: false |
| avatar     | 头像菜单配置 | object  | —                                                  | —

### Layout Events
| 事件名称       | 说明                             | 回调参数          |
| -------------- | -------------------------------- | ----------------- |
| search         | 当输入关键字回车时触发           | 输入关键字keyword |
| avatar         | 点击头像子菜单时触发 | 子菜单类型type    |

### Layout Slots
| 名称            | 说明                       |
| --------------- | -------------------------- |
| logo     | 自定义logo                 |
| menus    | 自定义导航栏               |
| icons    | 自定义右侧图标 |
| links    | 自定义右侧链接 |
| avatar   | 自定义右侧头像 |
