/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2024-05-27 15:32:53
 */
import { DataStorage, STORAGE_TYPE } from 'sdc-core'
import CoreService from './core.service'

export default class StaffService {
  // 获取角色
  static postStaff({ 
    limit = 1000, 
    staffIdList = undefined, 
    staffCombinedNameLike = undefined, 
    staffAccountNameLike = undefined, 
    staffAccountNameList = undefined, 
    orgIdList = undefined, 
    hrStatusIdList = [1], 
    contractCompanyIdList = undefined, 
    manageUnitIdList = undefined, 
    likeMode = 'full', 
    isContainSubStaff = false, 
    managerPositionLevelIdList = undefined,
    staffTypeIdList = undefined
  } = {}) {
    const params = {
      queryCondition: {
        limit,
        argMap: {
          staffIdList, // 搜索员工id
          staffCombinedNameLike, // 搜索员工名称 
          staffAccountNameLike, // 搜索员工英文名称 
          staffAccountNameList, // 批量搜索员工名称 
          likeMode, // 匹配模式 left full equal
          hrStatusIdList, // 员工状态，1：在职，2：离职，8：待入职
          contractCompanyIdList, // 合同公司Id
          manageUnitIdList, // 管理主体Id
          managerPositionLevelIdList: managerPositionLevelIdList, // 管理职级
          staffTypeIdList // 员工类型
        }
      }
    }
    params.queryCondition.argMap[isContainSubStaff ? 'scopeOrgIdList' : 'orgIdList'] = orgIdList // orgIdList只查当前下级， scopeOrgIdList查所有下级
    console.log('导师插件里面的人员选择接口~~~~~~~~~~~~')
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-staff/sdc-webui/data', { params })
  }

  // 通过曾用名获取角色
  static postStaffFormer({ 
    limit = 1000, 
    staffIdList = undefined, 
    staffCombinedNameLike = undefined, 
    staffAccountNameLike = undefined, 
    staffAccountNameList = undefined, 
    orgIdList = undefined, 
    hrStatusIdList = [1], 
    contractCompanyIdList = undefined, 
    manageUnitIdList = undefined, 
    likeMode = 'full', 
    isContainSubStaff = false, 
    managerPositionLevelIdList = undefined,
    staffTypeIdList = undefined
  } = {}) {
    const params = {
      queryCondition: {
        limit,
        argMap: {
          staffIdList, // 搜索员工id
          staffCombinedNameLike, // 搜索员工名称 
          staffAccountNameLike, // 搜索员工英文名称 
          staffAccountNameList, // 批量搜索员工名称 
          likeMode, // 匹配模式 left full equal
          hrStatusIdList, // 员工状态，1：在职，2：离职，8：待入职
          contractCompanyIdList, // 合同公司Id
          manageUnitIdList, // 管理主体Id
          managerPositionLevelIdList: managerPositionLevelIdList, // 管理职级
          staffTypeIdList // 员工类型
        }
      }
    }
    params.queryCondition.argMap[isContainSubStaff ? 'scopeOrgIdList' : 'orgIdList'] = orgIdList // orgIdList只查当前下级， scopeOrgIdList查所有下级
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-selector-staff-with-former-account-name/sdc-webui/data', { params })
  }

  // 获取组织
  static postOrg({ limit = 1000, parentOrgId = undefined, orgIdList = undefined, orgName = undefined, orgTypeIdList = [1], orgLevelIdList = [6, 8, 1, 7, 2], ...argus } = {}) {
    const params = {
      queryCondition: {
        limit,
        argMap: {
          parentOrgIdList: parentOrgId !== undefined ? Array.isArray(parentOrgId) ? parentOrgId : [parentOrgId] : undefined, // 父级组织, 需要兼容，parentOrgId原先是 Number类型，现在支持 Number | Array
          orgIdList, // 查询组织id列表
          orgName, // 组织名称
          orgTypeIdList, // 组织类型Id；1：实体组织，2：虚拟组织
          orgLevelIdList, // 组织级别Id; 6:系统(BG), 8:线, 1:部门, 7:中心, 2:小组
          ...argus // 其他传参
        }
      }
    }
    // 如果有传入orgIdList，则需要给返回的结果按照传入的id顺序排序
    if (orgIdList && orgIdList.length) {
      return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-org/sdc-webui/data', { params }).then(res => {
        if (res && res.content && res.content.length) {
          res.content.sort((x, y) => {
            const indexX = orgIdList.indexOf(x.orgId)
            const indexY = orgIdList.indexOf(y.orgId)
            return indexX - indexY
          })
        }
        return new Promise((resolve, reject) => {
          resolve(res)
        })
      })
    } else {
      return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-org/sdc-webui/data', { params })
    }
  }

  // 根据组织获取角色
  static postStaffByOrgId({ orgId = undefined, hrStatusIdList = [1], contractCompanyIdList = undefined, manageUnitIdList = undefined, relationTypeIdList = [1], managerPositionLevelIdList = undefined, staffTypeIdList = undefined } = {}) {
    const params = {
      queryCondition: {
        argMap: {
          orgIdList: [orgId], // 组织id
          contractCompanyIdList, // 合同公司Id
          manageUnitIdList, // 管理主体Id
          relationTypeIdList, // 岗位关系类型id 主岗：1，兼岗：0
          distinctFlag: 1, // 组织下多重任岗去重 标识去重: 1，不去重: 0;
          managerPositionLevelIdList: managerPositionLevelIdList, // 管理职级
          staffTypeIdList // 员工类型
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-report-relation/sdc-webui/data', { params })
  }
  
  static getDataList(name, { 
    count = 10, 
    includeDimission = false, 
    includeOnBoarding = false, 
    unitID = undefined, 
    isContainSubStaff = false, 
    managerPositionLevelIdList = [], 
    contractCompanyID = undefined, 
    contractCompanyIdList = [], 
    manageUnitIdList = [], 
    likeMode = 'full', 
    staffTypeIdList = [],
    useFormerNameSearch = false
  } = {}) {
    const isNumber = !isNaN(parseFloat(name)) && isFinite(name)
    const queryKey = isNumber ? 'staffIdList' : likeMode === 'equal' ? 'staffAccountNameLike' : 'staffCombinedNameLike'
    const params = {
      limit: count,
      likeMode,
      orgIdList: StaffService.setUnitIdList(unitID),
      hrStatusIdList: StaffService.setHrStatusIdList(includeDimission, includeOnBoarding),
      contractCompanyIdList: StaffService.setContractCompanyIdList(contractCompanyID, contractCompanyIdList),
      manageUnitIdList: manageUnitIdList.length ? manageUnitIdList : undefined,
      isContainSubStaff,
      managerPositionLevelIdList: managerPositionLevelIdList.length ? managerPositionLevelIdList : undefined,
      staffTypeIdList: staffTypeIdList.length ? staffTypeIdList : undefined
    }
    params[queryKey] = isNumber ? [Number(name)] : name
    // 不使用曾用名搜索
    if (!useFormerNameSearch) {
      return StaffService.postStaff(params).then(res => StaffService.mapStaffData(res.content || []))
    } else { // 使用曾用名搜索
      return StaffService.postStaffFormer(params).then(res => StaffService.mapStaffFormerData(res.content || []))
    }
  }

  static getPasteResult(name, { 
    includeDimission = false, 
    includeOnBoarding = false, 
    unitID = undefined, 
    isContainSubStaff = false, 
    managerPositionLevelIdList = [],
    contractCompanyID = undefined, 
    contractCompanyIdList = [],
    manageUnitIdList = [], 
    staffTypeIdList = [],
    useFormerNameSearch = false
  } = {}) {
    const isNumber = !isNaN(parseFloat(name.replace(/;/g, ''))) && isFinite(name.replace(/;/g, ''))
    const queryKey = isNumber ? 'staffIdList' : 'staffAccountNameList'
    let pasteArr = name.split(';').filter(item => item)
    if (isNumber) {
      pasteArr = pasteArr.map(item => Number(item))
    }
    const params = {
      orgIdList: StaffService.setUnitIdList(unitID),
      hrStatusIdList: StaffService.setHrStatusIdList(includeDimission, includeOnBoarding),
      contractCompanyIdList: StaffService.setContractCompanyIdList(contractCompanyID, contractCompanyIdList),
      manageUnitIdList: manageUnitIdList.length ? manageUnitIdList : undefined,
      isContainSubStaff,
      managerPositionLevelIdList: managerPositionLevelIdList.length ? managerPositionLevelIdList : undefined,
      staffTypeIdList: staffTypeIdList.length ? staffTypeIdList : undefined
    }
    params[queryKey] = pasteArr
    // 不使用曾用名搜索
    if (!useFormerNameSearch) {
      return StaffService.postStaff(params).then(res => StaffService.mapStaffData(res.content || []))
    } else { // 使用曾用名搜索
      return StaffService.postStaffFormer(params).then(res => StaffService.mapStaffFormerData(res.content || []))
    }
  }
  
  static getTreeData(unitId = 0, { 
    isCache, 
    includeDimission = false, 
    isContainSubStaff = false, 
    managerPositionLevelIdList = [], 
    contractCompanyID = undefined, 
    unitID = undefined, 
    contractCompanyIdList = [], 
    manageUnitIdList = [], 
    staffTypeIdList = [], 
    includePartTimePost = false 
  } = {}) {
    const cacheKey = includeDimission ? 'sdc:ui-staff-all' : 'sdc:ui-staff-list'
    const hasCache = DataStorage.contains(cacheKey, STORAGE_TYPE.Session)
    let promiseOrg = Promise.resolve()
    // 如果传入是空数组，需要转成0，不然会查所有组织
    if (Array.isArray(unitId) && !unitId.length) {
      unitId = 0
    }
    // 如果没设置根组织，或者查询unitId为数组时，或者设置了 range.isContainSubStaff === true时，需要查组织
    if (((!unitID || (Array.isArray(unitID) && !unitID.length)) || Array.isArray(unitId)) || isContainSubStaff) {
      const paramsKey = Array.isArray(unitId) ? 'orgIdList' : 'parentOrgId'
      const params = {}
      params[paramsKey] = unitId
      promiseOrg = isCache && hasCache ? Promise.resolve(DataStorage.get(cacheKey, { storageType: STORAGE_TYPE.Session })) : StaffService.postOrg(params)
    }
    let promiseStaff = Promise.resolve()
    // 查询的组织id>0 并且 组织id不是数组时，需要查员工
    if (unitId > 0 && !Array.isArray(unitId)) {
      const staffParams = {
        orgId: unitId,
        contractCompanyIdList: StaffService.setContractCompanyIdList(contractCompanyID, contractCompanyIdList),
        manageUnitIdList: manageUnitIdList.length ? manageUnitIdList : undefined,
        relationTypeIdList: includePartTimePost ? [1, 0] : undefined,
        managerPositionLevelIdList: managerPositionLevelIdList.length ? managerPositionLevelIdList : undefined,
        staffTypeIdList: staffTypeIdList.length ? staffTypeIdList : undefined
      }
      promiseStaff = StaffService.postStaffByOrgId(staffParams)
    }
    return Promise.all([promiseOrg, promiseStaff]).then(res => {
      // 兼容老版本，老版本组织返回字段为【SubUnitList】，新数据源返回字段【content】，存入sessionStorage是【SubUnitList】，所以需要判断是从缓存取还是从新数据源接口取
      const SubUnitList = res[0] ? isCache && hasCache ? (res[0].SubUnitList || []) : StaffService.mapOrgData(res[0].content || []) : []
      isCache && !hasCache && res[0] && DataStorage.set(cacheKey, { SubUnitList: SubUnitList }, { storageType: STORAGE_TYPE.Session })
      return {
        unit: SubUnitList,
        staff: StaffService.mapStaffData(res[1] ? (res[1].content || []) : [])
      }
    })
  }
  
  static getChildrenData(unitId, { contractCompanyID = undefined, contractCompanyIdList = [], manageUnitIdList = [], includePartTimePost = false, managerPositionLevelIdList = [], staffTypeIdList = [] } = {}) {
    const params = {
      orgId: unitId,
      contractCompanyIdList: StaffService.setContractCompanyIdList(contractCompanyID, contractCompanyIdList),
      manageUnitIdList: manageUnitIdList.length ? manageUnitIdList : undefined,
      relationTypeIdList: includePartTimePost ? [1, 0] : undefined,
      managerPositionLevelIdList: managerPositionLevelIdList.length ? managerPositionLevelIdList : undefined,
      staffTypeIdList: staffTypeIdList.length ? staffTypeIdList : undefined
    }
    return StaffService.postStaffByOrgId(params).then(res => StaffService.mapStaffData(res.content || []))
  }

  /**
   * 拼接 hrStatusIdList
   * @param {Boolean} includeDimission  是否包含离职
   * @param {Boolean} includeOnBoarding  是否包含待入职
   * @returns {Array} hrStatusIdList
   */
  static setHrStatusIdList(includeDimission, includeOnBoarding) {
    const hrStatusIdList = [1]
    if (includeDimission) {
      hrStatusIdList.push(2)
    }
    if (includeOnBoarding) {
      hrStatusIdList.push(8)
    }
    return hrStatusIdList
  }

  /**
   * 拼接 unitIdList 兼容老数据传参
   * @param {Number | Array} unitID  旧版本组织id传参是Number, 新版本是Array
   * @returns {Array | undefined}
   */
  static setUnitIdList (unitID) {
    return unitID ? (Array.isArray(unitID) ? unitID : [unitID]) : undefined
  }

  /**
   * 拼接 contractCompanyIdList
   * @param {Number} contractCompanyID  旧版本合同公司传参
   * @param {Array} contractCompanyIdList  新版本合同公司集合传参
   * @returns {Array | undefined}
   */
  static setContractCompanyIdList (contractCompanyID, contractCompanyIdList) {
    if (contractCompanyID) {
      contractCompanyIdList.push(contractCompanyID)
    }
    return contractCompanyIdList.length ? Array.from(new Set(contractCompanyIdList)) : undefined
  }

  /**
   * 格式化staffList，新的数据源字段与之前不同，需map转成老版本字段
   * @param {Array} staffList 
   * @returns  {Array}
   */
  static mapStaffData(staffList) {
    return staffList.filter(item => item).map(item => {
      const { staffAccountName, staffId, staffCombinedName, orgId, orgNameCn, orgFullNameCn } = item
      const staff = {
        Avatar: `http://rhrc.woa.com/photo/100/${staffAccountName}.png`,
        StaffID: staffId,
        StaffName: staffCombinedName,
        EngName: staffAccountName,
        UnitID: orgId,
        UnitName: orgNameCn,
        UnitFullName: orgFullNameCn
      }
      return staff
    })
  }

  /**
   * 格式化曾用名的staffList
   * @param {Array} staffList 
   * @returns  {Array}
   */
  static mapStaffFormerData(staffList) {
    return staffList.filter(item => item).map(item => {
      const { staffFormerAccountName, staffDisplayName, staffAccountName, staffId, staffCombinedName, orgId, orgNameCn, orgFullNameCn } = item
      const IsFormerName = staffFormerAccountName !== staffAccountName
      const accountName = IsFormerName ? staffFormerAccountName : staffAccountName
      const combinedName = IsFormerName ? `${staffFormerAccountName}(${staffDisplayName})` : staffCombinedName
      const staff = {
        Avatar: `http://rhrc.woa.com/photo/100/${accountName}.png`,
        StaffID: staffId,
        StaffName: combinedName,
        EngName: staffFormerAccountName,
        UnitID: orgId,
        UnitName: orgNameCn,
        UnitFullName: orgFullNameCn,
        IsFormerName
      }
      return staff
    })
  }

  /**
   * 格式化orgList，新的数据源字段与之前不同，需map转成老版本字段
   * @param {Array} staffList 
   * @returns  {Array}
   */
  static mapOrgData(orgList) {
    const arr = []
    orgList.filter(item => item).map(item => {
      const { orgId, orgNameCn, orgFullNameCn, ...unit } = item
      const unitObj = {
        UnitID: orgId,
        UnitName: orgNameCn,
        UnitFullName: orgFullNameCn,
        orgFullNameCn: orgFullNameCn,
        ...unit
      }
      // 【总办】需要放在第一位
      if (orgId === 1) {
        arr.unshift(unitObj)
      } else {
        arr.push(unitObj)
      }
    })
    return arr
  }
}
