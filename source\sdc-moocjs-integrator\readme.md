## Mooc集成第三方页面交互方法库

> 这个js库 [sdc-moocjs-integrator](https://mirrors.tencent.com/#/private/npm/detail?repo_id=537&project_name=%40tencent%2Fsdc-moocjs-integrator&search_label=package_admin&search_value=lydiatwang&page_num=1) 上传到腾讯软件源内部npm服务上 外部无法下载使

**地址：** https://mirrors.tencent.com/#/private/npm/detail?repo_id=537&project_name=%40tencent%2Fsdc-moocjs-integrator&search_label=package_admin&search_value=lydiatwang&page_num=1

**1.  说明：**
这是一个JS库，分为两个部分，两个页面通过iframe通信，分别集成这两个SDK进行信息交互，主要目的是降低业务系统接入的难度，尽量减少接入代码的侵入性。
![enter image description here#415px #244px](/download/attachments/2551679947/image-1683637594935.png?version=1&modificationDate=1683637595162&api=v2)

**2. @tencent/sdc-moocjs-integrator 使用**

**@tencent/sdc-moocjs-integrator 该SDK提供给第三方页面引入使用**

安装 npm install @tencent/sdc-moocjs-integrator

**使用示例**

``` 
import MoocJS from '@tencent/sdc-moocjs-integrator'

// 通知父级资源开始播放
MoocJS.play()
// 通知父级资源暂停播放
MoocJS.pause()
// 通知父级，考试系统-开始考试/开始练习
MoocJS.startAnswer()
// 通知父级，考试系统-结束考试/结束练习
/* 
params: {
  is_finished: Boolean // 是否完成考试/练习
  is_cheat: Boolean // 是否作弊
  score: number // 考试得分
  elapsed_seconds: integer // 本次学习的持续时间(秒)
}
*/
MoocJS.endAnswer(params)
// 通知父级，考试系统-进行考试详情
MoocJS.answerDetail()
// 通知父级，考试系统-从考试详情页返回首页
MoocJS.detailBackHome()

// 接收父级传过来的消息，要求暂停播放
MoocJS.setPause(() => {
	// 调暂停播放的方法
})
// 接收父级传过来的消息，要求开始播放
MoocJS.setPlay(() => {
	// 调开始播放的方法
})

// 给父级发送消息
/*
events：事件名称（自定义）
params：携带参数
orgin：默认是"*"，可以传当前域名地址
*/
SDKUtils.postMessage(events, params, orgin)

created() {
	// 通知父级当前页面加载完成
	MoocJS.onload(() => {
		// 回调函数中能接收到父级传过来的信息
	})
	
	// 监听iframe页面区域内的鼠标移动事件，并与父级通信
	MoocJS.mousemove()
}
```
