package com.tencent.hr.knowservice.courseInteraction.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 互动记录批量导出
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
public class InteractiveRecordExcelVo {

    @ColumnWidth(10)
    @ExcelProperty("序号")
    private Integer order;

    @ColumnWidth(20)
    @ExcelProperty("用户名")
    private String creatorName;

    @ColumnWidth(20)
    @ExcelProperty("互动ID")
    private String questionId;

    @ColumnWidth(35)
    @ExcelProperty("互动标题")
    private String title;
    /**
     * 互动类型 CHOOSE--选择 VOTE-投票 (时间切片-快照)
     */
    @ColumnWidth(15)
    @ExcelProperty("互动类型")
    private String activeType;
    /**
     * 互动行为
     */
    @ColumnWidth(25)
    @ExcelProperty("互动行为")
    private String activeAnswer;

    /**
     * 是否正确
     */
    @ColumnWidth(15)
    @ExcelProperty("是否正确")
    private String enabledCorrect;

    /**
     * 创建时间
     */
    @ColumnWidth(20)
    @ExcelProperty("互动时间")
    private Date createdAt;
}
