package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ClassRoomOutDTO;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.ClassroomRightCheckDTO;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.InteractiveClassroomAssociationInDTO;
import com.tencent.hr.knowservice.businessCommon.service.ClassRoomService;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @description: 互动课堂controller
 * @author: shizhouwang
 * @createDate: 2023/8/18
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/businessCommon/common/classRoom")
public class ClassRoomController {

    @Autowired
    ClassRoomService classRoomService;

    /**
     * 新建互动课堂
     * @param insert
     * @return
     */
    @PostMapping("/insertClassRoom")
    public TransDTO insertClassRoom(@RequestBody InteractiveClassroomAssociationInDTO insert){
        String classRoomId = classRoomService.insertClassRoom(insert);
        return new TransDTO().withData(classRoomId).withSuccess(true).withCode(HttpStatus.SC_OK);
    }

    /**
     * 获取互动课堂配置信息
     * @param actType
     * @param itemId
     * @return
     */
    @GetMapping("/getClassRoom")
    public TransDTO getClassRoom(@RequestParam("act_type") Integer actType,
                                 @RequestParam("item_id") String itemId){
        ClassRoomOutDTO classRoom = classRoomService.getClassRoom(actType,itemId);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withData(classRoom);
    }

    /**
     * 更新互动课堂配置信息
     * @param insert
     * @return
     */
    @PutMapping("/updateClassRoom")
    public TransDTO updateClassRoom(@RequestBody InteractiveClassroomAssociationInDTO insert){
        classRoomService.updateClassRoom(insert);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK);
    }

    /**
     * 推送班级全员数据
     * @param params
     * @return
     */
    @PostMapping("/pushAllStudent")
    public TransDTO pushAllStudent(@RequestBody Map<String,Object> params){
        Object actTypeObj = params.get("act_type");
        Object itemIdObj = params.get("item_id");
        int actType;
        String itemId;
        if(actTypeObj == null){
            throw new LogicException("act_type为空");
        }
        if(itemIdObj == null){
            throw new LogicException("item_id为空");
        }
        actType = Integer.parseInt(actTypeObj.toString());
        itemId = itemIdObj.toString();
        classRoomService.pushCourseStudent(actType,itemId);
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withMessage("推送成功");
    }


    /**
     * 互动课堂开启前置校验
     * @param actType
     * @param itemId
     * @return
     */
    @GetMapping("/classroomRightCheck")
    public TransDTO classroomRightCheck(@RequestParam("act_type") Integer actType,
                                         @RequestParam("item_id")String itemId){
        ClassroomRightCheckDTO checkDTO = classRoomService.classroomRightCheck(actType,itemId);
        return new TransDTO().withData(checkDTO).withSuccess(true).withCode(HttpStatus.SC_OK);
    }

}
