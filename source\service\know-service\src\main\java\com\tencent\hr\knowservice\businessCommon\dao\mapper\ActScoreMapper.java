package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActScore;
import com.tencent.hr.knowservice.businessCommon.dto.ActScoreDto;
import com.tencent.hr.knowservice.businessCommon.dto.ActScoreGroupDto;
import org.apache.ibatis.annotations.Param;

public interface ActScoreMapper extends BaseMapper<ActScore> {
    int deleteByPrimaryKey(Integer id);

    int insert(ActScore record);

    int insertSelective(ActScore record);

    ActScore selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ActScore record);

    int updateByPrimaryKey(ActScore record);

    int getScoreUserCount(String actId, Integer actType);

    IPage<ActScoreDto> gradeList(IPage<ActScoreDto> page, @Param("actId") String actId, @Param("actType") Integer actType, @Param("staffName") String staffName, @Param("score") Double score);

    ActScoreGroupDto getGradeGroupByScore(String actId, Integer actType);
}