import Vue from 'vue'
import App from './App.vue'

// 引入sdc-labeling组件
import sdcLabeling from '@tencent/sdc-labeling'
import '@tencent/sdc-labeling/lib/sdc-labeling.css'
// import sdcLabeling from '@tencent/sdc-labeling-test1'
// import '@tencent/sdc-labeling-test1/lib/sdc-labeling-test1.css'
Vue.use(sdcLabeling)

Vue.config.productionTip = false

/*********模板引用可以直接new*********/
new Vue({
  render: h => h(App),
}).$mount('#sdcLabeling')
/*********************************/

/********单页面需要在组件上new******/
let LabelObj = {
  App,
  Vue
}
window.sdcLabeling = LabelObj
/********************************/