package com.tencent.hr.knowservice.framework.dto;

import lombok.Data;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;

/**
 * @description:
 * @author: vincentyqwu
 * @createDate: 2020/11/20
 * @version: 1.0
 */
@Data
public class ContextEntity {
    private HttpServletRequest currentRequest;
    private Integer staffNo;
    private String staffId;
    private String staffName;
    private String globalId;
    private String nickName;
    private String appId;
    private String corpKey;
    private String corpId;
    private String corpName;
    private String platform;
    private String regionId;
    private String channel;
    //语音标志：中文：zh-cn 英文：en-us
    //默认中文 zh-cn
    private String language;
    /*private String timestamp;
    private String nonce;
    private String signature;*/
    @Override
    public String toString(){
        String str = "staffNo:%s,staffId:%s,staffName:%s,globalId:%s,nickName:%s,appId:%s,corpKey:%s,corpId:%s,corpName:%s,platform:%s,regionId:%s,channel:%s,language:%s";
        String format = String.format(str,staffNo,staffId,staffName,globalId,nickName,appId,corpKey,corpId,corpName,platform,regionId,channel,language);
        return format;
    }

    /**
     * 转换成登陆用户实体
     * @return
     */
    public LoginUser toLoginUser(){
        LoginUser user = new LoginUser();
        user.setAppId(this.getAppId());
        user.setCorpKey(this.getCorpKey());
        user.setCorpName(this.getCorpName());
        user.setGlobalId(this.getGlobalId());
        user.setPlatform(this.getPlatform());
        user.setRegionId(this.getRegionId());
        user.setStaffId(this.getStaffId());
        user.setStaffName(this.getStaffName());
        return user;
    }

}
