package com.tencent.hr.knowservice.businessCommon.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * act_course_summary
 *
 */
@Data
public class ActCourseSummary implements Serializable {
    /**
     * 课程Id
     */
    private Integer courseId;

    /**
     * 人气
     */
    private Integer hisHotCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 推荐数
     */
    private Integer recommCount;

    /**
     * 收藏数
     */
    private Integer favCount;

    /**
     * 报名数
     */
    private Integer registedCount;

    /**
     * 浏览数
     */
    private Integer viewCount;

    /**
     * 课程平均得分
     */
    private Double avgScore;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    /**
     */
    private Integer oldSyncId;

    private static final long serialVersionUID = 1L;

}
