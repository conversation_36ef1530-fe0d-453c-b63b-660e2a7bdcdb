package com.tencent.hr.knowservice.businessCommon.dto.myoa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MyOaRestDto {
    /**
     * 业务名称
     */
    @JsonProperty(value = "Category")
    private String Category;
    /**
     * 流程名称
     */
    @JsonProperty(value = "ProcessName")
    private String ProcessName;
    /**
     * 流程实例ID
     */
    @JsonProperty(value = "ProcessInstanceID")
    private String ProcessInstanceID;

    /**
     * // 花括号中的内容即审批单据字段，这5个字段为必填项
     *    *     "NewWorkItems":[{
     *    *         "category":"业务名称",
     *    *         "process_name": "流程名称",
     *    *         "process_inst_id": "流程实例ID",
     *    *         "title":"单据标题",
     *    *         "handler":"单据审批人",
     *    *         ...
     *    *     }]
     */
    @JsonProperty(value = "NewWorkItems")
    private List<MyOaMessageDto> NewWorkItems;

}
