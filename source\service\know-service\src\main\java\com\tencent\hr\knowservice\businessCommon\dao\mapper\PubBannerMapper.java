package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubBanner;
import com.tencent.hr.knowservice.businessCommon.dto.manage.PubBannerDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PubBannerMapper extends BaseMapper<PubBanner> {
    int deleteByPrimaryKey(Integer id);

    int insert(PubBanner record);

    int insertSelective(PubBanner record);

    PubBanner selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PubBanner record);

    int updateByPrimaryKey(PubBanner record);

    IPage<PubBanner> selectPageBannerInfos(IPage<PubBannerDto> page,@Param("bannerName") String bannerName,@Param("status") Integer status);

    List<PubBanner> selectPageBannerInfos(@Param("bannerName") String bannerName, @Param("status") Integer status);
    void changeOrderToUp(Integer orderNo,Integer beforeNo);

    void changeOrderToDown(Integer orderNo,Integer beforeNo);

    void changeOrderByDelete(Integer pubBannerId);

    int selectMaxOrderNo();

    int updateAddChange();
}