package com.tencent.hr.knowservice.courseInteraction.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-10-12 09:33:27
 */
@Data
@TableName("interactive_record_content")
public class InteractiveRecordContentEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.AUTO)
	private Integer id;
	/**
	 * 互动答题记录id，interactive_record的主键
	 */
	private Integer recordId;
	/**
	 * 互动配置唯一标识id
	 */
	private String interactiveConfigId;
	/**
	 * 互动配置项id（具体选项唯一标识）
	 */
	private String interactiveId;
	/**
	 * 互动介绍(时间切片-快照)
	 */
	private String introduction;
	/**
	 * 互动介绍英文版(时间切片-快照)
	 */
	private String introductionEn;
	/**
	 * 选项记录(时间切片-快照),对mongo数据中的selectContent进行json格式储存
	 */
	private String selectContent;
	/**
	 * 互动时间-切片（秒）
	 */
	private Integer activeTime;
	/**
	 * 删除时间
	 */
	private Boolean enabled;
	/**
	 * 创建人id
	 */
	private Integer creatorId;
	/**
	 * 创建人姓名
	 */
	private String creatorName;
	/**
	 * 创建时间
	 */
	private Date createdAt;
	/**
	 * 最后修改人id
	 */
	private Integer updateId;
	/**
	 * 最后修改人姓名
	 */
	private String updateName;
	/**
	 * 最后修改时间
	 */
	private Date updatedAt;

}
