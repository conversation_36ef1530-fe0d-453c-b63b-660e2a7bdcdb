package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubCourseClassify;

import java.util.List;

public interface PubCourseClassifyMapper extends BaseMapper<PubCourseClassify> {
    int deleteByPrimaryKey(Integer classifyId);

    int insert(PubCourseClassify record);

    int insertSelective(PubCourseClassify record);

    PubCourseClassify selectByPrimaryKey(Integer classifyId);

    int updateByPrimaryKeySelective(PubCourseClassify record);

    int updateByPrimaryKey(PubCourseClassify record);

    /**
     * 获取所有子类id
     * @param classifyId
     * @return
     */
    List<String> getAllClassifyById(String classifyId);
}