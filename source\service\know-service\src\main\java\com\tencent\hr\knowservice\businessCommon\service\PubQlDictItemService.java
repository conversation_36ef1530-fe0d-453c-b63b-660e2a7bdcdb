package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubQlDictItemMapper;
import org.apache.commons.lang3.StringUtils;

import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubQlDictItemMapper;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description Ql字典表相关
 * @createDate 2023.10.18
 */
@Slf4j
@Service
public class PubQlDictItemService {

    @Resource
    private PubQlDictItemMapper pubQlDictItemMapper;

    @Autowired
    private RedisUtil redisUtil;


    public boolean itemKeyContainCourseId(String itemKey, String courseId) {
        if (StringUtils.isEmpty(itemKey) && StringUtils.isEmpty(courseId)) {
            return false;
        }
        String value = pubQlDictItemMapper.findValByItemKeyAndCourseId(itemKey, courseId);
        return StringUtils.isNotEmpty(value);
    }


    /**
     * 获取字典值
     *
     * @param dictItemKey
     * @return
     */
    public String getValByItemKey(String dictItemKey) {
        String cacheKey = CommonCacheKeyEnum.QLDictItem.getKeyName().concat(dictItemKey);
        String result = (String) redisUtil.get(cacheKey);
        if (StringUtils.isNotEmpty(result)) {
            return result;
        }

        result = pubQlDictItemMapper.findValByItemKey(dictItemKey);
        redisUtil.set(cacheKey, result, Constants.CacheExpireEnum.Cache_Time_Expire_5_minute.getTime() * 2);
        return result;
    }

    /**
     * 清除字典缓存
     *
     * @param dictItemKey
     * @return
     */
    public boolean delItemKeyCache(String dictItemKey) {
        String cacheKey = CommonCacheKeyEnum.QLDictItem.getKeyName().concat(dictItemKey);
        if (redisUtil.hasKey(cacheKey)) {
            redisUtil.del(cacheKey);
        }

        return true;
    }
}

