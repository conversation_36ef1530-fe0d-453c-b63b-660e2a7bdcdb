package com.tencent.hr.knowservice.businessCommon.proxy;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;


@FeignClient(name = "grow-matrix-service-search", url = "${project.group-matrix-service-host}")
public interface GroupMatrixSerivceApi {
    @PostMapping("/api/user/search")
    String search(@RequestBody Map<String, Object> params);

    @RequestMapping(value = "/api/user/base/getModuleInfo", method = RequestMethod.GET)
    String getModuleInfo();

    @RequestMapping(value ="/api/user/search/getNetCourseClassify",method = RequestMethod.GET)
    String getNetCourseClassify();

    @PostMapping("/api/user/search/similar_course")
    String similarCourse(@RequestBody Map<String, Object> params);
}
