package com.tencent.hr.knowservice.businessCommon.controller.manage;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.service.PubQlDictItemService;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/businessCommon/manage/dict/")
public class PubDictController {

    @Autowired
    PubQlDictItemService dictItemService;

    /***
     * 获取字典项配置的值
     * @param dictKey
     * @return
     */
    @GetMapping("value")
    public TransDTO getDictValue(@RequestParam(name = "key") String dictKey){
        String value = dictItemService.getValByItemKey(dictKey);
        TransDTO<Object> dto = new TransDTO<>();
        dto.withData(value).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 检查是否系统繁忙时段
     * @return
     */
    @GetMapping("is_sys_busy")
    public TransDTO getSystemBusy(){
        String value = dictItemService.getValByItemKey("System_Is_Busy");
        TransDTO<Object> dto = new TransDTO<>();
        dto.withData(value).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }
}
