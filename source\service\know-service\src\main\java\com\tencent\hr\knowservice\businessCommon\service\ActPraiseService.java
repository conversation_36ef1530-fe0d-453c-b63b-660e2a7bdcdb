package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.knowservice.businessCommon.dto.common.ActPraiseDto;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.entity.ActPraise;
import com.tencent.hr.knowservice.graphic.dao.mapper.ActPraiseMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ActPraiseService {

    @Resource
    private ActPraiseMapper actPraiseMapper;

    /**
     * 取消点赞
     *
     * @param actType
     * @param actId
     * @return
     */
    public boolean deletePraise(String actType, String actId, Integer interactId, Integer interactType) {

        QueryWrapper<ActPraise> queryWrapper = new QueryWrapper();
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("interact_id", interactId);
        queryWrapper.eq("interact_type", interactType);
        queryWrapper.isNull("deleted_at");
        ActPraise praise = actPraiseMapper.selectOne(queryWrapper);
        boolean ret = false;

        if (praise != null) {
            String staffName = current.getStaffName();
            Date date = new Date();
            praise.setUpdatedAt(date);
            praise.setUpdateId(staffId);
            praise.setUpdateName(staffName);
            praise.setDeletedAt(date);
            actPraiseMapper.updateById(praise);
            ret = true;
        }
        return ret;
    }

    public ArrayList<ActPraiseDto> checkPraisedBatch(String actType,
                                                     String actId,
                                                     Integer interactType) {
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        QueryWrapper<ActPraise> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("interact_type", interactType);
        queryWrapper.isNull("deleted_at");
        List<ActPraise> actPraises = actPraiseMapper.selectList(queryWrapper);
        ArrayList<ActPraiseDto> actPraiseDtos = new ArrayList<>();
        actPraises.forEach(actPraise -> {
            ActPraiseDto praiseDto = new ActPraiseDto();
            BeanUtils.copyProperties(actPraise, praiseDto);
            actPraiseDtos.add(praiseDto);
        });
        return actPraiseDtos;
    }

    public ActPraiseDto checkPraised(String actType, String actId, Integer interactId, int interactType) {
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        QueryWrapper<ActPraise> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("interact_id", interactId);
        queryWrapper.eq("interact_type", interactType);
        queryWrapper.isNull("deleted_at");
        ActPraise actPraises = actPraiseMapper.selectOne(queryWrapper);
        if (actPraises != null) {
            ActPraiseDto praiseDto = new ActPraiseDto();
            BeanUtils.copyProperties(actPraises, praiseDto);
            return praiseDto;
        }
        return null;
    }

    public boolean addPraise(String actType, String actId, Integer interactId, int interactType) {
        try {
            ActPraiseDto praiseDto = checkPraised(actType, actId, interactId, interactType);
            if (praiseDto != null) {
                return true;
            }
            ContextEntity current = GatewayContext.current();
            Integer staffId = Integer.valueOf(current.getStaffId());
            String staffName = current.getStaffName();
            ActPraise actPraise = new ActPraise();
            actPraise.setActId(actId);
            actPraise.setEmpName(staffName);
            actPraise.setActType(Integer.valueOf(actType));
            actPraise.setStaffId(staffId);
            actPraise.setInteractType(interactType);
            actPraise.setInteractId(interactId);

            actPraise.setCreatedAt(new Date());
            actPraise.setCreatorId(staffId);
            actPraise.setCreatorName(staffName);
            actPraiseMapper.insert(actPraise);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public Integer getPraiseCount(String actType, String actId, Integer interactId, int interactType) {
        QueryWrapper<ActPraise> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("interact_id", interactId);
        queryWrapper.eq("interact_type", interactType);
        queryWrapper.isNull("deleted_at");
        Integer integer = actPraiseMapper.selectCount(queryWrapper);
        return integer;
    }


    public ArrayList<ActPraiseDto> checkPraisedList(String actType, String actId, Integer interactType) {
        QueryWrapper<ActPraise> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("interact_type", interactType);
        queryWrapper.isNull("deleted_at");
        List<ActPraise> actPraises = actPraiseMapper.selectList(queryWrapper);
        ArrayList<ActPraiseDto> actPraiseDtos = new ArrayList<>();
        actPraises.forEach(actPraise -> {
            ActPraiseDto praiseDto = new ActPraiseDto();
            BeanUtils.copyProperties(actPraise, praiseDto);
            actPraiseDtos.add(praiseDto);
        });
        return actPraiseDtos;
    }
    public Integer getPraiseCount(String actType, String actId) {
        QueryWrapper<ActPraise> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.isNull("deleted_at");
        Integer integer = actPraiseMapper.selectCount(queryWrapper);
        return integer;
    }
}
