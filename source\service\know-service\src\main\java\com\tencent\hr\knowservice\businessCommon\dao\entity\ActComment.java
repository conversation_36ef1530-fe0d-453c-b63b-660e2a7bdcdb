package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.Date;

/**
 * act_comment
 * <AUTHOR>
@Data
public class ActComment implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 单据类型(1 面授课 2 网络课 3 班级 4 活动 5 直播 6 图文 15 课单)
     */
    private Integer actType;

    /**
     * 课程/网课/直播id/图文id
     */
    private String actId;

    /**
     * 当前作品的作者id
     */
    private Integer actAuthorId;

    /**
     * 评论人Id
     */
    private Integer staffId;

    /**
     * 评论人姓名
     */
    private String empName;

    /**
     * 回复人id
     */
    private Integer replyStaffId;

    /**
     * 回复人姓名
     */
    private String replyStaffName;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 收藏数
     */
    private long favCount;

    /**
     * 点赞数
     */
    private long praiseCount;

    /**
     * 回复数
     */
    private long replyCount;

    /**
     * 内容审核通过
     */
    private Byte needShow;

    /**
     * 是否上墙
     */
    private Byte upPass;

    /**
     * 是否匿名
     */
    private Byte anonymous;

    /**
     * 置顶时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stickTime;

    /**
     * 是否移动端
     */
    private Byte fromMobile;

    /**
     * 回复的评论id
     */
    private Integer pid;

    /**
     * 全路径,上级id,本记录id,  如 10,23,
     */
    private String fullPath;

    /**
     * 备注拓展字段
     */
    private String remark;

    /**
     * 删除时间
     */
    private Byte enabled;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private static final long serialVersionUID = 1L;

}
