const {defineConfig} = require('@vue/cli-service')
const path = require("path");

function resolve(dir) {
    return path.join(__dirname, dir)
}

module.exports = defineConfig({
    assetsDir:'',
    transpileDependencies: true,
    filenameHashing: false,
    css: {
        extract: false,
    },
    devServer: {
        port: 12000,
        open: true,
        allowedHosts: 'all',
    },
    configureWebpack: {
        resolve: {
            alias: {
                '@': resolve('src'),
                'sdc-core': '@tencent/sdc-core',
                'sdc-webui': '@tencent/sdc-webui',
                'sdc-theme': '@tencent/sdc-theme',
            },
            fallback: {
                path: false,
            },
        }
    },
    chainWebpack(config) {
        // when there are many pages, it will cause too many meaningless requests
        // config.plugins.delete('prefetch')
        // 清除已有的font和file-loader配置
        config.module.rules.delete('font')
        config.module.rules.delete('images')

        config.when(process.env.NODE_ENV !== 'development',
            config => {
                config.entry('index').add('./src/main.js').end()
                config.output
                    .filename("[name].build.js?v=[hash:10]")
                    .chunkFilename("[name].build.js?v=[hash:10]")
                    .end()
                    
                config.devtool(false)
                config.optimization.splitChunks(false)


                config.module
                    .rule('images')
                    .test(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/)
                    .use('url-loader')
                    .loader('url-loader')
                    .options({limit: 500 * 1024})
                    .end()

                config.module
                    .rule('fonts')
                    .test(/\.(woff|woff2|eot|ttf|otf)(\?.*)?$/)
                    .use('file-loader')
                    .loader('url-loader')
                    .tap(options => ({
                        limit: 1024 * 1024,
                        name: 'fonts/[name].[hash:8].[ext]'
                    }))
                    .end()
            }
        )

    },

})
