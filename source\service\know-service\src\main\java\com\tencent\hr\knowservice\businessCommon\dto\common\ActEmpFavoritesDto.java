package com.tencent.hr.knowservice.businessCommon.dto.common;

import lombok.Data;

@Data
public class ActEmpFavoritesDto {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户Id
     */
    private Integer staffId;

    /**
     * 用户名
     */
    private String empName;

    /**
     * 课程/网课/直播id
     */
    private String actId;

    /**
     * 单据类型(1 面授课 2 网络课 3 班级 4 活动 5 直播)
     */
    private Byte actType;

    /**
     * 课程/网课/直播名称
     */
    private String actName;

    /**
     * 互动id（评论/提问）
     */
    private Integer interactId;

    /**
     * 单据类型(1 评论 2提问,3 课程)
     */
    private Integer interactType;

    /**
     * 老系统Id
     */
    private Integer oldSyncId;

    /**
     * 积分
     */
    private String credit;
}
