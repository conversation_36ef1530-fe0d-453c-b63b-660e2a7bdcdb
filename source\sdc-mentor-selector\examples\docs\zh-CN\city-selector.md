## CitySelector 省市选择器
> 贡献者：v_whaigong(龚文海)；最近更新时间：2024-06-06；

可通过级联选择器逐级查看并选择省市。

### 基础用法

适用广泛的基础选择，提供2种方式选择省市，用 Tag 展示已选省市。省市级联选择器目前默认使用多选模式<br/>
<br/>1. 展开级联面板选择； 2. 输入关键字搜索，使用下拉菜单展示筛选后的省市<br/>
<br/>提醒: 请避免在选择后动态改变选择器的宽度，这会造成 Tag 区域样式显示问题

:::demo `v-model` 的值为当前被选中的省市选项的 **value** 属性值
```html
<template>
  <div class="block">
    <span class="demonstration">基础单选</span>
    <sdc-city-selector v-model="value1" :map="map" placeholder="基础单选" />
  </div>
  <div class="block">
    <span class="demonstration">基础多选</span>
    <sdc-city-selector v-model="value2" placeholder="基础多选" @change="change"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: [],
        map: {
          multiple: false
        }
      }
    },
    methods: {
      change(e) {
        console.log(e)
      }
    }
  }
</script>
```
:::

### 级联面板展示层级

设置显示的级联层级数

:::demo 可通过 `level` 来设置显示层级数，默认二级。
```html
<template>
  <div class="block">
    <span class="demonstration">仅展示一级</span>
    <sdc-city-selector v-model="value1" :level="1"/>
  </div>
  <div class="block">
    <span class="demonstration">默认展示二级</span>
    <sdc-city-selector ref="selector" v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::
### 多选Tag展示
:::demo 多选模式下，默认情况下会展示所有已选中的选项的Tag，你可以使用`collapseTags`来折叠Tag。 可以使用`tagsLength`来设置Tag最大展示文字数
```html
  <div class="block">
    <span class="demonstration">默认显示所有Tag</span>
    <sdc-city-selector v-model="value1"/>
  </div>
  <div class="block">
    <span class="demonstration">折叠展示Tag</span>
    <sdc-city-selector v-model="value2" collapseTags :tagsLength="7"/>
  </div>
<script>
  export default {
    data(){
      return{
        value1: [1, 2],
        value2: [1, 2]
      }
    }
  }
</script>
```
:::

### 仅展示最后一级

可以仅在输入框中显示选中项最后一级的职级，而不是选中职级所在的完整路径。

:::demo 属性 `showAllLevels` 定义了是否显示完整的路径，将其赋值为`false`则仅显示最后一级。
```html
<template>
  <sdc-city-selector v-model="value" :showAllLevels="false" :filterable="false"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::

### 可搜索

:::demo 可以使用`filterable`属性来开启搜索功能, 默认开启搜索功能。
```html
  <div class="block">
    <span class="demonstration">不可搜索</span>
    <sdc-city-selector v-model="value1" :filterable="false"/>
  </div>
  <div class="block">
    <span class="demonstration">可搜索</span>
    <sdc-city-selector v-model="value2" />
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 多语言

:::demo 属性 `lang` 定义了展示语言，默认中文，可选值`en`。 
```html
<template>
  <sdc-city-selector v-model="value" lang="en"/>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**small**尺寸
```html
<template>
  <div class="block">
    <span class="demonstration">默认尺寸</span>
    <sdc-city-selector v-model="value1"/>
  </div>
  <div class="block">
    <span class="demonstration">较小尺寸</span>
    <sdc-city-selector size="small" v-model="value2"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::

### 数据源

选择器内置的省市数据是在初始时从远程服务器拉取的，用户可替换自己的数据源

:::demo `promise` 属性接收一个 `Promise`，完成状态时的返回值为有清晰层级结构的省市集合，如果数据源字段与选择器默认要求的 **value，label，children** 不一致，可通过 `map` 属性进行映射，
```html
<template>
  <sdc-city-selector v-model="value" :map="map" :promise="promise"></sdc-city-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: 'L3',
        map: {
          value: 'group',
          label: 'mark'
          // children: 'children'
        },
        remoteData: [
          { 
            group: 'L1', 
            mark: '选项1',
            children: [
              {
                group: 'L3', 
                mark: '子集2', 
              }
            ]
          },
          { 
            group: 'L2', 
            mark: '选项2',
            children: [
              {
                group: 'L4', 
                mark: '子集3', 
              }
            ]
          }
        ],
        promise: null,
      }
    },
    created() {
      this.promise = new Promise((resolve, reject) => {
        resolve(this.remoteData)
      })
    }
  }
</script>
```
:::

### CitySelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | Array | — | — |
| size | 输入框尺寸 | string | small | — |
| level | 层级数 | Number | 1、2 | 2 |
| disabled | 是否禁用 | boolean | — | false |
| lang | 语言 | string | 中文: zh，英文: en | zh |
| collapseTags | 多选模式下是否折叠Tag | boolean | — | false |
| tagsLength | Tag最大展示文字数, 最小1 | number | — | 13 |
| showAllLevels | 输入框中是否显示选中值的完整路径 | boolean | — | true |
| showTotal | 是否显示后置的已选数量 | boolean | — | false |
| placeholder | 占位符 | string | — | — |
| filterable | 是否可搜索选项 | boolean | — | true |
| separator | 选项分隔符 | string | — | 斜杠'/' |
| map | 映射配置，具体见下表 | object | — | — |
| promise | 获取层级省市数据的方法 | Promise | — | — |

### CitySelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项, 包含label、value、path数组、fullName |

### CitySelector Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| clearSelected | 用于清空选中项 | — |
| getCheckedNodes | 获取选中的节点 | (leafOnly) 是否只是叶子节点，默认值为 false |


### map
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value | 指定选项的值为选项对象的某个属性值 | string | — | 'value' |
| label | 指定选项标签为选项对象的某个属性值 | string | — | 'label' |
| children | 指定选项的子选项为选项对象的某个属性值 | string | — | 'children' |
| emitPath | 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 | boolean | — | false |
| multiple | 是否多选 | boolean | — | true |
