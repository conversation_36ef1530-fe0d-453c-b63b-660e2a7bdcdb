package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfo;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfoDetail;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubFileInfoDetailMapper;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubFileInfoMapper;
import com.tencent.hr.knowservice.netcourse.constant.CacheKeyEnum;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PubFileInfoTaskService {

    @Resource
    private PubFileInfoMapper fileInfoMapper;

    @Resource
    PubFileInfoDetailMapper detailMapper;

    @Autowired
    RedisUtil redisUtil;

    @Value("${portal.learn.training}")
    private String trainingHost;

    @Value("${extapi.contentCenter.host}")
    private String contentCenterHost;
    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${com.appSetting.tenantCode}")
    private String tenantCode;

    @Resource
    PubFileInfoMapper infoMapper;

    /**
     * 更新正在转码中素材的状态
     *
     * @return
     */
    public void syncPubFileInfoStatus() {
        String cacheKey = CacheKeyEnum.SYNC_FILE_INFO_TRANSCODE_KEY.getKeyName();
        if (!redisUtil.hasKey(cacheKey)) {
            try {
                redisUtil.set(cacheKey, 1, Constants.CacheExpireEnum.Cache_Time_Expire_30_minute.getTime() / 10);
                List<String> integers = infoMapper.getPubFileContentIds();
                if (CollectionUtils.isNotEmpty(integers)) {
                    // 每次查询 100条
                    int pageSize = 100;
                    int total = integers.size() / pageSize;
                    for (int pageNum = 0; pageNum <= total; pageNum++) {
                        syncStatus(integers.stream().skip((long) pageNum * pageSize).limit(pageSize).collect(Collectors.toList()));
                    }
                }
            } catch (Exception ex) {
                log.error("同步素材转码状态失败，错误信息：" + ex.getMessage(), ex);
                redisUtil.del(cacheKey);
            } finally {
                redisUtil.del(cacheKey);
            }
        }
    }

    /**
     * 同步转码状态
     *
     * @param integers
     */
    private void syncStatus(List<String> integers) {
        String params = StringUtils.join(integers.stream().filter(Objects::nonNull).toArray(), ",");
        String path = "/api/v1/content/content_status?content_ids=" + params;
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, null, null);
        StringBuffer errorMsg = new StringBuffer();
        String result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        if (errorMsg.length() > 0) {
            log.error("同步转码状态出错:{}", errorMsg);
        }
        JSONObject object = new JSONObject(result);
        boolean success = (boolean) object.get("success");
        if (success) {
            JSONArray data = object.getJSONArray("data");
            Map<String, List<String>> grouped = IntStream.range(0, data.length())
                    .mapToObj(data::getJSONObject)
                    .collect(Collectors.groupingBy(jsonObj -> jsonObj.optString("status_code", "-99"),
                            Collectors.mapping(jsonObj -> jsonObj.optString("content_id", "1"), Collectors.toList())));
            grouped.forEach((statusCode, contentIds) -> {
                if (Objects.equals(statusCode, "-99")) {
                    return;
                }
                PubFileInfo pubFileInfo = new PubFileInfo();
                switch (statusCode) {
                    case "-1":
                        pubFileInfo.setStatus(3);
                        pubFileInfo.setIsTransSucessed(0);
                        break;
                    case "-2":
                        pubFileInfo.setStatus(16);
                        break;
                    case "2":
                        pubFileInfo.setStatus(2);
                        break;
                    case "3":
                        pubFileInfo.setStatus(15);
                        break;
                    case "99":
                        pubFileInfo.setStatus(13);
                        pubFileInfo.setIsTransSucessed(1);
                        pubFileInfo.setIsDistributeSucessed(1);
                        pubFileInfo.setSupportMobile(1);
                        break;
                    default:
                        break;
                }
                if (pubFileInfo.getStatus() != null) {
                    pubFileInfo.setUpdatedAt(new Date());
                    UpdateWrapper<PubFileInfo> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().in(PubFileInfo::getContentId, contentIds);
                    fileInfoMapper.update(pubFileInfo, updateWrapper);
                }
            });
            batchUpdateFileDetail(data);
        }
    }

    /**
     * 批量更新 pub_file_info_detail
     *
     * @param data
     */
    private void batchUpdateFileDetail(JSONArray data) {
        List<PubFileInfoDetail> details = new ArrayList<>();
        data.forEach(o -> {
            JSONObject obj = (JSONObject) o;
            try {
                String contentId = obj.getString("content_id");
                if (contentId != null) {
                    Object o1 = obj.get("duration");
                    // 先获取
                    PubFileInfoDetail detail = new PubFileInfoDetail();
                    if (o1 != null && !"null".equals(o1.toString())) {
                        Integer val = (Integer) obj.get("duration");
                        detail.setDuration(Double.valueOf(val));
                    }
                    detail.setContentId(contentId);
                    details.add(detail);
                }
            } catch (Exception e) {
                log.error("更新转码状态时数据转换异常：{}", e);
            }
        });
        if (details.size() > 0) {
            // 把 contentId 换成file Id
            for (PubFileInfoDetail detail : details) {
                detailMapper.batchUpdate(detail);
            }
        }
    }


}
