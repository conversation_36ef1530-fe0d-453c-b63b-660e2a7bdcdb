package com.tencent.hr.knowservice.businessCommon.controller.manage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.PubFilePageDto;
import com.tencent.hr.knowservice.businessCommon.service.PubFileInfoService;
import com.tencent.hr.knowservice.businessCommon.vo.PubFileInfoVo;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileDto;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileInfoDto;
import org.apache.http.HttpStatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/businessCommon/manage/pub-file")
public class PubFileInfoController {

    @Autowired
    private PubFileInfoService fileInfoService;

    @PostMapping("/add-content-info")
    TransDTO addPubFileInfo(@RequestBody PubFileInfoDto fileInfoDto) {

        int objId = fileInfoService.addOrUpdateContentInfo(fileInfoDto, null);
        TransDTO<Object> dto = new TransDTO<>();
        dto.withData(objId).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 素材列表查询
     */
    @PostMapping("/get-file-list")
    public TransDTO<IPage<PubFileInfoVo>> getPubFileInfoList(@RequestBody PubFilePageDto dto) {
       return fileInfoService.getPubFileInfoList(dto);
    }
    /**
     * 根据素材id 查询素材
     * @param fileId
     * @return
     */
    @GetMapping("file/{fileId}")
    public TransDTO getPubFileInfo(@PathVariable Integer fileId){
        PubFileInfoDto fileInfo = fileInfoService.findFileInfoByFileId(fileId);
        if (fileInfo == null){
            return new TransDTO().withSuccess(false).withMessage("素材不存在！").withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        }
        return new TransDTO<PubFileInfoDto>().withData(fileInfo).withCode(HttpStatus.SC_OK);
    }

    /**
     * 素材删除
     * @param fileId
     * @return
     */
    @DeleteMapping("file/{fileId}")
    public TransDTO deleteFile(@PathVariable Integer fileId){
        return fileInfoService.deleteFile(fileId);
    }

    /**
     * 添加素材
     * @param fileInfoDto
     * @return
     */
    @PostMapping("/addOrUpdatePubFile")
    TransDTO addPubFile(@RequestBody PubFileDto fileInfoDto) {
        return fileInfoService.addOrUpdatePubFile(fileInfoDto);
    }

    /**
     *  查询草稿
     */
    @GetMapping("/get-draft")
    TransDTO getDraft() {
        return fileInfoService.getDraft();
    }

    /**
     * 根据素材id 查询素材引用详情
     *
     * @param fileId
     * @return
     */
    @GetMapping("refer/{fileId}")
    public TransDTO refer(@PathVariable Integer fileId) {
        return fileInfoService.referByFileId(fileId,false);
    }


}
