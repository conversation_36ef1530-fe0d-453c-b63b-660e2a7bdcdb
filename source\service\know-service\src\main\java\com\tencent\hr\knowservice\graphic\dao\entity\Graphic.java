package com.tencent.hr.knowservice.graphic.dao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * graphic
 *
 *
 *
 */
@Data
public class Graphic implements Serializable {
    /**
     * 图文id
     */
    private Integer graphicId;

    /**
     * 图文类型，1：用户2:管理端
     */
    private Integer graphicType;

    /**
     * 图文名称
     */
    private String graphicName;

    /**
     * 图文正文
     */
    private String graphicText;

    /**
     * 封面图id
     */
    private String coverImageId;

    /**
     * 封面图是否自己上传0：自己上传1：智能生成
     */
    private Integer isSelfUpload;

    /**
     * 图文描述
     */
    private String graphicDesc;

    /**
     * 访问权限0:全部1:部分
     */
    private Integer isOpen;

    /**
     * 目标学员
     */
    private String targetList;

    /**
     * 所属课单id
     */
    private String clId;

    /**
     * 0：草稿1:发布
     */
    private Integer graphicStatus;
    /**
     * 首次发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date firstSubmitTime;
    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 点赞次数
     */
    private Integer praiseCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 评论次数
     */
    private Integer commentCount;

    /**
     * 分享次数
     */
    private Integer shareCount;

    /**
     * 内容类型 0:原创；1:转载；2:推广;3:学习笔记
     */
    private Integer contentType;

    /**
     * 原文链接
     */
    private String fromUrl;

    /**
     * 内容分类
     */
    private String classifyId;

    /**
     * 标签
     */
    private String labels;

    /**
     * 是否开启目录0:开启1:不开启
     */
    private Integer isOpenCatalogue;

    /**
     * 图文字数
     */
    private Integer graphicNumber;

    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 作者
     */
    private String authors;
    /**
     * 作者英文名字
     */
    private String authorsEnName;
    /**
     * 管理员
     */
    private String administrators;
    /**
     * 管理员英文名字
     */
    private String administratorsEnName;

    /**文章来源类型，单单只属于平台，如果是关联关系课程，不对图文进行处理，不储存该字段 */
    private String fromType;

    /**
     * 文章文本转音频的资源id
     */
    private String audioContentId;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date creatorAt;

    /**
     * 最后修改人id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateAt;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 操作人姓名
     */
    private String remark;

    /**
     * 笔记id
     */
    private String oldGraphicId;

    private static final long serialVersionUID = 1L;
}
