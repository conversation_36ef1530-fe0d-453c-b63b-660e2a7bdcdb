import StaffService from './staff.service'

export default class UnitService {
  static getDataList(name, { count = 10, filterEnableFlag = true, includeVirtualUnit = false, unitID = 0, includeUnitSortIDs = [], LocationString = false, LocationCode = false } = {}) {
    const params = {
      limit: count,
      likeMode: 'full',
      scopeOrgIdList: Array.isArray(unitID) ? unitID : [unitID], // 限定搜索组织范围
      orgTypeIdList: includeVirtualUnit ? [1, 2] : [1],
      recordStatusList: filterEnableFlag ? [1] : [1, 0] // 组织状态
    }
    const isNumber = !isNaN(parseFloat(name)) && isFinite(name)
    // 数字代表查id， 否则查名称
    if (isNumber) {
      params.orgIdList = [Number(name)]
    } else {
      params.i18nOrgFullNameLike = name
    }
    // 限制组织选择范围
    if (includeUnitSortIDs.length) {
      params.orgLevelIdList = includeUnitSortIDs
    }
    return StaffService.postOrg(params).then(res => {
      return UnitService.mapUnitData(res.content, LocationString, LocationCode)
    })
  }
  
  // 获取组织列表
  static getTreeData(unitId = 0, { filterEnableFlag = true, includeVirtualUnit = false, LocationString = false, LocationCode = false } = {}) {
    const params = {
      orgLevelIdList: [0, 6, 8, 1, 7, 2], // 组织级别默认加上0：公司
      orgTypeIdList: includeVirtualUnit ? [1, 2] : [1],
      recordStatusList: filterEnableFlag ? [1] : [1, 0] // 组织状态
    }
    // 如果传入是空数组，需要转成0，不然会查所有组织
    if (Array.isArray(unitId) && !unitId.length) {
      unitId = 0
    }
    const paramsKey = Array.isArray(unitId) ? 'orgIdList' : 'parentOrgId'
    params[paramsKey] = unitId || 0
    return StaffService.postOrg(params).then(res => {
      return UnitService.mapUnitData(res.content, LocationString, LocationCode)
    })
  }

  static mapUnitData(unitList, LocationString, LocationCode) {
    const arr = []
    unitList.filter(item => item).map(item => {
      const { orgId, orgNameCn, orgLevelId, orgFullNameCn, hasChildFlag, orgOwnershipTypeId, orgOwnershipTypeNameCn, orgOwnershipTypeNameEn, ...unit } = item
      const unitObj = {
        UnitID: orgId,
        UnitName: orgNameCn,
        UnitFullName: orgFullNameCn,
        UnitSortID: orgLevelId,
        UnitOwnershipTypeId: orgOwnershipTypeId,
        UnitOwnershipTypeNameCn: orgOwnershipTypeNameCn,
        UnitOwnershipTypeNameEn: orgOwnershipTypeNameEn,
        isLeaf: !hasChildFlag, // 有子集为1，没有子集为0代表是树形的叶子节点
        ...unit
      }
      
      if (LocationString) { 
        unitObj.UnitIDPath = unit.orgFullPath.split('.').filter(item => item).join(';')
      }
      if (LocationCode) {
        unitObj.UnitLocationCode = unit.orgFullPathPs
      }
      // 【总办】需要放在第一位
      if (orgId === 1) {
        arr.unshift(unitObj)
      } else {
        arr.push(unitObj)
      }
    })
    return arr
  }
}
