package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * v_base_emp_info
 * <AUTHOR>
@Data
public class VBaseEmpInfo implements Serializable {
    /**
     * Qlearning员工的ID
     */
    private Integer empId;

    /**
     * 员工 StaffId
     */
    private Integer staffId;

    /**
     * 员工ENGNAME
     */
    private String empNameEn;

    /**
     * 员工中文名
     */
    private String empNameCh;

    /**
     * 性别
     */
    private String sex;

    /**
     * 出生年月
     */
    private Date birthday;

    /**
     * 员工类型（字典ID=2）:2 正式 3 外聘 4 实习 5 合作伙伴 6 顾问 7 毕业生 8 短期劳务工 9 外包
     */
    private Integer empType;

    /**
     * 员工类型名
     */
    private String empTypeName;

    /**
     * 员工在职状态（字典ID=3）
     */
    private Integer empStatus;

    /**
     * 员工在职状态名称
     */
    private String empStatusName;

    /**
     * 组织ID
     */
    private String deptId;

    /**
     * 组织全称
     */
    private String deptFullName;

    /**
     * 组织编码(6位)
     */
    private String locationCode;

    /**
     * 上级StaffId
     */
    private String parentStaffId;

    /**
     * 上级 英文名
     */
    private String parentName;

    /**
     * 工作地Id（字典ID=14）
     */
    private Integer workCityId;

    /**
     * 工作地
     */
    private String workCityName;

    /**
     * 工作地所属国家id
     */
    private Integer countryId;

    /**
     * 工作地所属国家名称
     */
    private String countryNameCn;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date joinDate;

    /**
     * 员工属性:（字典ID=96） (1:总办,2:EVP1,3:中层管理干部,4:基层管理干部,5:员工,6:VP1,7:SEVP,8:SVP,8:CVP)
     */
    private Integer staffPropertyId;

    /**
     * 员工属性名( (1:总办,2:EVP1,3:中层管理干部,4:基层管理干部,5:员工,6:VP1,7:SEVP,8:SVP,8:CVP))
     */
    private String staffPropertyName;

    /**
     * 招聘来源ID
     */
    private Integer recruitSourceId;

    /**
     * 招聘来源名称
     */
    private String recruitSourceName;

    /**
     * 试用期月份
     */
    private Long probationPeriod;

    /**
     * 导师ID
     */
    private String tutorIds;

    /**
     * 导师中英文姓名
     */
    private String tutorNames;

    /**
     * 转正日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beFormalDate;

    private String dimissionDate;

    private static final long serialVersionUID = 1L;
}