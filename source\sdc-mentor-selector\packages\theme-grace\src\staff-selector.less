@import "./vars.less";
@import "./selector.less";
@import "./modal.less";
@import "./toast.less";

.sdc-staff-selector-popper {
  width: 260px!important;
  .selector-item {
    display: flex;
    align-items: center;
    font-size: @font-14;
    line-height: normal;
    margin: 10px 0;
    .item-name {
      text-overflow: ellipsis;
      overflow: hidden;
      margin-left: 8px;
      white-space: nowrap;
      color: @color-text-black;
      line-height: 25px;
    }
    .item-avatar {
      width: 25px;
      border-radius: 50%;
      height: 25px;
      flex-shrink: 0;
    }
    .item-former-name{
      flex-shrink: 0;
      height: 14px;
      width: 14px;
      margin-left: 8px;
      background: url("../img/former-name.svg");
    }
  }
}
