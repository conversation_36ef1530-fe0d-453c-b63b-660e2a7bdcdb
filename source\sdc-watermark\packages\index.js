// 整个包的入口
// 统一导出

import sdcWaterMark from "./componentPage";
 
const components = [
  sdcWaterMark
]
// 定义install方法 接收Vue作为参数，如果使用use注册插件，那么所有的组件都会被注册
const install = function (Vue) {
  if (install.installed) return;
  components.map(component => Vue.component(component.name, component))
}

// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  install,
  sdcWaterMark
}

