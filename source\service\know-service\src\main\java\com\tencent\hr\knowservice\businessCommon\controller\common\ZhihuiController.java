package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.tencent.hr.knowservice.businessCommon.service.PortalCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/api/businessCommon/common/zhihui")
public class ZhihuiController {

    @Autowired
    PortalCommonService portalCommonService;

    /**
     * 获取智绘封面
     * @param title
     * @param num
     * @return
     */
    @GetMapping("/get_cover")
    public String getSmartCover(@RequestParam(value = "title") String title,
                                  @RequestParam(value = "num") Integer num){
        return portalCommonService.getAICover(title,num);
    }
}
