package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.JsonUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class ResourceService {
    @Value("${com.appSetting.appName}")
    private String appName;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${extapi.contentCenter.host}")
    private String host;

    /**
     * 获取文件下载地址
     * @param
     * @return
     */
    public String getContentData(String contentId) {
//        ContextEntity current = GatewayContext.current();
        String url = host + "/api/v1/content/"+contentId+"/url";
        HttpHeaders headers = HeaderSignUtil.getESBHeader(appId, appToken,null, null);
        String result = HttpUtil.sendGetByHttpClient(url, headers.toSingleValueMap());
        log.info("获取文件下载地址请求参数={}，返回结果{}", result, result);

        if (StringUtils.isNotBlank(result)) {
            Map<String, Object> map = JsonUtils.jsonToMap(result);
            return  String.valueOf(map.get("data"));
        }
        return null;
    }
}
