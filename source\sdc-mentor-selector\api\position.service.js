/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2023-09-25 10:34:31
 */

import CoreService from './core.service'
export default class PositionService {
  // 根据职位类查询职位级列表
  static getPositionData({ includeClans = [], limit = 1000 } = {}) {
    const params = {
      queryCondition: {
        limit,
        argMap: {
          positionClanIdList: includeClans.length ? includeClans : undefined // 需要展示的职位族
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-position-realtime/sdc-webui/data', { params }).then(res => {
      const genusList = res.content.reduce((prev, current) => {
        const item = prev.find(item => item.id === current.positionGenusId)
        const levelObj = {
          id: current.positionId,
          label: current.positionNameCn,
          labelEn: current.positionNameEn,
          value: current.positionId
        }
        if (item) {
          item.children.push(levelObj)
        } else {
          prev.push({ 
            positionClanId: current.positionClanId,
            positionClanNameCn: current.positionClanNameCn,
            positionClanNameEn: current.positionClanNameEn,
            id: current.positionGenusId,
            label: current.positionGenusNameCn,
            labelEn: current.positionGenusNameEn,
            value: current.positionGenusId,
            children: [levelObj] 
          })
        }
        return prev
      }, [])
      const clanList = genusList.reduce((prev, current) => {
        const item = prev.find(item => item.id === current.positionClanId)
        const genusObj = Object.assign({}, current)
        delete genusObj.positionClanId
        delete genusObj.positionClanNameCn
        delete genusObj.positionClanNameEn
        if (item) {
          item.children.push(genusObj)
        } else {
          prev.push({
            id: current.positionClanId, 
            label: current.positionClanNameCn, 
            labelEn: current.positionClanNameEn, 
            value: current.positionClanId, 
            children: [genusObj] 
          })
        }
        return prev
      }, [])
      return clanList
    })
  }
}
