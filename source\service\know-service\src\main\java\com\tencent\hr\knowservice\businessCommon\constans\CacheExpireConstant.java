package com.tencent.hr.knowservice.businessCommon.constans;

/**
 * @description:
 * @author: vincentyqwu
 * @createDate: 2021/3/19
 * @version: 1.0
 */
public class CacheExpireConstant {
    /**
     * 过期时间相关枚举
     */
    public static enum CacheExpireEnum{
        //未读消息的有效期为30天
        Cache_Time_Expire_5_second(5L),
        Cache_Time_Expire_30_second(30L),
        Cache_Time_Expire_1_minute(60L),
        Cache_Time_Expire_5_minute(5 * 60L),
        Cache_Time_Expire_30_minute(30 * 60L),
        Cache_Time_Expire_1_hour(60 * 60L),
        Cache_Time_Expire_12_hour(12 * 60 * 60L),
        Cache_Time_Expire_1_day(24 * 60 * 60L),
        Cache_Time_Expire_1_week(7 * 24 * 60 * 60L),
        Cache_Time_Expire_1_month(30 * 24 * 60 * 60L)
        ;

        /**
         * 过期时间
         */
        private Long time;

        CacheExpireEnum(Long time) {
            this.time = time;
        }

        public Long getTime() {
            return time;
        }
    }
}
