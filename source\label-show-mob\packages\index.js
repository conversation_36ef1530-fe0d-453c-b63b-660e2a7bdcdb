// 整个包的入口
import Vue from 'vue'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI)

import sdcLabelShowMob from "./comment";
const components = [
  sdcLabelShowMob
]
import AutoTrackBeacon from '@tencent/autotracker-beacon-oa';
import exposure from '@tencent/autotracker-beacon-oa/dist/plugin/exposure';
// 统计埋点
Vue.prototype.autoInstance = function(data) {
  const autoInstance = new AutoTrackBeacon({
    report: {
      enableReport: () => process.env.NODE_ENV === 'production',
      appkey: process.env.NODE_ENV === 'production' ? '0WEB05I0WC0H1J6I' : 'test', // 从datahub获取的appkey
      consolelog: process.env.NODE_ENV === 'production',
      commonParams: { // 自定义的上报公共参数, 每条上报都会携带
        uid: data ? data.staff_id || '' : '', // 业务用户身份标示，推荐使用uid作为key
      },
    },
    uselib: ['element'], // 预设了ui库track规则，包括omui,antd,element,tdesign等；不设置该项则没有预设规则，完全依据传入的track配置
  });
  autoInstance.use(exposure)
  // ！！！！初始化，注意这里要显式调用init方法
  autoInstance.init();
}
// 定义install方法 接收Vue作为参数，如果使用use注册插件，那么所有的组件都会被注册
const install = function (Vue) {
  if (install.installed) return;
  components.map(component => Vue.component(component.name, component))
}

// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  install,
  sdcLabelShowMob
}

