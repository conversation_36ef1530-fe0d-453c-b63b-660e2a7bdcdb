package com.tencent.hr.knowservice.businessCommon.constans;

public enum TaskCodeEnum {
    TASK_PUBLISH_COURSELIST("task_publish_CourseList",15,"首次创建课单激励"),
    TASK_PUBLISH_CASE("task_publish_Case",16,"首次发表案例激励"),
    TASK_PUBLISH_ARTICLE("task_publish_Article",18,"首次发表文章激励"),
    ;


    private String code;
    private Integer actType;
    private String meaasge;

    TaskCodeEnum(String code, Integer actType, String meaasge) {
        this.code = code;
        this.actType = actType;
        this.meaasge = meaasge;
    }

    public String getCode() {
        return code;
    }

    public Integer getActType() {
        return actType;
    }

    public String getMeaasge() {
        return meaasge;
    }

    public static String getCodeByActType(Integer actType){
        if (actType == null){
            return null;
        }
        for (TaskCodeEnum value : TaskCodeEnum.values()) {
            if (value.getActType().equals(actType)){
                return value.getCode();
            }
        }
        return null;
    }
}
