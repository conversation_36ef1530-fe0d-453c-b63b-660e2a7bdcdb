<template>
  <ul class="sdc-navbar" ref="navbar">
    <li v-for="(item, index) in menuList.current" :key="index">
      <div :class="getMenuClass(item)" @click="handleLinkClick(item)" :title="item.text">
        {{item.text}}
      </div>
    </li>
    <li v-if="showMoreMenu">
      <el-dropdown>
        <span class="el-dropdown-link">
          {{$st('sdc.layout.navbar.more')}}<i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="sdc-drop-menu app-drop-menu">
          <el-dropdown-item v-for="(item, index) in menuList.more" :key="index">
            <sdc-link v-if="item.url" :custom-class="getMenuClass(item)" :to="item.url" :target="item.target" :text="item.text" position="right"/>
            <div v-else :class="getMenuClass(item)" @click="handleLinkClick(item)">
              {{item.text}}
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </li>
  </ul>
</template>

<script>
  import { locale, link } from 'mixins'
  import SdcLink from 'packages/link'
  import { addResizeListener, removeResizeListener } from 'sdc-webui/src/utils/resize'

  export default {
    name: 'sdc-navbar',
    mixins: [locale, link],
    props: {
      menus: {
        type: Object,
        default() {
          return {}
        }
      }
    },
    data() {
      return {
        showMaxMenuCount: this.menus.adaptive === true ? this.menus.data.length : this.menus.maxMenuCount || 6
      }
    },
    computed: {
      menuList() {
        const res = { current: [], more: [] }
        const { map = {}, data = [] } = this.menus
        const mapMenus = data.map(item => ({
          key: item[map.key] || item.key,
          url: item[map.url] || item.url,
          text: item[map.text] || item.text,
          click: item[map.click] || item.click,
          target: item[map.target] || item.target
        }))
        if (mapMenus.length <= this.showMaxMenuCount) {
          res.current = mapMenus
        } else {
          res.current = mapMenus.slice(0, this.showMaxMenuCount - 1)
          res.more = mapMenus.slice(this.showMaxMenuCount - 1)
        }
        return res
      },
      showMoreMenu() {
        return this.menus.data.length > this.showMaxMenuCount
      }
    },
    mounted() {
      this.menus.adaptive === true && this.menus.data.length > 0 && addResizeListener(this.$el, this.handleResize)
    },
    destroyed() {
      this.menus.adaptive === true && this.menus.data.length > 0 && removeResizeListener(this.$el, this.handleResize)
    },
    methods: {
      isActiveMenu(item) {
        return this.menus.active !== undefined && item.key === this.menus.active
      },
      getMenuClass(item) {
        let res = 'nav-item ellipsis'
        if (this.isActiveMenu(item)) {
          res += ' selected'
        }
        return res
      },
      handleResize() {
        if (this.$refs.navbar) {
          this.showMaxMenuCount = Math.max(1, Number.parseInt(this.$refs.navbar.clientWidth / 130)) 
        }
      }
    },
    components: {
      SdcLink
    }
  }
</script>
