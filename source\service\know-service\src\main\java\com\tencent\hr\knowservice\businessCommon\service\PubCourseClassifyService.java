package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubCourseClassify;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.PubCourseClassifyMapper;
import com.tencent.hr.knowservice.businessCommon.dto.PubCourseClassifyDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【pub_course_classify(课程分类)】的数据库操作Service实现
* @createDate 2022-11-22 09:45:25
*/
@Service
public class PubCourseClassifyService {

    @Resource
    private PubCourseClassifyMapper classifyMapper;

    public Object getClassify(String actType,  String classifyFullPath) {

        QueryWrapper<PubCourseClassify> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(actType)) {
            wrapper.eq("act_type", actType);
        }

        if (StringUtils.isNotBlank(classifyFullPath)) {
            wrapper.eq("full_path", classifyFullPath);
        }

        wrapper.isNull("deleted_at");
        List<PubCourseClassify> courseClassifies = classifyMapper.selectList(wrapper);
        ArrayList<PubCourseClassifyDto> dtos = new ArrayList<>();
        List<PubCourseClassify> classifyList = courseClassifies.stream().filter(o -> o.getPid().equals(0)).collect(Collectors.toList());
        classifyList.forEach( pubCourseClassify -> {
            PubCourseClassifyDto dto = new PubCourseClassifyDto();
            BeanUtils.copyProperties(pubCourseClassify, dto);
            ArrayList<PubCourseClassifyDto> childs = getClassify(pubCourseClassify, courseClassifies);
            dto.setChild(childs);
            dtos.add(dto);
        });

        return dtos;
    }
    private ArrayList<PubCourseClassifyDto> getClassify(PubCourseClassify classify, List<PubCourseClassify> courseClassifies ) {

        List<PubCourseClassify> pubCourseClassifies = courseClassifies.stream().filter(o -> o.getPid().equals(classify.getClassifyId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pubCourseClassifies)) {
            return null;
        }
        ArrayList<PubCourseClassifyDto> dtos = new ArrayList<>();
        pubCourseClassifies.forEach( pubCourseClassify -> {
            PubCourseClassifyDto dto = new PubCourseClassifyDto();
            BeanUtils.copyProperties(pubCourseClassify, dto);
            dto.setChild(getClassify(pubCourseClassify, courseClassifies));
            dtos.add(dto);
        });
        return dtos;
    }
}




