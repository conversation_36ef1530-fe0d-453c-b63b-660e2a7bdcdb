## ManageSubjectSelector 管理主体选择器
> 贡献者：v_whaigong(龚文海)；最近更新时间：2024-04-26；

用于选择管理主体

### 单选

组件提供单选和多选两种选择方式，默认是多选。

:::demo  `v-model` 的值为当前被选中的选项的 `value` 属性值。可通过 `map.multiple` 属性设置单选
```html
<template>
  <div class="block">
    <span class="demonstration">基础单选</span>
    <sdc-manage-subject-selector v-model="value1" placeholder="基础单选" :map="map" filterable />
  </div>
  <div class="block">
    <span class="demonstration">有默认值</span>
    <sdc-manage-subject-selector v-model="value2" placeholder="有默认值" :map="map"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: 10101,
        map: {
          multiple: false
        }
      }
    }
  }
</script>
```
:::

### 多选

适用性较广的基础多选，用Tag展示已选内容

:::demo 设置`multiple`属性即可启用多选，此时`v-model`的值为当前选中值所组成的数组。默认情况下选中值会以 Tag 的形式展现，你也可以设置`collapse-tags`属性将它们合并为一段文字。
```html
<template>
  <div class="block">
    <span class="demonstration">基础多选</span>
    <sdc-manage-subject-selector v-model="value1" multiple filterable @change="change"/>
  </div>
  <div class="block">
    <span class="demonstration">折叠展示Tag</span>
    <sdc-manage-subject-selector v-model="value2" multiple collapse-tags @change="change"/>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: [10101, 10224]
      }
    },
    methods: {
      change(e) {
        console.log(e, this.value1)
      }
    }
  }
</script>
```
:::

### 限制管理主体选择范围

排除选项不会显示在下拉菜单中

:::demo 使用`range.manageUnitTypeIdList`属性设置仅选择某类型下的管理主体;使用`range.manageUnitIdList`属性设置仅选择某些管理主体
```html
<template>
  <sdc-manage-subject-selector v-model="value" :range="range" placeholder="请选择"/>
</template>
<script>
  export default {
    data(){
      return{
        value: '',
        range: {
          manageUnitTypeIdList: [102, 103],
          manageUnitIdList: [10301, 10304, 8663]
        }
      }
    }
  }
</script>
```
:::

### 多语言

设置选项展示的语言
:::demo 属性 `lang` 定义了展示语言，默认中文，可选值`en`。 
```html
<template>
  <sdc-manage-subject-selector v-model="value" lang="en"/>
</template>
<script>
  export default {
    data(){
      return{
        value: ''
      }
    }
  }
</script>
```
:::

### 仅展示最后一级

可以仅在输入框中显示选中项最后一级的职级，而不是选中职级所在的完整路径。

:::demo 属性 `showAllLevels` 定义了是否显示完整的路径，将其赋值为`false`则仅显示最后一级。
```html
<template>
  <sdc-manage-subject-selector v-model="value" :showAllLevels="false" :tagsLength="7"/>
</template>
<script>
  export default {
    data(){
      return{
        value: [60, 61]
      }
    }
  }
</script>
```
:::

### 可搜索

:::demo 可以使用`filterable`属性来开启搜索功能, 默认开启搜索功能。
```html
  <div class="block">
    <span class="demonstration">不可搜索</span>
    <sdc-manage-subject-selector v-model="value1" :filterable="false"/>
  </div>
  <div class="block">
    <span class="demonstration">可搜索</span>
    <sdc-manage-subject-selector v-model="value2" />
  </div>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: []
      }
    }
  }
</script>
```
:::


### 自定义数据源

可以自定义下拉菜单中的选项

:::demo 使用 `data` 属性来设置选项数据源，或者使用`promise`来设置访问选项数据源的方法。 如果你的数据源中不包含 `value` 和 `label` 默认字段，可以通过 `valueMap` 和`labelMap` 属性来指定
```html
<template>
  <sdc-manage-subject-selector v-model="value1" :data="remoteData" :map="map"/>
  <sdc-manage-subject-selector v-model="value2" :promise="promise" :map="map" style="margin-left: 20px;"/>
</template>
<script>
  export default {
    data(){
      return{
        value1: 'L3',
        value2: 'L4',
        map: {
          value: 'group',
          label: 'mark'
          // children: 'children'
        },
        remoteData: [
          { 
            group: 'L1', 
            mark: '选项1',
            children: [
              {
                group: 'L3', 
                mark: '子集2', 
              }
            ]
          },
          { 
            group: 'L2', 
            mark: '选项2',
            children: [
              {
                group: 'L4', 
                mark: '子集3', 
              }
            ]
          }
        ],
        promise: null,
      }
    },
    created() {
      this.promise = new Promise((resolve, reject) => {
        resolve(this.remoteData)
      })
    }
  }
</script>
```
:::

### ManageSubjectSelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | boolean/string/number | — | — |
| size | 输入框尺寸 | string | medium/small/mini | — |
| disabled | 是否禁用 | boolean | — | false |
| placeholder | 占位符 | string | — | — |
| lang | 语言 | string | 中文: zh，英文: en | zh |
| range | 限制选项范围，具体见下表 | Object | — | — |
| collapseTags | 多选模式下是否折叠Tag | boolean | — | false |
| tagsLength | Tag最大展示文字数, 最小1 | number | — | 13 |
| filterable | 是否可搜索选项 | boolean | — | true |
| showAllLevels | 输入框中是否显示选中值的完整路径 | boolean | — | true |
| data | 自定义选项 | array | — | [ ] |
| promise | 覆盖组件内部获取选项数据源的默认方法，`resolve` 函数的参数需要是一个由选项组成的数组 | Promise | — | — |
| showTotal | 是否显示后置的已选数量 | boolean | — | false |
| separator | 选项分隔符 | string | — | 斜杠'/' |
| map | 映射配置，具体见下表 | object | — | — |
| customClass | 自定义类名 | string | — | — |

### ManageSubjectSelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项, 包含label、value、path数组、fullName |

### ManageSubjectSelector Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| clearSelected | 用于清空选中项 | — |
| getCheckedNodes | 获取选中的节点 | (leafOnly) 是否只是叶子节点，默认值为 false |


### map
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value | 指定选项的值为选项对象的某个属性值 | string | — | 'value' |
| label | 指定选项标签为选项对象的某个属性值 | string | — | 'label' |
| children | 指定选项的子选项为选项对象的某个属性值 | string | — | 'children' |
| emitPath | 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 | boolean | — | false |
| multiple | 是否多选 | boolean | — | true |

### range 
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| manageUnitTypeIdList | 管理主体类型Id集合 | array | — | — |
| manageUnitIdList | 管理主体Id集合 | array | — | — |