/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2024-06-06 15:34:31
 */
import CoreService from './core.service'
export default class staffSubtypeService {
  static getData() {
    const params = {
      queryCondition: {
        argMap: {
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-selector-staff-subtype/sdc-webui/data', { params })
  }
  
  static getList() {
    return staffSubtypeService.getData().then(res => {
      const result = res.content.reduce((prev, current) => {
        const item = prev.find(item => item.value === current.staffTypeId)
        const levelObj = {
          label: current.staffSubtypeNameCn,
          labelEn: current.staffSubtypeNameEn,
          value: current.staffSubtypeId
        }
        if (item) {
          item.children.push(levelObj)
        } else {
          prev.push({ 
            label: current.staffTypeNameCn,
            labelEn: current.staffTypeNameEn,
            value: current.staffTypeId,
            children: [levelObj] 
          })
        }
        return prev
      }, [])
      return result
    })
  }
}
