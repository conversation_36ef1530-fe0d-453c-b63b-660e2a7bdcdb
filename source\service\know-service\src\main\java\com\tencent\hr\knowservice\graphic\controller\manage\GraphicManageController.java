package com.tencent.hr.knowservice.graphic.controller.manage;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.common.ExcellentDTO;
import com.tencent.hr.knowservice.framework.advice.exception.AuthException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.mapper.GraphicMapper;
import com.tencent.hr.knowservice.graphic.dto.*;
import com.tencent.hr.knowservice.graphic.service.*;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图文管理端
 */
@Slf4j
@RestController
@RequestMapping("/api/graphic/manage/graphic")
public class GraphicManageController {
    @Autowired
    GraphicService graphicService;

    @Autowired
    AdminService adminService;

    @Resource
    GraphicMapper graphicMapper;

    @Autowired
    PraiseService praiseService;

    @Autowired
    FavoriteService favoriteService;

    /**
     * 根据条件分页获取图文数据
     *
     * @param graphicConditionDTO
     * @return
     */
    @PostMapping("/query_graphic")
    public TransDTO<IPage<GraphicPageDTO>> getGraphicPage(@RequestBody GraphicConditionDTO graphicConditionDTO) {
        String staffName= GatewayContext.current().getStaffName();
        //判断是否是超级管理员
        if (!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        TransDTO<IPage<GraphicPageDTO>> dto = new TransDTO<>();
        IPage<GraphicPageDTO> page = graphicService.getGraphicPage(graphicConditionDTO);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(page);
    }

    /**
     * 管理后台删除图文
     *
     * @param graphicId
     * @return
     */
    @GetMapping("/del_graphic/{graphic_id}")
    public TransDTO<Boolean> delGraphicByGraphicId(@PathVariable("graphic_id") Integer graphicId) {
        String staffName= GatewayContext.current().getStaffName();
        //判断是否是超级管理员
        if (!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        TransDTO<Boolean> dto = new TransDTO<>();
        if (graphicId == null) {
            return dto.withSuccess(false).withCode(HttpStatus.SC_BAD_REQUEST).withMessage("图文id不能为空");
        }
        Boolean result = graphicService.delGraphicById(graphicId);
        return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(result);
    }

    /**
     * 导出数据
     *
     * @param response
     * @param graphicConditionDTO
     * @throws IOException
     */
    @PostMapping("/export_graphic")
    public void exportTestingResult(HttpServletResponse response,
                                    @RequestBody GraphicConditionDTO graphicConditionDTO) throws IOException {
        String staffName= GatewayContext.current().getStaffName();
        //判断是否是超级管理员
        if (!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        String fileName = URLEncoder.encode("图文列表" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
        //构建数据
        List<GraphicPageDTO> excelList = graphicService.getExcelExportData(graphicConditionDTO);
        List<ExportGraphicDTO> excelRecords = new ArrayList<>();
        for (GraphicPageDTO exportGraphicDTO : excelList) {
            ExportGraphicDTO excelDto = new ExportGraphicDTO();
            excelDto.setGraphicName(exportGraphicDTO.getGraphicName());
            excelDto.setGraphicStatus(graphicService.convertGraphicStatus(exportGraphicDTO.getGraphicStatus()));
            excelDto.setContentType(graphicService.convertType(exportGraphicDTO.getContentType()));
            String authors = exportGraphicDTO.getAuthors();
            if (StringUtils.isNotBlank(authors)) {
                excelDto.setAuthors(graphicService.convertStaffNames(authors));
            }
            excelDto.setFirstSubmitTime(exportGraphicDTO.getFirstSubmitTime());
            excelDto.setGraphicNumber(exportGraphicDTO.getGraphicNumber());
            excelDto.setViewCount(exportGraphicDTO.getViewCount());
            excelRecords.add(excelDto);
        }
        //导出
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            EasyExcel.write(response.getOutputStream(), ExportGraphicDTO.class).sheet("图文列表").doWrite(excelRecords);

    }


    /**
     * 保存笔记点赞数据
     *
     * @param noteIds
     * @return
     */
    @GetMapping("/save_note_praise")
    public TransDTO saveNotePrise(@RequestParam("noteIds") List<String> noteIds) {
        Map<String, Integer> res = new HashMap<>();
        //超级管理员校验
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        TransDTO dto = new TransDTO<>();
        List<String> graphicIds = new ArrayList<>();
        for(String noteId : noteIds) {
            if (graphicMapper.selectCountByOldGraphicIdEnable(noteId) > 0) {
                graphicIds.add(noteId);
            }
        }
        if(!graphicIds.isEmpty() && graphicIds.size() > 0) {
            res = graphicService.saveNoteActPraise(noteIds);
            return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true).withData(res);
        }{
            log.info("该笔记没有迁移，不能迁移对应点赞数据");
            return dto;
        }
    }


    /**
     * 保存笔记收藏数据
     *
     * @param noteIds
     * @return
     */
    @GetMapping("/save_note_emp_favorite")
    public TransDTO saveNoteEmpFavorites(@RequestParam("noteIds") List<String> noteIds) {
        Map<String, Integer> res = new HashMap<>();
        //超级管理员校验
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        TransDTO dto = new TransDTO<>();
        List<String> graphicIds = new ArrayList<>();
        for(String noteId : noteIds) {
            if (graphicMapper.selectCountByOldGraphicIdEnable(noteId) > 0) {
                graphicIds.add(noteId);
            }
        }
        if(!graphicIds.isEmpty() && graphicIds.size() > 0) {
            res = graphicService.saveNoteActEmpFavorites(graphicIds);
            return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true).withData(res);
        }else{
            log.info("该笔记没有迁移，不能迁移对应收藏数据");
            return dto;
        }
    }

    /**
     * 超级管理员迁移有识笔记数据
     *
     * @param graphicIds
     * @return
     */
    @GetMapping("/save_note")
    public TransDTO saveNote(@RequestParam("noteIds") List<String> graphicIds) {
        //超级管理员校验
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        List<String> noteIds = new ArrayList<>();
        for(String noteId : graphicIds) {
            if (graphicMapper.selectCountByOldGraphicIdEnable(noteId) <= 0) {
                noteIds.add(noteId);
            }else {
                log.warn("重复数据={},不进行导入",noteId);
            }
        }
        TransDTO<List<GraphicAndRelationContentDTO>> dto = null;
        if (CollectionUtils.isNotEmpty(noteIds)){
            dto = graphicService.getNote(noteIds);
        }else {
            return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true).withMessage("传入的id被过滤。没有查询数据");
        }
        //新增
        if (null != dto){
            List<GraphicAndRelationContentDTO> data = dto.getData();
            if (CollectionUtils.isEmpty(data)){
                return new TransDTO().withCode(HttpStatus.SC_BAD_GATEWAY).withSuccess(false).withMessage("查询数据"+graphicIds.toString()+"为空null");
            }
            Map<Integer, String> result = graphicService.saveNote(data);
            return new TransDTO().withCode(HttpStatus.SC_OK).withSuccess(true).withData(result);
        }else {
            return new TransDTO().withCode(HttpStatus.SC_BAD_GATEWAY).withSuccess(false).withMessage("查询数据为空null");
        }
    }

    @GetMapping("/cover_img")
    public TransDTO coverImg(Integer id){
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        graphicService.coverImg(id);
        return new TransDTO();
    }

    /**
     * 更新图文的关联课程和延伸课程数据
     * @return
     */
    @GetMapping("/update_content_data")
    public TransDTO updateExtendAndRelationContentData(){
        //超级管理员校验
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        graphicService.updateContentData();
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK);
    }

    @GetMapping("/convertActType")
    public TransDTO convertActType(){
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有访问该页面的权限！");
        }
        graphicService.convertActType();
        return new TransDTO();
    }

    /**
     * 加精或取消加精操作
     * @param excellentDTO
     * @return
     */
    @PostMapping("/add_excellent")
    public TransDTO<Boolean> addExcellent(@RequestBody ExcellentDTO excellentDTO){
        TransDTO<Boolean> dto = new TransDTO<>();
        if (excellentDTO.getDataId() == null){
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("id不能为空");
        }
        if (excellentDTO.getExcellentStatus() == null){
            return dto.withCode(HttpStatus.SC_BAD_REQUEST).withSuccess(false).withMessage("加精状态不能为空");
        }
        Boolean result = graphicService.addExcellent(excellentDTO);
        return  dto.withCode(HttpStatus.SC_OK).withSuccess(true).withData(result);
    }

    /**
     * 更新从有识同步过来的数据的浏览量
     * @return
     */
    @GetMapping("update_note_view_count")
    public TransDTO<String>  updateNoteViewCount(@RequestParam(value = "query_count" ,required = false) Integer queryCount){
        //超级管理员校验
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有权限访问！");
        }
        graphicService.updateNoteViewCount(queryCount);
        return new TransDTO<String>().withCode(HttpStatus.SC_OK).withSuccess(true).withData("更新有识数据浏览量成功");
    }

    /**
     * 刷新lrs的学习数据
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/update_lrs_learn")
    public TransDTO<String> updateLrsLearn(Integer pageNo,Integer pageSize){
        //超级管理员校验
        String staffName = GatewayContext.current().getStaffName();
        if(!adminService.checkSuperAdmin(staffName)){
            throw new AuthException("您暂时没有权限访问！");
        }
        graphicService.updateLrsLearn(pageNo,pageSize);
        return new TransDTO<String>().withCode(HttpStatus.SC_OK).withSuccess(true).withData("更新数据成功");
    }
}
