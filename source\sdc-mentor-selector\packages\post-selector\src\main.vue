<template>
  <fragment>
    <sdc-selector :custom-class="`sdc-post-selector ${selectClass}`" pasteable mode="post" ref="selector">
      <template slot-scope="{data}" slot="selector-item">
        <div class="selector-item">
          <span class="item-name" v-html="`${highlight(data.option[selectorMap.postFullName],data.keyword)}${showPostID ? (' ('+data.option[selectorMap.postID]+')') : '' }`"></span>
        </div>
      </template>
    </sdc-selector>
    <sdc-selector-modal ref="modal" :data="selected"/>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { selector, highlight, locale } from 'mixins'
  import PostService from 'api/post.service'
  import SdcSelector from 'packages/selector'
  import SdcSelectorModal from './modal'

  export default {
    name: 'sdc-post-selector',
    mixins: [selector, highlight, locale],
    props: {
      icon: {
        type: String,
        default: 'post'
      },
      showLastLevels: {
        type: Boolean,
        default: true
      },
      showPostID: {
        type: Boolean,
        default: false
      },
      filterEnableFlag: {
        type: Boolean,
        default: true
      },
      getDataList: {
        type: Function,
        default: PostService.getDataList
      },
      getTreeData: {
        type: Function,
        default: PostService.getTreeData
      },
      getChildrenData: {
        type: Function,
        default: PostService.getChildrenData
      }
    },
    data() {
      const selectorMap = {
        postID: 'PostID',
        postName: 'PostName',
        postFullName: 'PostFullName',
        unitID: 'UnitID',
        unitName: 'UnitName',
        type: {
          post: 'post',
          unit: 'unit'
        }
      }
      return {
        valueKey: selectorMap.postFullName,
        lastValueKey: selectorMap.postName,
        nodeKey: selectorMap.postID,
        selectedText: this.$st('sdc.postSelector.selected'),
        totalText: this.$st('sdc.postSelector.total'),
        modalProps: {
          title: this.$st('sdc.postSelector.title')
        },
        treeProps: {
          isLeaf: 'isLeaf'
        },
        selectorProps: { ...{ postID: 'PostID', postName: 'PostName', postFullName: 'PostFullName', unitID: 'UnitID' }, ...this.props },
        selectorMap,
        queryParams: {
          filterEnableFlag: this.filterEnableFlag
        }
      }
    },
    methods: {
      getCurrentItem(item) {
        return {
          key: item[this.nodeKey] || 0,
          text: item[this.valueKey] || '',
          lastText: item[this.lastValueKey] || '',
          tags: { minLength: 12, before: 5, after: 6 },
          modal: { minLength: 21, before: 9, after: 11 }
        }
      }
    },
    components: {
      Fragment,
      SdcSelector,
      SdcSelectorModal
    }
  }
</script>
