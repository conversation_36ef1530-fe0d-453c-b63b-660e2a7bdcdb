package com.tencent.hr.knowservice.courseInteraction.dto;

import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.SelectContent;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/10/11/16:06
 * @version: 1.0
 */
@Data
public class InteractiveInDto {
    /**
     * 互动规则唯一标识id
     */
    private String _id;

    /**
     * 互动配置id
     */
    private String interactiveId;
    /**
     * 课程类型
     */
    @NotNull(message = "课程类型不能为空")
    private Integer actType;
    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空")
    private String courseId;
    /**
     * 互动标题
     */
    @NotBlank(message = "互动标题不能为空")
    private String title;
    /**
     * 互动标题英文版
     */
    private String titleEn;
    /**
     * 互动时间
     */
    @NotNull(message = "互动时间不能为空")
    private Integer activeTime;
    /**
     * 互动介绍
     */
    private String introduction;
    /**
     * 互动介绍英文版
     */
    private String introductionEn;
    /**
     * 互动提示
     */
    private String continueStudyingTips;
    /**
     * 互动提示英文版
     */
    private String continueStudyingTipsEn;
    /**
     * 互动内容
     */
    private List<SelectContent> selectContent;
}
