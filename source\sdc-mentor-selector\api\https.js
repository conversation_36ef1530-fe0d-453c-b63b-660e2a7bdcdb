import { DataHttp } from 'sdc-core'
const headers = {
  // 'swagger': 'sdc-data-http',
  // 'caagw-username': 'happyhu',
  // 'caagw-staffid': '24673',
  // staffid: 24673,
  // staffname: 'happyhu'
}
export default DataHttp.getInstance({
  withCredentials: true,
  // 开启应用上下文
  ctx: true,
  // 当前请求实例(可使用DataHttp构建，也可配置详细参数，参考axios配置)
  axios: DataHttp.create({
    headers: headers
  }),
  // 数据响应映射(当返回数据不满足{status,result,message}格式时，对返回数据字段映射)
  map: { result: 'data', success: res => res }
})
