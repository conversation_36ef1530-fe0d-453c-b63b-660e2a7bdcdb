package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Service
public class ContentCenterService {
    @Value("${extapi.contentCenter.host}")
    private String contentCenterHost;

    @Value("${extapi.contentCenter.host-front}")
    private String contentCenterFrontHost;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${com.appSetting.tenantCode}")
    private String tenantCode;

    /**
     * @param contentId
     * @param operate
     * @return
     */
    public String getOperateSignature(String contentId,String operate) {
        String result;
        String path = "/api/v1/content/operatesignature?operate=" + operate + "&content_id=" + contentId;

        Integer staff= Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken,tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        if (!errorMsg.toString().isEmpty()) {
            TransDTO dto = new TransDTO().withSuccess(false).withData(errorMsg).withCode(500);
            result = JsonUtil.toJson(dto);
        }
        return result;
    }

    /**
     * 构建图片地址
     * @param fileId
     * @return
     */
    public String imageBuilder(String fileId){
        final String apiAddress = "/api/v1/content/imgage/";
        return contentCenterFrontHost + apiAddress + fileId + "/preview";
    }

    /**
     * 获取内容中心中的文件的原始路径
     * @param contentId
     * @return
     */
    public String getContentUrl(String contentId){
        String result;
        String captionUrl = "";
        String path = "/api/v1/content/"+contentId+"/url";

        Integer staff= Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken,tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        JSONObject jsonObject = new JSONObject(result);
        Boolean sucessed = jsonObject.has("success") ? jsonObject.getBoolean("success") : false;
        if (sucessed == true && jsonObject.has("data")) {
            captionUrl = jsonObject.getString("data");
        }

        return captionUrl;
    }


    /** 合成虚拟人视频
     * @param appId 应用id
     * @param requestId 虚拟人请求id
     * @param pptContentId ppt的文档id
     * @param faceContentId 人脸视频或图片的id
     * @param vcn 音色
     * @param vhPosition 虚拟人的位置
     * @param vhSize 虚拟人的大小
     * @param ttsSpeed 阅读语速
     * @param isPreview 是否预览
     * @return
     */
    public String VHPPTGenByContentId(String appId,
                                        String requestId,
                                        String pptContentId,
                                        String faceContentId,
                                        String vcn,
                                        String vhPosition,
                                        String vhSize,
                                        Integer ttsSpeed,
                                        String ttsSource,
                                        Boolean isPreview


    ) {
        String result;
        String path = "/api/v1/content/vh/vh-generate-by-contentid?app_id=" + appId + "&request_id=" + requestId + "&ppt_content_id=" + pptContentId + "&vcn=" + vcn + "&tts_speed=" + ttsSpeed
                 + "&is_preview=" + isPreview;

        if(!StringUtils.isEmpty(faceContentId)){
            path += "&face_content_id=" + faceContentId;
        }
        if(!StringUtils.isEmpty(vhPosition)){
            path += "&vh_position=" + vhPosition;
        }
        if(!StringUtils.isEmpty(vhSize)){
            path += "&vh_size=" + vhSize;
        }
        if(!StringUtils.isEmpty(ttsSource)){
            path += "&tts_source=" + ttsSource;
        }


        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        String jsonData = "";
        result = HttpUtil.sendPostByRestTemplate(contentCenterHost, path, jsonData, header, errorMsg);
        if (!errorMsg.toString().isEmpty()) {
            TransDTO dto = new TransDTO().withSuccess(false).withData(errorMsg).withCode(500);
            result = JsonUtil.toJson(dto);
        }
        return result;
    }


    /**
     * 获取内容的预览信息，包含不同格式内容的预览信息
     *
     * @param contentId
     * @return
     */
    public String getPreviewInfo(String contentId,String userAgent) {
        String result;
        String path = "/api/v1/content/"+contentId;
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        header.add("User-Agent", userAgent);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        if (!errorMsg.toString().isEmpty()) {
            TransDTO dto = new TransDTO().withSuccess(false).withData(errorMsg).withCode(500);
            result = JsonUtil.toJson(dto);
        }
        return result;
    }


    /**
     * 获取内容中心中的文件的原始路径相关信息
     *
     * @param contentId
     * @return
     */
    public String getContentUrlInfo(String contentId) {
        String result;
        String captionUrl = "";
        String path = "/api/v1/content/" + contentId + "/url";

        Integer staff = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffId = staff.toString();
        String staffName = GatewayContext.current().getStaffName();
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, staffId, staffName);
        StringBuffer errorMsg = new StringBuffer();
        result = HttpUtil.sendGetByRestTemplate(contentCenterHost, path, header, errorMsg);
        if (!errorMsg.toString().isEmpty()) {
            TransDTO dto = new TransDTO().withSuccess(false).withData(errorMsg).withCode(500);
            result = JsonUtil.toJson(dto);
        }
        return result;
    }
}
