package com.tencent.hr.knowservice.courseInteraction.constans;

/**
 * reids的缓存key
 *
 * <AUTHOR>
 */
public enum CacheKeyEnum {

    /**
     * 最基础的 - 课程的互动信息。示例:{_id}
     * interaction:base:interactiveInfo:_id
     */
    BaseInteractiveInfo("interaction:base:interactiveInfo:"),

    /**
     * 用户端-课程的互动信息。后面跟上课程类型参数，课程id参数。示例:{actType}:{courseId}
     * interaction:user:interactiveInfo:{actType}:{courseId}
     */
    UserInteractiveInfo("interaction:user:interactiveInfo:"),

    /**
     * * 用户端-课程的互动信息。当前课程是否开启了互动配置
     * * interaction:user:net:interactiveInUser:{courseId}
     */
    V8NetCourseInteractiveInfo("interaction:user:net:interactiveInUse:")
    ;

    /**
     * 缓存key值
     */
    private final String key;

    CacheKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
