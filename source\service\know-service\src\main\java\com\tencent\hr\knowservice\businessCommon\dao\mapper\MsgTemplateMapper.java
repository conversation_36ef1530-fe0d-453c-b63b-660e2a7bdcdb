package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.MsgTemplate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MsgTemplateMapper  extends BaseMapper<MsgTemplate> {
    int deleteByPrimaryKey(Integer templateId);

    int insert(MsgTemplate record);

    int insertSelective(MsgTemplate record);

    MsgTemplate selectByPrimaryKey(Integer templateId);

    int updateByPrimaryKeySelective(MsgTemplate record);

    int updateByPrimaryKey(MsgTemplate record);
}