<template>
  <div class="label-show">
    <div v-if="!showAll">
      <div
        class="label-show-line pub-flex pub-flex-wrap"
        :class="{ 'label-hidden': !extend }"
        id="label-content"
      >
        <template v-if="labelList.length">
          <div
            class="tag-item pub-flex"
            v-for="item in labelList"
            :key="item.label_id"
            @click="toSearch(item)"
            :dt-eid="dtTagList('eid', item)" 
            :dt-remark="dtTagList('remark', item)" 
            :dt-areaid="dtTagList('areaid', item)"
          >
            <img
              class="icon"
              v-if="item.label_type === '1'"
              src="../assets/img/link.png"
            />
            <span class="pub-ellipsis">{{ item.label_name }}</span>
          </div>
        </template>
        <template v-else>
          <div class="empty">暂无标签</div>
        </template>
      </div>
      <div class="open-close" :class="{'mgt-8': !extend}" v-if="isWrap && labelList.length" @click="changeExtendState">
        {{ extend ? '收起更多标签' : '展开更多标签' }}
        <img
          class="extend-icon"
          :class="{ 'extend-icon-active': extend }"
          src="../assets/img/arrow-right-black.png"
        />
      </div>
    </div>
    <div class="label-show-all pub-flex pub-flex-wrap" v-else>
      <template v-if="labelList.length">
        <div
          class="tag-item pub-flex"
          v-for="item in labelList"
          :key="item.label_id"
          @click="toSearch(item)"
          :dt-eid="dtTagList('eid', item)" 
          :dt-remark="dtTagList('remark', item)" 
          :dt-areaid="dtTagList('areaid', item)"
        >
          <img
            class="icon"
            v-if="item.label_type === '1'"
            src="../assets/img/link.png"
          />
          <span class="pub-ellipsis">{{ item.label_name }}</span>
        </div>
      </template>
      <template v-else>
        <div class="empty">暂无标签</div>
      </template>
    </div>
  </div>
</template>
<script src="https://unpkg.com/popper.js@2"></script>
<script>
import 'amfe-flexible/index.js'
import http from '../service/request'
// import axios from 'axios'
import mockData from '../utils/mock'
export default {
  name: 'sdc-label-show-mob',
  props: {
    isMock: { // 本地数据调试
      type: Boolean,
      default: false
    },
    labelNodeEnv: {
      // 环境变量
      type: String,
      default: 'production'
    },
    actType: {
      type: [String, Number],
      default: '2',
      require: true
    },
    courseId: {
      type: [String, Number],
      require: true
    },
    showCount: {
      // 接口获取标签的个数
      type: Number,
      default: 10
    },
    showAll: {
      // 是否显示所有标签
      type: Boolean,
      default: false
    },
    isH5: { // 是否是h5环境 非小程序
      type: Boolean,
      default: false
    },
    isPreview: { // 是否是预览 预览的情况下，只展示外部传进来的标签，不用发送请求拿
      type: Boolean,
      default: false
    },
    previewLbael: { // 预览时传进来显示的标签 不用另外发请求去拿
      type: Array,
      default: () => []
    },
    userInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      labelList: [], // 标签列表
      urlInfo: {
        // 接口基地址
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      isWrap: false, // 是否换行
      extend: false, // 展开/收起
      wrapIndex: 0 // 换行的下标
    }
  },
  computed: {
    commonUrl() {
      // api基地址
      return this.urlInfo[this.labelNodeEnv] || this.urlInfo.test
    },
    dtTagList() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: document.title,
            page_type: '网课详情页-新版', 
            container: `标签`,
            click_type: 'data',
            content_type: '',
            content_id: '',
            content_name: val.label_name,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.courseId}_${val.label_id}`
        } else {
          return `area_${this.courseId}_${val.label_id}`
        }
      }
    }
  },
  watch: {
    userInfo: {
      immediate: true,
      handler(val) {
        if (val && val.staff_id) {
          this.autoInstance(val)
        }
      }
    }
  },
  mounted() {
    if (this.isPreview) {
      this.labelList = this.previewLbael || []
      if (this.showAll) return
      setTimeout(() => {
        this.updateLabelPositon()
      }, 500) // 获取最新渲染后的dom，加延时
    } else {
      this.getLabelList()
    }
    window.addEventListener('resize', this.updateLabelPositon)
  },
  beforeDestroy() {},
  methods: {
    // 获取标签列表
    getLabelList() {
      if (!this.actType || !this.courseId) return
      let api = `/training/api/label/user/labelinfo/course-label-list`
      this.isH5 && (api = `${this.commonUrl}${api}`)
      // if (
      //   !(
      //     window.__wxjs_environment &&
      //     window.__wxjs_environment === 'miniprogram'
      //   )
      // ) {
      //   api = `${this.commonUrl}${api}`
      // }
      // axios
      http
      .get(api, {
          withCredentials: true,
          params: {
            act_type: this.actType,
            course_id: this.courseId,
            show_count: this.showCount
          }
        })
        .then(res => {
          // let res = Response.data
          let { code, data, message } = res
          if (code === 200) {
            this.labelList = data || []
            if (this.showAll) return
            setTimeout(() => {
              this.updateLabelPositon()
            }, 500) // 获取最新渲染后的dom，加延时
          } else {
            message && this.$message.error(message)
          }
        })
        .catch(err => {
          console.log('err: ', err)
          // 数据调试
          if (this.isMock) {
            this.labelList = mockData
            this.updateLabelPositon()
          }
        })
    },
    // 更新显示和隐藏的标签个数
    updateLabelPositon() {
      this.$nextTick(() => {
        let boxDom = document.querySelector('#label-content')
        let offsetTop = boxDom.offsetTop
        let index = 0
        let list = boxDom.children
        for (const key of list) {
          // if (offsetTop !== key.offsetTop) {
          if (key.offsetTop > offsetTop + 5) {
            this.isWrap = true
            this.wrapIndex = index
            break
          } else {
            this.isWrap = false
            index++
          }
        }
      })
    },
    changeExtendState() {
      this.extend = !this.extend
    },
    // 去搜索页面
    toSearch(item) {
      if(this.isPreview) return
      if (item.category_full_name === '其他/自定义') {
        let url = `/pages/special/search/index?keyword=${item.label_name}`
        this.$emit('toSearchPage', { url, item })
      } else {
        let url = `/pages/subscribe/index?isLabelGatherPage=true&label_id=${item.label_id}&label_name=${item.label_name}`
        this.$emit('toSearchPage', { url, item })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.label-show {
  .label-hidden {
    height: 24px;
    overflow: hidden;
  }

  .label-show-all,
  .label-show-line {
    .tag-item {
      max-width: 144px;
      padding: 4px 8px;
      margin: 0 8px 8px 0;
      color: #0052d9;
      font-size: 10px;
      border-radius: 22px;
      background: var(---Brand1-Light, #ecf2fe);
      .icon {
        flex-shrink: 0;
        width: 16px;
        margin-right: 4px;
      }
      span {
        line-height: 16px;
        flex: 1;
      }
    }
  }

  .open-close {
    display: flex;
    align-items: center;
    justify-content: center;
    // margin-top: 8px;
    height: 20px;
    color: #00000099;
    font-size: 12px;
    .extend-icon {
      transform: rotate(90deg);
      width: 16px;
      // height: 16px;
      transition: all 0.2s linear;
    }
    .extend-icon-active {
      transform: rotate(-90deg);
      transition: all 0.2s linear;
    }
  }

  .mgt-8 {
    margin-top: 8px;
  }

  .empty {
    font-size: 10px;
    margin-bottom: 8px;
  }
}

.pub-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pub-flex {
  display: flex;
  align-items: center;
}

.pub-flex-wrap {
  flex-wrap: wrap;
}
</style>
