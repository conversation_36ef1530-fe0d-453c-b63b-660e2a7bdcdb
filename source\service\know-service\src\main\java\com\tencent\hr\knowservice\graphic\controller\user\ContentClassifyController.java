package com.tencent.hr.knowservice.graphic.controller.user;

import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.proxy.GroupMatrixSerivceApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 网络课分类
 */
@RestController
@RequestMapping("/api/graphic/user/content_classify/")
public class ContentClassifyController {

    @Autowired
    GroupMatrixSerivceApi groupMatrixService;


    /**
     * 获取网络课分类
     * @return
     */
    @GetMapping("get_net_course_classify")
    public String getNetCourseClassify() {
        String result = groupMatrixService.getNetCourseClassify();
        Map jsonObject = JsonUtil.getMapByJson(result);
        Map res = (Map) jsonObject.get("data");
        List resData = (List) res.get("data");
        String resultData = "{\"success\": true," + "\"data\":" + JsonUtil.toJson(resData) + ",\"message\": null, \"code\": 200}";
        return resultData;
    }
}
