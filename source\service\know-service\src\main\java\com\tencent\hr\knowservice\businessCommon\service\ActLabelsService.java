package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActLabels;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.ActLabelsMapper;
import com.tencent.hr.knowservice.framework.dto.ActLabelRecommendDto;
import com.tencent.hr.knowservice.framework.dto.ActLabelRecommendV2Dto;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【act_labels(课程标签表)】的数据库操作Service
 * @createDate 2022-11-18 10:03:46
 */
@Service
@Slf4j
public class ActLabelsService {

    @Value("${extapi.hr-ai-recruit-center.host}")
    String recruitCenterHost;

    @Value("${extapi.hr-ai-recruit-center.api-host}")
    private String host;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Resource
    private ActLabelsMapper actLabelsMapper;

    @Autowired
    private BaseProxyService baseProxyService;

    public Page getLabels(String actType, int pageNo, int pageSize) {

        QueryWrapper<ActLabels> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(actType)) {
            wrapper.eq("act_type", actType);
        }
        Page<ActLabels> actLabelPage = actLabelsMapper.selectPage(new Page<>(pageNo, pageSize), wrapper);
        return actLabelPage;
    }

    /**
     * 取推荐标签，响应数据为对应标签的标签名称
     * @param recommendDto
     * @return
     */
    public Object getRecommendLabels(ActLabelRecommendDto recommendDto) {
        String url = recruitCenterHost+"/api/general_ai/keyword_extract_V2";
        String json = JsonUtil.toJson(recommendDto);
        String result = HttpUtil.sendPostByHttpClient(url, json, null);
        Map map = JsonUtil.getMapByJson(result);
        Long code = (Long) map.get("code");
        if ("Success".equals(map.get("msg")) && HttpStatus.SC_OK == code) {
            return map.get("data");
        } else {
            log.error("调用推荐标签接口出错，result={}", map);
            throw new RuntimeException("获取推荐标签失败");
        }
    }

    /**
     * 获取推荐标签，响应数据有对应标签的id值、标签名称和权重值，id值为-999为机器标签，其他的为官方标签
     * @param recommendDto
     * @return
     */
    public Object getRecommendLabels2(ActLabelRecommendV2Dto recommendDto) {
        String content = recommendDto.getContent();
        //删除html标签
        if (StringUtils.isNotBlank(content)){
            String delHtmlContent = com.tencent.hr.knowservice.utils.StringUtils.delHtmlTags(content);
            recommendDto.setContent(delHtmlContent);
        }
        String url = host+"/api/esb/hr-ai-recruit-center/graph_learn/recommend_kg_labels";
        HttpHeaders header = baseProxyService.getESBHeader(appId, appToken, GatewayContext.current().getStaffId(), GatewayContext.current().getStaffName());
        String json = JsonUtil.toJson(recommendDto);
        String result = HttpUtil.sendPostByHttpClient(url, json, header.toSingleValueMap());
        Map map = JsonUtil.getMapByJson(result);
        Long code = (Long)map.get("code");
        if ("成功".equals(map.get("msg")) && HttpStatus.SC_OK == code){
            List<Map<String,Object>> data = (List) map.get("data");
            //筛选官方标
            data = data.stream().filter(item->item.get("label_type") != null && item.get("label_type").toString().equals("1")).collect(Collectors.toList());
            if(recommendDto.getShowCount() != null){
                if(recommendDto.getShowCount() < data.size()){
                    data = data.subList(0,recommendDto.getShowCount());
                }
            }
            return data;
        }else {
            log.error("调用实时推荐课程标签接口出错，result={}",map);
            throw new RuntimeException("获取实时推荐课程标签失败");
        }
    }

    public Object autoComLabels(String name, String count, String actType) {

        if (StringUtils.equals(actType, "15")) {
            return actLabelsMapper.findLabelForCourseList(name, count, actType);
        } else {
            return actLabelsMapper.findLabelByName(name, count);
        }
    }

    public void saveLabel(String labelName, Integer actType) {

        String[] strings = labelName.split("[, ' ' ，]");
        List<String> labels = Arrays.asList(strings);
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        String staffName = current.getStaffName();
        Date date = new Date();
        labels.forEach(name -> {
            ActLabels actLabels = new ActLabels();
            actLabels.setName(name);
            actLabels.setActType(actType);
            actLabels.setCreatorId(staffId);
            actLabels.setCreatorName(staffName);
            actLabels.setCreatedAt(date);
            actLabelsMapper.insert(actLabels);
        });
    }


    public Integer addLabel(String labelName, Integer actType) {
        QueryWrapper<ActLabels> wrapper = new QueryWrapper<>();
        wrapper.eq("name", labelName);
        wrapper.eq("act_type", actType);
        wrapper.isNull("deleted_at");
        ActLabels actLabels = actLabelsMapper.selectOne(wrapper);
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        String staffName = current.getStaffName();
        if (actLabels == null) {
            actLabels = new ActLabels();
            actLabels.setName(labelName);
            actLabels.setActType(actType);
            actLabels.setCreatorId(staffId);
            actLabels.setCreatorName(staffName);
            actLabels.setCreatedAt(new Date());
            actLabelsMapper.insert(actLabels);
        }
        return actLabels.getLableId();
    }



    public Object getLabelByClassifyId(Integer classifyId) {
        if (classifyId == null) {
            throw new RuntimeException("分类id不能为空");
        }
        return actLabelsMapper.findLabelByClassifyId(classifyId);
    }
}
