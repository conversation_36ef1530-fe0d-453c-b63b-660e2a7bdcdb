package com.tencent.hr.knowservice.businessCommon.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileAdmins;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileAttachements;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PubFileInfoVo {
    /**
     * 素材id
     */
    private Integer fileId;
    /**
     * 素材名称，用于显示给用户看
     */
    private String fileShowName;

    /**
     * 上传的原始文件名
     */
    private String fileName;

    /**
     * 存放到服务器的新文件名
     */
    private String newFileName;

    /**
     * 文件后缀名
     */
    private String fileExtension;

    /**
     * 文件物理存储路径
     */
    private String filePath;

    /**
     * 文件查看url，暂时不用
     */
    private String fileUrl;

    /**
     * 素材对应的模块
     */
    private String appModule;

    /**
     * 素材来源
     */
    private String contentType;

    /**
     * 文章名称
     */
    private String ContentName;
    /**
     * 文章内容
     */
    private String Content;

    @JsonIgnore
    private String extContent;
    /**
     * 素材描述
     */
    private String fileDesc;

    /**
     * 素材类型(来源字典表 标准课件 、非标准课件、案例库)
     */
    private String fileType;

    private List<PubFileAdmins> admins;

    //文件是否加密存储
    @JsonIgnore
    private boolean encrypted;
    //文件是否公开
    @JsonIgnore
    private Boolean isPublic;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件的储存id不能为空
     */
    private String storageId;


    /**
     * 字数
     */
    private Integer wordNum;

    /**
     * 是否需要解压缩(0 否 1 是)
     */
    private Integer needDecompress;

    /**
     * 启动文件名称
     */
    private String startFileName;

    /**
     * 是否需要转码(0 无需转码 1需要转码)
     */
    private Integer needTranscode;

    /**
     * 批量读取标记，读取后更新此字段
     */
    private String batchNo;

    /**
     * 转码是否成功（0 失败 1成功）
     */
    private Integer isTransSucessed;

    /**
     * 转码重试次数
     */
    private Integer transCount;

    /**
     * 转码后的物理路径
     */
    private String transcodeFilePath;

    /**
     *
     */
    private String transcodeHlsFilePath;

    /**
     * 转码时间
     */
    private Date transcodeTime;

    /**
     * 文件分发后的url
     */
    private String distributeFileUrl;

    /**
     * 分发是否成功（0 失败 1成功）
     */
    private Integer isDistributeSucessed;

    /**
     * 分发次数
     */
    private Integer distributeCount;

    /**
     * 分发时间
     */
    private Date distributeTime;

    /**
     * 移动端转码重试次数
     */
    private Integer mobileTransCount;

    /**
     * 移动端分发次数
     */
    private Integer mobileDistributeCount;

    /**
     * 老系统中的视频地址
     */
    private String oldViewPath;

    /**
     * 外网音频地址(用于背景播放)
     */
    private String internetVideoPath;

    /**
     * 老系统中网络Id
     */
    private Integer activityFk;

    /**
     * 是否支持移动端（0 不支持 1 支持）
     */
    private Integer supportMobile;

    /**
     * 不同分辨率的版本列表用逗号分隔（0：不生成其他版本 1：1080p  2：720P  3：480P  4：360P 5：270p ）
     */
    private String diffRateVersion;

    /**
     * 是否分离过音频
     */
    private Integer isSplitAudio;

    /**
     * 状态（ 1：待转码  2：正在转码 3：转码失败 4：待分发   5：正在分发 6：分发失败 7：待移动端转码  8：正在移动端转码 9：转码移动端失败 10：待移动端分发   11：正在移动端分发 12：移动端分发失败 13：成功 14 正在音频转码 ）
     */
    private Integer status;

    /**
     * mp4文件存放服务器列表，用逗号分隔
     */
    private String storageServer;

    /**
     * hls文件存放服务器列表，用逗号分隔
     */
    private String hlsStorageServer;

    /**
     * 课件的语言（用于语音识别）
     */
    private String language;

    /**
     * 文件id
     */
    private String contentId;

    /**
     * 字幕内容id
     */
    private PubFileAttachements pubFileAttachements;

    /**
     * 视频时长
     */
    private int duration;

    /**
     * 创建人Id
     */
    @JsonIgnore
    private Integer creatorId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date updatedAt;

    private Integer referCount;

    private Boolean readOnly;
}
