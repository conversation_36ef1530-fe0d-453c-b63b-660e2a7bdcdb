package com.tencent.hr.knowservice.businessCommon.service;


import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.dto.PubLabelBasicDto;
import com.tencent.hr.knowservice.businessCommon.dto.PubLabelInfoDto;
import com.tencent.hr.knowservice.labelSystem.dto.label.CourseAssLabelListDTO;
import com.tencent.hr.knowservice.labelSystem.dto.label.PubLabelCategoryCourseAssInfoDTO;
import com.tencent.hr.knowservice.labelSystem.dto.label.PubLabelsDTO;
import com.tencent.hr.knowservice.labelSystem.service.LabelService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PubLabelService {


    @Autowired
    private LabelService labelService;
    /**
     * 添加课程所关联的标签，自定义标签
     * @param courseLabelType 标签类型 1-管理员打的标签，2-算法自动打标签，3-UGC用户打的标签
     * @param courseId
     * @param actTypeEnum
     * @return
     */
    public List<Integer> addCourseAssociateLabel(List<PubLabelBasicDto> pubLabelDtos, Integer courseLabelType, String courseId, ActTypeEnum actTypeEnum) {

        if (CollectionUtils.isEmpty(pubLabelDtos)) {
            return new ArrayList<>();
        }
        CourseAssLabelListDTO assLabelListDTO = new CourseAssLabelListDTO();
        ArrayList<PubLabelsDTO> labelsDTOS = new ArrayList<>();
        pubLabelDtos.forEach(pubLabelDto -> {
            PubLabelsDTO pubLabelsDTO = new PubLabelsDTO();
            BeanUtils.copyProperties(pubLabelDto, pubLabelsDTO);
            if (pubLabelDto.getLabelId() == null || pubLabelDto.getLabelId() == 0) {
                pubLabelsDTO.setLabelType(2);
                pubLabelsDTO.setLabelId(null);
            }
            if (pubLabelDto.getLabelId() != null && pubLabelDto.getLabelId() == -999) {
                pubLabelsDTO.setLabelType(3);
            }
            labelsDTOS.add(pubLabelsDTO);
        });
        assLabelListDTO.setLabels(labelsDTOS);
        assLabelListDTO.setCourseId(courseId);
        assLabelListDTO.setCourseLabelType(courseLabelType);
        assLabelListDTO.setActType(actTypeEnum.getActType());

        List<Integer> integers = labelService.addCourseAssociateLabel(assLabelListDTO);
        return integers;
    }

    /**
     * 通过courseId和actType获取关联的分类与标签数据
     * @param courseId
     * @param actTypeEnum
     * @return
     */
    public List<PubLabelInfoDto> getLabelInfoByCourseId(String courseId, ActTypeEnum actTypeEnum) {
        List<PubLabelCategoryCourseAssInfoDTO> assInfoDTOS = labelService.getLabelInfoByCourseId(courseId, actTypeEnum.getActType());
        ArrayList<PubLabelInfoDto> pubLabelExtDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(assInfoDTOS)) {
            assInfoDTOS.forEach(o -> {
                PubLabelInfoDto pubLabelExtDto = new PubLabelInfoDto();
                BeanUtils.copyProperties(o, pubLabelExtDto);
                pubLabelExtDtos.add(pubLabelExtDto);
            });
        }
        return pubLabelExtDtos;
    }

    /***
     * 获取标签信息
     * @param courseId
     * @param actTypeEnum
     * @return
     */
    public List<PubLabelBasicDto> getLabelBasicInfoList(String courseId, ActTypeEnum actTypeEnum) {
        List<PubLabelInfoDto> infoDtos = getLabelInfoByCourseId(courseId, actTypeEnum);
        ArrayList<PubLabelBasicDto> pubLabelDtos = new ArrayList<>();
        infoDtos.forEach(pubLabelExtDto -> {
            pubLabelDtos.add(pubLabelExtDto);
        });
        return pubLabelDtos;
    }
}
