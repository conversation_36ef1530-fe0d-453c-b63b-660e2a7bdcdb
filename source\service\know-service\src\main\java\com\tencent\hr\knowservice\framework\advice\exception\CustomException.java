package com.tencent.hr.knowservice.framework.advice.exception;


import com.tencent.hr.knowservice.businessCommon.constans.Constants;

/**
 * @description: 自定义异常，错误消息不能对外提示
 * @author: vincentyqwu
 * @createDate: 2020/6/11
 * @version: 1.0
 */
public class CustomException extends RuntimeException {
    /**
     * 错误码
     */
    protected Integer code;
    /**
     * 错误信息
     */
    protected String message;

    public CustomException() {
        super();
    }

    public CustomException(Constants.ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getCode().toString());
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getMessage();
    }

    public CustomException(Constants.ErrorCodeEnum errorCodeEnum, Throwable cause) {
        super(errorCodeEnum.getCode().toString(), cause);
        this.code = errorCodeEnum.getCode();
        this.message = errorCodeEnum.getMessage();
    }

    public CustomException(String errorMsg) {
        super(errorMsg);
        this.message = errorMsg;
    }

    public CustomException(Integer errorCode, String errorMsg) {
        super(errorCode.toString());
        this.code = errorCode;
        this.message = errorMsg;
    }

    public CustomException(Integer errorCode, String errorMsg, Throwable cause) {
        super(errorCode.toString(), cause);
        this.code = errorCode;
        this.message = errorMsg;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer errorCode) {
        this.code = errorCode;
    }

    public void setMsg(String errorMsg) {
        this.message = errorMsg;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }
}
