<!--
 * @Description: 
 * @Autor: kenoxia
 * @LastEditTime: 2021-07-06 11:19:37
-->
<template>
  <fragment>
    <div class="textarea-bar" v-if="textarea">
      <el-button size="mini" :icon="icon" @click="showSelectorModal" :disabled="getDisabled()" @mouseenter.native="buttonHover=true" @mouseleave.native="buttonHover=false">
        <sdc-svg-icon :name="icon" width="16px" height="16px" :custom-class="buttonHover ? 'hover' : ''"/>
        {{$st('sdc.selector.select')}}
      </el-button>
      <span class="num">{{totalItemsText}}</span>
    </div>
    <fragment v-else>
      <div class="suffix-open suffix-open-tutor-customer" :class="[size ? 'suffix-open--' + size : '']" v-if="!textarea">
        <el-button :size="size" @click="showSelectorModal" :disabled="getDisabled()" @mouseenter.native="buttonHover=true" @mouseleave.native="buttonHover=false">
          <sdc-svg-icon :name="icon" width="16px" height="16px" :custom-class="buttonHover ? 'hover' : ''"/>
        </el-button>
      </div>
      <span class="suffix-num" v-if="showSuffixNum">{{totalItemsText}}</span>
    </fragment>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { locale } from 'mixins'
  import SdcSvgIcon from 'packages/svg-icon'

  export default {
    name: 'selector-toolbar',
    inject: ['multiple', 'getDisabled', 'textarea', 'icon', 'mode', 'size', 'getSelected', 'totalText', 'showSelectorModal', 'showTotal'],
    mixins: [locale],
    data() {
      return {
        buttonHover: false
      }
    },
    computed: {
      totalItemsText() {
        return this.totalText.replace('$count', this.getSelected().length)
      },
      showSuffixNum() {
        return this.showTotal && this.multiple && !this.textarea
      }
    },
    components: {
      Fragment,
      SdcSvgIcon
    }
  }
</script>

<style lang="less" scoped>
.suffix-open-tutor-customer {
  position: relative;
}
</style>
