package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActActivityTeachers;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActActivityTeachersMapper extends BaseMapper<ActActivityTeachers> {
    int deleteByPrimaryKey(Integer id);

    int insert(ActActivityTeachers record);

    int insertSelective(ActActivityTeachers record);

    ActActivityTeachers selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ActActivityTeachers record);

    int updateByPrimaryKey(ActActivityTeachers record);

    List<ActActivityTeachers> findActivityTeachers(@Param("activityId") Integer activityId);
}