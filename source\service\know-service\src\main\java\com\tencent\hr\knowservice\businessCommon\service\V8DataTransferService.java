package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 和V8接口进行通讯的服务类
 * @author: shizhouwang
 * @createDate: 2023/10/27
 * @version: 1.0
 */

@Service
public class V8DataTransferService extends BaseProxyService{
    @Value("${extapi.Qlearning-V8.host}")
    private String host;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    private final String appInfoId = "6wUzvEAICYl6bDyBLqdP";

    private final String appInfoKey = "9mEKjVVhcGq8IFifzKCR";

    public String delRedisKey(String redisKey){
        String api = "/api/ext-interface/net-course/del-course-cache";
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        Map<String,Object> params = new HashMap<>();
        params.put("key",redisKey);
        params.put("timestamp",timestamp);
        params.put("appid", appInfoId);

        String signature = "";
        try {
            signature = QlearningApiService.getQLSignature(params, appInfoKey);
        } catch (Exception e) {
            throw new LogicException("QL签名错误" + e.getMessage());
        }

        params.put("signature", signature);

        HttpHeaders header = getESBHeader(appId, appToken, GatewayContext.current().getStaffId(), GatewayContext.current().getStaffName());
        String jsonBody = JsonUtils.objectToJson(params);
        return  postJsonStrWithHeaders(host,api,jsonBody,header);
    }
}
