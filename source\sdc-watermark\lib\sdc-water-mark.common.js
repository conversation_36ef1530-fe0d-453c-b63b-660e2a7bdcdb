module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "fb15");
/******/ })
/************************************************************************/
/******/ ({

/***/ "fb15":
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/@vue/cli-service/lib/commands/build/setPublicPath.js
// This file is imported into lib/wc client bundles.

if (typeof window !== 'undefined') {
  var currentScript = window.document.currentScript
  if (false) { var getCurrentScript; }

  var src = currentScript && currentScript.src.match(/(.+\/)[^/]+\.js(\?.*)?$/)
  if (src) {
    __webpack_require__.p = src[1] // eslint-disable-line
  }
}

// Indicate to webpack that this file can be concatenated
/* harmony default export */ var setPublicPath = (null);

// CONCATENATED MODULE: ./node_modules/cache-loader/dist/cjs.js?{"cacheDirectory":"node_modules/.cache/vue-loader","cacheIdentifier":"9eef41cc-vue-loader-template"}!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--5!./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./packages/componentPage/src/index.vue?vue&type=template&id=4211fb62
var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:"component-wrapper"})
}
var staticRenderFns = []


// CONCATENATED MODULE: ./packages/componentPage/src/index.vue?vue&type=template&id=4211fb62

// CONCATENATED MODULE: ./node_modules/cache-loader/dist/cjs.js??ref--1-0!./node_modules/vue-loader/lib??vue-loader-options!./packages/componentPage/src/index.vue?vue&type=script&lang=js

/* harmony default export */ var srcvue_type_script_lang_js = ({
  name: 'sdcWaterMark',
  props: {
    // 被覆盖水印的目标元素的id
    targetId: {
      required: false
    },
    // 被覆盖水印的目标元素的class
    targetClass: {
      required: false
    },
    // 水印文字
    text: {
      type: String
    },
    // 水印图案的用户配置项
    canvasUserOptions: Object,
    // 水印元素的用户配置项
    wmUserOptions: Object,
    // 是否手动初始化
    // 因为水印元素依赖于目标元素，如果目标元素没有渲染完成，那么水印元素不能正常工作，所以用户可根据实际情况进行手动初始化
    isManualInit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 水印元素的id
      watermarkId: '',
      // 水印图案，由canvas生成的url
      url: '',
      // 被覆盖水印的目标元素
      $target: undefined,
      // 水印元素
      $wm: undefined,
      // 水印图案的配置项
      canvasOptions: {
        width: 200,
        height: 160,
        fillStyle: 'rgba(12, 12, 12, 0.1)',
        font: '24px Microsoft Yahei',
        translate: {
          x: 20,
          y: 20
        },
        rotateDegree: 39
      },
      // 水印元素的配置项
      wmOptions: {
        'z-index': 99999
      },
      modifyCallback: null,
      // 是否关闭监控告警
      isCloseModifyAlert: false,
      destroyed: false
    }
  },
  methods: {
    /**
     * [init 进行一些初始化操作]
     * @return {[type]} [description]
     */
    init() {
      if (this.targetId) {
        // 生成水印元素的id，用于监控该元素是否被删除
        // 后缀是为了增强id随机性
        this.watermarkId = this.targetId + '_watermark_xx512'
        // 获取到目标元素
        this.$target = document.getElementById(this.targetId)
      } else if (this.targetClass) {
        this.watermarkId = this.targetClass + '_watermark_xx512'
        this.$target = document.getElementsByClassName(this.targetClass)[0]
      }
      // 生成水印图案的配置项
      this.createCanvasOption()
      // 生成水印元素的配置项
      this.createWmOption()
      // 生成水印图案的url
      this.url = this.createCanvasDataUrl()
    },

    /**
     * [addWatermark 添加水印]
     */
    addWatermark() {
      if (this.$target) {
        this.addWatermarkToTarget()
        this.observeWaterMark()
      }
    },

    /**
     * [createCanvasOption 根据用户传入的参数，生成水印图案的配置项]
     * @return {[type]} [description]
     */
    createCanvasOption() {
      for (const key in this.canvasUserOptions) {
        // eslint-disable-next-line no-prototype-builtins
        if (this.canvasOptions.hasOwnProperty(key)) {
          this.canvasOptions[key] = this.canvasUserOptions[key]
        }
      }
    },

    /**
     * [createWmOption 根据用户传入的参数，生成水印元素的配置项]
     * @return {[type]} [description]
     */
    createWmOption() {
      for (const key in this.wmUserOptions) {
        // eslint-disable-next-line no-prototype-builtins
        if (this.wmOptions.hasOwnProperty(key)) {
          this.wmOptions[key] = this.wmUserOptions[key]
        }
      }
    },

    /**
     * [createCanvasDataUrl 生成水印图案的url]
     * @return {[type]} [description]
     */
    createCanvasDataUrl() {
      // 创建canvas
      const canvas = document.createElement('canvas')
      canvas.width = this.canvasOptions.width
      canvas.height = this.canvasOptions.height
      const ctx = canvas.getContext('2d')
      ctx.fillStyle = this.canvasOptions.fillStyle
      ctx.font = this.canvasOptions.font
      ctx.translate(this.canvasOptions.translate.x, this.canvasOptions.translate.y)
      ctx.rotate(this.canvasOptions.rotateDegree * Math.PI / 180)
      ctx.fillText(this.text, 20, 20)
      return canvas.toDataURL('image/png')
    },

    /**
     * [addWatermarkToTarget 在目标元素上面添加水印层，这种方式没有直接修改目标元素的background，这样可以单独操纵水印元素]
     */
    addWatermarkToTarget() {
      // 创建水印覆盖目标元素
      const $wm = document.createElement('div')
      $wm.setAttribute('id', this.watermarkId)
      // $wm.style.width = '100%'
      $wm.style.width = getComputedStyle(this.$target).width
      // 注意：此处不能使用$wm.height('100%');那样只会渲染一屏
      $wm.style.height = '100%'
      // $wm.style.height = getComputedStyle(this.$target).height
      $wm.style.position = 'absolute'
      $wm.style.top = '0px'
      $wm.style.left = '0px'
      $wm.style['pointer-events'] = 'none'
      for (const key in this.wmOptions) {
        $wm.style[key] = this.wmOptions[key]
      }
      // this.url = this.createCanvasDataUrl();
      $wm.style.background = 'url(' + this.url + ') repeat top left'
      this.$wm = $wm
      this.$target.append($wm)
    },

    /**
     * [observeWaterMark 监控水印元素，从两方面防止被修改：1.属性被修改，2.元素被删除]
     * @param  {[type]} $wm      [水印元素]
     * @param  {[type]} selector [被添加水印的目标元素的选择器]
     * @param  {[type]} url      [水印的url]
     * @return {[type]}          [description]
     */
    observeWaterMark() {
      const obConfig = {
        attributes: true,
        characterData: true
      }
      // 增加监控，防止水印被修改
      const observer = new MutationObserver((mutations, observer) => {
        for (const m of mutations) {
          // 先取消监听，避免死循环
          observer.disconnect()
          // 此处用了一点小技巧：直接删除$wm元素，删除动作会引发下面的监控，进而重新生成元素
          this.$wm.parentNode.removeChild(this.$wm)
          // 如果用户删除或者修改了id，那么下面n.id==this.watermarkId将会为flase，那么不能重新渲染水印元素，所以此处要对这一情况单独处理
          if (m.attributeName === 'id') {
            this.addWatermarkToTarget()
            this.observeWaterMark()
          }
        }
      })
      observer.observe(this.$wm, obConfig)

      // 进一步加强监控，防止元素被删除
      // 因为
      const pObserver = new MutationObserver((mutations) => {
        for (const m of mutations) {
          if (m.type === 'childList' && m.removedNodes.length > 0) {
            for (const n of m.removedNodes) {
              if (n.id === this.watermarkId || n.id === 'watermark-warpper') {
                pObserver.disconnect()
                // 如果是代码内删除的水印则不重新生成水印
                if (!this.destroyed) {
                  this.addWatermarkToTarget()
                  this.observeWaterMark()
                }
                if (!this.isCloseModifyAlert) {
                  // 如果是删除了父元素
                  if (n.id === 'watermark-warpper') {
                    setTimeout(() => {
                      location.reload()
                    }, 1000)
                  }
                } else {
                  this.isCloseModifyAlert = false
                }
              }
            }
          }
        }
      })
      const pObConfig = {
        childList: true,
        subtree: true
      }
      const videoBox = document.getElementById('videoBox')
      if (videoBox) {
        pObserver.observe(videoBox, pObConfig)
      } else {
        pObserver.observe(this.$target, pObConfig)
      }
    },
    createWatermark() {
      this.destroyed = false
      this.init()
      this.addWatermark()
    },
    refreshWatermark() {
      if (this.$wm) {
        this.$wm.parentNode.removeChild(this.$wm)
      } else {
        this.init()
        this.addWatermark()
      }
    },
    destroyWatermark() {
      this.destroyed = true
      this.$wm.parentNode.removeChild(this.$wm)
    }
  },
  mounted() {
    if (!this.isManualInit) {
      this.init()
      this.addWatermark()
    }
  }
});

// CONCATENATED MODULE: ./packages/componentPage/src/index.vue?vue&type=script&lang=js
 /* harmony default export */ var componentPage_srcvue_type_script_lang_js = (srcvue_type_script_lang_js); 
// CONCATENATED MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
/* globals __VUE_SSR_CONTEXT__ */

// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).
// This module is a runtime utility for cleaner component module output and will
// be included in the final webpack user bundle.

function normalizeComponent(
  scriptExports,
  render,
  staticRenderFns,
  functionalTemplate,
  injectStyles,
  scopeId,
  moduleIdentifier /* server only */,
  shadowMode /* vue-cli only */
) {
  // Vue.extend constructor export interop
  var options =
    typeof scriptExports === 'function' ? scriptExports.options : scriptExports

  // render functions
  if (render) {
    options.render = render
    options.staticRenderFns = staticRenderFns
    options._compiled = true
  }

  // functional template
  if (functionalTemplate) {
    options.functional = true
  }

  // scopedId
  if (scopeId) {
    options._scopeId = 'data-v-' + scopeId
  }

  var hook
  if (moduleIdentifier) {
    // server build
    hook = function (context) {
      // 2.3 injection
      context =
        context || // cached call
        (this.$vnode && this.$vnode.ssrContext) || // stateful
        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional
      // 2.2 with runInNewContext: true
      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__
      }
      // inject component styles
      if (injectStyles) {
        injectStyles.call(this, context)
      }
      // register component module identifier for async chunk inferrence
      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier)
      }
    }
    // used by ssr in case component is cached and beforeCreate
    // never gets called
    options._ssrRegister = hook
  } else if (injectStyles) {
    hook = shadowMode
      ? function () {
          injectStyles.call(
            this,
            (options.functional ? this.parent : this).$root.$options.shadowRoot
          )
        }
      : injectStyles
  }

  if (hook) {
    if (options.functional) {
      // for template-only hot-reload because in that case the render fn doesn't
      // go through the normalizer
      options._injectStyles = hook
      // register for functional component in vue file
      var originalRender = options.render
      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context)
        return originalRender(h, context)
      }
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]
    }
  }

  return {
    exports: scriptExports,
    options: options
  }
}

// CONCATENATED MODULE: ./packages/componentPage/src/index.vue





/* normalize component */

var component = normalizeComponent(
  componentPage_srcvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var componentPage_src = (component.exports);
// CONCATENATED MODULE: ./packages/componentPage/index.js
// 导入组件  组件必须声明name

// 为组件提供install安装方法  供按需引入
componentPage_src.install = function(Vue) {
    Vue.component(componentPage_src.name, componentPage_src)
}
// 导出组件
/* harmony default export */ var componentPage = (componentPage_src);
// CONCATENATED MODULE: ./packages/index.js
// 整个包的入口
// 统一导出


 
const components = [
  componentPage
]
// 定义install方法 接收Vue作为参数，如果使用use注册插件，那么所有的组件都会被注册
const install = function (Vue) {
  if (install.installed) return;
  components.map(component => Vue.component(component.name, component))
}

// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

/* harmony default export */ var packages_0 = ({
  install,
  sdcWaterMark: componentPage
});


// CONCATENATED MODULE: ./node_modules/@vue/cli-service/lib/commands/build/entry-lib.js


/* harmony default export */ var entry_lib = __webpack_exports__["default"] = (packages_0);



/***/ })

/******/ });
//# sourceMappingURL=sdc-water-mark.common.js.map