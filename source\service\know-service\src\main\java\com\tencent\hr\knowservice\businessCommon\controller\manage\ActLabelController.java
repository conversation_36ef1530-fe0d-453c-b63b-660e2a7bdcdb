package com.tencent.hr.knowservice.businessCommon.controller.manage;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.service.ActLabelsService;
import com.tencent.hr.knowservice.framework.dto.ActLabelRecommendDto;
import com.tencent.hr.knowservice.framework.dto.ActLabelRecommendV2Dto;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.security.auth.message.AuthException;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;

@RestController
@RequestMapping("/api/businessCommon/manage/label")
public class ActLabelController {

    @Autowired
    private ActLabelsService actLabelsService;

    @GetMapping("/get_labels")
    TransDTO getLabels(@RequestParam(name = "act_type", required = false) String actType,
                       @RequestParam(name = "page_no", required = true) int pageNo,
                       @RequestParam(name = "page_size", required = false, defaultValue = "20") int pageSize) {

        Object obj = actLabelsService.getLabels(actType, pageNo, pageSize);
        TransDTO<Object> dto = new TransDTO<>();
        dto.withData(obj).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 保存用户标签
     * @param labelName
     * @return
     * @throws AuthException
     */
    @GetMapping("/save_label")
    public Object saveLabel(@RequestParam("label_name") String labelName,
                            @RequestParam(name = "act_type", required = false) Integer actType){

        TransDTO<Object> dto = new TransDTO<>();
        dto.withSuccess(true).withCode(HttpStatus.SC_OK);
        actLabelsService.saveLabel(labelName, actType);
        return dto;
    }

    /**
     * 匹配标签
     * @param labelName
     * @return
     * @throws AuthException
     */
    @GetMapping("/auto_com_labels")
    public Object autoComLabels(@RequestParam(value = "label_name") String labelName,
                                @RequestParam(value = "count") String count,
                                @RequestParam(name = "act_type", required = false) String actType){
        TransDTO<Object> dto = new TransDTO<>();
            Object data = actLabelsService.autoComLabels(labelName, count, actType);
            dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(data);
        return dto;
    }

    /**
     * 获取推荐标签
     * @param recommendDto
     * @return
     */
    @PostMapping("/get_recommend_labels")
    public TransDTO getRecommendLabels(@RequestBody ActLabelRecommendDto recommendDto){
        TransDTO<Object> dto = new TransDTO<>();

            if (StringUtils.isBlank(recommendDto.getTitle())) {
                return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(new ArrayList());
            }
            Object data = actLabelsService.getRecommendLabels(recommendDto);
            dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(data);

        return dto;
    }
    /**
     * 获取推荐标签
     * @param recommendDto
     * @return
     */
    @PostMapping("/get_recommend_labels_v2")
    public TransDTO getRecommendLabelsV2(@RequestBody ActLabelRecommendV2Dto recommendDto){
        TransDTO<Object> dto = new TransDTO<>();
            if (StringUtils.isBlank(recommendDto.getTitle())) {
                return dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(new ArrayList());
            }
            Object data = actLabelsService.getRecommendLabels2(recommendDto);
            dto.withSuccess(true).withCode(HttpStatus.SC_OK).withData(data);

        return dto;
    }
}
