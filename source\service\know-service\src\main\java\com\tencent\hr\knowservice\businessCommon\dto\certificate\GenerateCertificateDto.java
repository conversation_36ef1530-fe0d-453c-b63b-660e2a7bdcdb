package com.tencent.hr.knowservice.businessCommon.dto.certificate;

import lombok.Data;

@Data
public class GenerateCertificateDto {
    /**
     * 证书拥有者id(必填)
     */
    private String certificateId;

    /**
     * 证书拥有者id(必填)
     */
    private String userId;

    /**
     * 证书展示名称(必填)
     */
    private String userName;
    /**
     * 证书拥有者英文名(必填)
     */
    private String userEngName;

    /**
     * 证书拥有者组织架构信息(必填)
     */
    private String userOrg;

    /**
     * 业务属性数据：业务类型(非必填)
     */
    private String itemType;
    /**
     * /业务属性数据：业务id(非必填)
     */
    private String itemId;
    /**
     * 业务属性数据：业务来源id(非必填)
     */
    private String itemFromId;

    /**
     * 自定义证书编号(非必填)，
     */
    private String customBatchNo;

    /**
     * 证书生成完成之后的初始状态值：0-待领取，1-已经领取（必填）
     */
    private String createdStatus;

    /**
     * 是否重复发放证书，如果不允许重复发放，多次生成只会生成一个证书。(必填)
     */
    private boolean repeatSendCertificate;

}
