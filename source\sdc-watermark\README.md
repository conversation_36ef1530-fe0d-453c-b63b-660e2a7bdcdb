# test

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```
### 上传依赖库
```
npm run pub
```
### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### 关于插件 打包时需要修改package.json文中里面的name、version、main的值
```
测试环境
name: "@tencent/sdc-water-mark"
version: "0.0.0" (改成最新的版本+1)
main: "lib/sdc-waterMark.common.js"
```

```
生产环境
name: "@tencent/sdc-water-mark"
version: "0.0.0" (改成最新的版本+1)
main: "lib/sdc-water-mark.common.js"
```
### npm仓库地址：https://mirrors.tencent.com/#/private/npm
