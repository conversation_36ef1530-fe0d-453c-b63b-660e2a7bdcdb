package com.tencent.hr.knowservice.businessCommon.proxy;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.graphic.dto.ContentSearchDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name="training-portal-content",url="${project.portal-content-host}")
public interface ContentServiceApi {

    /**
     *  module_id|item_id
     * @param combinationIds
     * @return
     */
    @PostMapping("/api/v1/content/manage/contents/search-specific-content")
    public TransDTO searchContentByModuleIdAndItemId(@RequestBody List<String> combinationIds);
    /**
     * 从成长矩阵中搜索内容
     * @param searchDto
     * @return
     */
    @PostMapping("/api/v1/content/manage/contents/search")
    public String searchContentByGropMatrix(@RequestBody ContentSearchDto searchDto);
}
