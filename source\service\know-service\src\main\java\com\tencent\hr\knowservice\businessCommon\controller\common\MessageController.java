package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dao.entity.MsgTemplate;
import com.tencent.hr.knowservice.businessCommon.service.FileProdConnService;
import com.tencent.hr.knowservice.businessCommon.service.message.MsgTemplateService;
import com.tencent.hr.knowservice.mooc.dao.entity.MoocCourseMsgTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 邮件
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/businessCommon/common/message")
public class MessageController {
    @Autowired
    MsgTemplateService msgTemplateService;

    /**
     * 更改通用催办模板
     * @param
     * @param
     * @return
     */
    @PutMapping("/update-common-msg-template")
    public TransDTO<Integer> updateCommonMsgTemplate(@RequestBody MsgTemplate msgTemplate) {
        TransDTO<Integer> dto = new TransDTO<>();
            Integer obj = msgTemplateService.updateCommonMsgTemplate(msgTemplate);
            dto.withData(obj).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 获取通用催办模板
     * @param
     * @param
     * @return
     */
    @GetMapping("/get-common-msg-template")
    public TransDTO<MsgTemplate> getCommonMsgTemplate( @RequestParam(name = "act_type", required = true) Integer actType,
                                          @RequestParam(name = "module_name", required = true) String moduleName,
                                          @RequestParam(name = "template_type", required = true) String templateType) {
        TransDTO<MsgTemplate> dto = new TransDTO<>();

            MsgTemplate result = msgTemplateService.getCommonMsgTemplate(moduleName,templateType,actType);
            dto.withData(result).withSuccess(true).withCode(HttpStatus.SC_OK);

        return dto;
    }
}
