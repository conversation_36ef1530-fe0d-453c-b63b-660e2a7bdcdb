package com.tencent.hr.knowservice.businessCommon.controller.manage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubBanner;
import com.tencent.hr.knowservice.businessCommon.dto.manage.PubBannerDto;
import com.tencent.hr.knowservice.businessCommon.service.PubBannerService;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.mooc.service.MoocAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 通用的Banner管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/business-common/manage/banner")
public class PubBannerManageController {

    @Autowired
    PubBannerService bannerService;

    @Autowired
    MoocAuthService moocAuthService;




    /**
     * 轮播图列表
     *
     * @param
     * @return
     */
    @GetMapping("/get-banner-list")
    public TransDTO<IPage<PubBanner>> getBannersInfos(@RequestParam(value = "current", required = true) Integer current,
                                                      @RequestParam(value = "size", required = true) Integer size,
                                                      @RequestParam(value = "banner_name", required = false) String bannerName,
                                                      @RequestParam(value = "status", required = false) Integer status) {
        TransDTO dto = new TransDTO<>();

            String staffId = GatewayContext.get().getStaffId();
            boolean hasRight = moocAuthService.haveMoocCourseBannerAuth(staffId);
            if(hasRight){
                IPage<PubBanner> pubBanners = bannerService.getBannersInfos(current, size, bannerName, status);
                dto.withCode(HttpStatus.OK.value()).withSuccess(true).withData(pubBanners);
            }else {
                String e = "用户无设置轮播图权限";
                dto.withMessage(e).withCode(HttpStatus.OK.value()).withSuccess(false);
            }

        return dto;
    }

    /**
     * 新建轮播图
     *
     * @param pubBannerDto
     * @return
     */
    @PostMapping("/add")
    TransDTO<PubBanner> addBanner(@Valid @RequestBody PubBannerDto pubBannerDto) {
        TransDTO dto = new TransDTO<>();

            String staffId = GatewayContext.get().getStaffId();
            boolean hasRight = moocAuthService.haveMoocCourseBannerAuth(staffId);
            if(hasRight){
               dto.withCode(HttpStatus.OK.value()).withSuccess(true)
                        .withData(bannerService.addBanner(pubBannerDto));
            }else {
                String e = "用户无设置轮播图权限";
                dto.withMessage(e).withCode(HttpStatus.OK.value()).withSuccess(false);
            }

        return dto;
    }

    /**
     * 编辑轮播图
     *
     * @param pubBannerDto
     * @return
     */
    @PutMapping("/update")
    public TransDTO<PubBanner> updateBanner(@Valid @RequestBody PubBannerDto pubBannerDto) {
        TransDTO dto = new TransDTO<>();

            String staffId = GatewayContext.get().getStaffId();
            boolean hasRight = moocAuthService.haveMoocCourseBannerAuth(staffId);
            if(hasRight){
                dto.withCode(HttpStatus.OK.value()).withSuccess(true)
                        .withData(bannerService.updateBanner(pubBannerDto));
            }else {
                String e = "用户无设置轮播图权限";
                dto.withMessage(e).withCode(HttpStatus.OK.value()).withSuccess(false);
            }

        return dto;
    }

    /**
     * 删除轮播图
     *
     * @return
     */
    @DeleteMapping("/delete/{banner_id}")
    public TransDTO<Object> deleteBanner(@PathVariable(value = "banner_id") Integer pubBannerId) {
        TransDTO dto = new TransDTO<>();

            String staffId = GatewayContext.get().getStaffId();
            boolean hasRight = moocAuthService.haveMoocCourseBannerAuth(staffId);
            if(hasRight){
                bannerService.deleteBanner(pubBannerId);
                return dto.withCode(HttpStatus.OK.value()).withSuccess(true);
            }else {
                String e = "用户无设置轮播图权限";
                dto.withMessage(e).withCode(HttpStatus.OK.value()).withSuccess(false);
            }

        return dto;
    }

    /**
     * 上下架轮播图
     *
     * @return
     */
    @PostMapping("/bannerStatus/{banner_id}/{status_id}")
    public TransDTO<Object> onOffBanner(@PathVariable(value = "banner_id") Integer pubBannerId,
                                        @PathVariable(value = "status_id") Integer statusId) {
        TransDTO dto = new TransDTO<>();

            String staffId = GatewayContext.get().getStaffId();
            boolean hasRight = moocAuthService.haveMoocCourseBannerAuth(staffId);
            if(hasRight){
                bannerService.onOffBanner(pubBannerId, statusId);
                return new TransDTO<Object>().withCode(HttpStatus.OK.value()).withSuccess(true);
            }else {
                String e = "用户无设置轮播图权限";
                dto.withMessage(e).withCode(HttpStatus.OK.value()).withSuccess(false);
            }

        return dto;
    }


    /**
     * 排序轮播图
     *
     * @return
     */
    @GetMapping("/order/{banner_id}/{order_no}")
    public TransDTO<Boolean> modifyBannerOrder(@PathVariable("banner_id") Integer bannerId,
                                               @PathVariable("order_no") Integer orderNo) {
        TransDTO dto = new TransDTO<>();
            String staffId = GatewayContext.get().getStaffId();
            boolean hasRight = moocAuthService.haveMoocCourseBannerAuth(staffId);
            if(hasRight){
                bannerService.order(bannerId, orderNo);
                dto.withCode(HttpStatus.OK.value()).withSuccess(true);
            }else {
                String e = "用户无设置轮播图权限";
                dto.withMessage(e).withCode(HttpStatus.OK.value()).withSuccess(false);
            }

        return dto;
    }
}
