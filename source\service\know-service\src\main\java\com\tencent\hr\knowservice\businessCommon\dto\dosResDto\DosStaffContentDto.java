package com.tencent.hr.knowservice.businessCommon.dto.dosResDto;

import lombok.Data;

/**
 * @description:
 * @author: shi<PERSON><PERSON>
 * @createDate: 2023/9/28
 * @version: 1.0
 */
@Data
public class DosStaffContentDto {

    /**
     * 员工id
     */
    private String staffId;

    /**
     * 员工英文名字
     */
    private String staffAccountName;

    /**
     * 员工联合名字
     */
    private String staffCombinedName;
    /**
     * 族群
     */
    private String proPositionJobFunction;
    /**
     * 工作地
     */
    private String baseLocationRegionNameCn;
    /**
     * 员工类型，正式与否
     */
    private String staffTypeNameCn;
    /**
     * 管理主体
     */
    private String manageUnitNameCn;

}
