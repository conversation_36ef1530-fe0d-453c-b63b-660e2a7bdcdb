/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2024-04-23 17:34:31
 */
import { DataType } from 'sdc-core'
import CoreService from './core.service'
export default class manageSubjectService {
  static getData({ manageUnitTypeIdList = undefined, manageUnitIdList = undefined } = {}) {
    const params = {
      queryCondition: {
        argMap: {
          manageUnitTypeIdList: manageSubjectService.formatList(manageUnitTypeIdList), // 管理主体类型Id
          manageUnitIdList: manageSubjectService.formatList(manageUnitIdList) // 管理主体Id 
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-manage-unit/sdc-webui/data', { params })
  }

  static getList(range) {
    return manageSubjectService.getData(range).then(res => {
      const result = res.content.reduce((prev, current) => {
        const item = prev.find(item => item.id === current.manageUnitTypeId)
        const levelObj = {
          id: current.manageUnitId,
          label: current.manageUnitNameCn,
          labelEn: current.manageUnitNameEn,
          value: current.manageUnitId
        }
        if (item) {
          item.children.push(levelObj)
        } else {
          prev.push({ 
            id: current.manageUnitTypeId,
            label: current.manageUnitTypeNameCn,
            labelEn: current.manageUnitTypeNameEn,
            value: current.manageUnitTypeId,
            children: [levelObj] 
          })
        }
        return prev
      }, [])
      const sortList = [101, 102, 104, 103]
      return result.sort((a, b) => sortList.indexOf(a.id) - sortList.indexOf(b.id))
    })
  }

  static formatList(arr) {
    return (arr && !DataType.isEmptyArray(arr)) ? arr : undefined
  }
}
