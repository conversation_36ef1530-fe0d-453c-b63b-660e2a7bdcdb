@import "./vars.less";

.sdc-loading {
  @-webkit-keyframes spinner {
    50% {
      transform: scale(.4);
      opacity: .3;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  .mask {
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      margin-top: 10px;
      font-size: 13px;
      color: #fff;
    }
    .spinner {
      position: absolute;
      transform: translateY(-50px);
      i {
        position: absolute;
        display: block;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background-color: @color-primary;
        &:nth-child(1) {
          top: 25px;
          left: 0;
          -webkit-animation: spinner 1s ease 0s infinite;
        }
        &:nth-child(2) {
          top: 17px;
          left: 17px;
          -webkit-animation: spinner 1s ease -.12s infinite;
        }
        &:nth-child(3) {
          top: 0;
          left: 25px;
          -webkit-animation: spinner 1s ease -.24s infinite;
        }
        &:nth-child(4) {
          top: -17px;
          left: 17px;
          -webkit-animation: spinner 1s ease -.36s infinite;
        }
        &:nth-child(5) {
          top: -25px;
          left: 0;
          -webkit-animation: spinner 1s ease -.48s infinite;
        }
        &:nth-child(6) {
          top: -17px;
          left: -17px;
          -webkit-animation: spinner 1s ease -.6s infinite;
        }
        &:nth-child(7) {
          top: 0;
          left: -25px;
          -webkit-animation: spinner 1s ease -.72s infinite;
        }
        &:nth-child(8) {
          top: 17px;
          left: -17px;
          -webkit-animation: spinner 1s ease -.84s infinite;
        }
      }
    }
  }
  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.2s;
  }
  .fade-enter, .fade-leave-active {
    opacity: 0;
  }
}
