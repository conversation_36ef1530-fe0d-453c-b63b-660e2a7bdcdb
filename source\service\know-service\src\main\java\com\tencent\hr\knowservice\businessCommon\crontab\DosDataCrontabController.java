package com.tencent.hr.knowservice.businessCommon.crontab;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.service.BaseEmpInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 同步dos数据
 * @author: shi<PERSON>wang
 * @createDate: 2023/9/28
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/crontab/dosData/")
public class DosDataCrontabController {

    @Autowired
    BaseEmpInfoService baseEmpInfoService;


    /**
     * 从dos同步人员信息数据
     * 同步的信息字段是：DosStaffContentDto实体中的字段
     * @return
     */
    @PostMapping("fullDataSync")
    public TransDTO syncFileInfo() {
        baseEmpInfoService.dosStaffFullDataSync(null,null);
        return new TransDTO<>().withSuccess(true).withMessage("操作成功!").withCode(HttpStatus.SC_OK);
    }
}
