/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2024-06-06 15:34:31
 */
import CoreService from './core.service'
export default class cityService {
  static getData() {
    const params = {
      queryCondition: {
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-selector-city-native/sdc-webui/data', { params })
  }

  static getList() {
    return cityService.getData().then(res => {
      const result = res.content.reduce((prev, current) => {
        const item = prev.find(item => item.value === current.provinceId)
        const levelObj = {
          label: current.cityNameCn,
          labelEn: current.cityNameEn,
          value: current.cityId
        }
        if (item) {
          item.children.push(levelObj)
        } else {
          prev.push({ 
            label: current.provinceNameCn,
            labelEn: current.provinceNameEn,
            value: current.provinceId,
            children: [levelObj] 
          })
        }
        return prev
      }, [])
      return result
    })
  }
}
