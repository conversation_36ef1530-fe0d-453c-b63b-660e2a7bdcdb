package com.tencent.hr.knowservice.businessCommon.service.message;

import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.dto.message.BotMessage;
import com.tencent.hr.knowservice.businessCommon.dto.message.MailMessage;
import com.tencent.hr.knowservice.businessCommon.dto.message.TipsMessage;
import org.json.JSONObject;

import java.util.HashMap;

public class ActMessageUtil {

    public static BotMessage getBotMessage(String title, String receiver, String content, String actId, ActTypeEnum actType) {
        return ActMessageUtil.getBotMessage(title, receiver, "ACADEMY",  content, "markdown", actId, actType);
    }

    public static TipsMessage getTipsMessage(String title, String receiver, String content, String actId, ActTypeEnum actType) {
        TipsMessage tipsMessage = new TipsMessage();
        tipsMessage.setTitle(title);
        tipsMessage.setReceiver(receiver);
        tipsMessage.setSender("ACADEMY");
        tipsMessage.setCanSend(true);
        tipsMessage.setActId(actId);
        tipsMessage.setActType(actType.getActType());
        tipsMessage.setContent(content);
        return tipsMessage;
    }

    public static BotMessage getBotMessage(String title, String receiver, String sender, String content, String msgType, String actId, ActTypeEnum actType) {
        BotMessage botMessage = new BotMessage();
        botMessage.setMsgType(msgType);
        botMessage.setTitle(title);
        botMessage.setReceiver(receiver);
        botMessage.setSender(sender);
        botMessage.setCanSend(true);
        botMessage.setActId(actId);
        botMessage.setActType(actType.getActType());
        HashMap<String, String> stringHashMap = new HashMap<>();
        stringHashMap.put("content", content);
        JSONObject jsonObject = new JSONObject(stringHashMap);
        String jsonString = jsonObject.toString();
        botMessage.setContent(jsonString);
        return botMessage;
    }

    public static MailMessage getMailMessage(String title, String receiver, String content, String actId, ActTypeEnum actType) {
        return ActMessageUtil.getMailMessage(title, receiver, "ACADEMY", content, actId, actType);
    }

    public static MailMessage getMailMessage(String title, String receiver, String sender, String content, String actId, ActTypeEnum actType) {
        MailMessage mailMessage = new MailMessage();
        mailMessage.setTitle(title);
        mailMessage.setReceiver(receiver);
        mailMessage.setSender(sender);
        mailMessage.setCanSend(true);
        mailMessage.setActId(actId);
        mailMessage.setActType(actType.getActType());
        mailMessage.setContent(content);
        return mailMessage;
    }
}
