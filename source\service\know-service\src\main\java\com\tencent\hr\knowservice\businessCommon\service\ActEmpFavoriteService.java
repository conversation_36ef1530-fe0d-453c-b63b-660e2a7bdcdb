package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActEmpFavorites;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.ActEmpFavoritesMapper;
import com.tencent.hr.knowservice.businessCommon.dto.common.ActEmpFavoritesDto;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ActEmpFavoriteService {

    @Resource
    private ActEmpFavoritesMapper empFavoritesMapper;

    public ActEmpFavoritesDto checkFavorited(String actType, String actId) {
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        QueryWrapper<ActEmpFavorites> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.isNull("deleted_at");
        ActEmpFavorites empFavorites = empFavoritesMapper.selectOne(queryWrapper);
        if (empFavorites != null) {
            ActEmpFavoritesDto empFavoritesDto = new ActEmpFavoritesDto();
            BeanUtils.copyProperties(empFavorites, empFavoritesDto);
            return empFavoritesDto;
        }
        return null;
    }

    public boolean addFavorite(String actType, String actId, String actName, Integer interactType) {

        try {
            ActEmpFavoritesDto empFavoritesDto = checkFavorited(actType, actId);
            if (empFavoritesDto != null) {
                return true;
            }
            ContextEntity current = GatewayContext.current();
            Integer staffId = Integer.valueOf(current.getStaffId());
            String staffName = current.getStaffName();
            ActEmpFavorites empFavorites = new ActEmpFavorites();
            empFavorites.setActId(actId);
            empFavorites.setStaffId(staffId);
            empFavorites.setEmpName(staffName);
            empFavorites.setActType(Byte.valueOf(actType));
            empFavorites.setActName(actName);
            empFavorites.setInteractType(interactType);
            empFavorites.setCreatedAt(new Date());
            empFavorites.setCreatorId(staffId);
            empFavorites.setCreatorName(staffName);
            empFavoritesMapper.insert(empFavorites);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public boolean deleteFavorite(String actType, String actId) {
        boolean state = false;
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        QueryWrapper<ActEmpFavorites> queryWrapper = new QueryWrapper();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.isNull("deleted_at");
        ActEmpFavorites empFavorites = empFavoritesMapper.selectOne(queryWrapper);
        if (empFavorites != null) {
            Date date = new Date();
            empFavorites.setDeletedAt(date);
            empFavorites.setUpdatedAt(date);
            empFavorites.setUpdateName(current.getStaffName());
            empFavorites.setUpdateId(staffId);
            empFavoritesMapper.updateById(empFavorites);
            state = true;
        }
        return state;
    }

    public Integer getFavoritedCount(String actType, String actId) {

        ContextEntity current = GatewayContext.current();
//        Integer staffId = Integer.valueOf(current.getStaffId());
        QueryWrapper<ActEmpFavorites> queryWrapper = new QueryWrapper();
//        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.isNull("deleted_at");
        return empFavoritesMapper.selectCount(queryWrapper);
    }

    /**
     * 获取我的收藏列表
     *
     * @param actType      课程类型 ActTypeEnum
     * @param staffId      学员id
     * @param contentName  课程名称
     * @param interactType 单据类型(1 评论 2提问,3 课程)
     * @param orderBy      排序类型（字段名）
     * @param pageNo       页码
     * @param pageSize     数量
     * @return
     */
    public Page<ActEmpFavorites> getUserFavorite(String actType, Integer staffId, String contentName, Integer interactType, String orderBy, Integer pageNo, Integer pageSize) {
        QueryWrapper<ActEmpFavorites> queryWrapper = new QueryWrapper();
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.eq("interact_type", interactType);

        if (StringUtils.isNotBlank(actType)) {
            queryWrapper.like("act_type", actType);
        }

        if (StringUtils.isNotBlank(contentName)) {
            queryWrapper.like("act_name", contentName);
        }

        String orderByStr = "created_at";
        if (StringUtils.isNotBlank(orderBy)) {
            orderByStr = orderBy;
        }

        queryWrapper.isNull("deleted_at");
        queryWrapper.orderByAsc(orderByStr);

        return empFavoritesMapper.selectPage(new Page<>(pageNo, pageSize), queryWrapper);

    }
}
