package com.tencent.hr.knowservice.graphic.dao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * pub_task_info
 * <AUTHOR>
@Data
public class PubTaskInfo implements Serializable {
    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 任务名称
     */
    private String taskName;

    private String appId;

    private String appName;

    /**
     * 模块Id
     */
    private String moduleId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 运行方法
     */
    private String functionName;

    /**
     * 执行周期
     */
    private String execInterval;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 最后运行时间
     */
    private Date lastRunTime;

    /**
     * 删除时间
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}
