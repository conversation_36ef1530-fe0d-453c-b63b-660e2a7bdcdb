package com.tencent.hr.knowservice.framework.config.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class AuthInterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private AuthInterceptor authInterceptor;

    @Autowired
    private AccessInterceptor accessInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .excludePathPatterns("/api/crontab/**");
        registry.addInterceptor(accessInterceptor)
                .addPathPatterns("/api/mooc/**")
                .excludePathPatterns("/hello")
                .excludePathPatterns("/webjars/**", "/api/crontab/**");
    }
}
