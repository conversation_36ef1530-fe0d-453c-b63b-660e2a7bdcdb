package com.tencent.hr.knowservice.framework.annotation;

import com.tencent.hr.knowservice.framework.constant.UserRoleEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface AuthorityAudited {
    UserRoleEnum[] value();
}
