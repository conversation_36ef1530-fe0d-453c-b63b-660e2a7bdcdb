<template>
  <div id="app">
    <!-- <el-button>点击按钮</el-button> -->
    <div class="content">
      <sdc-search-label v-model="labels"  :labelNodeEnv="labelNodeEnv"></sdc-search-label>
    </div>
  </div>
</template>

<script>

export default {
  name: 'App',
  data() {
    return {
      labels: [],
      labelNodeEnv: 'test',
    }
  },
}
</script>

<style lang="less" scoped>
#app {
  display: flex;
  justify-content: center;
  padding-top: 100px;
}
.content {
  width: 250px;
}
/* #app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
} */
</style>
