<template>
  <SdcCascader ref="cascader" :getData="getData" :value.sync="internalValue" v-bind="$attrs" v-on="$listeners" :formatSelected="formatSelected" :separator="separator" class="sdc-position-level" :class="customClass"/>
</template>

<script>
  import SdcCascader from 'packages/cascader'
  import LevelService from 'api/level.service'
  export default {
    name: 'sdc-position-level',
    components: { SdcCascader },
    props: {
      promise: Promise,
      customClass: {
        type: String,
        default: ''
      },
      value: {
        require: true
      },
      range: {
        type: Object,
        default: () => {}
      },
      separator: {
        type: String,
        default: '/'
      }
    },
    watch: {
      range: {
        handler(newVal, oldVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            this.$nextTick(() => {
              this.$refs.cascader.onloadOptions()
              this.clearSelected()
            })
          }
        },
        deep: true
      }
    },
    computed: {
      internalValue: {
        get() {
          return this.value
        },
        set(newValue) {
          this.$emit('input', newValue)
        }
      }
    },
    methods: {
      async getData() {
        try {
          const result = this.promise ? await this.promise : await LevelService.getList(this.range)
          return result 
        } catch (error) {
          return [] 
        }
      },
      getCheckedNodes(leafOnly = false) {
        return this.$refs.cascader.getCheckedNodes(leafOnly)
      },
      clearSelected() {
        this.$refs.cascader.clearSelected()
      },
      formatSelected(node) {
        return {
          positionLevelFullName: node.pathLabels.join(this.separator),
          positionLevelName: node.label
        }
      }
    }
  }
</script>
