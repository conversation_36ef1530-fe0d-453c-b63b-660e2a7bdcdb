package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileRefer;
import com.tencent.hr.knowservice.businessCommon.vo.PubFileReferVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
public interface PubFileReferMapper extends BaseMapper<PubFileRefer> {

    /**
     * 批量保存
     * @param saveList
     */
    void insertBatch(@Param("saveList") List<PubFileRefer> saveList);

    /**
     * 查询课程引用
     * @param fileId
     * @return
     */
    List<PubFileReferVo> courseReferList(@Param("fileId") Integer fileId);

    /**
     * 删除素材
     * @param moocCourseId
     * @param actId
     */
    void updateOne(@Param("moocCourseId") String moocCourseId, @Param("actId") String actId);
}
