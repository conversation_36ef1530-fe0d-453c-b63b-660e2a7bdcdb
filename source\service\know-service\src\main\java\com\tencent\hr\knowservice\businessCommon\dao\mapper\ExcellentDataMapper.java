package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ExcellentData;
import org.springframework.stereotype.Repository;

@Repository
public interface ExcellentDataMapper extends BaseMapper<ExcellentData> {
    int deleteByPrimaryKey(Integer id);

    int insert(ExcellentData record);

    int insertSelective(ExcellentData record);

    ExcellentData selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ExcellentData record);

    int updateByPrimaryKey(ExcellentData record);

    /**
     * 修改加精状态
     * @param excellentData
     * @return
     */
    int updateExcellentStatus(ExcellentData excellentData);
}