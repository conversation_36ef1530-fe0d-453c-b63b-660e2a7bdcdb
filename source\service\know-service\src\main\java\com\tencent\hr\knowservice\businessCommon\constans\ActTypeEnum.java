package com.tencent.hr.knowservice.businessCommon.constans;

import com.tencent.hr.knowservice.framework.advice.exception.LogicException;

public enum ActTypeEnum {
    FACE_COURSE(1, "faceCourse", "面授课"),
    NET_COURSE(2, "netCourse", "网络课"),
    CLASSES(3, "classes", "班级"),
    ACTIVITY(4, "activity", "活动"),
    LIVE(5, "live", "直播"),
    ARTICLE(6, "article", "图文"),
    SERIESCOURSE(7, "seriesCourse", "系列课"),
    SERIESCLASS(8, "seriesClass", "系列班"),
    THESIS(9, "thesis", "论文"),
    FILEPROD(10, "fileProd", "文档"),
    MOOC(11, "mooc", "慕课"),
    CONTENT(12, "content", "内容中心"),
    SPECIAL(13, "special", "专区"),
    COURSE_LIST(15, "courselist", "课单"),
    LITTLE_CASE(16, "case", "案例"),
    NOTE(17, "note", "笔记"),
    GRAPHIC(18, "graphic", "新图文"),
    HANG_JIA(19, "hangjia", "行家"),
    EXAM(20, "exam", "考试"),
    Resource(21, "resource", "素材"),
    Homework(22, "homework", "作业"),
    ThirdParty(23, "third-party", "第三方任务"),
    FLASH(24, "flash", "压缩包"),
    OTHER(99, "other", "外部链接"),
    KNOWLEDGE(101, "knowledge", "knowledge"),
    GEEKBANG(102, "geekBang", "极客时间"),
    QLEARNING(999, "qlearning", "qlearning"),
    UNKNOWN(-1000, "UNKNOWN", "未知");

    private Integer actType;
    private String actTypeName;
    private String text;


    ActTypeEnum(Integer actType, String actTypeName, String text) {

        this.actType = actType;
        this.actTypeName = actTypeName;
        this.text = text;
    }

    public Integer getActType() {
        return actType;
    }

    public void setActType(Integer actType) {
        this.actType = actType;
    }

    public String getActTypeName() {
        return actTypeName;
    }

    public void setActTypeName(String actTypeName) {
        this.actTypeName = actTypeName;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static Integer getActType(String actTypeName) {
        if ("document".equals(actTypeName)) {
            return FILEPROD.getActType();
        }
        ActTypeEnum[] actTypeEnums = ActTypeEnum.values();
        for (ActTypeEnum actTypeEnum : actTypeEnums) {
            if (actTypeEnum.getActTypeName().equals(actTypeName)) {
                return actTypeEnum.getActType();
            }
        }
        return ActTypeEnum.UNKNOWN.getActType();
    }

    /**
     * 获取actType 枚举类型 - 名字
     * @param actType
     * @return
     */
    public static String getActTypeName(Integer actType) {
        ActTypeEnum[] actTypeEnums = ActTypeEnum.values();
        for (ActTypeEnum actTypeEnum : actTypeEnums) {
            if (actTypeEnum.getActType().equals(actType)) {
                return actTypeEnum.getActTypeName();
            }
        }
        return ActTypeEnum.UNKNOWN.getActTypeName();
    }


    /**
     * 获取actType 枚举类型 - 文本
     * @param actType
     * @return
     */
    public static String getActTypeText(Integer actType){
        ActTypeEnum[] actTypeEnums = ActTypeEnum.values();
        for (ActTypeEnum actTypeEnum : actTypeEnums) {
            if (actTypeEnum.getActType().equals(actType)){
                return actTypeEnum.getText();
            }
        }
        return ActTypeEnum.UNKNOWN.getText();
    }

    /**
     * 校验课程类型
     * @param actType
     */
    public static void  checkActType(Integer actType){
        ActTypeEnum[] actTypeEnums = ActTypeEnum.values();
        for (ActTypeEnum actTypeEnum : actTypeEnums) {
            if (actTypeEnum.getActType().equals(actType)){
                return ;
            }
        }
        throw new LogicException("课程类型错误");
    }

    public static ActTypeEnum getActTypeEnum(Integer actType){
        if (actType == null){
            return null;
        }
        for (ActTypeEnum value : ActTypeEnum.values()) {
            if (actType.equals(value.getActType())){
                return value;
            }
        }
        return null;
    }
}
