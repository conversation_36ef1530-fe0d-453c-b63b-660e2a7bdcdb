package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActEmpFavorites;
import com.tencent.hr.knowservice.businessCommon.dto.common.ActCheckFavoriteDto;
import com.tencent.hr.knowservice.businessCommon.dto.common.ActEmpFavoritesDto;
import com.tencent.hr.knowservice.businessCommon.service.ActEmpFavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.security.auth.message.AuthException;
import java.util.regex.Pattern;

/**
 * 用户收藏
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/businessCommon/common/favorite/")
public class ActEmpFavoriteController {


    @Autowired
    private ActEmpFavoriteService empFavoriteService;

    /**
     * 获取用户是否收藏
     *
     * @param actType
     * @param actId
     * @return
     * @throws AuthException
     */
    @GetMapping("check-favorited")
    public TransDTO checkFavorited(@RequestParam("act_type") String actType,
                                   @RequestParam("act_id") String actId,
                                   @RequestParam(value = "staff_id", required = false) Integer staffId
    ) {
        TransDTO<Object> dto = new TransDTO<>();
        ActEmpFavoritesDto empFavoritesDto = empFavoriteService.checkFavorited(actType, actId);
        dto.withData(empFavoritesDto != null).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 获取用户是否收藏
     *
     * @param query
     * @return
     * @throws AuthException
     */
    @PostMapping("check-favorited-batch")
    public TransDTO checkFavoritedBatch(@RequestBody ActCheckFavoriteDto query
    ) {
        //QL的访问路径

        return null;

    }


    /**
     * 添加收藏
     *
     * @param actType
     * @param actId
     * @return
     * @throws AuthException
     */
    @GetMapping("add-favorite")
    public TransDTO addFavorite(@RequestParam("act_type") String actType,
                                @RequestParam("act_id") String actId,
                                @RequestParam("act_name") String actName,
                                @RequestParam(value = "interact_type", required = false) Integer interactType,
                                @RequestParam(value = "staff_id", required = false) Integer staffId,
                                @RequestParam(value = "staff_name", required = false) String staffName,
                                @RequestParam(value = "author", required = false) String author
    ) {

        TransDTO<Object> dto = new TransDTO<>();
        boolean state = empFavoriteService.addFavorite(actType, actId, actName, interactType);
        dto.withData(state).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 获取正确的笔记actType
     *
     * @param actType
     * @param actId
     * @return
     */
    private String getCorrectActType(String actType, String actId) {
        if (ActTypeEnum.NOTE.getActType().toString().equals(actType)) {
            String regex = "[0-9]{1,10}";
            //新的笔记itemId是整数
            if (Pattern.matches(regex, actId)) {
                //act为整数表示是新的笔记，需要用新的笔记actType去查
                actType = ActTypeEnum.GRAPHIC.getActType().toString();
            }
        }
        return actType;
    }

    /**
     * 取消收藏
     *
     * @param actType
     * @param actId
     * @return
     * @throws AuthException
     */
    @GetMapping("delete-favorite")
    public TransDTO deleteFavorite(@RequestParam("act_type") String actType,
                                   @RequestParam("act_id") String actId,
                                   @RequestParam(value = "staff_id", required = false) Integer staffId,
                                   @RequestParam(value = "staff_name", required = false) String staffName,
                                   @RequestParam(value = "author", required = false) String author,
                                   @RequestParam(value = "act_name", required = false) String actName
    ) {
        TransDTO<Object> dto = new TransDTO<>();
        boolean state = empFavoriteService.deleteFavorite(actType, actId);
        dto.withData(state).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    /**
     * 获取收藏的课程
     *
     * @param staffId
     * @param actType
     * @param contentName
     * @param orderBy
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("get-favorite")
    public TransDTO<Page<ActEmpFavorites>> getFavorite(@RequestParam(value = "staff_id") Integer staffId,
                                                       @RequestParam(value = "act_type", required = false) String actType,
                                                       @RequestParam(value = "content_name", required = false) String contentName,
                                                       @RequestParam(value = "order_by", required = false) String orderBy,
                                                       @RequestParam(value = "page_no", required = false, defaultValue = "1") int pageNo,
                                                       @RequestParam(value = "page_size", required = false, defaultValue = "20") int pageSize) {
        TransDTO<Page<ActEmpFavorites>> dto = new TransDTO<>();
        Page<ActEmpFavorites> data = empFavoriteService.getUserFavorite(actType, staffId, contentName, 3, orderBy, pageNo, pageSize);
        dto.withData(data).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }
}
