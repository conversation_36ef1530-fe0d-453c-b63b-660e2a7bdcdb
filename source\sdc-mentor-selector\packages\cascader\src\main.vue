<template>
  <div class="sdc-cascader" :class="customClass">
    <div class="cascader-container" ref="container" :placeholder="placeholder" :class="containerClassList" @click.stop="containerClick">
      <div class="tags" ref="tags" :class="[size ? 'tags--' + size : '', filterSelected.length ? '' : 'tags--empty']">
        <el-tooltip v-for="(item, index) in filterSelected" :key="item[cascaderProps.value]" :content="item.fullName" placement="top" :disabled="showAllLevels ? item.fullName.length <= tagsEllipsis.minLength : false">
          <el-tag :size="size || 'medium'" :closable="!disabled" type="info" class="tag" @close="handleDeleteTag(index)">
            <span>{{textEllipsis(item[showAllLevels ? 'fullName' : 'label'], tagsEllipsis)}}</span>
          </el-tag>
        </el-tooltip>
        <el-tag :size="size || 'medium'" type="info" class="tag" v-if="selected && selected.length > 1 && collapseTags" key="collapseTags">
          <span>+{{(selected.length - 1)}}</span>
        </el-tag>
      </div>
      <i v-if="showIconArrowDown" class="el-icon-arrow-down cascader-container-right-icon" :class="{ 'is-show-close': showClose }"></i>
      <i v-if="showClose" class="el-icon-circle-close cascader-container-right-icon" @click.stop="handleClose"></i>
      <el-cascader
        :disabled = disabled
        ref="cascader"
        v-model="cascaderValue"
        :options="formatOptions"
        :props="cascaderProps"
        :placeholder="placeholder"
        :size="inputSize"
        filterable
        clearable
        @visible-change="handleVisibleChange"
        :popper-class="popperClass"
        :separator="separator"
        :style="cascaderStyle"
        @change="handleInnerChange">
      </el-cascader>
    </div>
    <span class="suffix-num" v-if="showTotal">{{totalItemsText}}</span>
  </div>
</template>

<script>
  import { oneOf, textEllipsis, isDef } from 'sdc-webui/src/utils/main'
  import { DataType } from 'sdc-core'
  import { locale } from 'mixins'

  export default {
    name: 'sdc-cascader',
    mixins: [locale],
    props: {
      map: Object,
      customClass: {
        type: String,
        default: ''
      },
      getData: Function,
      data: {
        type: Array,
        default: () => []
      },
      placeholder: {
        type: String,
        default: ''
      },
      size: {
        type: String,
        validator(val) {
          return oneOf(val, ['small'], 'size')
        }
      },
      value: {
        required: true
      },
      showTotal: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      lang: {
        type: String,
        default: 'zh'
      },
      separator: {
        type: String,
        default: "/"
      },
      collapseTags: {
        type: Boolean,
        default: false
      },
      showAllLevels: {
        type: Boolean,
        default: true
      },
      filterable: {
        type: Boolean,
        default: true
      },
      tagsLength: {
        type: Number,
        default: 13
      },
      level: {
        type: Number,
        default: 3
      },
      formatSelected: {
        type: Function,
        default: () => {}
      }
    },
    data() {
      return {
        options: [],
        selected: [],
        focus: false,
        cascaderValue: '',
        totalText: this.$st('sdc.positionLevel.total'),
        firstOpen: true,
        loading: false
      }
    },
    model: {
      prop: 'value',
      event: 'input'
    },
    watch: {
      value(newVal) {
        if (this.cascaderValue !== newVal) {
          this.handleOutChange(newVal)
        }
      }
    },
    computed: {
      showIconArrowDown() {
        return !this.filterable && !(!this.cascaderProps.multiple && !this.isEmpty(this.cascaderValue))
      },
      showClose() {
        return !this.disabled && ((!this.cascaderProps.multiple && !this.isEmpty(this.cascaderValue)) || (!this.filterable && !this.isEmpty(this.cascaderValue)))
      },
      totalItemsText() {
        return this.totalText.replace('$count', this.selected.length)
      },
      cascaderProps() {
        const config = this.map || {}
        return {
          multiple: config.multiple !== false,
          emitPath: config.emitPath === true,
          value: config.value || 'value',
          label: config.label || (this.lang === 'en' ? 'labelEn' : 'label'),
          children: config.children || 'children'
        }
      },
      inputSize() {
        return this.size === 'small' ? 'mini' : 'small'
      },
      cascaderStyle() {
        const obj = {}
        if ((!this.cascaderProps.multiple && !this.isEmpty(this.cascaderValue)) || !this.filterable) {
          obj['z-index'] = -1
        }
        return obj
      },
      filterSelected() {
        if (this.collapseTags) {
          return this.selected.slice(0, 1)
        } else {
          return this.selected
        }
      },
      cascader() {
        return this.$refs.cascader
      },
      containerClassList() {
        return [
          this.size ? 'cascader-container--' + this.size : '', 
          this.focus ? 'cascader-container--focus' : '', 
          this.filterable ? '' : 'cascader-container--not-filterable', 
          this.disabled ? 'is-disabled' : '',
          !this.filterSelected.length && !this.filterable ? 'cascader-placeholder' : ''
        ]
      },
      tagsEllipsis() {
        const tagsLength = this.tagsLength < 1 ? 1 : this.tagsLength
        return { minLength: tagsLength, before: Math.ceil(tagsLength / 2), after: Math.floor(tagsLength / 2) }
      },
      formatOptions() {
        return this.options.map(item => {
          if (this.level === 1) {
            item.children = null
          }
          if (this.level === 2) {
            item.children = item.children.map(child => {
              child.children = null
              return child
            })
          }
          return item
        })
      },
      popperClass() {
        const loadingClass = this.loading ? 'cascader-filterable-popper--loading' : ''
        return ['cascader-filterable-popper', loadingClass].join(' ')
      }
    },
    created() {
      // 当设置初始值时，需要先去拉取远程数据源
      if (!this.isEmpty(this.value)) {
        this.handleOutChange(this.value)
      }
    },
    mounted() {
      const domArr = document.querySelectorAll('.cascader-filterable-popper .el-cascader-menu__empty-text')
      domArr.forEach(dom => {
        const text = dom.textContent.trim()
        const htmlContent = `<span>${text}</span>` + "<i class='el-icon-loading'></i>"
        dom.innerHTML = htmlContent
      })
    },
    methods: {
      textEllipsis,
      // 外部主动设置导致改变
      handleOutChange(val) {
        const changeFn = () => {
          this.cascaderValue = val
          setTimeout(() => {
            this.updateSelected(val)
          }, 10)
        }
        if (this.options && this.options.length) {
          changeFn()
        } else {
          // 当没有下拉数据时，需要先拉取数据
          this.onloadOptions().then(changeFn)
        }
      },
      handleInnerChange(val) {
        // 内部选择导致改变
        setTimeout(() => {
          this.updateSelected(val)
          this.$emit('input', val)
          this.$emit('change', this.selected)
        }, 10)
      },
      updateSelected(val) {
        if (!this.isEmpty(val)) {
          const checkedNodes = this.$refs.cascader ? (this.$refs.cascader.getCheckedNodes(true) || []) : []
          this.selected = checkedNodes.filter(node => isDef(node)).map(node => ({
            ...node.data,
            label: node.label,
            fullName: node.pathLabels.join(this.separator),
            path: [...node.path].map(this.delPrefix),
            ...this.formatSelected(node)
          }))
        } else {
          this.selected = []
        }
        this.resetInputSize()
      },
      delPrefix(item) {
        // id重复的情况下，会手动加prefix前缀标识，所以要判断去除
        if (typeof item === 'string' && item.startsWith('prefix')) {
          const parts = item.split('-')
          return parseInt(parts[1])
        }
        return item
      },
      resetInputSize() {
        this.$nextTick(() => {
          const tags = this.$refs.tags
          tags.scrollLeft = tags.scrollWidth
        })
      },
      handleDeleteTag(index) {
        const { cascaderValue, selected } = this
        this.cascaderValue = this.cascaderProps.multiple ? cascaderValue.filter((n, i) => i !== index) : ''
        this.$emit('input', this.cascaderValue)
        this.selected = selected.filter((n, i) => i !== index)
        this.$emit('change', this.selected)
      },
      getCheckedNodes(leafOnly = false) {
        return this.$refs.cascader.getCheckedNodes(leafOnly)
      },
      clearSelected() {
        const value = this.cascaderProps.multiple ? [] : ''
        this.handleOutChange(value)
        this.$emit('input', value)
      },
      handleClose() {
        this.cascader.handleClear()
      },
      handleVisibleChange(val) {
        this.focus = val
        if (val && this.firstOpen && this.options.length === 0) {
          this.onloadOptions()
        }
      },
      onloadOptions() {
        this.loading = true
        this.firstOpen = false
        if (!DataType.isEmptyArray(this.data)) {
          this.options = this.data
          this.loading = false
          return Promise.resolve()
        }
        return this.getData().then(res => {
          if (DataType.isArray(res)) {
            this.options = res
          }
          this.loading = false
        })
      },
      isEmpty(value) {
        return value === '' || value === null || value === undefined || (Array.isArray(value) && value.length === 0)
      },
      containerClick(e) {
        // 单选且有值的情况不展示下拉选项 或者 做筛选的时候不展示下拉框 或者 禁用状态下
        if (this.filterable || this.disabled || (!this.cascaderProps.multiple && !this.isEmpty(this.cascaderValue))) return
        this.cascader.toggleDropDownVisible(true)
        this.boxScroll()
      },
      boxScroll() {
        if (this.disabled || !this.filterable) return
        this.$nextTick(() => {
          const box = this.$refs.container
          box.scrollTop = box.scrollHeight
        })
      }
    }
  }
</script>
