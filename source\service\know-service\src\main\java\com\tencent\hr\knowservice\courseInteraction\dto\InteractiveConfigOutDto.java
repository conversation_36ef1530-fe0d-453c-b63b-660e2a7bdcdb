package com.tencent.hr.knowservice.courseInteraction.dto;

import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig.ConfigurationsOfSelect;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: shi<PERSON>wang
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class InteractiveConfigOutDto {
    /**
     * 主键，配置文件主键，一个课程对应一个互动配置文件
     */
    private String _id;
    /**
     * 课程类型枚举值
     */
    private int actType;


    private String actTypeName;
    /**
     * 课程id
     */
    private String courseId;

    /**
     * 选择类型的互动配置
     */
    private List<ConfigurationsOfSelectOutDto> configurationsOfSelect;
}
