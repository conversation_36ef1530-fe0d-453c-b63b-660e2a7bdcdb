## Container 容器
> 贡献者：miraclehe(何名宇)；jeeliu(刘志杰)；最近更新时间：2020-10-13；

将其他组件进行包裹，控制内部元素的浮动布局。

### 基础用法

设置布局

:::demo 使用 `direction` 属性来设置布局方向，可选值："vertical"(默认)、"horizontal"
```html
<template>
  <sdc-container>
    <sdc-link>链接1</sdc-link>
    <sdc-link>链接2</sdc-link>
  </sdc-container>
  <sdc-container direction="horizontal">
    <sdc-link>链接1</sdc-link>
    <sdc-link>链接2</sdc-link>
  </sdc-container>
</template>
```
:::

### Container Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| direction | 布局方向 | string | vertical/horizontal | vertical |
| customClass | 自定义类名 | string | — | — |
