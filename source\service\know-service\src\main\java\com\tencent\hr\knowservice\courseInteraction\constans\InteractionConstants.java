package com.tencent.hr.knowservice.courseInteraction.constans;

/**
 * 互动类型常量
 *
 * <AUTHOR>
 */
public abstract class InteractionConstants {
    /**
     * 选择类型的互动题目的枚举值
     */
    public enum selectActiveType {
        /**
         * 选择题
         */
        CHOOSE("choose"),
        /**
         * 投票
         */
        VOTE("vote"),

        //======分隔线======下面是选择题专用的枚举类型
        SINGLE("single"),

        MULTI("multi"),
        ;

        /**
         * 过期时间
         */
        private final String value;

        selectActiveType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 完成条件的枚举值
     */
    public enum completionCondition {
        /**
         * 选择题
         */
        CHOOSE("choose"),
        /**
         * 投票
         */
        CORRECT("correct"),
        ;
        /**
         * 过期时间
         */
        private final String value;

        completionCondition(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 下面区分生成id前缀的枚举类型
     */
    public enum  prefixId{

        ACTIVE_PREFIX("ACTIVE"),

        QUESTION_PREFIX("QUESTION"),

        ;

        /**
         * 过期时间
         */
        private final String value;

        prefixId(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

}

