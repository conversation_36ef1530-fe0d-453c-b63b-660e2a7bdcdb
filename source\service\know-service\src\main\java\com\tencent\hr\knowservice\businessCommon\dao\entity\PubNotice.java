package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * pub_notice
 * <AUTHOR>
@TableName(value ="pub_notice")
@Data
public class PubNotice implements Serializable {
    private Integer noticeId;
    /**
     * 标题
     */
    private String title;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 公告类型
     */
    private String noticeType;

    /**
     * 资源id
     */
    private String actId;

    /**
     * 资源类型
     */
    private String actType;

    /**
     * 计划发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preparePublishTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private String creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private String updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}