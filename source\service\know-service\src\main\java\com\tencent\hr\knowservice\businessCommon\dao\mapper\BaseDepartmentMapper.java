package com.tencent.hr.knowservice.businessCommon.dao.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.BaseDepartment;
import org.springframework.stereotype.Repository;

@Repository
public interface BaseDepartmentMapper extends BaseMapper<BaseDepartment> {
    int deleteByPrimaryKey(Integer deptId);

    int insert(BaseDepartment record);

    int insertSelective(BaseDepartment record);

    BaseDepartment selectByPrimaryKey(Integer deptId);

    int updateByPrimaryKeySelective(BaseDepartment record);

    int updateByPrimaryKey(BaseDepartment record);
}
