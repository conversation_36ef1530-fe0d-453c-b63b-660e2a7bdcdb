@import "./vars.less";

.sdc-cascader {
  display: flex;
  align-items: center;
  .cascader-container {
    width: 0;
    flex: 1;
    padding-top: 1px;
    background-color: @color-text-white;
    height: 40px;
    position: relative;
    border: 1px solid #dcdcdc;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    &.is-disabled {
      background-color: @color-bg-disabled;
    }
    &--focus {
      border-color: #3464e0;
      .cascader-container-right-icon.el-icon-arrow-down{
        transform: rotate(180deg);
        transition: all 0.3s;
      }
    }
    &--small {
      height: 36px;
    }
    &:hover {
      overflow: visible;
      .cascader-container-right-icon.el-icon-circle-close {
        display: block;
      }
      .cascader-container-right-icon.el-icon-arrow-down.is-show-close {
        display: none;
      }
    }
    &.cascader-placeholder::after{
      position: absolute;
      content: attr(placeholder);
      color: #999;
      font-size: 14px;
      height: 20px;
      line-height: 20px;
      left: 15px;
      top: calc(50% - 10px);
    }
    .cascader-container-right-icon{
      position: absolute;
      color: #999;
      right: 10px;
      font-size: 14px;
      height: 20px;
      line-height: 20px;
      top: calc(50% - 10px);
      &.el-icon-circle-close{
        display: none;
        cursor: pointer;
      }
    }
    .el-icon-circle-close{
      font-size: 14px;
    }
    .tags {
      margin-top: 2px;
      height: 45px;
      max-width: calc(100% - 100px);
      white-space: nowrap;
      padding: 0 3px;
      overflow-x: auto;
      overflow-y: hidden;
      line-height: 28px;
      &--small {
        height: 41px;
        line-height: 24px;
      }
      &--empty {
        padding: 0;
      }
    }
    .tag {
      margin: 2px 6px 2px 0;
      cursor: pointer;
      &.el-tag {
        transition: none !important;
      }
    }
    .tag:last-child {
      margin-right: 0;
    }
    .el-tag--info {
      background-color: #f2f2f2;
      border-color: #eee;
      color: #666;
    }
    .el-cascader {
      flex: 1;
      .el-tag {
        display: none;
      }
      .el-input{
        margin-top: 2px;
      }
      .el-input--mini {
        .el-input__inner {
          height: 30px !important;
        }
      }
      .el-input__inner {
        border: none;
      }
      .el-cascader__search-input {
        margin: 2px 0 2px 5px;
        padding-left: 10px;
      }
    }
    &--not-filterable{
      .tags{
        max-width: calc(100% - 30px);
      }
    }
    &--not-filterable:not(&.is-disabled){
      cursor: pointer;
    }
  }
  .suffix-num {
    font-size: 14px;
    color: #999;
    margin-left: 9px;
    white-space: nowrap;
    flex-shrink: 0;
  }
}
.el-cascader__suggestion-list {
  min-width: 300px !important;
}
.cascader-filterable-popper{
  .el-checkbox__inner {
    overflow: hidden;
  }
  .el-cascader-menu__empty-text{
    i{
      display: none;
    }
  }
  &--loading{
    .el-cascader-menu__empty-text{
      span{
        display: none;
      }
      i{
        font-size: 16px;
        display: block;
      }
    }
  }
}
