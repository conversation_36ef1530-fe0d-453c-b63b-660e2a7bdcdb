package com.tencent.hr.knowservice.graphic.dao.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * pub_task_run_log
 * <AUTHOR>
@Data
public class PubTaskRunLog implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 运行开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 服务器ip
     */
    private String serverIp;

    /**
     * 是否成功
     */
    private Boolean isSucessed;

    /**
     * 结果描述
     */
    private String result;

    /**
     * 删除时间
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}
