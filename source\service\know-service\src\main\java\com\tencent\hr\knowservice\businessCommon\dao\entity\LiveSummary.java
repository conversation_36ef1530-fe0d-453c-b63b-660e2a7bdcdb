package com.tencent.hr.knowservice.businessCommon.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * live_summary
 *
 */
@Data
public class LiveSummary implements Serializable {
    /**
     * 直播Id
     */
    private String liveId;

    /**
     * 直播名称
     */
    private String title;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 讲师
     */
    private String speaker;

    /**
     * 观看人次
     */
    private Integer pv;

    /**
     * 观看人数
     */
    private Integer uv;

    /**
     * 最高在线
     */
    private Integer topOnline;

    /**
     * 平均得分
     */
    private Integer avgScore;

    /**
     * 评分人数
     */
    private Integer scoreCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 提问数
     */
    private Integer questionCount;

    /**
     * 推荐数
     */
    private Integer recommCount;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;

}
