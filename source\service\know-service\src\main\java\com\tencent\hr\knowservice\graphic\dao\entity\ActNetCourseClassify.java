package com.tencent.hr.knowservice.graphic.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * act_net_course_classify
 *
 *
 *
 */
@Data
public class ActNetCourseClassify implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 网络课id
     */
    private Integer netCourseId;

    /**
     * 分类ID
     */
    private Integer classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     */
    private String classifyFullName;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}