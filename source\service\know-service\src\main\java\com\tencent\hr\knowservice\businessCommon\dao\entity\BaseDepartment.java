package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * base_department
 * <AUTHOR>
@Data
public class BaseDepartment implements Serializable {
    /**
     * 组织ID
     */
    private Integer deptId;

    /**
     * 组织名称
     */
    private String deptName;

    /**
     * 组织全称
     */
    private String deptFullName;

    /**
     * 组织类型
     */
    private Integer deptTypeId;

    /**
     * 组织层级
     */
    private Integer deptLevel;

    /**
     * 上级组织ID
     */
    private Integer pid;

    /**
     * 组织编码
     */
    private String shortLocationCode;

    /**
     * 组织编码
     */
    private String locationCode;

    /**
     * 所属部级组织id
     */
    private Integer belongDeptId;

    /**
     * 所属部级组织名称
     */
    private String belongDeptName;

    /**
     * BG名称
     */
    private String bg;

    /**
     * 是否虚拟组织
     */
    private Boolean isVirtualDept;

    /**
     * 是否有效
     */
    private Boolean enableFlag;

    /**
     * 删除时间（未删除为空）
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}
