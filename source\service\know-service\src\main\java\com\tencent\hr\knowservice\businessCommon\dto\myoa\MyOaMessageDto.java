package com.tencent.hr.knowservice.businessCommon.dto.myoa;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@lombok.Data
public class MyOaMessageDto {
    /**
     * 所属业务领域，单据会被MyOA归类到指定的领域中。可用的领域请参考下面的 业务领域分类表 一节
     */
    @NotEmpty(message = "category 不能为空")
    private String category;
    /**
     * 流程名称。标明单据所属的业务系统（流程）。比如：针对报销审批单据，它是费用系统的报销流程创建的。因此其 process_name 为 Cost/ExpenseProcess
     */
    @NotEmpty(message = "process_name 不能为空")
    @JsonProperty(value = "process_name")
    private String processName;
    /**
     * 流程实例标识。通常是其对应的业务单据的流水号
     */
    @NotEmpty(message = "process_inst_id 不能为空")
    @JsonProperty(value = "process_inst_id")
    private String processInstId;
    /**
     * 也发发生的节点。当前的审批单是报销流程中，申请人直接上级审批。那么这个字段的值即可叫做 申请人直接上级审批，或者对应的英文名称
     */
    @JsonProperty(value = "activity")
    private String activity;
    /**
     * 当前审批单据的处理人的英文名，比如：kevinbyang
     */
    @NotEmpty(message = "handler 不能为空")
    private String handler;
    /**
     * 审批单据的标题，这会展示在审批人的待办列表中
     */
    @NotEmpty(message = "title 不能为空")
    private String title;
    /**
     * 业务单据的访问地址。审批人点击此地址可以跳转到业务系统的当前单据进行进一步查看和操作
     */
    @JsonProperty(value = "form_url")
    private String formUrl;
    /**
     * 业务单据的移动端访问地址。审批人可以在移动端（微信、MOA、RTX、手Q）点击对应的链接打开此单据
     */
    @JsonProperty(value = "mobile_form_url")
    private String mobileFormUrl;
    /**
     * 回调地址。当审批人在MyOA提交了单据后，MyOA会调用此地址告知业务系统审批的结果
     */
    @JsonProperty(value = "callback_url")
    private String callbackUrl;
    /**
     * 是否允许快速审批。此选项只有在审批动作的数量大于或等于2时起效果。当为true时，给定的审批动作的第一个默认理解为同意，第二个默认理解为驳回。审批人可以在待办列表上直接快速审批。如果为false，则在待办列表上无法快速审批，只能打开单据详情进行审批
     */
    @JsonProperty(value = "enable_quick_approval")
    private Boolean enableQuickApproval;
    /**
     * 是否允许批量审批。当为true时，审批人点击“全部同意”后，此单据会默认提交第一个动作。如果为false，则审批人无法通过“全部同意”来审批此单，只能进入此单详情进行审批
     */
    @JsonProperty(value = "enable_batch_approval")
    private Boolean enableBatchApproval;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 优先级。此字段暂不开放（设置无效果）
     */
    private Integer priority;
    /**
     * 审批的开始时间，如果未到则审批人待办列表上不会展示
     */
    @JsonProperty(value = "")
    private Date startTime;
    /**
     * 审批的结束时间，如果超过了结束时间，则会被优先排序到前面，并有提醒
     */
    private Date dueTime;
    /**
     * 审批动作。详情请参考 审批动作 一节
     */
    private List<Actions> actions;
    /**
     * 附件。目前仅支持附件链接。详情请参考 附件 一节
     */
    private List<Attachments> attachments;
    /**
     * 审批历史。单据上可以展示过往的全部审批历史。详情请参考 审批历史 一节
     */
    @JsonProperty(value = "approval_history")
    private List<ApprovalHistory> approvalHistory;
    /**
     * 单据变量。可以记录一些业务系统传递过来的变量，并在触发回调的时候返回给业务系统。详情请参考 单据变量 一节
     */
    private List<Data> data;
    /**
     * 自定义表单。可以在审批单上展示一个自定义控件的表单。并在回调的时候，把用户的输入结果一并返回给业务系统。详情请参考 自定义表单 一节
     */
    private List<Form> form;
    /**
     * 列表视图。在这里的定义的字段会在待办列表中被简要展示出来。如果此字段没有定义，则会使用详情视图的数据来进行填充。详情请参考 列表/详情视图 一节。
     */
    @JsonProperty(value = "list_view")
    private List<View> listView;
    /**
     * 详情视图。在这里的定义的字段会在待办详情页中被展示出来。详情请参考 列表/详情视图 一节
     */
    @JsonProperty(value = "detail_view")
    private List<View> detailView;
    /**
     * 用于定义一些特殊的标签，暂不可用
     */
    private Map tags;
}
