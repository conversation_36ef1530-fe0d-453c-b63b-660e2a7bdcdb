export const hasOwn = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)
export const isListened = (vm, evt) => vm._events[evt] && typeof vm._events[evt] === 'object'
export const getClientHeight = () => document.documentElement.clientHeight
export const getScrollTop = () => window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
export const setScrollTop = (val) => document.documentElement.scrollTop = document.body.scrollTop = val
export const getMapItem = (item, map, ...props) => props.reduce((res, prop) => ({ ...res, [prop]: item[map[prop]] || item[prop] }), {})
export const oneOf = (current, values = [], field = '') => {
  const result = values.some(item => item === current)
  if (!result) {
    console.warn(`${field}必须是:【${values.join(',')}】其中之一！`)
  }
  return result
}
export const isRouteLink = (url = '') => url.startsWith('/') || url.startsWith('#/')
export const highlight = (val, keyword) => {
  const transformKeyword = keyword.replace(/[.[*?+^$|()/]|\]|\\/g, '\\$&')
  const reg = new RegExp(transformKeyword, 'gi')
  if (val) {
    return val.replace(reg, (text) => `<span style="color:#3464e0;">${text}</span>`)
  }
}
export const trim = (str) => str.trim().replace(/[ ]/g, '')
export const textEllipsis = function (val, { minLength = 20, before = 10, after = 6 } = {}) {
  if (!val) return ''
  const len = val.length
  if (len > minLength) {
    return val.substring(0, before) + '...' + val.substring(len - after, len)
  }
  return val
}
export const isNotLogin = (err) => {
  return err && err.message && err.message.response && err.message.response.status === 401
}
export const isDef = (val) => {
  return val !== null && val !== undefined
}
