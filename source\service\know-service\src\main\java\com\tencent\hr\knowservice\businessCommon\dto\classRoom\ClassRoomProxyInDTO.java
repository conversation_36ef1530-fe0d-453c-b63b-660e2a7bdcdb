package com.tencent.hr.knowservice.businessCommon.dto.classRoom;

import lombok.Data;

import java.util.List;

/**
 * @description: 互动课堂服务创建课堂实体
 * @author: liqiang
 * @createDate: 2023/08/21/17:02
 * @version: 1.0
 */
@Data
public class ClassRoomProxyInDTO {

    /**
     * 后端接口调用签名时校验的签名
     */
    private String verify;

    /**
     * 会议id  线下时非必传
     */
    private String lessonNumber;

    /**
     * 课堂标题
     */
    private String title;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建人id
     */
    private String userId;

    /**
     * 授课形式 1-线上 2-线下 3-结合
     */
    private Integer lessonType;

    /**
     * 邀请老师
     */
    private List<String> teacher;

    /**
     * 互动课堂唯一标识
     */
    private String code;

}
