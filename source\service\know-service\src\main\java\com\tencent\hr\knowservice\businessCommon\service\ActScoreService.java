package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActScore;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.ActScoreMapper;
import com.tencent.hr.knowservice.businessCommon.dto.ActScoreDto;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ActScoreService {

    @Resource
    ActScoreMapper actScoreMapper;

    /**
     * 获取评分用户数
     *
     * @param actId
     * @param actType
     * @return
     */
    public int getScoreUserCount(String actId, Integer actType) {
        if (actId == null) {
            throw new RuntimeException("id不能为空");
        }
        if (actType == null) {
            throw new RuntimeException("actType不能为空");
        }
        return actScoreMapper.getScoreUserCount(actId, actType);
    }

    /**
     * @param actId   课程id
     * @param actType 课程类型
     * @return
     */
    public Object getScoreGroup(String actId, Integer actType) {
        if (actId == null) {
            throw new RuntimeException("id不能为空");
        }
        if (actType == null) {
            throw new RuntimeException("actType不能为空");
        }
        return actScoreMapper.getGradeGroupByScore(actId, actType);
    }

    /**
     * 獲取評分列表
     *
     * @param actId
     * @param actType
     * @param staffName
     * @param score
     * @param pageNo
     * @param pageSize
     * @return
     */
    public IPage<ActScoreDto> getGradeUserList(String actId, Integer actType, String staffName, Double score, Integer pageNo, Integer pageSize) {
        if (actId == null) {
            throw new RuntimeException("id不能为空");
        }
        if (actType == null) {
            throw new RuntimeException("actType不能为空");
        }
        return actScoreMapper.gradeList(new Page<>(pageNo, pageSize), actId, actType, staffName, score);
    }


    /**
     * 添加评分
     *
     * @param actScoreDto
     * @return
     */
    public Integer addScore(ActScoreDto actScoreDto) {
        if (actScoreDto.getActId() == null) {
            throw new RuntimeException("id不能为空");
        }
        if (actScoreDto.getActType() == null) {
            throw new RuntimeException("actType不能为空");
        }

        Date date = new Date();
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        String staffName = current.getStaffName();

        if (actScoreDto.getStaffId() == null){
            actScoreDto.setStaffId(staffId);
        }

        ActScore actScore = new ActScore();
        BeanUtils.copyProperties(actScoreDto, actScore);
        //是否已评分
        ActScoreDto actScoreData = checkScored(actScoreDto.getActId(), String.valueOf(actScoreDto.getActType()));

        if (actScoreData != null) {
            actScore.setId(actScoreData.getId());
            actScore.setUpdateId(staffId);
            actScore.setUpdateName(staffName);
            actScore.setUpdatedAt(date);
            return actScoreMapper.updateByPrimaryKeySelective(actScore);
        } else {
            actScore.setCreatorId(staffId);
            actScore.setCreatorName(staffName);
            actScore.setCreatedAt(date);
            return actScoreMapper.insert(actScore);
        }
    }

    /**
     * 校验是否已评分
     *
     * @param actId
     * @param actType
     * @return
     */
    public ActScoreDto checkScored(String actId, String actType) {
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());

        QueryWrapper<ActScore> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("act_id", actId);
        queryWrapper.eq("act_type", actType);
        queryWrapper.eq("staff_id", staffId);
        queryWrapper.isNull("deleted_at");

        ActScore actScore = actScoreMapper.selectOne(queryWrapper);
        if (actScore != null) {
            ActScoreDto actScoreDto = new ActScoreDto();
            BeanUtils.copyProperties(actScore, actScoreDto);
            return actScoreDto;
        }

        return null;
    }

    /**
     * 获取最近一次评分
     * @param
     * @return
     */
    public Double getLatestScore(String actId, String staffId) {
        QueryWrapper<ActScore> actScoreQueryWrapper = new QueryWrapper<>();
        actScoreQueryWrapper.eq("act_id",actId).eq("staff_id",staffId).isNull("deleted_at")
                .orderByDesc("id").select("score");
        List<ActScore> actScores = actScoreMapper.selectList(actScoreQueryWrapper);
        if(actScores.size()==0){
           return 0.0;
        }
        Double actScore = actScoreMapper.selectList(actScoreQueryWrapper).get(0).getScore();
        return actScore==null?0:actScore;
    }
}
