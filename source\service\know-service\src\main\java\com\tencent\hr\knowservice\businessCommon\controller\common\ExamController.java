package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.exam.ExamSearchDto;
import com.tencent.hr.knowservice.businessCommon.proxy.ExamServiceApi;
import com.tencent.hr.knowservice.businessCommon.service.ExamService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 调用考试选题的相关接口
 */
@RequestMapping("/api/businessCommon/common/exam/")
@RestController
@Slf4j
public class ExamController {

    @Autowired
    ExamService examService;

    /**
     * 获取考试列表
     * @param searchDto
     * @return
     */
    @PostMapping("exam-list")
    public TransDTO getExamList(@RequestBody ExamSearchDto searchDto) {
        TransDTO result = examService.getExamList(searchDto);
        return result;
    }

    /**
     * 获取练习列表
     * @param searchDto
     * @return
     */
    @PostMapping("practice-list")
    public TransDTO getPracticeList(@RequestBody ExamSearchDto searchDto) {
        TransDTO result = examService.getPracticeList(searchDto);
        return result;
    }

    /**
     * 获取考试结果
     *
     * @param examId     考试主键id
     * @param staffNames 考生姓名,多人用;分隔
     * @param startTime  考生考试时间前置区间
     * @param endTime    考生考试前置区间
     * @return
     */
    @GetMapping("exam-result")
    public TransDTO getExamResultList(@RequestParam("exam_id") String examId,
                                      @RequestParam("staff_names")String staffNames,
                                      @RequestParam(value = "start_time",required = false)String startTime,
                                      @RequestParam(value = "end_time",required = false) String endTime) {
        TransDTO result = examService.getExamResultList(examId,staffNames,startTime,endTime);
        return result;
    }

    /**
     * 获取分类
     * @param categoryType 分类类型 1-题目分类 2-试卷分类 3-练习分类 4-考试分类
     * @return
     */
    @GetMapping("categories")
    public TransDTO getCategories(@RequestParam("category_type") String categoryType) {
        TransDTO result = examService.getCategories(categoryType);
        return result;
    }

}
