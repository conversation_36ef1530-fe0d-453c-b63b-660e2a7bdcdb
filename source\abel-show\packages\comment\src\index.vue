<template>
  <div class="label-show-page" v-if="labelList.length">
    <div class="label-content">
      <template v-for="item in labelList.slice(0, 10)">
        <!-- label_type: 标签类型：1-官方标签(通过标签管理后台创建的标签)，2-用户自定义标签(标签组件创建的标签)，3-机器推荐（算法生成的标签） -->
        <el-popover
          v-if="+item.label_type === 1"
          :key="item.label_id"
          placement="bottom"
          title=""
          width="auto"
          trigger="hover"
          popper-class="show-label-content"
          :disabled="isPreview"
        >
          <div
            v-if="showBurialPoint"
            class="tag"
            @click="toPortalPage(item)"
            @mouseenter="getLabelDetail(item)"
            slot="reference"
            :dt-areaid="dtTags(item, 'area')"
            :dt-eid="dtTags(item, 'eid')"
            :dt-remark="dtTags(item, 'remark')"
          >
            <i class="el-icon-link link-icon-style"></i>
            <span class="label-text">
              {{ item.label_name }}
            </span>
          </div>
          <div
            v-else
            class="tag"
            @click="toPortalPage(item)"
            @mouseenter="getLabelDetail(item)"
            slot="reference"
          >
            <i class="el-icon-link link-icon-style"></i>
            <span class="label-text">
              {{ item.label_name }}
            </span>
          </div>
          <div class="label-detail-box" v-loading="labelLoading">
            <div class="title">
              <span class="label-name">{{ item.label_name }}</span>
              <span class="menage" @click="labelDialogShow = true"
                >管理标签</span
              >
            </div>
            <div class="p1">{{ item.category_full_name }}</div>
            <div class="p2">
              {{ item.subscribe_count || 0 }}订阅&nbsp; · &nbsp;{{
                item.content_count || 0
              }}内容
            </div>
            <div class="footer-content">
              <el-button
                :type="item.subscribe?'danger':'primary'"
                :plain="item.subscribe"
                size="mini"
                @click="changeSubs(item, item.subscribe)"
                >{{item.subscribe?'取消订阅':'立即订阅'}}</el-button
              >
              <el-button
                plain
                size="mini"
                style="margin-left: 24px"
                @click="toLabelInfo(item)"
                >查看相关内容</el-button
              >
            </div>
          </div>
        </el-popover>

        <el-tooltip
          v-else
          class="item"
          effect="dark"
          :content="item.category_full_name"
          placement="bottom"
          :key="item.label_id"
          :disabled="isPreview"
        >
          <div 
          class="tag" 
          :key="item.label_id" 
          @click="toSearchPage(item)"
          :dt-areaid="dtTags(item, 'area')"
          :dt-eid="dtTags(item, 'eid')"
          :dt-remark="dtTags(item, 'remark')"
            >
            <!-- <i class="el-icon-link link-icon-style"></i> -->
            <span class="label-text">
              {{ item.label_name }}
            </span>
          </div>
        </el-tooltip>
      </template>
    </div>
    <el-popover
      v-model="showExtraLabel"
      placement="bottom-end"
      title=""
      width="auto"
      trigger="click"
      popper-class="hidden-label-content"
    >
      <span class="unfold-btn" slot="reference" v-show="isWrap">
        <!-- <span class="unfold-btn" slot="reference"> -->
        {{ showExtraLabel ? '收起' : '展开' }}
        <img
          class="unfold-img"
          :class="{
            'unfold-img-active': showExtraLabel
          }"
          src="../assets/img/unfold.png"
          alt=""
        />
      </span>
      <div
        class="hidden-label"
        :class="{ 'hidden-label-active': showExtraLabel }"
      >
        <template
          v-for="item in labelList.slice(sliceIndex, labelList.length)"
        >
          <el-popover
            v-if="+item.label_type === 1"
            :key="item.label_id"
            placement="bottom"
            title=""
            width="auto"
            trigger="hover"
            popper-class="show-label-content"
            :disabled="isPreview"
          >
            <div v-if="showBurialPoint"
              class="tag"
              @click="toPortalPage(item)"
              @mouseenter="getLabelDetail(item)"
              slot="reference"
              :dt-areaid="dtTags(item, 'area')"
              :dt-eid="dtTags(item, 'eid')"
              :dt-remark="dtTags(item, 'remark')"
            >
              <i class="el-icon-link link-icon-style"></i>
              <span class="label-text">
                {{ item.label_name }}
              </span>
            </div>
            <div v-else
              class="tag"
              @click="toPortalPage(item)"
              @mouseenter="getLabelDetail(item)"
              slot="reference"
            >
              <i class="el-icon-link link-icon-style"></i>
              <span class="label-text">
                {{ item.label_name }}
              </span>
            </div>
            <div class="label-detail-box" v-loading="labelLoading">
              <div class="title">
                <span class="label-name">{{ item.label_name }}</span>
                <span class="menage" @click="labelDialogShow = true"
                  >管理标签</span
                >
              </div>
              <div class="p1">{{ item.category_full_name }}</div>
              <div class="p2">
                {{ item.subscribe_count || 0 }}订阅&nbsp; · &nbsp;{{
                  item.content_count || 0
                }}内容
              </div>
              <div class="footer-content">
                <el-button
                  :type="item.subscribe?'danger':'primary'"
                  :plain="item.subscribe"
                  size="mini"
                  @click="changeSubs(item, item.subscribe)"
                  >{{item.subscribe?'取消订阅':'立即订阅'}}</el-button
                >
                <el-button
                  plain
                  size="mini"
                  style="margin-left: 24px"
                  @click="toLabelInfo(item)"
                  >查看相关内容</el-button
                >
              </div>
            </div>
          </el-popover>
          <el-tooltip
            v-else
            class="item"
            effect="dark"
            :content="item.category_full_name"
            placement="bottom"
            :key="item.label_id"
            :disabled="isPreview"
          >
            <div class="tag" @click="toSearchPage(item)">
              <!-- <i class="el-icon-link link-icon-style"></i> -->
              <span class="label-text">
                {{ item.label_name }}
              </span>
            </div>
          </el-tooltip>
        </template>
      </div>
    </el-popover>
    <sdc-sub-label-manage v-if="labelDialogShow" :labelse="labelDialogShow" :labelNodeEnv="labelNodeEnv" :dtArg="dtArg" @input="handlerCloseDialog"></sdc-sub-label-manage>
  </div>
  <div v-else class="empty">暂无标签</div>
</template>
<script src="https://unpkg.com/popper.js@2"></script>
<script>
import http from '../service/request'
import mockData from '../utils/mock'
export default {
  name: 'sdc-label-show',
  props: {
    debug: { // 是否开启调试
      type: Boolean,
      default: false
    },
    labelNodeEnv: {
      // 环境变量
      type: String,
      default: 'production'
    },
    actType: {
      type: [String, Number],
      default: '',
      require: true
    },
    courseId: {
      type: [String, Number],
      require: true
    },
    show_count: {
      // 接口获取标签的个数
      type: Number,
      default: 10
    },
    showBurialPoint: {
      // 是否需要埋点，点击标签统计，当显示埋点时，courseData为必传属性
      type: Boolean,
      default: false
    },
    courseInfo: {
      // 课程详情数据 showBurialPoint = true时必传
      type: Object,
      default: () => ({})
    },
    from_page: {
      // 点击标签跳转时所带的参数from_page
      type: String,
      default: 'ql新首页'
    },
    dtTagsFn: {
      // 埋点方法
      type: Function,
      default: () => {}
    },
    isPreview: {
      // 是否是预览时使用 预览时不可订阅标签 纯展示,并且标签从外面传进来，无需接口获取
      type: Boolean,
      default: false
    },
    previewLbael: {
      // 预览时，标签从外面传进来，无需从接口获取
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      labelList: [], // 标签列表
      labelLoading: false, // 获取单个标签详情时的loading
      urlInfo: {
        // 接口基地址
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      portalInfo: {
        // 标签聚合页面
        production: '//portal.learn.woa.com',
        test: '//test-portal-learn.woa.com'
        // test: '//test.portal.learn.oa.com'
      },
      // searchInfo: {
      //   // 搜索页面
      //   production: '//learn.woa.com',
      //   test: '//test-learn.woa.com'
      // },
      sliceIndex: 0, // 换行那个标签的下标
      isWrap: false, // 是否换行
      showExtraLabel: false, // 显示剩下的标签
      labelDialogShow: false // 是否显示管理标签组件
    }
  },
  computed: {
    dtArg() {
      let { mooc_course_id, page, page_type } = this.courseInfo
      return {
        page: page || document.title,
        page_type: page_type || `${document.title}页`,
        container: '订阅弹窗',
        content_name: '订阅抽奖入口',
        course_id: mooc_course_id || Math.random().toFixed(3) * 1000
      }
    },
    commonUrl() {
      // api基地址
      return this.urlInfo[this.labelNodeEnv] || this.urlInfo.test
    },
    portalUrl() {
      // 标签聚合页面
      return this.portalInfo[this.labelNodeEnv] || this.portalInfo.test
    },
    // searchUrl() {
    //   // 搜索页面
    //   return this.searchInfo[this.labelNodeEnv] || this.searchInfo.test
    // }
  },
  watch: { // 渲染完毕后 重新更新隐藏标签的个数
    isWrap() {
      this.updateLabelPositon()
    }
  },
  mounted() {
    if (this.isPreview) {
      this.labelList = this.previewLbael || []
      setTimeout(() => {
        this.updateLabelPositon()
      }, 500) // 获取最新渲染后的dom，加延时
    } else {
      this.getLabelList()
    }
    window.addEventListener('resize', this.updateLabelPositon)
  },
  beforeDestroy() {},
  methods: {
    // 获取标签列表
    getLabelList() {
      if (!this.actType || !this.courseId) {
        return
      }
      http
        .get(
          `${this.commonUrl}/training/api/label/user/labelinfo/course-label-list`,
          {
            withCredentials: true,
            params: {
              act_type: this.actType,
              course_id: this.courseId,
              show_count: this.show_count
            }
          }
        )
        .then(res => {
          let { code, data, message } = res
          if (code === 200) {
            this.labelList = data
            setTimeout(() => {
              this.updateLabelPositon()
            }, 500) // 获取最新渲染后的dom，加延时
            this.$emit('isGetLabelData', data)
          } else {
            message && this.$message.error(message)
          }
        })
        .catch(err => {
          console.log('err: ', err)
          // 数据调试
          if (this.debug) {
            this.labelList = mockData
            this.updateLabelPositon()
          }
        })
    },
    // 更新显示和隐藏的标签个数
    updateLabelPositon() {
      this.$nextTick(() => {
        let boxDom = document.querySelector('.label-content')
        if (!boxDom) return
        let offsetTop = boxDom.offsetTop
        let list = boxDom.children
        let index = 0
        for (const key of list) {
          if (key.offsetTop > offsetTop + 5) {
            this.isWrap = true
            break
          } else {
            this.isWrap = false
          }
          index += 1
        }
        this.sliceIndex = index
      })
    },
    // 获取标签详情
    getLabelDetail(item, firstTime = true) {
      if(this.isPreview) return
      try {
        if ( firstTime &&
          ![null, undefined].includes(item.subscribe_count) &&
          ![null, undefined].includes(item.content_count) && ![null, undefined].includes(item.subscribe)
        ) {
          return
        }
        this.labelLoading = true
          http.post(
            `${
              this.portalUrl
            }${this.getPostF()}/api/label/subscribe/getLabelSubscribeDetailInfo`,
            { label_id: item.label_id },
            { withCredentials: true }
          ).then(res => {
            let { code, data: {  content_count, subscribe_count, subscribe }, message } = res
            if (code === 200) {
              this.$set(item, 'subscribe_count', subscribe_count)
              this.$set(item, 'content_count', content_count)
              this.$set(item, 'subscribe', subscribe)
            } else {
              message && this.$message.error(message)
            }
            this.labelLoading = false
          }).catch(() => {
            this.labelLoading = false
          })
      } catch (error) {
        console.log('error: ', error)
      }
    },
    // 去标签聚合页面
    toPortalPage(item) {
      if (this.isPreview) return
      let href = `${this.portalUrl}/training/label-subs?isLabelGatherPage=true&label_id=${item.label_id}&label_name=${item.label_name}`
      window.open(href)
    },
    // 去搜索页面
    toSearchPage(item) {
      if (this.isPreview) return
      const obj = {
        production: {
          woa: 'https://learn.woa.com/mat/user/search',
          oa: 'http://v8.learn.oa.com/mat/user/search'

        },
        test: {
          woa: 'https://test-learn.woa.com/mat/user/search',
          oa: 'http://test.v8.learn.oa.com/mat/user/search'
        }
      }
      const key = location.hostname.endsWith('.woa.com') ? 'woa' : 'oa'
      const url = this.labelNodeEnv === 'production' ? obj['production'][key] : obj['test'][key]
      const commonUrl = url + `?keywords=${item.label_name}&from_page=${this.from_page}&type=label`
      // let commonUrl = `${this.searchUrl}/mat/user/search?keywords=${item.label_name}&from_page=ql新首页&type=label`
      window.open(commonUrl)
    },
    // 接口路径
    getPostF() {
      let f = ''
      if (location.host.indexOf('local.oa') > -1) {
        f = '/know-service'
      } else {
        f = '/training'
      }
      return f
    },
    // 订阅/取消订阅 opt_type:1 订阅，opt_type:2 取消订阅
    changeSubs(item, cancel = false) {
      this.labelLoading = true
      try {
        http
          .post(
            `${
              this.portalUrl
            }${this.getPostF()}/api/label/subscribe/subscribeLabel`,
            {
              opt_type: item.subscribe ? 2 : 1,
              label_id: item.label_id
            },
            {
              withCredentials: true
            }
          )
          .then(res => {
            let { code, message } = res
            if (code === 200) {
              this.$message({
                customClass: 'label--el-message',
                message: `已${cancel?'取消':''}订阅标签「${item.label_name}」`,
                iconClass: 'el-icon-warning'
              })
              this.getLabelDetail(item, false)
            } else {
              message && this.$message.error(message)
            }
            this.labelLoading = false
          }).catch(() => {
            this.labelLoading = false
          })
      } catch (error) {
        console.log('error: ', error)
      }
    },
    // 地址处理
    getHost() {
      let url = ''
      if (this.labelNodeEnv === 'production') {
        url = window.location.hostname.endsWith('.woa.com')
          ? '//portal.learn.woa.com'
          : '//portal.learn.oa.com'
      } else {
        url = window.location.hostname.endsWith('.woa.com')
          ? '//test-portal-learn.woa.com'
          : '//test.portal.learn.oa.com'
      }
      return url
    },
    // 查看相关内容
    toLabelInfo(item) {
      if (!item.label_id) return
      window.open(
        `${this.getHost()}/training/label-subs?isLabelGatherPage=true&label_id=${
          item.label_id
        }&label_name=${item.label_name}`
      )
    },
    // 管理标签弹窗回调
    handlerCloseDialog(show) {
      this.labelDialogShow = show
      this.getLabelList()
    },
    // 埋点
    dtTags (item, type) {
      let { mooc_course_id, page, page_type, container, click_type, terminal } = this.courseInfo
      if (mooc_course_id) {
        if (type === 'area') {
          return `area_${mooc_course_id}_${item.label_id}`
        } else if (type === 'eid') {
          return `element_${mooc_course_id}_${item.label_id}`
        } else {
          return JSON.stringify({
            page, // 任务名称
            page_type,
            container, // 板块的名称
            click_type,
            content_type: item.category_name,
            content_id: item.label_id,
            content_name: item.label_name,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal
          })
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.label-show-page {
  min-width: 150px;
  width: 100%;
  display: flex;
  align-items: center;
  .el-button--primary {
    color: #fff;
    background-color: #409eff;
    border-color: #409eff;
  }
  .label-content {
    height: 25px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    overflow: hidden;
    .tag {
      display: flex;
      align-items: center;
      height: 24px;
      font-size: 10px;
      color: #0052d9;
      padding: 0 8px;
      margin: 0 8px 8px 0;
      border-radius: 22px;
      background: var(---Brand1-Light, #ecf2fe);
      cursor: pointer;
      .link-icon-style {
        margin-right: 4px;
        font-size: 16px;
        // font-weight: 700;
      }
      .label-text {
        max-width: 108px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
  .unfold-btn {
    width: 58px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0052d9;
    font-size: 10px;
    cursor: pointer;
    flex-shrink: 0;
    .unfold-img {
      width: 16px;
      margin-left: 2px;
      transition: transform 0.2s linear;
    }
    .unfold-img-active {
      transform: rotate(-180deg);
    }
  }
}
.empty {
  line-height: 25px;
}
.hidden-label-content {
  .hidden-label {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    transition: all ease-in 0.2s;
    overflow: hidden;
    .tag {
      display: flex;
      align-items: center;
      height: 24px;
      font-size: 10px;
      color: #0052d9;
      padding: 0 8px;
      margin: 0 4px 8px 0;
      border-radius: 22px;
      background: var(---Brand1-Light, #ecf2fe);
      cursor: pointer;
      .link-icon-style {
        margin-right: 4px;
        font-size: 16px;
        // font-weight: 700;
      }
      .label-text {
        max-width: 108px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
}
</style>
<style lang="less">
.show-label-content.el-popover {
  margin-top: 10px;
  border-radius: 6px;
  border: 0.5px solid var(--Gray-Gray4-, #dcdcdc);
  .label-detail-box {
    .title {
      display: flex;
      justify-content: space-between;
      .label-name {
        color: #000000e6;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
      }
      .menage {
        margin-left: 20px;
        color: #00000042;
        font-size: 12px;
        line-height: 20px;
        cursor: pointer;
      }
    }
    .p1 {
      margin-top: 6px;
      color: #00000066;
      font-size: 12px;
      line-height: 20px;
    }
    .p2 {
      margin-top: 6px;
      color: #00000099;
      font-size: 12px;
      line-height: 20px;
    }
    .footer-content {
      margin-top: 6px;
      display: flex;
      .el-button--danger.is-plain {
        padding: 2px 8px;
        line-height: 20px;
        background: none;
        color: #d54941;
        border-color: #d54941;
      }
      .el-button--danger.is-plain:hover {
        background: none;
        color: #d54941;
      }
      .el-button--default {
        padding: 2px 8px;
        line-height: 20px;
        color: #000000e6;
        margin-left: 24px;
      }
    }
  }
}
.hidden-label-content.el-popover {
  padding: 8px 4px 0 8px;
  max-width: 640px;
}
</style>
