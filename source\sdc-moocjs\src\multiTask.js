let storageInfo = null
let storageCallback = null
function handleStorageEvent(e) {
  if (e.key === storageInfo.key && storageCallback) {
    storageCallback(e)
  }
}
const multiTask = {

  handleMultitasking: (params, callback) => {
    window.localStorage.setItem(params.key, params.value)
    storageInfo = params
    storageCallback = callback
    window.addEventListener('storage', handleStorageEvent)
  },

  removeStorageListener: () => {
    window.removeEventListener('storage', handleStorageEvent)
    window.localStorage.removeItem(storageInfo.key)
  }
}

export default multiTask
