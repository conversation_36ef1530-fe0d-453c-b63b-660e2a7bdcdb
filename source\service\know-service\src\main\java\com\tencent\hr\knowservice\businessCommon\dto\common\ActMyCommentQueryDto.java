package com.tencent.hr.knowservice.businessCommon.dto.common;

import java.util.List;
import java.util.Objects;
import lombok.Data;

@Data
public class ActMyCommentQueryDto {

//    /**
//     * 主键id
//     */
//    private Integer id;
    /**
     * 应用id
     */
    private String appId;

    /**
     * 单据类型(1 面授课 2 网络课 3 班级 4 活动 5 直播 6 图文 11 慕课 15 课单)
     */
    private Integer actType;

    /**
     * 回复的评论id
     */
    private Integer pid;

    /**
     * 课程/网课/直播id/图文id/慕课id/
     */
    private String actId;

    private List<String> actIdList;

    /**
     * 回复人id
     */
    private Integer replyStaffId;

    /**
     * 回复人姓名
     */
    private String replyStaffName;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 是否移动端
     */
    private Byte fromMobile;
    /**
     * 作者id
     */
    private Integer authorId;
    /**
     * 1-显示评论 0-隐藏评论
     */
    private Integer show;

    private Integer staffId;
    /**
     * 用户名
     */
    private String staffName;

    /**
     * 1-最新 2-最热 3- 回复最多
     */
    private Integer orderType;

    /**
     * 查询类型 全部不传，0-置顶、1-隐藏、2-显示。
     */
    private Integer type;

    /**
     * 页数大小
     */
    private int pageSize;

    /**
     * 页码
     */
    private int pageNo;


    /**
     * 排序类型 DESC ASC
     */
    private String orderStr;


    /**
     * 登陆id
     */
    private  Integer loginStaffId;
    /**
     * 登陆姓名
     */
    private String loginStaffName;


    @Override
    public int hashCode() {
        return Objects.hash(this.actType, this.actId);
    }

    @Override
    public boolean equals(Object obj){
        return super.equals(obj);
    }

}
