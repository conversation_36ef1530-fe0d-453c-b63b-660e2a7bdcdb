package com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class SelectContent {

    /**
     * 具体题目的唯一标识id
     */
    private String questionId;

    /**
     * 分数
     */
    private int questionScore;

    /**
     * 题目类型 CHOOSE--选择；VOTE--投票
     */
    private String activeType;
    /**
     * 选择类型题目才有的配置。如果不是则为null
     */
    private ChooseTypeConfig chooseTypeConfig;
    /**
     * 投票类型题目才有的配置。如果不是则为null
     */
    private VoteTypeConfig voteTypeConfig;
    /**
     * 我是选项题干
     */
    private String questionText;
    /**
     * 我是选项题干英文
     */
    private String questionTextEn;
    /**
     * 选项
     */
    private List<Options> options;
    /**
     * 正确选项
     */
    private List<String> correctAnswer;
    /**
     * 正确选项-英文
     */
    private List<String> correctAnswerEn;
}
