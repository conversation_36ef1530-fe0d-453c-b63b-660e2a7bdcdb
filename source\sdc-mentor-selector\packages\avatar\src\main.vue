<template>
  <div class="sdc-avatar" :class="customClass">
    <el-dropdown @command="handleCommand">
      <div class="sdc-avatar-inner">
        <sdc-link :to="avatar.url">
          <span class="avatar-item">
            <el-avatar size="small" :src="avatar.avatarUrl">
              <img v-if="defaultUrl" :src="defaultUrl" alt=""/>
            </el-avatar>
            <span class="name" v-if="name || avatar.name || hasSlot">
              <slot>
                {{name || avatar.name}}
              </slot>
              <i v-if="menus.length" class="el-icon-arrow-down el-icon--right"></i>
            </span>
          </span>
        </sdc-link>
      </div>
      <el-dropdown-menu slot="dropdown" class="sdc-avatar-dropdown" :class="{ hidden: !menus.length }">
        <sdc-link :to="item.url" :target="item.target" v-for="(item, index) in menus" :key="index">
          <el-dropdown-item :icon="item.icon" :command="getCommandType(item)" :divided="item.divided" :disabled="item.disabled">
            {{item.text}}
          </el-dropdown-item>
        </sdc-link>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
  import { classes, locale } from 'mixins'
  import SdcLink from 'packages/link'

  export default {
    name: 'sdc-avatar',
    mixins: [classes, locale],
    props: {
      avatar: {
        type: Object,
        default() {
          return {}
        }
      },
      name: {
        type: String
      }
    },
    computed: {
      defaultUrl() {
        return this.avatar.defaultUrl || require('packages/theme-grace/img/avatar.gif')
      },
      hasSlot() {
        return !!(this.$slots && this.$slots.default)
      },
      menus() {
        const { map = {}, data = [] } = this.avatar
        return data.map(item => ({
          text: item[map.text] || item.text,
          target: item[map.target] || item.target,
          url: this.getLinkUrl(item, map),
          type: item[map.type] || item.type,
          divided: item[map.divided] || item.divided || false,
          disabled: item[map.disabled] || item.disabled || false
        }))
      }
    },
    methods: {
      handleCommand(command) {
        if (command !== undefined) {
          this.$emit('click', command)
          this.$bus.$emit('avatar', command)
        }
      },
      getLinkUrl(item, map) {
        if (item.disabled) return 'javascript:void(0)'
        return item[map.url] || item.url || 'javascript:void(0)'
      },
      getCommandType(item) {
        if (item.disabled || item.url !== 'javascript:void(0)') return ''
        return item.type || ''
      }
    },
    components: {
      SdcLink
    }
  }
</script>
