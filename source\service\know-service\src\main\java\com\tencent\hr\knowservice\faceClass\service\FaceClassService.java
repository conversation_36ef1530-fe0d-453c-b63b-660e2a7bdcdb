package com.tencent.hr.knowservice.faceClass.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActClassAttendance;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.ActClassAttendanceMapper;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.ActClassMapper;
import com.tencent.hr.knowservice.faceClass.dao.entity.ActUserSign;
import com.tencent.hr.knowservice.faceClass.dao.mapper.ActUserSignMapper;
import com.tencent.hr.knowservice.faceClass.dto.ClassDetailDTO;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/9/11
 * @version: 1.0
 */
@Service
public class FaceClassService {
    @Autowired
    ActClassMapper actClassMapper;

    @Autowired
    ActClassAttendanceMapper actClassAttendanceMapper;

    @Autowired
    ActUserSignMapper actUserSignMapper;


    /**
     * 获取面授课班级详情
     * @param classId
     * @return
     */
    public ClassDetailDTO getClassDetail(Integer classId) {
        if(!authCheck(classId)){
            throw new LogicException("暂无权限！");
        }
        ClassDetailDTO classDetail = actClassMapper.getClassDetail(classId);
        if(classDetail == null){
            throw new LogicException("该班级不存在，请检查！");
        }
        //查询当前学员是否签到
        classDetail.setIsSign(findAttendanByCurrentName(classId));
        return classDetail;
    }

    /**
     * 查询当前用户是否签到（班级）
     * @param classId
     * @return
     */
    public Boolean findAttendanByCurrentName(Integer classId){
        String staffId = GatewayContext.current().getStaffId();
        QueryWrapper<ActClassAttendance> query = new QueryWrapper<>();
        query.eq("act_type",3);
        query.eq("class_id",classId);
        query.isNull("deleted_at");
        query.eq("staff_id",staffId);
        List<ActClassAttendance> actClassAttendances = actClassAttendanceMapper.selectList(query);
        //判断学员是否报名
        if(CollectionUtils.isEmpty(actClassAttendances)){
            return false;
        }
        //判断学员是否报名
        QueryWrapper<ActUserSign> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("deleted_at");
        queryWrapper.eq("act_type",3);
        queryWrapper.eq("act_id",classId);
        Integer count = actUserSignMapper.selectCount(queryWrapper);
        if(count == 0){
            return false;
        }else {
            return true;
        }
    }

    /**
     * 权限控制：仅班级学员可访问授课详情页（已报名、全勤、部分缺勤、旷课）
     * @param classId
     */
    public Boolean authCheck(Integer classId){
        String staffId = GatewayContext.current().getStaffId();
        QueryWrapper<ActClassAttendance> query = new QueryWrapper<>();
        query.eq("act_type",3);
        query.eq("class_id",classId);
        query.isNull("deleted_at");
        query.eq("staff_id",staffId);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(0);
        statusList.add(4);
        statusList.add(5);
        statusList.add(18);
        query.in("status",statusList);
        query.last("limit 1");
        ActClassAttendance actClassAttendance = actClassAttendanceMapper.selectOne(query);
        return actClassAttendance != null;
    }
}
