package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * pub_notice_view
 * <AUTHOR>
@Data
public class PubNoticeView implements Serializable {
    /**
     * 主键id
     */
    private String id;

    /**
     * 公告id
     */
    private Integer noticeId;

    /**
     * 用户id
     */
    private Integer staffId;

    /**
     * 用户姓名
     */
    private String staffName;

    /**
     * 查看时间
     */
    private Date viewTime;

    /**
     * 是否移动端
     */
    private Boolean isMobile;

    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private String creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private String updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}