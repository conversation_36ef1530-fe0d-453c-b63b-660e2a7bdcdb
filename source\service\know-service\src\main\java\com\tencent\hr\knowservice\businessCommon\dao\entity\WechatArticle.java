package com.tencent.hr.knowservice.businessCommon.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * wechat_article
 *
 */
@Data
public class WechatArticle implements Serializable {
    /**
     * 文章id
     */

    private Integer articleId;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 作者
     */
    private String author;

    /**
     * 发布时间
     */
    private Date publishDate;

    /**
     * 封面图片地址
     */
    private String photoUrl;

    /**
     * 目标学员
     */
    private String targetList;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 文章类型（1 图文 2 文字 3 视频）
     */
    private Integer articleType;

    /**
     * 1 公司级 2 BG级 3 部门级 文章
     */
    private Integer articleLevel;

    /**
     * 描述
     */
    private String description;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer praiseCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 是否可以评论 1 可以评论 0 不能评论
     */
    private Integer canComment;

    /**
     * 是否默认显示评论 1 默认展示评论 0 默认不展示评论
     */
    private Integer isShowComment;

    /**
     * 支持终端（ 1 移动端  2 PC 3 全部）
     */
    private Integer supportMobile;

    /**
     * 文章状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;

}
