## Mooc集成第三方页面交互方法库

> 这个js库 [sdc-moocjs](https://mirrors.tencent.com/#/private/npm/detail?repo_id=537&project_name=%40tencent%2Fsdc-moocjs&search_label=package_name&search_value=sdc&page_num=2) 上传到腾讯软件源内部npm服务上 外部无法下载使

**地址：** 
https://mirrors.tencent.com/#/private/npm/detail?repo_id=537&project_name=%40tencent%2Fsdc-moocjs&search_label=package_name&search_value=sdc&page_num=2

**1.  说明：**
这是一个JS库，分为两个部分（@tencent/sdc-moocjs、@tencent/sdc-moocjs-integrator 两个npm包），两个页面通过iframe通信，分别集成这两个SDK进行信息交互，主要目的是降低业务系统接入的难度，尽量减少接入代码的侵入性。
![enter image description here#415px #244px](/download/attachments/2551679947/image-1683637594935.png?version=1&modificationDate=1683637595162&api=v2)



**2.  @tencent/sdc-moocjs 使用**

**@tencent/sdc-moocjs 该SDK提供给嵌入第三方页面的系统引入使用**

安装：npm install @tencent/sdc-moocjs

**方法**
- SDKTimer包含定时器开启、暂停，定时器时长记录、防挂机检测等方法
	1. limtHangUpTime：Number，防挂机时长，单位s，默认值3600s
	2. antiHangUp: Boolean，是否开启防挂机功能，默认开启
	3. events：Object，回调函数事件
		- getDurationSeconds，定时器开始后每一秒执行的回调函数，参数一seconds表示new SDKTimer整个周期定时器持续时长，参数二durtation本轮定时器持续时间，定时器有暂停重新开启，则会清空（用于定时上报学习记录）
		- hangUpTimerCalback，超过limtHangUpTime设置的时间内未检测到用户使用，执行回调函数，触发防挂机提示，参数e表示limtHangUpTime设置的时间
	4.  start()：Function，定时器开始
	5. pause()：Function，定时器暂停
	6. formatSeconds()：Function，将s转换成时分秒[h, m, s]
	7. removeEvent()：Function，离开页面时需要清除事件，new SDKTimer时会注册setInterval、visibilitychange等事件
		
``` 
// 组件使用示例

import { SDKTimer } from 'sdc-moocjs'

let timer = new SDKTimer({
	limtHangUpTime: 10,
	events: {
		getDurationSeconds: (seconds, durtation) => {
		    // 本次学习时长
          this.seconds = seconds
		    // durtation 本轮定时器持续时间，定时器有暂停重启，则会清空（用于定时上报学习记录）
          if (durtation % 15 === 0) {
            console.log('上报学习记录')
          }
		},
		hangUpTimerCalback: (e) => {
			console.log('长时间未操作页面：' + e)
		}
	}
})
this.timer.start()
this.timer.pause()

computed: {
	learningTime() {
      let time = (this.timer && this.timer.formatSeconds(this.seconds)) || [0, 0, 0]
      return `${time[0]}时${time[1]}分${time[2]}秒`
    },
}

beforeDestroy() {
    this.timer.removeEvent()
    this.timer = null
},
```

- SDKMultiTask包含多任务同时进行检测方法
	1. handleStorageEvent：多任务监听事件

	2. removeStorageListener：移除多任务监听
	
``` 
import { SDKMultiTask } from 'sdc-moocjs'
mounted(){
	let params = {
        key: 'mooc-multi-task',
        value: JSON.stringify({
          task_id: this.task_id,
          time: Date.now()
        })
      }
      SDKMultiTask.handleMultitasking(params, (e) => {
        let value = JSON.parse(e.newValue)
        let flag = this.taskData.some(item => item.task_id === value.task_id)
        if (flag && !this.isMultitasking) {
          
        }
      })
}
beforeDestroy() {
    SDKMultiTask.removeStorageListener()
},
```


- SDKUtils包含与第三方页面进行iframe通信方法
	
``` 
import { SDKUtils } from 'sdc-moocjs'

mounted() {
	// 先注册message监听方法
	SDKUtils.registerMessageListener()
	
	let iframeDOM = document.getElementById('iframe')
	// 回调函数：接收iframe传来的消息，告知第三方页面加载完成（一般在第三方页面的crated、ready、mouted触发发送消息）
	// 第二个参数传引入iframe页面的DOM元素，是为了接收到加载完成消息同时，给第三方页面发送消息告知已成功接收
	SDKUtils.onload((e) => {
		
	}, iframeDOM)
	
	// 回调函数：接收iframe传来的消息，告知资源开始播放
    SDKUtils.onPlay((e) => {

    })
	
	// 回调函数：接收iframe传来的消息，告知资源已暂停播放
    SDKUtils.onPause((e) => {

    })
	
	// 回调函数：接收iframe传来的消息，告知资源已完成播放
	// res.data.params === 'init' 为true表示资源刚加载完视频已完成，为false表示播放了一段时间以后才完成
    SDKUtils.onComplete(e => {
      let isInit = e.data.params === 'init'

    })
	
	// iframe页面区域内的鼠标监听事件通信
	SDKUtils.mousemoveIframe(e => {

    })
	
	// 回调函数：接收iframe传来的消息，告知考试系统-开始考试/开始练习
    SDKUtils.onStratAnswer((e) => {
      
    })
	
	// 回调函数：接收iframe传来的消息，告知考试系统-结束考试/结束始练习
	/*
	params: {
      is_finished: Boolean // 是否完成考试/练习
      is_cheat: Boolean // 是否作弊
      score: number // 考试得分
      elapsed_seconds: integer // 本次学习的持续时间(秒)
    }
	*/
    SDKUtils.onEndAnswer(e => {
      let params = e.data.params
    })
	
	// 回调函数：接收iframe传来的消息，告知考试系统-进入详情页
    SDKUtils.onAnswerDetail((e) => {
      
    })
	
	// 回调函数：接收iframe传来的消息，告知考试系统-从详情页返回首页
    SDKUtils.onDetailBackHome((e) => {
      
    })
	
	// 回调函数：所有来自iframe的消息事件
    SDKUtils.messageListener((e) => {
      
    }, iframeDOM)

	// 给iframe页面发消息，通知第三方资源暂停播放
	SDKUtils.setPause(iframeDOM)

	// 给iframe页面发消息，通知第三方资源开始播放
	SDKUtils.setPlay(iframeDOM)
	
	// 给iframe页面发消息
	/*
	iframeDOM：DOM元素
	events：事件名称（自定义）
	params：携带参数
	orgin：默认是"*"，可以传当前域名地址
	*/
	SDKUtils.postMessage(iframeDOM, events, params, orgin)
    
  },
  
  beforeDestroy() {
  	// 移除message消息监听事件
    SDKUtils.removeMessageListener()
  },

```

**注意事项 - 区分onload**

``` 
let iframeDOM = document.getElementById('iframe')
SDKUtils.onload((e) => {
	// 该onlaod，是第三方页面通过postMessage方法与当前页面的通信触发的onload回调函数
}, iframeDOM)

iframeDOM.onload = () => {
	// 该onload是在iframe加载完成触发
}
```





**使用demo地址：**
https://test-portal-learn.woa.com/training/mooc/taskDetail?mooc_course_id=WZDfVTdE&task_id=1367
  