package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubNoticeView;

public interface PubNoticeViewMapper  extends BaseMapper<PubNoticeView> {
    int deleteByPrimaryKey(Integer id);

    int insert(PubNoticeView record);

    int insertSelective(PubNoticeView record);

    PubNoticeView selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PubNoticeView record);

    int updateByPrimaryKey(PubNoticeView record);
}