package com.tencent.hr.knowservice.graphic.controller.crontab;

import com.tencent.hr.knowservice.graphic.service.GraphicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @description: 图文定时任务
 * @author: shizhouwang
 * @createDate: 2023/7/25
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/crontab/graphic")
public class GraphicCrontabController {

    @Autowired
    GraphicService graphicService;

    /**
     * 定时任务把没有音频文件的图文转换成为有音频文件的图文
     * 定时转换没有音频文件的图文
     */
    @PostMapping("/convertGraphicToAudio")
    public void convertGraphicToAudio(){
        graphicService.convertGraphicToAudio();
    }
}
