package com.tencent.hr.knowservice.businessCommon.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 课程分类
 * @TableName pub_course_classify
 */
@TableName(value ="pub_course_classify")
@Data
public class PubCourseClassify implements Serializable {
    /**
     * 分类Id
     */
    @TableId(type = IdType.AUTO)
    private Integer classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 业务类型
     */
    private Integer actType;

    /**
     * 父级id
     */
    private Integer pid;

    /**
     * 全路径
     */
    private String fullPath;

    /**
     * 全路径名称
     */
    private String fullName;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}