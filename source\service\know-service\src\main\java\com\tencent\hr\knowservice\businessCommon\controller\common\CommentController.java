package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActComment;
import com.tencent.hr.knowservice.businessCommon.dto.common.*;
import com.tencent.hr.knowservice.businessCommon.service.CommentService;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 评论
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/businessCommon/common/comment/")
public class CommentController {

    @Autowired
    private CommentService commentService;

    /**
     * 新增评论
     *
     * @return
     */
    @PostMapping("/add")
    public TransDTO addComment(@RequestBody ActCommentDto actCommentDto) {
        TransDTO dto = new TransDTO<>();
        Integer id = null;
        id = commentService.addComment(actCommentDto);
        return dto.withCode(HttpStatus.SC_OK).withData(id).withSuccess(true).withMessage("评论成功！");
    }

    /**
     * 获取评论列表
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/get_comments")
    public TransDTO getComments(@RequestBody ActCommentDto actCommentDto) {
        TransDTO dto = new TransDTO<>();
        Object comments = null;
        comments = commentService.getComments(actCommentDto);
        return dto.withCode(HttpStatus.SC_OK).withData(comments).withSuccess(true).withMessage("操作成功！");
    }

    /**
     * 获取评论列表--get版本，为了方便适配前端组件
     *
     * @param actId
     * @param actType
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/getUserComments")
    public TransDTO<CommentPageDto<ActCommentResDto>> getUserComments(@RequestParam("act_id") String actId,
                                                                      @RequestParam("act_type") Integer actType,
                                                                      @RequestParam("order_type") Integer orderType,
                                                                      @RequestParam("page_no") Integer pageNo,
                                                                      @RequestParam("page_size") Integer pageSize,
                                                                      @RequestParam(value = "pid", required = false) Integer pid) {
        TransDTO<CommentPageDto<ActCommentResDto>> dto = new TransDTO<>();
        ActCommentDto actCommentDto = new ActCommentDto();
        actCommentDto.setActId(actId);
        actCommentDto.setActType(actType);
        actCommentDto.setOrderType(orderType);
        actCommentDto.setPageNo(pageNo);
        actCommentDto.setPageSize(pageSize);
        actCommentDto.setPid(pid);
        CommentPageDto<ActCommentResDto> comments;
        try {
            comments = commentService.getComments(actCommentDto);
        } catch (Exception e) {
            return dto.withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage(e.getMessage());
        }
        return dto.withCode(HttpStatus.SC_OK).withData(comments).withSuccess(true).withMessage("操作成功！");
    }

    /**
     * 评论删除
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/delete")
    public TransDTO deleteComment(@RequestBody ActDeleteCommentDto actCommentDto) {
        Object data = null;
        TransDTO dto = new TransDTO<>();
        data = commentService.deleteComment(actCommentDto);
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withData(data).withMessage("操作成功！");
    }

    /**
     * 评论删除 -- 用户端使用
     *
     * @param commentId
     * @return
     */
    @PostMapping("/delete/{commentId}")
    public TransDTO deleteUserComment(@PathVariable("commentId") Integer commentId) {
        TransDTO dto = new TransDTO<>();
        ActComment actComment = commentService.findCommentById(commentId);
        if (null == actComment) {
            return dto.withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage("评论已被删除/不存在!");
        }
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        if (!actComment.getCreatorId().equals(staffId)) {
            return dto.withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage("只能删除自己的评论！");
        }
        ActDeleteCommentDto actCommentDto = new ActDeleteCommentDto();
        actCommentDto.setId(commentId);
        ActComment data = commentService.deleteComment(actCommentDto);
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withData(data).withMessage("操作成功！");
    }

    /**
     * 评论点赞
     *
     * @param actPraisedCommentDto
     * @return
     */
    @PostMapping("/praised")
    public TransDTO praisedComment(@RequestBody ActPraisedCommentDto actPraisedCommentDto) {
        TransDTO dto = new TransDTO<>();
        commentService.praisedComment(actPraisedCommentDto);
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withMessage("操作成功！");
    }

    /**
     * 获取评论数量
     *
     * @param actId
     * @param actType
     * @return
     */
    @GetMapping("get_comment_count")
    public TransDTO getCommentCount(@RequestParam("act_id") String actId, @RequestParam("act_type") Integer actType) {
        TransDTO dto = new TransDTO<>();
        long count;
        try {
            count = commentService.getCommentCount(actId, actType);
        } catch (Exception e) {
            return dto.withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage(e.getMessage());
        }
        return dto.withCode(HttpStatus.SC_OK).withData(count).withSuccess(true).withMessage("操作成功！");
    }

    /**
     * 获取评论列表 for朱哥使用
     *
     * @param appId
     * @param actId
     * @param actType
     * @param staffId
     * @param empName
     * @param PageNo
     * @param PageSize
     * @return
     */
    @GetMapping("/get_user_comments")
    public TransDTO<Page<ActCommentResDto>> getUserComments(@RequestParam(value = "app_id") String appId,
                                                            @RequestParam(value = "act_id", required = false) String actId,
                                                            @RequestParam(value = "act_type", required = false) Integer actType,
                                                            @RequestParam(value = "staff_id") Integer staffId,
                                                            @RequestParam(value = "emp_name", required = false) String empName,
                                                            @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer PageNo,
                                                            @RequestParam(value = "page_size", required = false, defaultValue = "20") Integer PageSize) {

        TransDTO<Page<ActCommentResDto>> dto = new TransDTO<>();
        Page<ActCommentResDto> comments = null;
        try {
            comments = commentService.getUserComments(appId, actId, actType, staffId, empName, PageNo, PageSize);
        } catch (Exception e) {
            return dto.withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage(e.getMessage());
        }
        return dto.withCode(HttpStatus.SC_OK).withData(comments).withSuccess(true).withMessage("");
    }

    /**
     * 获取我的评论列表
     *
     * @param queryDto
     * @return
     */
    @PostMapping("get_my_comments")
    public TransDTO getMyComments(@RequestBody ActMyCommentQueryDto queryDto) {
        TransDTO<Page<ActComment>> dto = new TransDTO<>();
        Page<ActComment> data = commentService.getMyComments(queryDto);
        dto.withData(data).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }

    //==========================================================管理端接口================================================

    /**
     * 评论置顶
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/sticky")
    public TransDTO stickyComment(@RequestBody ActStickyCommentDto actCommentDto) {
        TransDTO dto = new TransDTO<>();
        commentService.stickyComment(actCommentDto);
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withMessage("操作成功！");
    }

    /**
     * 评论隐藏
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/show")
    public TransDTO showComment(@RequestBody ActShowCommentDto actCommentDto) {
        TransDTO dto = new TransDTO<>();
        try {
            commentService.showComment(actCommentDto);
        } catch (Exception e) {
            return dto.withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage(e.getMessage());
        }
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withMessage("操作成功！");
    }
}
