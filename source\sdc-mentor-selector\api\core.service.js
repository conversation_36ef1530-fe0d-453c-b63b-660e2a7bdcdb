/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2023-10-16 11:32:53
 */
import { DataStorage, STORAGE_TYPE, DataHttp } from 'sdc-core'

export default class CoreService {
  // 判断是否是生产环境
  static isProd() {
    var env = window.SDC_BUILD_ENV || 'prod'
    // var env = window.SDC_BUILD_ENV || 'test' // TODO:开发测试环境用
    // 判断路径是否传参sdcuiEnvironment
    const query = location.href.split('?')[1] || ''
    if (query) {
      const queryItem = query.split('&').find(item => item.includes('sdcuiEnvironment='))
      if (queryItem) {
        env = queryItem.split('=')[1]
      }
    }
    if (!['production', 'prd', 'prod'].includes(env)) {
      return false
    }
    return true
  }

  static http = DataHttp.getInstance({
    multi: true,
    axios: DataHttp.create({
      baseURL: CoreService.getBaseUrl(),
      withCredentials: true,
      retry: 0,
      headers: { 
        'X-Requested-With': 'XMLHttpRequest', 
        'hrgw-appname': 'sdc-webui'
      }
    }),
    map: { result: 'data', success: res => res }
  })

  static getLocationList({ includeRegionList = [100, 200, 300, 400, 500], includeCountryList = [] }) {
    const params = {
      timeZone: "Asia/Shanghai",
      pageIndex: 1,
      queryCondition: {
        type: "WHERE_SQL",
        whereSql: ""
      }
    }
    const cacheKey = 'sdc:ui-post-location-all'
    const hasCache = DataStorage.contains(cacheKey, STORAGE_TYPE.Session)
    const promise = hasCache ? Promise.resolve(DataStorage.get(cacheKey, { storageType: STORAGE_TYPE.Session })) : CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/api-public-core-scene-md-dictionary-map-region-country-location-realtime/sdc-webui/data', { params })
    return promise.then(res => {
      !hasCache && res && DataStorage.set(cacheKey, res, { storageType: STORAGE_TYPE.Session })
      const result = res.content || []
      let arr = []
      arr = result.reduce((prev, cur) => {
        // 当不包含在显示的大区集合中时，跳出
        if (!includeRegionList.includes(cur.region_item_id)) return prev
        // 当不包含在显示的国家集合中时，跳出
        if (includeCountryList && includeCountryList.length && !includeCountryList.includes(cur.country_item_id)) return prev
        // 显示完整路径时 或者 展示层级为1，2时，绑定原始id
        const region_item_id = cur.region_item_id
        const country_item_id = cur.country_item_id
        const regionObj = {
          leaf: false,
          item_id: region_item_id,
          item_name_cn: cur.region_item_name_cn,
          item_name_en: cur.region_item_name_en,
          item_order_id: cur.region_item_order_id
        }
        const locationObj = {
          leaf: true,
          item_id: cur.location_item_id,
          item_name_cn: cur.location_item_name_cn,
          item_name_en: cur.location_item_name_en,
          item_order_id: cur.location_item_order_id
        }
        const countryObj = {
          leaf: false,
          item_id: country_item_id,
          item_name_cn: cur.country_item_name_cn,
          item_name_en: cur.country_item_name_en,
          item_order_id: cur.country_item_order_id,
          children: [locationObj]
        }
        const index = prev.findIndex(item => item.item_id === region_item_id)
        if (index === -1) {
          const obj = { ...regionObj }
          obj.children = [countryObj]
          prev.push(obj)
        } else {
          const index2 = prev[index].children.findIndex(item => item.item_id === country_item_id)
          if (index2 === -1) {
            prev[index].children.push(countryObj)
          } else {
            prev[index].children[index2].children.push(locationObj)
          }
        }
        return prev
      }, [])
      // 按item_order_id从小到大排序 0放最后
      const sortFn = arr => {
        const list = arr.sort((a, b) => {
          return a.item_order_id - b.item_order_id
        })
        const isZeroList = list.filter(item => item.item_order_id === 0)
        const notZeroList = list.filter(item => item.item_order_id !== 0)
        return [...notZeroList, ...isZeroList]
      }
      // 递归排序
      const mapFn = arr => {
        return sortFn(arr).map(item => {
          if (item.children && item.children.length) {
            item.children = mapFn(item.children)
          }
          delete item.item_order_id
          return item
        })
      }
      const options = mapFn(arr)
      return options
    })
  }

  static getBaseUrl() {
    const protocol = location.protocol || 'https:'
    const domain = window.SDC_DATA_DOMAIN || `${protocol}//${CoreService.isProd() ? '' : 'uat-'}ntsgw.woa.com`
    return domain
  }
}
