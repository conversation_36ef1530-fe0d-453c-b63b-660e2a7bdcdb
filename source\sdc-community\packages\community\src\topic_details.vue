<template>
    <div id="topicDetailsPage">
        <tabHeader :active="1" @changeTab="changeTab" v-show="isShowTab"></tabHeader>
        <header class="topicInfo flex">
            <div class="topicImg" v-if="topicInfo.topic_pic">
                <img :src="topicInfo.topic_pic" alt="">
            </div>
            <section class="flex-1">
                <h3 class="topicName">#{{ topicInfo.topic_name }}</h3>
                <p class="topicDesc">{{ topicInfo.topic_intro }}</p>
                <div class="topicViews flex justify-between">
                    <div>
                        <span>发帖：{{ topicInfo.post_count }}</span>
                        <span>回帖：{{ topicInfo.comment_count }}</span>
                        <span>点赞：{{ topicInfo.like_count }}</span>
                        <span>浏览：{{ topicInfo.view_count }}</span>
                    </div>
                    <div class="postBtn" @click.stop="changePage({path:'/user/post',name:'post',query:{'topic_name':topicInfo.topic_name, 'topic_id':topicInfo.topic_id}});">
                        <img src="../assets/img/send.png" alt="">
                        发布帖子
                    </div>
                </div>
            </section>
        </header>
        <div class="postData flex">
            <section class="postList flex-1" @scroll="handleScroll($event)">
                <ul>
                    <li v-for="(item, index) in postList" :key="item.post_id">
                        <resultItem :item="item" :index="index" :isShowPanel="false" :communityRole="communityRole" :headersData="headersData" :isToTopicsPage="false" :env="env" :showContract="showContract"></resultItem>
                        <!-- <section class="postUser flex justify-between">
                            <div>
                                <div class="flex align-center">
                                    <span class="postType"  v-if="item.post_type === 2 || item.post_type === 3 " :class="{'operate':item.post_type === 2,'notice':item.post_type === 3}">{{item.post_type === 2 ? '运营帖' : (item.post_type === 3 ? '公告帖' : '' )}}</span>
                                    <span class="postName">{{decodeURIComponent(item.post_create_name)}}</span>
                                </div>
                                <p class="postTime">{{item.post_create_time}}</p>
                            </div>
                            <div class="flex align-center" style="height:24px">
                                <img v-if="item.top_status" class="top_status" src="../assets/img/topping.png" alt="">
                                <img v-if="item.excellent_status" class="excellent_status" src="../assets/img/jingh.png" alt="">
                            </div>
                        </section>
                        <section class="postTopics" v-if="item.community_post_topic_list&&item.community_post_topic_list.length>0">
                            <span v-for="(v, i) in item.community_post_topic_list" :key="i">#{{v.topic_name}}</span>
                        </section>
                        <p class="postWord" :style="{'max-height':item.showFullText ? 'none' : '8em'}" :ref="'pText'+index" @click.stop="changePage({path:'/user/post_details',name:'postDetails',query:{'post_id':item.post_id}});" v-html="item.post_word"></p>
                        <div class="collect" v-if="item.showBtn" @click="item.showFullText = !item.showFullText">{{item.showFullText?'收起':'展开'}}</div>
                        <section class="postImgList flex flex-wrap" v-if="item.post_resource">
                            <div class="imgItem flex justify-center align-center" v-for="(n, i) in item.post_resource.split(',')" @click="imgList=item.post_resource.split(',');imageIndex=i;imgShow=true" :key="i">
                                <img :src="n" @load="loadImg($event)" alt="">
                            </div>
                        </section>
                        <section class="postInfo flex justify-between align-center">
                            <span>{{item.view_count}}次浏览</span>
                            <div class="flex align-center">
                                <span class="flex align-center" :class="{'active':item.praise_flag}" @click="praise_post(item, index)">
                                    <img src="../assets/img/thumb-up.png" v-if="!item.praise_flag" alt="">
                                    <img src="../assets/img/thumb-up2.png" v-else alt="">
                                    <span>{{item.praise_count}}次点赞</span>
                                </span>
                                <span class="flex align-center" style="margin-left: 20px;" @click="item.reply_flag=!item.reply_flag;item.reply_word='';item.wordLen=0">
                                    <img src="../assets/img/chat.png" alt="">
                                    <span>{{item.post_result_count}}条回帖</span>
                                </span>
                            </div>
                        </section>
                        <section class="postComments" v-if="item.comments.records&&item.comments.records.length>0">
                            <div v-for="(n, i) in item.comments.records" :key="i">
                                <span class="fontName">{{decodeURIComponent(n.emp_name)}}：</span><span v-html="n.content"></span>
                            </div>
                            <p>共{{item.post_result_count}}条回帖<span @click.stop="changePage({path:'/user/post_details',name:'postDetails',query:{'post_id':item.post_id,'target':1}})">点击查看</span></p>
                        </section>
                        <section class="addComments" v-if="item.reply_flag">
                            <div class="replyWord">
                                <textarea @input="changeWord(item,$event)" :id="'inputTextarea'+index" placeholder="请输入回帖内容" ></textarea>
                                <span class="wordLimit">{{item.wordLen}}/2000</span>
                            </div>
                            <div class="flex justify-end">
                                <el-button size="small"  :class="{'is-disabled':!item.reply_word.replace(/\s/g, '')}" @click="savePostComment(item)" type="primary">发布回帖</el-button>
                            </div>
                        </section> -->
                    </li>
                </ul>
                <p v-if="post_finished" class="post_finished">已经到底了～</p>
            </section>
        </div>
    </div>
</template>
<script>
import tabHeader from './components/tabHeader.vue'
import resultItem from './components/resultItem.vue'
import { getPostList,
    //  postOperate, addComment, getPostDetails, 
    getTopicCommentCount } from '../service/index.js'
import headers from './common/headers'
export default{
    name: 'topicDetailsPage',
    data() {
        return {
            // 话题id
            topic_id: '',
            // 话题详情信息
            topicInfo: {},
            // 查询参数
            searchData: {
                page_no: 1,
                page_size: 10
            },
            // 帖子列表数据
            postList: [],
            // 是否点赞中
            isPraise: false,
            post_loading: false,
            post_finished: false
        }
    },
    mixins: [headers],
    components: {
        tabHeader,
        resultItem
    },
    created() {
        // this.getHeaders(this.initData)
        this.initData()
    },
    methods: {
        async initData() {
            console.log('详题详情_pageInfo',this.pageInfo)
            this.topic_id = this.pageInfo.topic_id
            this.topicInfo.topic_id = this.pageInfo.topic_id
            console.log(this.topic_id)
            console.log(this.topicInfo)
            await this.getTopicCommentCount()
            this.getPostList()
            this.$nextTick(()=>{
                let height = document.getElementsByClassName('topicInfo')[0].clientHeight + 40
                document.getElementsByClassName('postData')[0].style.height = 'calc(100% - ' + height + 'px)'  
            })
        },
        getTopicCommentCount() {
            return getTopicCommentCount({
                params: {
                    topic_id: this.topic_id
                },
                headers: this.headersData
            }).then((res) => {
                this.topicInfo = {
                    ...this.topicInfo,
                    ...res
                }
                this.searchData.topic_name = res.topic_name
                return res
            })
        },
        changeTab(val){
            this.changePage({path:'/user/training',name:'homePage',query:{active:val}})
        },
        // 帖子下拉加载
        handleScroll(event){
            const { scrollTop, clientHeight, scrollHeight } = event.target
            if (this.post_finished) return
            if (Math.ceil(scrollTop) + clientHeight >= scrollHeight - 10 && !this.post_loading) {
                this.post_loading = true
                this.searchData.page_no += 1
                this.getPostList()
            }
        },
        // 获取帖子列表
        getPostList() {
            this.post_loading = true
            getPostList({ params: this.searchData, headers: this.headersData }).then((res) => {
                this.post_loading = false
                // 帖子列表数据返回长度小于10时，代表帖子列表已全部取出
                if (res.records.length < 10) {
                    this.post_finished = true
                }
                this.postList = [...this.postList, ...res.records]
            }).catch(() => {
                this.post_loading = false
                this.post_finished = true
            })
        }
    }
}
</script>
<style lang="less">
@import './common/style.less';
#topicDetailsPage{
    min-width: 1200px;
    margin: 20px auto 0;
    height: calc(100% - 20px);
    overflow: hidden;
    .topicInfo{
        padding: 24px;
        border-radius: 4px;
        background: linear-gradient(156deg, #F1F8FF 13.97%, #F1F8FF 13.98%, #F8FEFF 44.69%, #FEFFFF 98.79%);
        box-shadow: 0 0 12px 0 #F2F2F2;
        .topicImg{
            width: 120px;
            height: 120px;
            margin-right: 34px;
            border-radius: 9px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fafafa;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .topicName{
            color: #000000e6;
            font-size: 18px;
            font-weight: 600;
            line-height: 26px;
        }
        .topicDesc{
            word-break: break-all;
            margin-top: 14px;
            color: #00000099;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
        }
        .topicViews{
            margin-top: 4px;
            align-items: flex-end;
            span{
                color: #00000099;
                font-size: 14px;
                font-weight: 400;
                line-height: 22px;
                margin-right: 12px;
            }
            .postBtn{
                width: 120px;
                height: 32px;
                color: #ffffff;
                font-weight: 600;
                font-size: 14px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: var(--Brand-Brand7-Normal, #0052D9);
                cursor: pointer;
                img{
                    width: 22px;
                    margin-right: 4px;
                }
            }
        }
    }
    .postData{
        min-width: 1200px;
        margin: 20px auto;
        height: calc(100% - 228px);
        overflow: hidden;
        box-shadow: 0 0 12px 0 #F2F2F2;
        border-radius: 4px;
        padding: 24px 0;
        background: #FFF;
        overflow: hidden;
        .postList{
            padding: 0 24px;
            height: 100%;
            overflow: auto;
            >ul{
                >li{
                    border-bottom: 1px solid #F2F3F5;
                    padding-bottom: 16px;
                    margin-bottom: 16px;
                    &:last-child{
                        border-bottom: none;
                        margin-bottom: 0;
                    }
                }
            }
            .post_finished{
                color: #a6a6a6ff;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
                text-align: center;
            }
        }
        .postSide{
            width: 268px;
            margin-left: 20px;
            header{
                padding: 20px 0;
                border-radius: 6px;
                background: #FFF;
                box-shadow: 0 0 12px 0 #F2F2F2;
                justify-content: center;
                div{
                    text-align: center;
                    padding: 0 35px;
                    position: relative;
                    cursor: pointer;
                    &:last-child::after{
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 17px;
                        height: 36px;
                        width: 1px;
                        background: #EEE;
                    }
                    img{
                        width: 40px;
                        height: 40px;
                    }
                    p{
                        color: #000000e6;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 22px;
                        margin-top: 8px;
                    }
                }
            }
        }
        .topicData{
            height: 330px;
            margin-top: 20px;
            padding: 20px 12px 12px 12px;
            background: linear-gradient(156deg, #F1F8FF 13.97%, #F8FEFF 44.69%, #FAFEFF 98.79%);
            border-radius: 6px;
            box-shadow: 0 0 12px 0 #F2F2F2;
            position: relative;
            .topicName{
                padding-left: 12px;
                position: relative;
                .topicTitleImg{
                    width: 24px;
                }
                span{
                    color: #000000e6;
                    font-size: 20px;
                    font-weight: 600;
                    line-height: 28px;
                    margin-left: 8px;
                }
                .rightImg{
                    position: absolute;
                    right: -2px;
                    top: -10px;
                    width: 60px;
                }
            }
            .topicList{
                position: relative;
                z-index: 2;
                min-height: 258px;
                max-height: 600px;
                margin-top: 12px;
                padding: 16px 12px;
                border-radius: 6px;
                background: #FFF;
                overflow: auto;
                li{
                    margin-bottom: 12px;
                    color: #000000e6;
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: 400;
                    cursor: pointer;
                    &:hover{
                        color: #266FE8;
                    }
                }
                .no_topic{
                    text-align: center;
                    img{
                        margin: 38px 54px 0;
                        width: 112px;
                    }
                    p{
                        margin-top: 16px;
                        color: #000000ff;
                        font-size: 16px;
                        font-weight: 400;
                        font-family: "PingFang SC";
                    }
                }
            }
        }
    }
}

</style>
