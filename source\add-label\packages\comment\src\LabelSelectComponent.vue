<template>
  <div class="label-select-component">
    <div class="cascader-component">
      <div
        class="cascader-select"
        :class="[{ focus: isShowDropdown }]"
        @click.stop="togglePopper(true)"
      >
        <div class="select-input">
          <input
            type="text"
            v-model.trim="inputVal"
            spellcheck="false"
            maxlength="20"
            :placeholder="placeholder"
            @keydown.enter="handleEnter"
            @input="search"
          />
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </div>
        <div class="clear" v-if="showClear">
          <span class="icon" @click.stop="clear">×</span>
        </div>
      </div>
      <div
        class="cascader-dropdown"
        infinite-scroll-disabled="false"
        infinite-scroll-immediate="true"
        infinite-scroll-distance="1"
        v-infinite-scroll="handleScroll"
        v-show="searchList.length && isShowDropdown && yanzheng"
        @click.stop="togglePopper(true)"
      >
        <!-- 搜索面板 -->
        <div class="search-panel" v-if="inputVal">
          <template v-if="searchList.length">
            <el-checkbox
              class="search-item"
              style="z-index: 99999999999999"
              v-for="(item, index) in searchList"
              :key="item.label_id + index"
              :label="item"
              :value="isChecked(item.label_id)"
              @change="changeSelectedLabel(item)"
            >
              <span class="label" v-html="item.label_full_name"></span>
            </el-checkbox>
          </template>
        </div>
      </div>
      <div
        class="cascader-dropdown"
        v-show="!searchList.length && !isDataLoading"
        @click.stop="togglePopper(true)"
      >
        <div class="search-panel" v-if="inputVal">
          <template v-if="!searchList.length && !isDataLoading">
            <span style="font-size: 12px"
              >暂无匹配数据，<span
                style="color: #000000; font-size: 12px"
                size="small"
                >敲击回车快速创建标签</span
              ></span
            >
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
const debounce = (fn, wait = 200) => {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this)
    }, wait)
  }
}
export default {
  props: {
    // 双向绑定值
    value: {
      type: Array,
      default: () => []
    },
    tags: {
      type: Array,
      default: () => []
    },
    newTags: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请输入标签名称'
    },
    // 禁止回车创建
    disableCreate: {
      type: Boolean,
      default: false
    },
    // 是否有推荐（课程名称-title、描述-desc）
    recommend: {
      type: Object,
      default () {
        return null
      }
    },
    // 最多选择数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 可清空
    clearable: {
      type: Boolean,
      default: true
    },
    showLabelList: {
      type: Array,
      default: () => []
    },
    labelNodeEnv: {
      type: String,
      default: 'production'
    }
  },
  data () {
    return {
      inputVal: '',
      isEnter: false,
      isDataLoading: true,
      searchList: [],
      recommendList: [],
      labelOptions: [],
      categoryTree: [
        { title: '一级分类', data: [], active: 0 },
        { title: '二级分类', data: [], active: 0 },
        { title: '三级分类', data: [], active: 0 },
        { title: '四级分类', data: [], active: 0 }
      ],
      selectedLabels: [], // 已选择标签
      isShowDropdown: false, // 显示下拉弹窗
      urlInfo: {
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      page: {
        current: 1,
        size: 6,
        total: 0,
        isRequesting: false
      },
      isRequesting: false,
      yanzheng: false
    }
  },
  computed: {
    // 显示清空按钮
    showClear () {
      return this.clearable && this.inputVal
    },
    isChecked () {
      return label_id => {
        const _index = this.selectedLabels.findIndex(
          item => item.label_id === label_id
        )
        return _index > -1
      }
    },
    // 是否显示底部推荐
    showRecommend () {
      return !!this.recommend
    },
    commonUrl () {
      return location.hostname.endsWith('.woa.com')
        ? this.urlInfo[this.labelNodeEnv]
        : this.urlInfo[this.labelNodeEnv]
    }
  },
  watch: {
    value: {
      handler (newVal) {
        this.selectedLabels = newVal
      },
      immediate: true
    },
    'recommend.title' () {
      this.getRecommendLabels()
    },
    'recommend.desc' () {
      this.getRecommendLabels()
    },
    selectedLabels (newVal) {
      this.$emit('input', newVal)
      this.$emit('getSelectedLabelList', newVal)
    }
  },
  mounted () {
    this.timer = null
    this.addLabel()
    document.addEventListener('click', this.hideDropdown)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.hideDropdown)
  },
  methods: {
    handleEnter (event) {
      event.preventDefault() //阻止页面自动刷新
      if (this.isEnter || !this.inputVal) return
      this.isEnter = true
      axios
        .get(
          `${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            //精准搜索是否又匹配数据
            params: {
              page_no: this.page.current,
              page_size: 6,
              label_name: this.inputVal,
              label_type: '5',
              order_by: 'level'
            },
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.data.records.length > 0) {
            //有匹配数据，就选中
            const data = this.newTags.find(item => item.label_id === res.data.data.records[0].label_id)
            if (data === undefined) {
              let addLabel = {
                label_name: this.inputVal,
                clicked: true,
                label_id: res.data.data.records[0].label_id
              }
              this.$emit('push-tag', addLabel)
              this.inputVal = ''
              this.isEnter = false
            } else {
              this.inputVal = ''
              this.isEnter = false
            }
          } else {
            //无匹配数据，就创建
            axios
              .post(
                `${this.commonUrl}/training/api/label/user/labelinfo/user-insert-label`,
                {
                  label_name: this.inputVal
                },
                {
                  withCredentials: true
                }
              )
              .then(res => {
                if (res.data.code === 200) {
                  let addLabel = {
                    label_name: this.inputVal,
                    clicked: true,
                    label_id: res.data.data
                  }
                  this.$emit('push-tag', addLabel)
                  this.inputVal = ''
                  this.isEnter = false
                } else {
                  this.isEnter = false
                }
              })
          }
        })
    },
    showInput () {
      const inputVisible = true
      this.isShowDropdown = false
      this.$emit('custom-name', inputVisible)
    },
    addLabel () {
      // 在这里实现添加标签的逻辑
      this.selectedLabels.push(this.tags)
    },
    // 推荐标签
    getRecommendLabels: debounce(function () {
      if (!this.showRecommend) {
        return
      }
      axios
        .post(
          `${this.commonUrl}/training/api/businessCommon/manage/label/get_recommend_labels_v2`,
          {
            object: 'online_course',
            title: (this.recommend && this.recommend.title) || '',
            content: (this.recommend && this.recommend.desc) || ''
          },
          {
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            this.recommendList = res.data.data.map(item => {
              return {
                ...item,
                label_name: item.name
              }
            })
          }
        })
    }, 1500),
    handleScroll () {
      if (!this.isRequesting) {
        // 如果没有在请求数据
        this.isRequesting = true // 设置标志为 true，表示开始请求数据
        setTimeout(() => {
          if (this.page.total > this.searchList.length) {
            this.page.current++
            this.loadMoreTags()
          }
          this.isRequesting = false // 请求完成后，设置标志为 false
        }, 500)
      }
    },
    loadMoreTags () {
      this.page.isRequesting = true
      axios
        .get(
          `${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params: {
              page_no: this.page.current,
              page_size: 6,
              search_name: this.inputVal,
              label_type: '5',
              order_by: 'level'
            },
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            this.isDataLoading = false
            this.page.total = res.data.data.total
            if (res.data.data.records) {
              const highlightedArray = res.data.data.records.map(obj => {
                const highlightedName = `<font style="font-size: 14px;margin-right: 8px;" color="#000000">${obj.label_name.replace(
                  new RegExp(this.escapeRegExp(this.inputVal), 'g'),
                  '<font color="#0052D9">$&</font>'
                )}</font>`
                const highlightedCategory = `<font style="font-size: 12px;" color="#153000000">${obj.category_full_name.replace(
                  new RegExp(this.escapeRegExp(this.inputVal), 'g'),
                  '<font color="#0052D9">$&</font>'
                )}</font>`
                const labelFullName = `${highlightedName}   ${highlightedCategory}`
                return { ...obj, label_full_name: labelFullName }
              })
              console.log(highlightedArray)
              if (this.page.current > 1) {
                this.searchList.push(...highlightedArray)
              } else {
                this.searchList = highlightedArray || []
              }
            }
          }
        })
        .finally(() => {
          this.page.isRequesting = false
        })
    },
    escapeRegExp (string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    },
    // 搜索
    getSearchList: debounce(function () {
      this.yanzheng = false
      axios
        .get(
          `${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params: {
              page_no: 1,
              page_size: 6,
              search_name: this.inputVal,
              label_type: '5',
              order_by: 'level'
            },
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            this.yanzheng = false
            this.isDataLoading = false
            this.page.total = res.data.data.total
            if (res.data.data.records) {
              const highlightedArray = res.data.data.records.map(obj => {
                const highlightedName = `<font style="font-size: 14px;margin-right: 8px;" color="#000000">${obj.label_name.replace(
                  new RegExp(this.escapeRegExp(this.inputVal), 'g'),
                  '<font color="#0052D9">$&</font>'
                )}</font>`
                const highlightedCategory = `<font style="font-size: 12px;" color="#153000000">${obj.category_full_name.replace(
                  new RegExp(this.escapeRegExp(this.inputVal), 'g'),
                  '<font color="#0052D9">$&</font>'
                )}</font>`
                const labelFullName = `${highlightedName}   ${highlightedCategory}`
                return { ...obj, label_full_name: labelFullName }
              })
              console.log(highlightedArray)
              if (this.page.current > 1) {
                this.searchList.push(...highlightedArray)
              } else {
                this.searchList = highlightedArray || []
                this.yanzheng = true
              }
            }
          }
        })
    }, 200),
    // 搜索
    search () {
      if (!this.inputVal) {
        this.$emit('search-visibleTags',this.inputVal)
        return
      }
      this.page.current = 1
      this.yanzheng = false
      const reg = /[^a-zA-Z0-9-+#&\\/()（）\u4e00-\u9fa5\s]/g
      if (reg.test(this.inputVal)) {
        this.inputVal = this.inputVal.replace(reg, '')
        return
      }
      this.yanzheng = false
      if (
        this.inputVal &&
        String(this.inputVal) === this.inputVal &&
        this.inputVal !== undefined &&
        this.inputVal !== null &&
        this.inputVal !== ''
      ) {
        this.$emit('search-visibleTags',this.inputVal)
        this.getSearchList()
      } else {
        this.yanzheng = false
      }
    },
    // 当前标签添加或删除管理
    changeSelectedLabel (label, type) {
      const _index = this.selectedLabels.findIndex(item => {
        return (
          item.label_id === label.label_id &&
          item.label_name === label.label_name
        )
      })
      if (_index === -1) {
        label.clicked = true
        this.selectedLabels.push(label)
        this.$emit('update-labels', label)
        if (type === 'create') {
          this.inputVal = ''
        }
      } else {
        // 点击推荐如果存在则不删除
        if (type === 'recommend' || type === 'create') {
          return
        }
        label.clicked = false
        this.selectedLabels.splice(_index, 1)
      }
    },
    // 显示隐藏面板
    togglePopper (visible) {
      const isDef = visible !== undefined && visible !== null
      this.isShowDropdown = isDef ? visible : !this.isShowDropdown
    },
    // 下拉框隐藏事件
    hideDropdown () {
      this.isShowDropdown = false
      this.$emit('hideDropdwon', this.selectedLabels)
    },
    deleteLabel (label) {
      const _index = this.selectedLabels.findIndex(item => {
        return item.label_id === label.label_id
      })
      this.selectedLabels.splice(_index, 1)
    },
    // 清空标签
    clear () {
      this.inputVal = ''
      this.$emit('search-visibleTags',this.inputVal)
    }
  }
}
</script>

<style lang="less" scoped>
.label-select-component {
  width: 100%;
  .cascader-component {
    position: relative;
  }
  .cascader-select {
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    &.focus {
      border: 1px solid #0052d9ff;
    }
    .select-input {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .tag {
        max-width: 100%;
        height: 24px;
        background: #e7e7e7;
        border-radius: 4px;
        box-sizing: border-box;
        color: rgba(0, 0, 0, 0.9);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-flex;
        align-items: center;
        margin: 3px 0 3px 4px;
        padding: 0 8px;
        .tag-close {
          width: 16px;
          height: 16px;
          color: rgba(0, 0, 0, 0.4);
          font-size: 14px;
          font-weight: bold;
          font-style: normal;
          line-height: 16px;
          text-align: center;
          margin-left: 2px;
          cursor: pointer;
        }
      }
      & > input {
        flex: 1;
        height: 30px;
        min-width: 60px;
        padding: 2px 8px;
        color: #606266;
        font-size: 14px;
        border: 0;
        outline: none;
        box-sizing: border-box;
        &::placeholder {
          color: rgba(0, 0, 0, 0.4);
          font-size: 13px;
        }
      }
    }
    .clear {
      margin-right: 8px;
      display: flex;
      position: relative;
      .icon{
          width: 14px;
          height: 14px;
          text-align: center;
          line-height: 14px;
          border-radius: 50%;
          background-color:  rgba(0, 0, 0, 0.4);
          color: #fff;
          cursor: pointer;
      }
    }
  }
  .recommend {
    margin-top: 6px;
    line-height: 24px;
    display: flex;
    .tip {
      color: #666;
      font-size: 12px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      & > i {
        margin-right: 5px;
        font-size: 14px;
      }
    }
    .recommend-label {
      margin-left: 16px;
      .tag {
        max-width: 100%;
        height: 24px;
        background: #e7e7e7;
        border-radius: 4px;
        box-sizing: border-box;
        color: rgba(0, 0, 0, 0.9);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-flex;
        align-items: center;
        margin: 0 4px;
        padding: 0 8px;
        cursor: pointer;
      }
    }
  }
  .cascader-dropdown {
    overflow-y: auto;
    max-height: 220px;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    background: #ffffffff;
    box-shadow: 0 3px 14px 2px rgba(0, 0, 0, 0.1),
      0 8px 10px 1px rgba(0, 0, 0, 0.06), 0 5px 5px -3px rgba(0, 0, 0, 0.05);
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 6px;
    z-index: 999999999999999;
    .cascader-panel {
      display: flex;
      .cascader-list {
        min-width: 180px;
        max-height: 320px;
        overflow-y: auto;
        padding: 8px;
        &:not(:last-of-type) {
          border-right: 1px solid #e7e7e7;
        }
      }
      .cascader-node {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        margin: 0;
        &:hover {
          background: #ecf2fe;
        }
        &.active {
          background: #ecf2fe;
          color: #0052d9;
        }
      }
    }
    .search-panel {
      min-width: 220px;
      padding: 8px;
      .search-item {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        &:hover {
          background: #ecf2fe;
        }
        &.active {
          background: #ecf2fe;
          color: #0052d9;
        }
      }
      .search-empty {
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
