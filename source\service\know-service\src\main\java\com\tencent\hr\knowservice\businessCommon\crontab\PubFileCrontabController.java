package com.tencent.hr.knowservice.businessCommon.crontab;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfo;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileDto;
import com.tencent.hr.knowservice.businessCommon.service.PubFileInfoService;
import com.tencent.hr.knowservice.businessCommon.service.PubFileInfoTaskService;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/crontab/pub-file/")
public class PubFileCrontabController {


    @Autowired
    private PubFileInfoTaskService pubFileInfoTaskService;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    PubFileInfoService fileInfoService;

    /**
     * 定时任务-同步素材转码结果
     *
     * @return
     */
    @PostMapping("syncFileInfo")
    public TransDTO syncFileInfo() {
        pubFileInfoTaskService.syncPubFileInfoStatus();
        return new TransDTO<>().withSuccess(true).withMessage("操作成功!").withCode(HttpStatus.SC_OK);
    }
}
