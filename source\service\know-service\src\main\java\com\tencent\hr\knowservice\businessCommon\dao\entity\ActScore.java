package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * act_score
 * <AUTHOR>
@Data
public class ActScore implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 网课/直播id
     */
    private String actId;

    /**
     * 单据类型( 2 网络课 4 活动 5 直播 11 慕课)
     */
    private Byte actType;

    /**
     * 评分人Id
     */
    private Integer staffId;

    /**
     * 分数
     */
    private Double score;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}