package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.constans.TaskCodeEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.Tip;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.TipMapper;
import com.tencent.hr.knowservice.businessCommon.dto.common.PopupAndStaffMsgDTO;
import com.tencent.hr.knowservice.businessCommon.dto.common.TaskListResultDTO;
import com.tencent.hr.knowservice.businessCommon.proxy.LearnPointApi;
import com.tencent.hr.knowservice.framework.advice.exception.CustomException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class TipService {

    @Autowired
    private TipMapper tipMapper;

    @Autowired
    private LearnPointApi learnPointApi;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 关闭提示弹窗
     *
     * @param actType
     * @return
     */
    public Boolean closePopup(Integer actType,Integer staffId,String staffName) {
        String actTypeName = ActTypeEnum.getActTypeName(actType);
        if (actTypeName.equals(ActTypeEnum.UNKNOWN.getActTypeName())) {
            throw new CustomException("actType不存在！");
        }
        ContextEntity current = GatewayContext.current();
        if (staffId == null) {
            String id = current.getStaffId();
            if (id == null){
                throw new CustomException("staffId is null");
            }
            staffId = Integer.valueOf(id);
        }
        if (staffName == null) {
            staffName = current.getStaffName();
            if (staffName == null){
                throw new CustomException("staffId is null");
            }
        }
        QueryWrapper<Tip> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", 1);
        queryWrapper.eq("creator_id", staffId);
        queryWrapper.eq("act_type", actType);
        Tip tip = tipMapper.selectOne(queryWrapper);
        
        if (tip != null) {
            if (tip.getTipStatus() == 1) {
                return true;
            } else {
                UpdateWrapper<Tip> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("tip_status", 1);
                updateWrapper.set("update_id", staffId);
                updateWrapper.set("update_name", staffName);
                updateWrapper.set("updated_at", new Date());
                updateWrapper.eq("id", tip.getId());
                return tipMapper.update(null, updateWrapper) > 0;
            }
        } else {
            Tip addTip = new Tip();
            addTip.setActType(actType);
            addTip.setActTypeName(actTypeName);
            addTip.setTipStatus(1);
            addTip.setCreatorAndUpdate(staffId, staffName);
            return tipMapper.insert(addTip) > 0;
        }

    }

    /**
     * 获取是否关闭弹窗信息
     * @param actType
     * @return
     */
    public PopupAndStaffMsgDTO getPoPupMsg(Integer actType,Integer staffId) {
        String actTypeName = ActTypeEnum.getActTypeName(actType);
        if (actTypeName.equals(ActTypeEnum.UNKNOWN.getActTypeName())) {
            throw new CustomException("actType不存在！");
        }
        PopupAndStaffMsgDTO popupAndStaffMsgDTO = new PopupAndStaffMsgDTO();
        ContextEntity current = GatewayContext.current();
        if (staffId == null) {
            String id = current.getStaffId();
            if (id == null){
                throw new CustomException("staffId is null");
            }
            staffId = Integer.valueOf(id);

        }

        //获取是否是正式员工
        TransDTO data = learnPointApi.isFormalStaff(staffId);

        Boolean isFormalStaff = (Boolean) data.getData();
        popupAndStaffMsgDTO.setIsFormalStaff(isFormalStaff);
        QueryWrapper<Tip> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable", 1);
        queryWrapper.eq("creator_id", staffId);
        queryWrapper.eq("act_type", actType);
        Tip tip = tipMapper.selectOne(queryWrapper);
        StringBuilder sb = new StringBuilder();
        //处理qlearning调用的
        if (actType.equals(ActTypeEnum.QLEARNING.getActType())) {
            sb.append(TaskCodeEnum.TASK_PUBLISH_COURSELIST.getCode())
                    .append(",")
                    .append(TaskCodeEnum.TASK_PUBLISH_CASE.getCode())
                    .append(",")
                    .append(TaskCodeEnum.TASK_PUBLISH_ARTICLE.getCode());


        } else {
            //处理不是qlearning调用的
            String code = TaskCodeEnum.getCodeByActType(actType);
            if (StringUtils.isBlank(code)){
                throw new CustomException("首次发表枚举中不存在此actType");
            }
            sb.append(code);
        }
        //默认没关闭提示弹窗
        popupAndStaffMsgDTO.setIsClosePopup(false);
        //用户已主动关闭弹窗
        if (tip != null && tip.getTipStatus() == 1){
            popupAndStaffMsgDTO.setIsClosePopup(true);
        }else {
            //用户任务结果查询
            TransDTO<List<TaskListResultDTO>> result = learnPointApi.resultList(sb.toString(), staffId);
            List<TaskListResultDTO> taskListResultDTOS = result.getData();
            if (CollectionUtils.isNotEmpty(taskListResultDTOS)){
                popupAndStaffMsgDTO.setIsClosePopup(true);
                for (TaskListResultDTO taskListResultDTO : taskListResultDTOS) {
                    if (taskListResultDTO.getTaskStatus()  == 0){
                        //有一个是未完成就不关闭弹窗
                        popupAndStaffMsgDTO.setIsClosePopup(false);
                        break;
                    }
                }
            }
        }
        return popupAndStaffMsgDTO;
    }

    /**
     * 弹窗展示控制开关
     * @return
     */
    public Boolean displayControl() {
        try {
            Boolean result = (Boolean) redisUtil.get(CommonCacheKeyEnum.GraphicDisplayControl.getKeyName());
            if (result == null){
                redisUtil.set(CommonCacheKeyEnum.GraphicDisplayControl.getKeyName(),true);
                return true;
            }else {
                return result;
            }
        } catch (Exception e) {
            return true;
        }
    }
}
