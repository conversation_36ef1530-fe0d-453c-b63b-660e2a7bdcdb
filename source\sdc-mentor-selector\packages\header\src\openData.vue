<template>
    <span>
      <ww-open-data v-show="enableOpenData" :type="type" :openid="openid" ref="opendata"/>
      <span v-show="!enableOpenData">{{openid}}</span>
    </span>
</template>
<script>
/**
 * 企业微信通讯录展示组件 https://work.weixin.qq.com/api/doc/90001/90143/91958
 * 若 type=userName，此时 openid 对应 userid
 * 若 type=departmentName，此时 openid 对应 departmentid
 * 每 20ms 最多绑定 1000 个 open-data 元素，超出的部分将被忽略
 */
export default {
  name: 'openData',
  props: ['type', 'openid'],
  data () {
    return {
      // 是否启用WWOpenData
      enableOpenData: true
    }
  },
  mounted () {
    this.$bus.$on('initOpenDataComplate', this.bind)
  },
  methods: {
    bind (enable) {
      // console.log('initOpenDataComplate', enable)
      if (window.WWOpenData && enable) {
        window.WWOpenData.bind(this.$refs.opendata)
      } else {
        this.enableOpenData = false
        // console.info('WWOpenData is not defined')
      }
    }
  }
}
</script>
