<template>
  <div class="sdc-exception-page" :class="customClass">
    <div class="wrapper">
      <img :src="imgUrl" :alt="code" v-if="imgUrl"/>
      <div class="content">
        <slot>
          <p class="title">{{$st(`sdc.exception.error.${code}`)}}</p>
          <div class="links">
            <sdc-link v-if="code==='401'" :to="authority">{{$st('sdc.exception.apply')}}</sdc-link>
            <sdc-link v-if="code==='404'" @click="$router.back(-1)" >{{$st('sdc.exception.back')}}</sdc-link>
            <span class="or">{{$st('sdc.exception.or')}}</span>
            <sdc-link :to="home">{{$st('sdc.exception.home')}}</sdc-link>
          </div>
          <p class="info">{{$st('sdc.exception.contactUs')}}</p>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
  import { locale, classes } from 'mixins'
  import SdcLink from 'packages/link'

  export default {
    name: 'sdc-exception-page',
    mixins: [locale, classes],
    props: {
      code: {
        type: String,
        required: true
      },
      authority: {
        type: String,
        default: '/authority'
      },
      home: {
        type: String,
        default: '/'
      }
    },
    computed: {
      imgUrl() {
        if (this.code === '401') {
          return require('packages/theme-grace/img/401.png')
        } else if (this.code === '404') {
          return require('packages/theme-grace/img/404.png')
        }
        return ''
      }
    },
    components: {
      SdcLink
    }
  }
</script>
