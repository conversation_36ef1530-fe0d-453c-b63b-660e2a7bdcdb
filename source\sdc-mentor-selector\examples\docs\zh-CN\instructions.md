## 组件使用说明
> 贡献者：v_whaigong(龚文海)；最近更新时间：2023-11-24；

当项目中使用到有拉取远程数据的组件时，需要按以下步骤进行配置，可解决请求跨域问题。

#### 1. hosts 配置

业务组件中的http请求需要本地配置hosts才能请求成功。
- 记事本打开hosts文件，window系统在此目录下
```shell
C:\Windows\System32\drivers\etc
```
- 在hosts文件中输入并保存
```shell
127.0.0.1 test.woa.com
```
配置hosts之后要清除DNS缓存才能生效
- 命令行(cmd)运行
```shell
ipconfig /flushdns
```
#### 2. 环境变量配置

需要在项目public/index.html下配置全局变量。

```js
window.SDC_BUILD_ENV = 'dev' // 打包开发/测试环境
window.SDC_BUILD_ENV = 'prd' // 打包生产环境
```

```html
<!DOCTYPE html>
<html>
  <head>
    <title><%= webpackConfig.name %></title>
    <meta charset="utf-8" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"/>
    <link href="<%= BASE_URL %>favicon.ico" rel="icon" />
    <script>
      window.SDC_BUILD_ENV = 'dev' // 打包开发/测试环境
      window.SDC_BUILD_ENV = 'prd' // 打包生产环境
      window.SDC_BUILD_ENV = '<%= VUE_APP_BUILD_ENV %>' // 动态设置当前环境变量
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
```
#### 3. IP配置

需要在项目vue.config.js下配置host。

```js
module.exports = {
  devServer: {
    host: 'test.woa.com'
  }
}
```
#### 4. 访问路径

启动项目后，通过下面路径访问页面。
<br>注：如果还跨域，直接访问跨域的链接，登录后再返回刷新页面

```js
  test.woa.com+端口号
```

### 自定义请求域名

如果需要自己做请求转发，可以通过配置`window.SDC_DATA_DOMAIN`实现

```js
window.SDC_DATA_DOMAIN = 'https://xxx.woa.com'
```

### 使用测试环境数据源

如果需要使用测试环境的数据源，可以打开新页面在路径后面拼接?sdcuiEnvironment=test

```js
https://sdcwebui.pages.woa.com/#/zh-CN/component/instructions?sdcuiEnvironment=test
```