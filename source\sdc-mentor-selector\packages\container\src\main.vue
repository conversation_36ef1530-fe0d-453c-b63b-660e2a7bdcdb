<template>
  <div class="sdc-container" :class="containerClass">
    <slot></slot>
  </div>
</template>

<script>
  import { oneOf } from 'sdc-webui/src/utils/main'
  import { classes } from 'mixins'

  export default {
    name: 'sdc-container',
    mixins: [classes],
    props: {
      direction: {
        type: String,
        default: 'vertical', // horizontal, vertical
        validator(val) {
          return val && oneOf(val, ['horizontal', 'vertical'], 'direction')
        }
      }
    },
    computed: {
      containerClass() {
        return [this.customClass, this.direction === 'vertical' ? 'vertical' : '']
      }
    }
  }
</script>
