package com.tencent.hr.knowservice.businessCommon.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dao.entity.VBaseEmpInfo;
import com.tencent.hr.knowservice.businessCommon.service.BaseEmpInfoService;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/api/common/user/userinfo/")
public class UserInfoController {

    @Autowired
    BaseEmpInfoService empInfoService;

    /**
     * 获取登录人员工信息
     *
     * @return
     */
    @GetMapping("baseinfo")
    public TransDTO getEmpBaseInfo() {
        ContextEntity context = GatewayContext.current();
        Integer staffId = Integer.valueOf(context.getStaffId());
        TransDTO dto = new TransDTO<>().withCode(HttpStatus.SC_OK).withSuccess(true);
        VBaseEmpInfo result = empInfoService.getEmpInfo(staffId);
        dto.withData(result);
        return dto;
    }

    /**
     * 缓存所有员工信息
     *
     * @return
     */
    @PostMapping("cache_all")
    public TransDTO cacheAllUserInfo() {
        TransDTO dto = new TransDTO<>().withCode(HttpStatus.SC_OK).withSuccess(true);
        Integer result = empInfoService.cacheAllEmpInfo();
        dto.withData(result);
        return dto;
    }

}
