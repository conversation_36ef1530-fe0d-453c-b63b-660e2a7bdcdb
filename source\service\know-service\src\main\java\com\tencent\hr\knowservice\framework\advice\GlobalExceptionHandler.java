package com.tencent.hr.knowservice.framework.advice;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.framework.advice.exception.AuthException;
import com.tencent.hr.knowservice.framework.advice.exception.CustomException;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.advice.exception.NotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Properties;

/**
 * 全局异常处理
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GlobalExceptionHandler {
    @Value("${spring.profiles.active}")
    private String env;
    /**
     * 处理业务逻辑异常，异常信息可以对外显示
     * 错误代码400
     * @param e
     * @return
     */
    @ExceptionHandler(LogicException.class)
    public TransDTO<String> logicExceptionHandler(LogicException e) {
        log.warn("业务逻辑异常，异常信息={}", e.getMessage(), e);
        TransDTO<String> result = new TransDTO<>();
        result.setSuccess(false);
        result.setMessage(e.getMessage());
        if (e.getCode() == null) {
            e.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
        result.setCode(e.getCode());
        return result;
    }

    /**
     * 处理没有找到数据权限异常
     * 错误码404
     * @param e
     * @return
     */
    @ExceptionHandler(NotFoundException.class)
    public TransDTO<String> notFindExceptionHandler(NotFoundException e){
        log.warn("数据不存在，异常信息={}",e.getMessage(),e);
        TransDTO<String> result = new TransDTO<>();
        result.setSuccess(false);
        result.setMessage("数据不存在");
        result.setCode(HttpStatus.NOT_FOUND.value());
        return result;
    }

    /**
     * 处理权限异常，异常信息可以对外显示
     * 错误码403
     * @param e
     * @return
     */
    @ExceptionHandler(AuthException.class)
    public TransDTO<String> authExceptionHandler(AuthException e) {
        log.warn("权限异常，异常信息={}", e.getMessage(), e);
        TransDTO<String> result = new TransDTO<>();
        result.setSuccess(false);
        String message = e.getMessage();
        if (StringUtils.isEmpty(message)) {
            message = "暂无访问权限";
        }
        result.setMessage(message);
        result.setCode(HttpStatus.FORBIDDEN.value());
        return result;
    }

    /**
     * 处理自定义异常，异常信息不可以对外展示
     * @param e
     * @return
     */
    @ExceptionHandler(CustomException.class)
    public TransDTO<String> customExceptionHandler(CustomException e){
        log.error("自定义异常，异常信息={}",e.getMessage(),e);
        TransDTO<String> result = new TransDTO<>();
        result.setSuccess(false);
        result.setMessage(getExceptionMsg(e));
        if (e.getCode() == null) {
            e.setCode(-100);
        }
        result.setCode(e.getCode());
        return result;



    }

    /** 其他异常交给错误日志框架处理
     *
     * 处理未分类的异常,异常信息不对外进行输出
     * @param e
     * @return
     */
    /*@ExceptionHandler(Exception.class)
    public TransDTO<String> exceptionHandler(Exception e){
        log.error("系统异常，异常信息={}",e.getMessage(),e);
        TransDTO<String> result = new TransDTO<>();
        result.setSuccess(false);
        result.setMessage(getExceptionMsg(e));
        result.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return result;
    }*/

    /**
     * 非正式环境就输出错误到前端
     * @param ex
     * @return
     */
    private String getExceptionMsg(Exception ex){
        String msg = "网络异常，请稍后重试!";

        if(!env.equals("prod") && !env.equals("eprod")){
            StringWriter stringWriter = new StringWriter();
            PrintWriter printWriter = new PrintWriter(stringWriter);
            ex.printStackTrace(printWriter);
            msg = stringWriter.toString();
            printWriter.close();
        }
        return msg;
    }

}
