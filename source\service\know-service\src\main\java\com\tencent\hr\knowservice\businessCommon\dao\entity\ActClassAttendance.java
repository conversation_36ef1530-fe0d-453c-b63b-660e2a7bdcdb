package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * act_class_attendance
 * <AUTHOR>
@Data
public class ActClassAttendance implements Serializable {
    /**
     * 考勤Id(主键)
     */
    private Integer attId;

    /**
     * 班级id
     */
    private Integer classId;

    /**
     * 单据类型(3 班级 4 活动)
     */
    private Byte actType;

    /**
     * 课程Id
     */
    private Integer courseId;

    /**
     * 学员Id
     */
    private Integer staffId;

    /**
     * 学员姓名
     */
    private String empName;

    /**
     * 报名类型（0 未报名 1 已报名 2 未报名并霸课 ）
     */
    private Byte regType;

    /**
     * 注册时间
     */
    private Date regDate;

    /**
     * 当前状态
     */
    private Short status;

    /**
     * 当前状态名称
     */
    private String statusName;

    /**
     * 当前处理人
     */
    private Integer handlemanId;

    /**
     * 当前处理人姓名
     */
    private String handlemanName;

    /**
     * 得分
     */
    private BigDecimal score;

    /**
     * 是否接收调查问卷
     */
    private Byte isReceiveSurvey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发送异常考勤的时间
     */
    private Date sendAbnormalMailTime;

    private Byte isMobile;

    /**
     * 老系统id
     */
    private Integer oldSyncId;

    /**
     * 删除时间
     */
    private Date deletedAt;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}