package com.tencent.hr.knowservice.framework.advice.exception;

import com.tencent.hr.knowservice.businessCommon.constans.Constants;

/**
 * @description: 没有找到数据异常
 * @author:
 * @createDate: 2020/6/6
 * @version: 1.0
 */
public class NotFoundException extends RuntimeException {

    private Integer code;
    private String message;

    public NotFoundException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.message = msg;
    }

    public NotFoundException(Constants.ErrorCodeEnum error) {
        super(error.getMessage());
        this.code = error.getCode();
        this.message = error.getMessage();
    }

    public NotFoundException(String msg) {
        super(msg);
        this.message = msg;
    }

    public Integer getCode(){
        return this.code;
    }

    @Override
    public String getMessage(){
        return this.message;
    }
}
