package com.tencent.hr.knowservice.businessCommon.controller.manage;


import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.service.PubCourseClassifyService;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/businessCommon/manage/course-classify")
public class PubCourseClassifyController {

    @Autowired
    private PubCourseClassifyService classifyService;

    @GetMapping("/get-course-classify")
    TransDTO getClassify(@RequestParam(name = "act_type", required = false) String actType,
                         @RequestParam(value = "classify_full_path",required = false) String classifyFullPath) {

        Object obj = classifyService.getClassify(actType, classifyFullPath);
        TransDTO<Object> dto = new TransDTO<>();
        dto.withData(obj).withSuccess(true).withCode(HttpStatus.SC_OK);
        return dto;
    }
}
