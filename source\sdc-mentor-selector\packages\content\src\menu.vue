<template>
  <fragment>
    <component
      v-for="(item, index) in data" :key="index"
      :is="hasChildren(item) ? 'el-submenu' : 'el-menu-item'"
      :index="item.key"
      :class="[item.className, { disabled: item.disabled }]"
      :disabled="item.disabled"
      @click="() => !item.disabled && handleLinkClick(item)"
      @contextmenu.native.prevent.stop="handleContextmenu($event, item)"
      :offset="item.offset"
      popper-class="sdc-sidebar-menu-popper">
      <template :slot="hasChildren(item)?'title':'default'">
        <el-tooltip
          :disabled="!collapse || hasChildren(item)"
          :content="item.text"
          popper-class="sdc-sidebar-menu-tooltip-popper"
          placement="right"
          effect="light">
          <div class="menu-item">
            <i v-if="item.icon" class="menu-icon" :class="item.icon"></i>
            <span slot="title">{{item.text}}<sup class="badge" v-if="item.badge">{{getBadgeValue(item)}}</sup></span>
          </div>
        </el-tooltip>
      </template>
      <sdc-sidebar-menu :data="item.children" v-if="hasChildren(item)" @menuContextmenu="handleContextmenu"/>
    </component>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { link } from 'mixins'
  import { DataType } from 'sdc-core'

  export default {
    name: 'sdc-sidebar-menu',
    mixins: [link],
    props: {
      data: {
        type: Array,
        default: () => []
      },
      collapse: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      hasChildren(item) {
        return !DataType.isEmptyArray(item.children)
      },
      getBadgeValue(item) {
        const badge = item.badge || ''
        const badgeMax = item.badgeMax || ''
        if (typeof badge === 'number' && typeof badgeMax === 'number' && badgeMax > 0) {
          return badge > badgeMax ? badgeMax + '+' : badge
        }
        return badge
      },
      handleContextmenu($event, menu) {
        if (menu.disabled) return
        this.$emit('menuContextmenu', $event, menu)
      }
    },
    components: {
      Fragment
    }
  }
</script>
