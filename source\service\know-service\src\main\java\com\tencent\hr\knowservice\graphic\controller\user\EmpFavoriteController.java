package com.tencent.hr.knowservice.graphic.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dao.entity.Graphic;
import com.tencent.hr.knowservice.graphic.dto.credit.CreditResDto;
import com.tencent.hr.knowservice.graphic.service.FavoriteService;
import com.tencent.hr.knowservice.graphic.service.GraphicService;
import com.tencent.hr.knowservice.graphic.service.LRSService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.security.auth.message.AuthException;

/**
 * 用户收藏
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/graphic/user/favorite/")
public class EmpFavoriteController {

    @Autowired
    FavoriteService favoriteService;

    @Autowired
    GraphicService graphicService;

    @Autowired
    LRSService lrsService;

    /**
     * 获取用户是否收藏
     *
     * @param graphicId
     * @return
     * @throws AuthException
     */
    @GetMapping("/check-favorited")
    public TransDTO checkFavorited(@RequestParam("graphic_id") Integer graphicId) throws AuthException {
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        TransDTO dto = new TransDTO<>().withSuccess(true).withCode(HttpStatus.SC_OK);
        //获取数据
        Boolean result = favoriteService.checkFavorited(graphicId, staffId);
        dto.withData(result);
        return dto;
    }

    /**
     * 添加收藏
     * 添加收藏的积分由V8那边进行上报就好。我们这边只有管结果就好。这边不在进行积分上报。由V8上报
     *
     * @param graphicId
     * @param graphicName
     * @return
     * @throws AuthException
     */
    @GetMapping("add-favorite")
    public TransDTO addFavorite(@RequestParam("graphic_id") Integer graphicId,
                                @RequestParam("graphic_name") String graphicName) {
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
        Graphic graphic = graphicService.getGraphicByIdEnable(graphicId);
        if (null == graphic) {
            throw new LogicException("图文不存在！");
        }
        CreditResDto data = favoriteService.addFavorite(graphicId, graphicName, staffId, staffName);
        return new TransDTO<CreditResDto>().withSuccess(true).withCode(org.apache.commons.httpclient.HttpStatus.SC_OK).withData(data);
    }

    /**
     * 取消收藏
     * 这边不在进行积分上报。由V8上报。V8已经处理取消收藏的行为上报
     *
     * @param graphicId
     * @return
     * @throws AuthException
     */
    @GetMapping("delete-favorite")
    public TransDTO deleteFavorite(@RequestParam("graphic_id") Integer graphicId) throws AuthException {
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
        TransDTO dto = new TransDTO<>().withSuccess(true).withCode(HttpStatus.SC_OK);
        //获取数据
        Boolean result = favoriteService.deleteFavorite(graphicId, staffId);
        dto.withData(result);
        return dto;
    }

}
