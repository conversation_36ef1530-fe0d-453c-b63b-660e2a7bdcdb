package com.tencent.hr.knowservice.businessCommon.dto.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TaskListResultDTO {
    @JsonProperty("staff_id")
    private Integer staffId;
    @JsonProperty("staff_name")
    private String staffName;
    /**
     * 获得积分
     */
    @JsonProperty("point")
    private Integer point;
    /**
     * 任务状态 0-未完成 1-已完成
     */
    @JsonProperty("task_status")
    private Integer taskStatus;
    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;
    /**
     * 任务编码
     */
    @JsonProperty("task_code")
    private String taskCode;
    /**
     * 完成时间
     */
    @JsonProperty("finish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date finishTime;
    @JsonProperty("business_id")
    private String businessId;
}
