package com.tencent.hr.knowservice.framework.service;

import com.tencent.hr.auth.sdk.common.constants.AuthConstants;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.framework.constant.UserRoleEnum;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.framework.dto.UserRoleDto;
import com.tencent.hr.knowservice.mooc.constant.CacheKeyEnum;
import com.tencent.hr.knowservice.mooc.dto.moocCourse.MoocUserAdminDto;

import com.tencent.hr.knowservice.utils.RedisUtil;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import com.tencent.hr.auth.sdk.model.auth.*;
import com.tencent.hr.auth.sdk.service.AuthService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.tencent.hr.knowservice.framework.constant.UserRoleEnum.*;

@Service
public class AuthorityService {
    @Autowired
    AuthService authService;

    @Autowired
    private RedisUtil redisUtil;


    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 获取单人所有权限项
     *
     * @param tenantCode
     * @return
     */
    public List<String> getAuthorities(String tenantCode, String staffId) {
        return authService.getOperations(tenantCode, staffId);
    }

    /**
     * 检验是否拥有权限
     *
     * @param tenantCode  租户
     * @param operateCode 权限项编号
     * @return
     */
    public boolean checkRight(String tenantCode, String operateCode, String staffId) {
        return authService.checkRight(tenantCode, staffId, operateCode);
    }

    /**
     * 获取用户的拥有的所有角色
     *
     * @param tenantCode
     * @param staffId
     * @return
     */
    public UserRoleDto getUserRoles(String tenantCode, String staffId) {
        String cacheKey = CacheKeyEnum.MoocCourseStaffRole.getKeyName().concat(".").concat(staffId);
        UserRoleDto userRoles = (UserRoleDto) redisUtil.get(cacheKey);
        if (userRoles == null) {
            List<AuthModel> objectList = getStaffRightObjectList(tenantCode, staffId);
            List<String> roleCodes = objectList.stream().map(o -> o.getRolecode()).collect(Collectors.toList());
            userRoles = new UserRoleDto();
            userRoles.setSupperAdmin(roleCodes.contains(UserRoleEnum.SupperAdmin.getRoleCode()));
            userRoles.setBGTrainingAdmin(roleCodes.contains(UserRoleEnum.BGTrainingAdmin.getRoleCode()));
            userRoles.setExamAdmin(roleCodes.contains(UserRoleEnum.ExamAdmin.getRoleCode()));
            userRoles.setDeptTrainingAdmin(roleCodes.contains(UserRoleEnum.DeptTrainingAdmin.getRoleCode()));
            userRoles.setCompanyTrainingAdmin(roleCodes.contains(UserRoleEnum.CompanyTrainingAdmin.getRoleCode()));

            redisUtil.set(cacheKey, userRoles);
            redisUtil.expire(cacheKey, Constants.CacheExpireEnum.Cache_Time_Expire_30_minute.getTime());
        }
        return userRoles;
    }

    private List<AuthModel> getStaffRightObjectList(String tenantCode, String staffId) {
        String cacheKey = CommonCacheKeyEnum.AuthorityUserRole.getKeyName() + tenantCode + ":" + staffId;
        if (redisUtil.hasKey(cacheKey)) {
            return (List<AuthModel>) redisUtil.get(cacheKey);
        }
        List<AuthModel> objectList = authService.getStaffRightObjectList(tenantCode, staffId);
        redisUtil.set(cacheKey, objectList, Constants.CacheExpireEnum.Cache_Time_Expire_1_hour.getTime());
        return objectList;
    }

    /**
     * 检查用户是否包含某个角色权限
     *
     * @param roleEnum
     * @param tenantCode
     * @param staffId
     * @return
     */
    public boolean checkUserRole(String tenantCode, String staffId, UserRoleEnum roleEnum) {
        List<AuthModel> objectList = getStaffRightObjectList(tenantCode, staffId);
        List<String> roleCodes = objectList.stream().map(o -> o.getRolecode()).collect(Collectors.toList());
        return roleCodes.contains(roleEnum.getRoleCode());
    }


    /**
     * 判断是否是几种管理员之一
     *
     * @return
     */
    public boolean checkUserAuthority(String staffId, UserRoleEnum[] roleEnums) {
        if ("dev".equals(env) && staffId == null) {
            staffId = "68317";
        }
        //拿到个人所拥有权限码
        UserRoleDto userRoles = getUserRoles(AuthConstants.TENCENT, staffId);

        UserRoleEnum[] value = roleEnums;
        List<UserRoleEnum> list = Arrays.asList(value);

        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        //判断个人所拥有的权限码是否包含接口的权限码
        if (userRoles.isExamAdmin() && list.contains(ExamAdmin)) {
            return true;
        }
        if (userRoles.isSupperAdmin() && list.contains(SupperAdmin)) {
            return true;
        }
        if (userRoles.isBGTrainingAdmin() && list.contains(BGTrainingAdmin)) {
            return true;
        }
        if (userRoles.isDeptTrainingAdmin() && list.contains(DeptTrainingAdmin)) {
            return true;
        }
        if (userRoles.isCompanyTrainingAdmin() && list.contains(UserRoleEnum.CompanyTrainingAdmin)) {
            return true;
        }
        TransDTO<String> dto = new TransDTO<>();
        dto.withSuccess(false).withCode(HttpStatus.SC_FORBIDDEN).withMessage("该操作无权限，请联系管理员！");
        return false;
    }

    /**
     * 判断用户管理员状态
     *
     * @return
     */
    public MoocUserAdminDto getMoocAdmin(String staffId) {
        MoocUserAdminDto moocUserAdminDto = new MoocUserAdminDto();
        moocUserAdminDto.setStaffId(staffId);
        moocUserAdminDto.setStaffName(GatewayContext.get().getStaffName());
        //拿到个人所拥有权限码
        UserRoleDto userRoles = getUserRoles(AuthConstants.TENCENT, staffId);
        moocUserAdminDto.setSupperAdmin(userRoles.isSupperAdmin());
        moocUserAdminDto.setMoocBGAdmin(userRoles.isBGTrainingAdmin());
        moocUserAdminDto.setMoocDeptAdmin(userRoles.isDeptTrainingAdmin());
        moocUserAdminDto.setMoocCompanyAdmin(userRoles.isCompanyTrainingAdmin());

        return moocUserAdminDto;
    }


}
