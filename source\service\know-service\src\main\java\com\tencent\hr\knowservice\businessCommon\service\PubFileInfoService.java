package com.tencent.hr.knowservice.businessCommon.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.businessCommon.dao.entity.*;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.*;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileDto;
import com.tencent.hr.knowservice.businessCommon.dto.PubFileInfoDto;
import com.tencent.hr.knowservice.businessCommon.dto.PubFilePageDto;
import com.tencent.hr.knowservice.businessCommon.vo.PubFileInfoVo;
import com.tencent.hr.knowservice.businessCommon.vo.PubFileReferVo;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.graphic.dto.ContentEntityDTO;
import com.tencent.hr.knowservice.graphic.service.AdminService;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.RedisUtil;
import com.tencent.hr.knowservice.utils.SignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【pub_file_info(素材表)】的数据库操作Service实现
 * @createDate 2022-11-22 14:29:47
 */
@Slf4j
@Service
public class PubFileInfoService {

    @Resource
    private PubFileInfoMapper fileInfoMapper;

    @Resource
    private PubFileInfoDetailMapper fileInfoDetailMapper;

    @Resource
    private PubFileAttachementsMapper fileAttachementsMapper;

    @Resource
    private PubFileAdminsMapper adminsMapper;

    @Autowired
    AdminService adminService;

    @Resource
    private PubFileReferMapper referMapper;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${com.appSetting.appName}")
    private String appName;

    @Value("${com.appSetting.tenantCode}")
    private String tenantCode;

    @Value("${extapi.contentCenter.host}")
    private String contentCenterHost;

    @Autowired
    RedisUtil redisUtil;

    @Value("${extapi.videoServer.host}")
    private String normalHost;

    @Value("${extapi.videoServer.woaHost}")
    private String woaHost;

    @Value(value = "${extapi.videoServer.signkey}")
    private String signKey;


    /**
     * 插入文件
     *
     * @param pubFileInfoDto
     * @param courseType
     * @return
     */
    public int addOrUpdateContentInfo(PubFileInfoDto pubFileInfoDto, String courseType) {

        ContextEntity current = GatewayContext.current();
        String staffId = current.getStaffId();
        String staffName = current.getStaffName();
        Date date = new Date();
        if (pubFileInfoDto.getFileId() != null) {
            PubFileInfo pubFileInfo = new PubFileInfo();
            BeanUtils.copyProperties(pubFileInfoDto, pubFileInfo);
            UpdateWrapper<PubFileInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("file_id", pubFileInfoDto.getFileId());
            pubFileInfo.setUpdateName(staffName);
            pubFileInfo.setUpdateId(Integer.valueOf(staffId));
            pubFileInfo.setUpdatedAt(date);
            fileInfoMapper.update(pubFileInfo, updateWrapper);
            return pubFileInfoDto.getFileId();
        } else {
            PubFileInfo pubFileInfo = new PubFileInfo();
            BeanUtils.copyProperties(pubFileInfoDto, pubFileInfo);
            pubFileInfo.setSupportMobile(1);
            if (StringUtils.isNotBlank(courseType)) {
                if (courseType.equals("Video")) {
                    pubFileInfo.setStatus(2);
                } else {
                    pubFileInfo.setStatus(15);
                }
            }
            pubFileInfo.setBatchNo("UGC");
            pubFileInfo.setCreatedAt(date);
            if (pubFileInfo.getCreatorId() == null && pubFileInfo.getCreatorName() == null) {
                pubFileInfo.setCreatorId(Integer.valueOf(staffId));
                pubFileInfo.setCreatorName(staffName);
            }
            fileInfoMapper.insert(pubFileInfo);
            return pubFileInfo.getFileId();
        }
    }

    /**
     * 后台获取素材信息，用于管理端
     * @param fileId
     * @return
     */
    public PubFileInfoDto findFileInfoByFileId(Integer fileId) {
        if (fileId == null) {
            return null;
        }
        PubFileInfoDto info = new PubFileInfoDto();
        QueryWrapper<PubFileInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        queryWrapper.isNull("deleted_at");
        PubFileInfo pubFileInfo = fileInfoMapper.selectOne(queryWrapper);
        if(pubFileInfo!=null) {
            BeanUtils.copyProperties(pubFileInfo, info);

            String fileUrl = "";
            ContextEntity context = GatewayContext.current();
            try {
                if (!StringUtils.isEmpty(pubFileInfo.getTranscodeHlsFilePath())) {
                    fileUrl = pubFileInfo.getTranscodeHlsFilePath();
                } else {
                    fileUrl = pubFileInfo.getDistributeFileUrl();
                }
                if(!StringUtils.isEmpty(fileUrl)) {
                    //拼接视频图片地址
                    String SignUrl = SignatureUtil.getSignatureForVideo(fileId, context.getStaffNo(), context.getStaffName(), "1", signKey);

                    fileUrl = woaHost + fileUrl + "?" + SignUrl;
                }
            } catch (Exception ex) {
            }
            info.setFileUrl(fileUrl);

            QueryWrapper<PubFileInfoDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.eq("file_id", pubFileInfo.getFileId());
            detailQueryWrapper.isNull("deleted_at");
            PubFileInfoDetail pubFileInfoDetail = fileInfoDetailMapper.selectOne(detailQueryWrapper);

            if (pubFileInfoDetail != null && pubFileInfoDetail.getDuration() != null) {
                info.setDuration(pubFileInfoDetail.getDuration().intValue());
            }
        }
        // 获取授权人
        QueryWrapper<PubFileAdmins> adminsQuery = new QueryWrapper<>();
        adminsQuery.lambda().eq(PubFileAdmins::getFileId, fileId);
        adminsQuery.lambda().eq(PubFileAdmins::getEnabled, true);
        info.setAdmins(adminsMapper.selectList(adminsQuery));

        // 字幕
        QueryWrapper<PubFileAttachements> attachementsQuery = new QueryWrapper<>();
        attachementsQuery.lambda().eq(PubFileAttachements::getFileId, fileId);
        attachementsQuery.lambda().isNull(PubFileAttachements::getDeletedAt);
        attachementsQuery.lambda().eq(PubFileAttachements::getAttachementType, "Caption");
        List<PubFileAttachements> pubFileAttachements = fileAttachementsMapper.selectList(attachementsQuery);
        if (CollectionUtils.isNotEmpty(pubFileAttachements)){
            if (pubFileAttachements.size() > 1){
                pubFileAttachements.forEach(item ->{
                    if ("中文".equals(item.getTitle())){
                        info.setPubFileAttachements(item);
                    }
                });
                if (info.getPubFileAttachements() == null){
                    info.setPubFileAttachements(pubFileAttachements.get(0));
                }
            }else {
                info.setPubFileAttachements(pubFileAttachements.get(0));
            }
        }
        if (info.getFileId() !=null && Arrays.asList(35388,35389,35390,35391,35392,35413).contains(info.getFileId())){
            info.setPubFileAttachements(null);
        }
        return info;

    }

    /**
     * 用户端获取素材信息，用于播放页面
     * @param fileId
     * @return
     */
    public PubFileInfoDto getFileInfoByFileId(Integer fileId) {
        if (fileId == null) {
            return null;
        }
        PubFileInfoDto info = null;
        String cacheKey = CommonCacheKeyEnum.FileInfo.getKeyName() + fileId;
        Object resultObj = redisUtil.get(cacheKey);
        if (resultObj == null) {
            info = findFileInfoByFileId(fileId);
            redisUtil.set(cacheKey, info, Constants.CacheExpireEnum.Cache_Time_Expire_5_minute.getTime() * 2);
        } else {
            info = (PubFileInfoDto) resultObj;
        }
        return info;
    }



    public List<PubFileInfo> findPubFileInIds(List<Integer> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            List<PubFileInfo> pubFileInfos = fileInfoMapper.selectBatchIds(ids);
            return pubFileInfos;
        }
        return new ArrayList<>();
    }


    public PubFileInfoDto findFileInfoByContentId(Integer fileId, String contentId) {
        QueryWrapper<PubFileInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("file_id", fileId);
        queryWrapper.eq("content_id", contentId);
        queryWrapper.isNull("deleted_at");
        PubFileInfo pubFileInfo = fileInfoMapper.selectOne(queryWrapper);
        if (pubFileInfo != null) {
            PubFileInfoDto info = new PubFileInfoDto();
            BeanUtils.copyProperties(pubFileInfo, info);
            return info;
        }
        return null;
    }

    /**
     * 分页查询素材列表
     *
     * @param dto
     * @return
     */
    public TransDTO<IPage<PubFileInfoVo>> getPubFileInfoList(PubFilePageDto dto) {
        IPage page = new Page<>(dto.getCurrent(), dto.getSize());
        IPage<PubFileInfoVo> res = new Page<>();
        try {
            ContextEntity current = GatewayContext.current();
            if (adminService.checkSuperAdmin(current.getStaffName())){
                // 管理员
                res = fileInfoMapper.pages(page, dto, current,true);
            }else {
               res = fileInfoMapper.pages(page, dto, current,false);
            }
            List<Integer> fileIds = res.getRecords().stream().map(PubFileInfoVo::getFileId).collect(Collectors.toList());
            if (!fileIds.isEmpty()) {
                // 查看视频时长
                List<PubFileInfoDetail> pubFileInfoDetails = fileInfoDetailMapper.selectBatchIds(fileIds);
                // 查看引用详情
                QueryWrapper<PubFileRefer> referQuery = new QueryWrapper<>();
                referQuery.lambda().in(PubFileRefer::getFileId, fileIds);
                referQuery.lambda().eq(PubFileRefer::getEnabled, true);
                List<PubFileRefer> pubFileRefers = referMapper.selectList(referQuery);
                Map<Integer, Long> referMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(pubFileRefers)) {
                    referMap = pubFileRefers.stream().collect(Collectors.groupingBy(PubFileRefer::getFileId, Collectors.counting()));
                }
                Map<Integer, Long> finalReferMap = referMap;
                res.setRecords(res.getRecords().stream().map(o -> {
                    PubFileInfoVo vo = new PubFileInfoVo();
                    BeanUtils.copyProperties(o, vo);
                    // 计算时长
                    pubFileInfoDetails.forEach(d -> {
                        if (Objects.equals(o.getFileId(), d.getFileId())) {
                            if (d.getDuration() != null) {
                                vo.setDuration((int) Math.ceil((double) d.getDuration().intValue() /60));
                            }
                        }
                    });
                    // 计算引用次数
                    vo.setReferCount(finalReferMap.get(o.getFileId()) == null ? 0 : finalReferMap.get(o.getFileId()).intValue());
                    // 判断是否只读
                    if (adminService.checkSuperAdmin(current.getStaffName())) {
                        vo.setReadOnly(false);
                    } else {
                        if (Objects.equals(vo.getCreatorId().toString(), current.getStaffId())) {
                            vo.setReadOnly(false);
                        } else {
                            vo.setReadOnly(true);
                        }
                    }
                    return vo;
                }).collect(Collectors.toList()));
            }
            return new TransDTO<IPage<PubFileInfoVo>>().withData(res).withCode(HttpStatus.SC_OK);
        } catch (Exception e) {
            log.error("查询素材列表出错:{}", e);
            return new TransDTO<IPage<PubFileInfoVo>>().withSuccess(false).withMessage("素材列表查询出错").withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 组装素材列表查询条件
     *
     * @param dto
     * @param current
     * @return
     */
    private QueryWrapper<PubFileInfo> getPubFileInfoQueryWrapper(PubFilePageDto dto, ContextEntity current) {
        QueryWrapper<PubFileInfo> queryWrapper = new QueryWrapper<>();
        // 素材名称
        if (StringUtils.isNotBlank(dto.getFileName())) {
            queryWrapper.lambda().like(PubFileInfo::getFileShowName, dto.getFileName());
        }
        // 素材类型
        if (CollectionUtils.isNotEmpty(dto.getFileType())) {
            queryWrapper.lambda().in(PubFileInfo::getFileType, dto.getFileType());
        }
        // 升序
        if (StringUtils.isNotBlank(dto.getSort_order()) && Objects.equals(dto.getSort_order().toUpperCase(), Constants.OrderByEnum.DESC.toString())) {
            queryWrapper.orderByDesc(dto.getSort_by());
        }
        // 默认降序
        if (StringUtils.isBlank(dto.getSort_order())){
            queryWrapper.lambda().orderByDesc(PubFileInfo::getCreatedAt);
        }
        // 降序
        if (StringUtils.isNotBlank(dto.getSort_order()) && Objects.equals(dto.getSort_order().toUpperCase(), Constants.OrderByEnum.ASC.toString())) {
            queryWrapper.orderByAsc(dto.getSort_by());
        }
        // 非超级管理员 仅显示项目管理员本人上传或有使用权限的UGC课程素材
        if (!adminService.checkSuperAdmin(current.getStaffName())) {
           List<Integer> list = fileInfoMapper.getFileIdsByStaffId(current.getStaffId());
           if (CollectionUtils.isNotEmpty(list)){
               queryWrapper.lambda().and(i ->{
                   i.in(PubFileInfo::getFileId, list).or().eq(PubFileInfo::getCreatorId, current.getStaffId());
               });
           }else {
               queryWrapper.lambda().eq(PubFileInfo::getCreatorId, current.getStaffId());
           }
            // 创建人
            if (StringUtils.isNotBlank(dto.getCreatorName())) {
                queryWrapper.lambda().like(PubFileInfo::getCreatorName, dto.getCreatorName());
                if (StringUtils.isNotBlank(dto.getContentType())){
                    queryWrapper.lambda().eq(PubFileInfo::getContentType, dto.getContentType());
                }

            }
            if (dto.getContentType() != null){
                queryWrapper.lambda().eq(PubFileInfo::getContentType, dto.getContentType());
            }
        } else {
            if (StringUtils.isNotBlank(dto.getCreatorName())) {
                queryWrapper.lambda().like(PubFileInfo::getCreatorName, dto.getCreatorName());
            }
            if (StringUtils.isNotBlank(dto.getContentType())) {
                queryWrapper.lambda().eq(PubFileInfo::getContentType, dto.getContentType());
            }
        }

        /**
         * 转码状态
         */
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            for (Integer status : dto.getStatus()) {
                if (status == 13) {
                    // 转码成功
                    queryWrapper.lambda().and(i -> {
                        i.and(q ->q.eq(PubFileInfo::getIsTransSucessed, 1).eq(PubFileInfo::getIsDistributeSucessed, 1).ne(PubFileInfo::getStatus, 0)).or(r->r.eq(PubFileInfo::getStatus, 13).isNotNull(PubFileInfo::getContentId));
                    });
                    break;
                } else if (status == 3) {
                    // 正在转码
                    queryWrapper.lambda().and(j->j.in(PubFileInfo::getStatus, Arrays.asList(3, 6, 9, 12, 16)).and(i->i.ne(PubFileInfo::getIsTransSucessed, 1).or().isNull(PubFileInfo::getIsTransSucessed).or().ne(PubFileInfo::getIsDistributeSucessed, 1).or().isNull(PubFileInfo::getIsDistributeSucessed)));
                    break;
                } else if (status == 2) {
                    // 转码失败
                    queryWrapper.lambda().and(j->j.in(PubFileInfo::getStatus, Arrays.asList(2, 4, 5, 7, 8, 10, 11, 14, 15)).and(i->i.ne(PubFileInfo::getIsTransSucessed, 1).or().isNull(PubFileInfo::getIsTransSucessed).or().ne(PubFileInfo::getIsDistributeSucessed, 1).or().isNull(PubFileInfo::getIsDistributeSucessed)));
                    break;
                }else if (status == 1){
                    queryWrapper.lambda().and(j->j.in(PubFileInfo::getStatus, Collections.singletonList(1)).and(i->i.ne(PubFileInfo::getIsTransSucessed, 1).or().isNull(PubFileInfo::getIsTransSucessed).or().ne(PubFileInfo::getIsDistributeSucessed, 1).or().isNull(PubFileInfo::getIsDistributeSucessed)));
                    break;
                }
            }
        } else {
            // 排除草稿
            queryWrapper.lambda().ne(PubFileInfo::getStatus, 0);
        }

        /**
         *  存储位置
         */
        if (Objects.equals(dto.getStorageLocation(), 1)) {
            queryWrapper.lambda().ne(PubFileInfo::getContentId, "").isNotNull(PubFileInfo::getContentId);
        } else if (Objects.equals(dto.getStorageLocation(), 2)) {
            queryWrapper.lambda().isNull(PubFileInfo::getContentId);
        }

        /**
         * 上传时间
         */
        if (dto.getStartToCloudTime() != null && dto.getEndToCloudTime() != null) {
            queryWrapper.lambda().between(PubFileInfo::getCreatedAt, dto.getStartToCloudTime(), dto.getEndToCloudTime());
        }
        if (dto.getStartToCloudTime() == null && dto.getEndToCloudTime() != null) {
            queryWrapper.lambda().ge(PubFileInfo::getCreatedAt, dto.getEndToCloudTime());
        }
        if (dto.getStartToCloudTime() != null && dto.getEndToCloudTime() == null) {
            queryWrapper.lambda().ge(PubFileInfo::getCreatedAt, dto.getStartToCloudTime());
        }
        return queryWrapper;
    }

    /**
     * 添加或更新素材
     *
     * @param fileInfoDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TransDTO addOrUpdatePubFile(PubFileDto fileInfoDto) {
        ContextEntity current = GatewayContext.current();
        if (fileInfoDto.getFileType() == null || "".equals(fileInfoDto.getFileType().trim())){
            return new TransDTO<>().withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage("参数：FileType不能为空");
        }
        //  保存到内容中心
        StringBuffer error = new StringBuffer();
        // 如果是文章类型 contentId 需要返回
        if (Objects.equals(fileInfoDto.getFileType().toUpperCase(), ActTypeEnum.ARTICLE.getActTypeName().toUpperCase())) {
            String key = CommonCacheKeyEnum.FileInfoDraft.getKeyName() + current.getStaffId();
            if (fileInfoDto.getDraft() != null && fileInfoDto.getDraft()) {
                // 如果是草稿 则保存在缓存里面
                fileInfoDto.setCreatorId(Integer.valueOf(current.getStaffId()));
                fileInfoDto.setCreatorName(current.getStaffName());
                fileInfoDto.setUpdatedAt(new Date());
                redisUtil.set(key, fileInfoDto,Constants.CacheExpireEnum.Cache_Time_Expire_3_day.getTime());
                return new TransDTO<>().withCode(HttpStatus.SC_OK).withData(fileInfoDto);
            }
            //文章保存到内容中心
            String contentId = saveFileInfoToContent(fileInfoDto, error);
            // 根据contentid
            if (error.length() > 0) {
                return new TransDTO<>().withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage(error.toString());
            }
            if (contentId != null && !"null".equals(contentId)){
                fileInfoDto.setContentId(contentId);
            }
            redisUtil.del(key);
        }
        //压缩包和scorm的启动文件更新到内容中心
        if (fileInfoDto.getStartFileName() != null && !fileInfoDto.getStartFileName().isEmpty()){
            updateStartFilePath(fileInfoDto.getContentId(), fileInfoDto.getStartFileName(),current);
        }
        if (fileInfoDto.getFileId() == null && fileInfoDto.getContentId() != null && !"null".equals(fileInfoDto.getContentId().trim())) {
            PubFileInfo fileInfo = fileInfoMapper.selectOne(new QueryWrapper<PubFileInfo>().lambda().eq(PubFileInfo::getContentId, fileInfoDto.getContentId()).isNull(PubFileInfo::getDeletedAt));
            if (fileInfo != null) {
                return new TransDTO<>().withSuccess(false).withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withMessage("保存失败；已存在该数据");
            }
        }
        //  更新相关信息
        if (fileInfoDto.getFileId() != null) {
            PubFileInfo fileInfo1 = fileInfoMapper.selectById(fileInfoDto.getFileId());
            if (fileInfo1 != null && ObjectUtils.allNotNull(fileInfo1.getFileId())) {
                updatePubFileInfo(fileInfoDto, current, fileInfo1.getStatus());
            } else {
                // 添加素材
                addPubFileInfo(fileInfoDto, current);
            }
        } else {
            // 添加素材
            addPubFileInfo(fileInfoDto, current);
        }
        return new TransDTO<>().withCode(HttpStatus.SC_OK);
    }

    /**
     * 更新启动文件路径
     * @param contentId
     * @param startFilePath
     */
    private void updateStartFilePath(String contentId, String startFilePath, ContextEntity current) {
        log.info("更新启动文件参数contentId:{},startFilePath:{}",contentId,startFilePath);
        HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, current.getStaffId(), current.getStaffName());
        StringBuffer errorMsg = new StringBuffer();
        HttpUtil.sendPostByRestTemplate(contentCenterHost, "/api/v1/content/" + contentId + "/update_zip_start_path?start_path=" + startFilePath,"{}", header,errorMsg);
        if (errorMsg.length() > 0){
            log.error("更新启动文件路径出错:{}", errorMsg);
        }
    }

    /**
     * 更新素材
     *
     * @param fileInfoDto
     * @param current
     */
    public void updatePubFileInfo(PubFileDto fileInfoDto, ContextEntity current, Integer status) {
        // 更新 pub_file_info
        PubFileInfo fileInfo = new PubFileInfo();
        BeanUtils.copyProperties(fileInfoDto, fileInfo);
        if (fileInfoDto.getWordNum() != null && fileInfoDto.getWordNum() >0){
            fileInfo.setWordNum(fileInfoDto.getWordNum());
        }
        fileInfo.setUpdatedAt(new Date());
        fileInfo.setUpdateId(Integer.parseInt(current.getStaffId()));
        fileInfo.setUpdateName(current.getStaffName());
        if (fileInfoDto.getDraft() != null && fileInfoDto.getDraft()) {
            fileInfo.setStatus(0);
        } else {
            if (status == 0){
               fileInfo.setCreatedAt(new Date());
            }
            PubFileInfo info = fileInfoMapper.selectById(fileInfoDto.getFileId());
            if (info != null && info.getContentId() != null && Objects.equals(info.getContentId(), fileInfoDto.getContentId())){
                fileInfo.setStatus(info.getStatus());
            }else {
                if (Objects.equals(fileInfoDto.getFileType().toUpperCase(), ActTypeEnum.ARTICLE.getActTypeName().toUpperCase())) {
                    fileInfo.setStatus(13);
                }else {
                    fileInfo.setStatus(2);
                }
            }

        }
        fileInfo.setBatchNo("New");
        fileInfoMapper.updateById(fileInfo);
        /**
         * 保存管理员或字幕
         */
        saveAdminAndCaption(fileInfoDto, current, fileInfo);

        // 更新 pub_file_info_detail
        PubFileInfoDetail detail = new PubFileInfoDetail();
        BeanUtils.copyProperties(fileInfoDto, detail);
        if (fileInfoDto.getDuration() != 0) {
            detail.setDuration(Double.valueOf(fileInfoDto.getDuration()));
        }
        detail.setUpdatedAt(new Date());
        detail.setUpdateId(Integer.parseInt(current.getStaffId()));
        detail.setUpdateName(current.getStaffName());
        fileInfoDetailMapper.updateById(detail);
    }

    /**
     * 添加素材
     *
     * @param fileInfoDto
     * @param current
     */
    public void addPubFileInfo(PubFileDto fileInfoDto, ContextEntity current) {
        // 添加素材
        PubFileInfo fileInfo = new PubFileInfo();
        BeanUtils.copyProperties(fileInfoDto, fileInfo);
        if (fileInfoDto.getWordNum() != null && fileInfoDto.getWordNum() > 0){
            fileInfo.setWordNum(fileInfoDto.getWordNum());
        }
        fileInfo.setCreatedAt(new Date());
        fileInfo.setCreatorId(Integer.parseInt(current.getStaffId()));
        fileInfo.setCreatorName(current.getStaffName());
        fileInfo.setUpdatedAt(new Date());
        fileInfo.setUpdateId(Integer.parseInt(current.getStaffId()));
        fileInfo.setUpdateName(current.getStaffName());
        if (fileInfoDto.getDraft() != null && fileInfoDto.getDraft()) {
            fileInfo.setStatus(0);
        } else {
            if (Objects.equals(fileInfoDto.getFileType().toUpperCase(), ActTypeEnum.ARTICLE.getActTypeName().toUpperCase())) {
                fileInfo.setStatus(13);
            }else {
                fileInfo.setStatus(2);
            }
        }
        fileInfo.setBatchNo("New");
        fileInfoMapper.insert(fileInfo);
        /**
         * 保存管理员或字幕
         */
        saveAdminAndCaption(fileInfoDto, current, fileInfo);

        // 添加 pub_file_info_detail
        PubFileInfoDetail detail = new PubFileInfoDetail();
        BeanUtils.copyProperties(fileInfoDto, detail);
        if (fileInfoDto.getDuration() != 0) {
            detail.setDuration(Double.valueOf(fileInfoDto.getDuration()));
        }
        detail.setFileId(fileInfo.getFileId());
        detail.setCreatedAt(new Date());
        detail.setCreatorId(Integer.parseInt(current.getStaffId()));
        detail.setCreatorName(current.getStaffName());
        detail.setUpdatedAt(new Date());
        detail.setUpdateId(Integer.parseInt(current.getStaffId()));
        detail.setUpdateName(current.getStaffName());
        fileInfoDetailMapper.insert(detail);
    }

    /**
     * 保存管理员或字幕
     *
     * @param fileInfoDto
     * @param current
     * @param fileInfo
     */
    private void saveAdminAndCaption(PubFileDto fileInfoDto, ContextEntity current, PubFileInfo fileInfo) {
        if (fileInfoDto.getPubFileAttachements() != null && ObjectUtils.allNotNull(fileInfoDto.getPubFileAttachements().getContentId())) {
            QueryWrapper<PubFileAttachements> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(PubFileAttachements::getFileId, fileInfo.getFileId());
            queryWrapper.lambda().isNull(PubFileAttachements::getDeletedAt);
            List<PubFileAttachements> attachements = fileAttachementsMapper.selectList(queryWrapper);
            AtomicReference<PubFileAttachements> attachement = new AtomicReference<>(new PubFileAttachements());
            if (CollectionUtils.isNotEmpty(attachements)){
                if (attachements.size() > 1){
                    attachements.forEach(item ->{
                        if ("中文".equals(item.getTitle())){
                            attachement.set(item);
                        }
                    });
                    if (attachement.get() == null){
                        attachement.set(attachements.get(0));
                    }
                }else {
                    attachement.set(attachements.get(0));
                }
            }
            PubFileAttachements pubFileAttachements = attachement.get();
            if (pubFileAttachements != null && pubFileAttachements.getId() != null) {
                // 更新
                PubFileAttachements attachment = fileInfoDto.getPubFileAttachements();
                attachment.setId(pubFileAttachements.getId());
                attachment.setFileId(fileInfo.getFileId());
                attachment.setGenerateType(1);
                attachment.setUpdatedAt(new Date());
                attachment.setUpdateId(Integer.parseInt(current.getStaffId()));
                attachment.setUpdateName(current.getStaffName());
                fileAttachementsMapper.updateById(attachment);
            } else {
                // 插入
                PubFileAttachements attachment = fileInfoDto.getPubFileAttachements();
                attachment.setFileId(fileInfo.getFileId());
                attachment.setGenerateType(1);
                attachment.setCreatedAt(new Date());
                attachment.setCreatorId(Integer.parseInt(current.getStaffId()));
                attachment.setCreatorName(current.getStaffName());
                attachment.setUpdatedAt(new Date());
                attachment.setUpdateId(Integer.parseInt(current.getStaffId()));
                attachment.setUpdateName(current.getStaffName());
                fileAttachementsMapper.insert(attachment);
            }
        }

        if (CollectionUtils.isNotEmpty(fileInfoDto.getAdmins()) && fileInfoDto.getAdmins().get(0).getStaffId() != null) {
            // 授权使用 先删除 在保存
            QueryWrapper<PubFileAdmins> adminsQueryWrapper = new QueryWrapper<>();
            adminsQueryWrapper.lambda().eq(PubFileAdmins::getFileId, fileInfo.getFileId());
            adminsQueryWrapper.lambda().eq(PubFileAdmins::getEnabled, true);
            PubFileAdmins admins = new PubFileAdmins();
            admins.setEnabled(false);
            adminsMapper.update(admins, adminsQueryWrapper);

            List<PubFileAdmins> adminsList = fileInfoDto.getAdmins();
            List<PubFileAdmins> pubFileAdminsList = adminsList.stream().map(item -> {
                item.setFileId(fileInfo.getFileId());
                item.setShareType(1);
                item.setEnabled(true);
                item.setCreatedAt(LocalDateTime.now());
                item.setCreatorId(String.valueOf(Integer.parseInt(current.getStaffId())));
                item.setCreatorName(current.getStaffName());
                item.setUpdatedAt(LocalDateTime.now());
                item.setUpdateId(String.valueOf(Integer.parseInt(current.getStaffId())));
                item.setUpdateName(current.getStaffName());
                return item;
            }).collect(Collectors.toList());
            adminsMapper.batchInsert(pubFileAdminsList);
        }
    }

    /**
     * 素材存储
     *
     * @param
     * @param error
     * @return
     */
    public String saveFileInfoToContent(PubFileDto fileInfoDto, StringBuffer error) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ContextEntity current = GatewayContext.current();
            if (current == null){
                current = new ContextEntity();
                current.setStaffId(String.valueOf(fileInfoDto.getCreatorId()));
                current.setStaffName(fileInfoDto.getCreatorName());
            }
            ContentEntityDTO dto = new ContentEntityDTO()
                    .setApp_id(appId)
                    .setStart_file_path(fileInfoDto.getStartFileName())
                    .setContent_id(fileInfoDto.getContentId())
                    .setStaff_id(current.getStaffId())
                    .setStaff_name(current.getStaffName())
                    .setFile_name(fileInfoDto.getFileName())
                    .setFile_new_name(fileInfoDto.getNewFileName())
                    .setFile_size(fileInfoDto.getFileSize())
                    .setContent_name(fileInfoDto.getFileShowName())
                    .setContent_type("article")
                    .setDraft(fileInfoDto.getDraft())
                    .setContent(fileInfoDto.getContent());
            // 启动文件名称
            String json = objectMapper.writeValueAsString(dto);
            HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, current.getStaffId(), current.getStaffName());
            String res = HttpUtil.sendPostByRestTemplate(contentCenterHost, "/api/v1/content/contentinfo?signature=&return_file_id=", json, header, error);
            Map<String, Object> map = objectMapper.readValue(res, new TypeReference<Map<String, Object>>() {
            });
            return String.valueOf(map.get("data"));
        } catch (Exception e) {
            error.append("文本转语音出错:" + e);
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 素材删除
     *
     * @param fileId
     */

    @Transactional(rollbackFor = Exception.class)
    public TransDTO deleteFile(Integer fileId) {

        // 判断素材有无引用
        QueryWrapper<PubFileRefer> referQueryWrapper = new QueryWrapper<>();
        referQueryWrapper.lambda().eq(PubFileRefer::getFileId, fileId);
        referQueryWrapper.lambda().eq(PubFileRefer::getEnabled, true);
        Integer referCount = referMapper.selectCount(referQueryWrapper);
        if (referCount > 0) {
            return new TransDTO<>().withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage("当前素材已被其他资源引用，无法删除");
        }

        // 删除 PubFileInfo
        QueryWrapper<PubFileInfo> fileInfoQuery = new QueryWrapper<>();
        fileInfoQuery.lambda().eq(PubFileInfo::getFileId, fileId);
        fileInfoQuery.lambda().isNull(PubFileInfo::getDeletedAt);
        PubFileInfo pubFileInfo = new PubFileInfo();
        pubFileInfo.setDeletedAt(new Date());
        fileInfoMapper.update(pubFileInfo, fileInfoQuery);

        // 删除PubFileInfoDetail
        QueryWrapper<PubFileInfoDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.lambda().isNull(PubFileInfoDetail::getDeletedAt);
        detailQueryWrapper.lambda().eq(PubFileInfoDetail::getFileId, fileId);
        PubFileInfoDetail detail = new PubFileInfoDetail();
        detail.setDeletedAt(new Date());
        fileInfoDetailMapper.update(detail, detailQueryWrapper);

        //  先删除 PubFileAdmins
        QueryWrapper<PubFileAdmins> adminsQueryWrapper = new QueryWrapper<>();
        adminsQueryWrapper.lambda().eq(PubFileAdmins::getFileId, fileId);
        adminsQueryWrapper.lambda().eq(PubFileAdmins::getEnabled, true);
        PubFileAdmins admins = new PubFileAdmins();
        admins.setEnabled(false);
        adminsMapper.update(admins, adminsQueryWrapper);

        // 先删除 PubFileAttachements
        QueryWrapper<PubFileAttachements> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PubFileAttachements::getFileId, fileId);
        queryWrapper.lambda().isNotNull(PubFileAttachements::getDeletedAt);
        PubFileAttachements update = new PubFileAttachements();
        update.setDeletedAt(new Date());
        fileAttachementsMapper.update(update, queryWrapper);
        return new TransDTO<>().withCode(HttpStatus.SC_OK);
    }

    /**
     * 重新转码
     *
     * @param id
     * @return
     */
    public TransDTO reTransCode(String id) {
        ContextEntity current = GatewayContext.current();
        if (StringUtils.isNumeric(id)) {
            // 本地资源
            fileInfoMapper.updateByFileId(id);
        } else {
            // 根据 contentId更新转码状态为待转码
            fileInfoMapper.updateStatusByContentId(id);
            // 云资源 调用内容中心接口更新
            StringBuffer error = new StringBuffer();
            HttpHeaders header = HeaderSignUtil.getESBHeader(appId, appToken, tenantCode, current.getStaffId(), current.getStaffName());
            HttpUtil.sendGetByRestTemplate(contentCenterHost, "/api/v1/content/manage/ReTransCodeByContentId/" + id, header, error);
            if (error.length() > 0) {
                return new TransDTO<>().withCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).withSuccess(false).withMessage(error.toString());
            }
        }
        return new TransDTO<>().withCode(HttpStatus.SC_OK);
    }


    /**
     * 根据素材id 查询素材引用详情
     *
     * @param fileId
     * @return
     */
    public TransDTO referByFileId(Integer fileId, Boolean isCache) {
        if (fileId == null) {
            return new TransDTO<>().withCode(HttpStatus.SC_OK);
        }
        if (!isCache) {
            // 管理端 直接返回
            List<PubFileReferVo> pubFileReferVos = referMapper.courseReferList(fileId);
            if (pubFileReferVos != null && !pubFileReferVos.isEmpty()){
                return new TransDTO<>().withData(pubFileReferVos.stream()
                        .sorted(Comparator.comparing(PubFileReferVo::getCreatedAt).reversed())
                        .collect(Collectors.toList())).withCode(HttpStatus.SC_OK);

            }
            return new TransDTO<>().withData(Arrays.asList()).withCode(HttpStatus.SC_OK);
        }
        String cacheKey = CommonCacheKeyEnum.FileRefer.getKeyName() + fileId;
        Object resultObj = redisUtil.get(cacheKey);
        if (org.springframework.util.ObjectUtils.isEmpty(resultObj)) {
            List<PubFileReferVo> pubFileReferVos = referMapper.courseReferList(fileId);
            List<PubFileReferVo> result = new ArrayList<>();
            if (pubFileReferVos != null && !pubFileReferVos.isEmpty()){
                result = pubFileReferVos.stream()
                        .sorted(Comparator.comparing(PubFileReferVo::getCreatedAt).reversed())
                        .collect(Collectors.toList());
                // 用户端 缓存10分钟
                redisUtil.set(cacheKey, result, Constants.CacheExpireEnum.Cache_Time_Expire_5_minute.getTime() * 2);
            }
            return new TransDTO<>().withData(result).withCode(HttpStatus.SC_OK);
        } else {
            return new TransDTO<>().withData(resultObj).withCode(HttpStatus.SC_OK);
        }
    }

    /**
     * 查询草稿
     *
     * @return
     */
    public TransDTO getDraft() {
        ContextEntity current = GatewayContext.current();
        String key = CommonCacheKeyEnum.FileInfoDraft.getKeyName() + current.getStaffId();
        Object o = redisUtil.get(key);
        if (o != null) {
            return new TransDTO<>().withCode(HttpStatus.SC_OK).withData(o);
        }
        return new TransDTO<>().withData(null).withCode(HttpStatus.SC_OK);
    }

    /**
     * 查询草稿
     * @param creatorId
     * @return
     */
    public PubFileInfo getPubFileDraft(Integer creatorId) {
        QueryWrapper<PubFileInfo> fileInfoQuery = new QueryWrapper<>();
        fileInfoQuery.lambda().eq(PubFileInfo::getCreatorId, creatorId).eq(PubFileInfo::getStatus, 0).isNull(PubFileInfo::getDeletedAt);
        return fileInfoMapper.selectOne(fileInfoQuery);
    }
}




