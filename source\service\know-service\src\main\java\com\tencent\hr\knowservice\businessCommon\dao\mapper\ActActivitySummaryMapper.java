package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActActivitySummary;
import org.apache.ibatis.annotations.Mapper;
@Mapper
public interface ActActivitySummaryMapper extends BaseMapper<ActActivitySummary> {
    int deleteByPrimaryKey(Integer activityId);

    int insert(ActActivitySummary record);

    int insertSelective(ActActivitySummary record);

    ActActivitySummary selectByPrimaryKey(Integer activityId);

    int updateByPrimaryKeySelective(ActActivitySummary record);

    int updateByPrimaryKey(ActActivitySummary record);

}
