package com.tencent.hr.knowservice.graphic.dao.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * graphic_learning_note_conn
 * <AUTHOR>
@Data
public class GraphicLearningNoteConn implements Serializable {
    /**
     * 产品关联Id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**
     * 产品类型
1        面授课                act_course
2        网课                act_net_course
3        班级                act_class
4        活动                act_activity
5        直播                live_schedule
6        WeChat图文                wechat_article
7        系列课                act_series_course
8        系列班                act_series_class
9        论文                act_thesis
10       文档                prod_file
18       新图文               graphic
     */
    private Integer prodType;

    /**
     * 产品id
     */
    private Integer prodId;

    /**
     * 关联产品类型
1        面授课                act_course
2        网课                act_net_course
3        班级                act_class
4        活动                act_activity
5        直播                live_schedule
6        WeChat图文                wechat_article
7        系列课                act_series_course
8        系列班                act_series_class
9        论文                act_thesis
10       文档                prod_file
18       新图文               graphic
     */
    private Integer connProdType;

    /**
     * 关联产品类型名称
     */
    private String connProdTypeName;

    /**
     * 关联产品moduleId
     */
    private Integer connProdModuleId;

    /**
     * 关联产品moduleName
     */
    private String connProdModuleName;

    /**
     * 关联产品单据id
     */
    private String connProdItemId;

    /**
     * 观看数量
     */
    private Integer viewCount;

    /**
     * 评分
     */
    private BigDecimal avgScore;

    /**
     * 关联产品创建时间
     */
    private Date connProdCreatedTime;

    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    /**
     * 产品名称
     */
    private String connProdName;

    /**
     * 跳转链接
     */
    private String connProdUrl;

    /**
     * 封面图id
     */
    private String connProdCoverId;

    /**
     * 内容类型
     */
    private Integer contentType;

    private static final long serialVersionUID = 1L;
}
