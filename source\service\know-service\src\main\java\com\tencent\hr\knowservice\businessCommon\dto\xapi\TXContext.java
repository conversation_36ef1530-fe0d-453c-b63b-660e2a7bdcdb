package com.tencent.hr.knowservice.businessCommon.dto.xapi;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

@Data
@JsonNaming
public class TXContext {

    /**
     * 相关事件
     */
    private TXStatementRef statementRef;

    /**
     * 相关事件
     */
    private List<TXStatementRef> statementRefs;

    /**
     * 相关人员
     */
    private TXAgent actorRef;

    /**
     * 相关人员
     */
    private List<TXAgent> actorRefs;

    /**
     * 讲师
     */
    private TXAgent instructor;

    /**
     * 所属队伍/组织
     */
    private TXAgent team;

    /**
     * 平台
     */
    private String platform;

    /**
     * 原系统记录ID
     */
    private String oriRecordId;

    /**
     * 自定义扩展字段
     */
    private HashMap<String, String> extensions;
}
