<template>
  <div class="add-label-outermost">
    <div class="label-select-component">
      <div class="cascader-component">
        <div :class="{ 'custom-height': tapds.length < 1 }" v-if="isAdmin">
          <el-tag v-for="(tag, index) in tapds" :key="index" class="custom-tag clicked-tag" :type="tag.type"
            disable-transitions :class="{ 'clicked-tag': tag.clicked }">
            {{ tag.label_name }}
          </el-tag>
          <el-button 
          v-if="tapds.length > 0" 
          type="text" 
          @click.stop="togglePopper(true)"
          :dt-areaid="dtTags('area', '修改标签')"
          :dt-eid="dtTags('eid', '修改标签')"
          :dt-remark="dtTags('remark', '修改标签')">
            <img src="../assets/img/modify.png" style="height: 19px;">
            <span style="float: right; margin-top: 3px;">&nbsp;修改标签</span>
          </el-button>
          <el-button 
          v-else 
          type="text" 
          @click.stop="togglePopper(true)"   
          :dt-areaid="dtTags('area', '添加标签')"
          :dt-eid="dtTags('eid', '添加标签')"
          :dt-remark="dtTags('remark', '添加标签')"
          >
            <img src="../assets/img/add.png" style="height: 19px;">
            <span style="float: right; margin-top: 3px;">&nbsp;添加标签</span>
          </el-button>
        </div>
        <el-button 
        v-else 
        type="text" 
        @click.stop="togglePopper(true)" 
        :dt-areaid="dtTags('area', '打标签')"
        :dt-eid="dtTags('eid', '打标签')"
        :dt-remark="dtTags('remark', '打标签')"
        >
          打标签
        </el-button>
        <el-dialog title="添加标签" width="800px" top="8vh" class="dialogHome add-label-outermost-npm-dialog"
          :before-close="handleClose" :visible.sync="dialogFormVisible" :close-on-click-modal="false" append-to-body>
          <el-form :rules="rules" :inline="true" :model="form">
            <el-form-item style="padding:10px 0;background: #F3F3F3;border-radius: 5px;width: 100%;
              border-bottom:#F8F8F8 solid">
              <el-form-item style="background: #F3F3F3;border-radius: 5px;width: 100%;">
                <div style="width: 100%;">
                  <span>&nbsp;&nbsp;&nbsp;已选 <span
                      style="font-weight: 600;font-family: 'PingFang SC';font-size: 16px;color: #0052D9;">{{ tags.length
                      }}</span> 个标签&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="paratext">建议内容标签数量不超过10个，如果找不到合适的标签，</span>
                    <span class="addlLabel" @click="showInput">&nbsp;&nbsp;&nbsp;点击创建自定义标签</span>
                    <span class="ClearAllLabels" v-if="tags.length > 0" @click="ClearLabel">清除全部标签</span></span>

                </div>
                <el-tag v-for="(tag, index) in tags" :key="index" closable disable-transitions class="custom-tag"
                  style="padding-left:0px;margin-left: 14px;margin-right: 10px;" :type="tag.type"
                  :class="{ 'clicked-tag': tag.clicked }" @close="handleTagClose(tag)">
                  &nbsp;
                  <div style="display: inline-block;height: 20px;min-width: 28px;">
                    {{ tag.label_name }}
                  </div>
                </el-tag>
              </el-form-item>
            </el-form-item>
            <el-form-item style="padding:0 10px 10px 10px;border-bottom:#E7E7E7 solid 1px">
              <el-form-item label="推荐标签 ： " class="tagsd" v-if="recommendList.length > 0">
                <el-tag disable-transitions v-for="tag in recommendList" style="padding-left:0px;min-width: 0px;"
                  :key="tag.label_name" class="custom-tag" :type="tag.type" :class="{ 'clicked-tag': tag.clicked }"
                  @click="addToTaga(tag)">
                  <img v-if="tag.clicked && tag.label_type === 2" :src="imgeuesr" style="height: 16px;
                    display: inline-block;vertical-align: middle; margin-bottom: 2px;margin-left:5px">
                  <img v-if="!tag.clicked && tag.label_type === 2" :src="imgeuesrto" style="height: 16px;
                    display: inline-block;vertical-align: middle; margin-bottom: 2px;margin-left:5px">
                  &nbsp;
                  <el-tooltip class="item" effect="dark" :content="tag.category_full_name" placement="bottom-start">
                    <div :class="{ 'clicked': tag.clicked }" style="display: inline-block;height: 20px;min-width: 28px;">
                      {{ tag.label_name }}</div>
                  </el-tooltip>
                </el-tag>
              </el-form-item>
              <el-form-item label="推荐标签 ： " class="tagsd" v-else>
                <span style="display: flex; justify-content: flex-start; align-items: center;line-height: 32px;">
                  暂无推荐数据
                </span>
              </el-form-item>
              <el-form-item label="最近使用 ： " class="tagsd">
                <el-tag disable-transitions v-for="tag in recently" :key="tag.label_name + tag.label_id"
                  class="custom-tag" style="padding-left:0px;min-width: 0px;" :type="tag.type"
                  :class="{ 'clicked-tag': tag.clicked }" @click="addToTaga(tag)">
                  &nbsp;
                  <el-tooltip class="item " effect="dark" :content="tag.category_full_name" placement="bottom-start">
                    <div :class="{
                      'clicked': tag.clicked,
                      'isClosables': tag.isClosable
                    }" style="display: inline-block;height: 20px;min-width: 28px;">
                      {{ tag.label_name }}</div>
                  </el-tooltip>
                </el-tag>
              </el-form-item>
            </el-form-item>
            <el-form-item style="padding:10px 5px;">
              <span style="font-weight:bold">标签列表</span>
              <el-form-item style="margin-top: 20px;" class="npm-radio-content">
                <el-form-item label="内容分类" prop="resource" class="LabelResource">
                  <el-radio-group v-model="form.resource" class="radio-list-center" @change="handleRadioChange">
                    <el-radio label="1" class="label">专业</el-radio>
                    <el-radio label="2" class="label">领导力</el-radio>
                    <el-radio label="3" class="label">通用</el-radio>
                    <el-radio label="4" class="label">新人</el-radio>
                    <el-radio label="5" class="label">其他</el-radio>
                  </el-radio-group>
                </el-form-item>
                <LabelSelectComponent v-model="tags" ref="labelSelect" class="project-tag-box" :recommend="{
                  title: this.form.course_name,
                  desc: this.form.course_desc
                }" @getSelectedLabelList="getSelectedLabelList" :labelNodeEnv="labelNodeEnv" :newTags="tags"
                  @custom-name="inputVisible = true" @push-tag="upDataTag" @search-visibleTags="habdleSearch"
                  @update-labels="updateList">
                </LabelSelectComponent>
              </el-form-item>

            </el-form-item>
            <div>
              <template>
                <el-tabs v-model="activeTab" class="tags" style="line-height: 39px;" @tab-click="handleClickButton"
                  :class="{ 'show-star': bixuan }">
                  <el-tab-pane :label="'全部'" :name="'全部'" :data-item="wholes">
                    <!-- 内容区域 -->
                  </el-tab-pane>
                  <el-tab-pane v-for="tab in labelOptions" :data-item="JSON.stringify(tab)" :key="tab.category_id"
                    :label="tab.category_name" :name="String(tab.category_id)">
                    <el-tabs v-model="activeTabs" @tab-click="handleClick" class="tabsto"
                      v-if="tab.category_name !== '其他'">
                      <el-tab-pane :label="'全部'" :name="'全部'" :data-item="whole" :data-id="tab.category_id"
                        class="tabquanbu">
                        <!-- 内容区域 -->
                      </el-tab-pane>
                      <el-tab-pane style="margin: 0;" v-for="tabs in tab.sub_categories" :data-item="JSON.stringify(tabs)"
                        :key="tabs.category_id" :label="tabs.category_name" :name="tabs.category_name">
                        <!-- 内容区域 -->
                      </el-tab-pane>
                    </el-tabs>
                    <!-- 内容区域 -->
                  </el-tab-pane>
                </el-tabs>
              </template>
            </div>
            <div style="height: 350px; overflow-y: auto;border-bottom: #e7e7e7 solid 1px;" v-if="visibleTags.length > 0"
              ref="tagContainer">
              <el-form-item style="
                          position: relative;
                          z-index: 99999;
                          overflow-y: hidden;
                        " infinite-scroll-disabled="false" infinite-scroll-immediate="true"
                infinite-scroll-distance="1" v-infinite-scroll="handleScroll">
                <el-tag disable-transitions v-for="(tag, index) in visibleTags" :key="index" class="custom-tag"
                  style="min-width: 0px" @mouseover="showDropdownItem(tag.label_name)" :type="tag.type"
                  :class="{ 'clicked-tag': tag.clicked }" @click="addToTaga(tag)">
                  <el-tooltip style="" class="item-tooltip" effect="dark" placement="bottom-start"
                    :content="tag.category_full_name" :hide-afte="0">
                    <div :class="{ clicked: tag.clicked }">
                      {{ tag.label_name }}
                    </div>
                  </el-tooltip>
                </el-tag>
              </el-form-item>
            </div>
            <el-form-item style="min-height: 350px;max-height: 450px;overflow-y: auto;width: 100%;" class="chaungjian"
              v-else>
              <div style="justify-content: center; align-items: center;height: 70%;">
                <img src='../../../packages/comment/assets/img/Label.png' alt="" style="height: 200px;width: 200px;">
              </div>
              <div style="justify-content: center; align-items: center;">
                <span>
                  {{ noTagsMessage }}
                </span>
                <span style="color: #4B84E4; cursor: pointer;" size="small" @click="showInput">点击创建自定义标签</span>
              </div>
            </el-form-item>

            <el-dialog :close-on-click-modal="false" width="360px" height="240px" style="margin-top: 10%;" title="创建自定义标签"
              :visible.sync="inputVisible" @close="CancelCreation('selectedOptions')" class="dialogs" append-to-body>
              <div>
                <span style="color: #EFAE6E;font-size: 12px;">标签名称建议不超过25个中文字符，请勿创建重复/无关标签</span>
              </div>
              <br>
              <el-form :rules="ruld" :inline="true" :model="selectedOptions" ref="selectedOptions">
                <el-form-item style="margin-bottom: 15px !important;">
                  <div class="block">
                    <span class="demonstration">分类</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>其他/自定义</span>
                  </div>
                </el-form-item>
                <el-form-item prop="label_name" style="margin-bottom: 0px;">
                  <div class="block">
                    <span class="demonstration">名称</span>
                    &nbsp;&nbsp;&nbsp;
                    <el-input v-model.trim="selectedOptions.label_name" placeholder="请输入标签名称" style="width: 263px;"
                      class="input-label" @input="checkNickName">
                      <template slot="suffix" v-if="selectedOptions.label_name">
                        <div style="height: 36px;line-height: 36px;">{{ selectedOptions.label_name ?
                          handleValidor(selectedOptions.label_name, 25) : 0 }}/25</div>
                      </template>
                    </el-input>
                    <span class="labelnameerror"
                      style="margin-left: 59px;height: 10px;color: red; margin-top: 10px; display: block;"
                      v-show="nickFlag">
                      {{ errorMsg }}
                    </span>
                  </div>
                </el-form-item>
                <div style="margin-left: 50px;color: #245BB8;margin-top: 15px;" v-show="nackname">
                  <span>存在重名的标签，确认后将使用已有标签</span>
                </div>
              </el-form>
              <div slot="footer" class="dialog-footer dialogbotton">
                <el-button class="cancel-button" @click="CancelCreation('selectedOptions')"><span>取 消</span></el-button>
                <el-button class="cancel-button" :disabled="appStatus" type="primary"
                  @click="SubmitCreation('selectedOptions')"><span>确 定</span></el-button>
              </div>
            </el-dialog>

          </el-form>
          <div slot="footer" class="dialog-footer dialogbotton">
            <el-button class="Submit-button" @click="Canceldialog">取 消</el-button>
            <el-tooltip style="z-index: 999999;" class="item" effect="dark" :content="addsss" placement="top"
              v-if="Addlabels">
              <el-button class="Submit-button" :disabled="Addlabels" type="primary" @click="Submitdialog">确 定</el-button>
            </el-tooltip>
            <el-button class="Submit-button" v-if="!Addlabels" :disabled="Addlabels" type="primary"
              @click="Submitdialog">确 定</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script src="https://unpkg.com/popper.js@2"></script>
<script>
import axios from 'axios'
import LabelSelectComponent from './LabelSelectComponent.vue'
const debounce = (fn, wait = 200) => {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this)
    }, wait)
  }
}
const oneStairList = {
  8: 49,
  9: 59,
  10:98,
  11:64,
}
export default {
  name: 'sdc-addlabel',
  components: {
    LabelSelectComponent
  },
  props: {
    // 是否在管理端使用
    isAdmin: {
      type: Boolean,
      default: true
    },
    // 双向绑定值
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请输入标签名称'
    },
    // 禁止回车创建
    disableCreate: {
      type: Boolean,
      default: false
    },
    // 是否有推荐（课程名称-title、描述-desc）
    recommend: {
      type: Object,
      default () {
        return null
      }
    },
    // 最多选择数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 可清空
    clearable: {
      type: Boolean,
      default: true
    },
    showLabelList: {
      type: Array,
      default: () => []
    },
    labelNodeEnv: {
      type: String,
      default: 'production'
    },
    course_id: {
      type: Number
    },
    course_name: {
      type: String
    },
    courseInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      bixuan:true,
      inputVal: '',
      imgeuesr:require('../assets/img/usergroup.png'),
      imgeuesrto:require('../assets/img/usergroup (1).png'),
      addsss:'专业相关内容通道职位标签不能为空',
      nickFlag: true, // 创建标签时 是否显示名称的输入报错提示
      Determines: false, // tags列表里面是否已经存在当前要创建的标签
      nackname: false, // 创建标签时 是否显示名称的重复提示
      nackList:[],
      errorMsg: '',
      searchList: [],
      recommendList: [],
      recently: [],
      tag: [],
      noTagsMessage:'',
      selectedOptions: {
        category_id: [],
        label_name: ''
      },
      labelOptions: [],
      cascaderProps: {
        value: 'category_id',
        label: 'category_name',
        children: 'sub_categories'
      },
      dialogFormVisible: false,
      selectedLabels: [], // 已选择标签
      isShowDropdown: false, // 显示下拉弹窗
      page: {
        current: 1,
        // size: 60,
        size: 40,
        total: 0,
        isRequesting: false
      },
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '1',
        desc: '',
        course_name: '',
        is_original: '',
        link_url: '', // 转载链接
        course_labels: [], // 标签
        photo_url: '',
        photo_id: '',
        photo_storage_type: '',
        course_admins: [],
        innerChecked: true,
        inner_teacher_names: [],
        outChecked: false,
        course_desc: '',
        out_teacher_names: '',
        show_caption: 0,
        limit: 0, // 权限
        target_list: '', // 权限人员
        status: '',
        dept_id: '', // 所属组织单元id
        dept_name: '', // 所属组织单元名
        extend_list: [], // 延伸学习
        caption_id: '', // 字幕文件id
        caption_name: '',
        caption_size: '',
        cl_ids: []
      },
      visibleTags: [],
      tags: [],
      Dialogtag:[],
      activeTab: '全部', // 设置默认激活的标签
      inputTag: {
        label_name: '',
        type: ''
      },
      editable: [],
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      activeTabs: '全部', // 设置默认激活的标签
      whole: '481',
      wholes: '480',
      loadingId: '',
      dataitem: '',
      dataid: '',
      appStatus: true,
      Addlabels:true,
      tapds:[],
      Tabarr:'',
      paramd:{},
      content: '1',
      searchId: 1,//搜索用的ID
      searchInput:'',//搜索的文案
      inputTimer:null,
      urlInfo: {
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      rules: {
      },
      ruld: {
        label_name: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ],
      },
      firstTime: true // 第一次加载
    }
  },
  computed: {
    commonUrl() {
      return location.hostname.endsWith(".woa.com") ? this.urlInfo[this.labelNodeEnv]: this.urlInfo[this.labelNodeEnv];
    },
    dtTags () {
      return (type, val) => {
        let { mooc_course_id, page, page_type, container } = this.courseInfo
        if (mooc_course_id) {
          if (type === 'area') {
            return `area_${mooc_course_id}_${val}`
          } else if (type === 'eid') {
            return `element_${mooc_course_id}_${val}`
          } else {
            return JSON.stringify({
              page, // 任务名称
              page_type,
              container, // 板块的名称
              click_type: 'button',
              content_type: '',
              content_id: '',
              content_name: val,
              act_type: '',
              container_id: '',
              page_id: '',
              terminal: 'pc'
            })
          }
        }
      }
    }
  },
  watch: {
    tags: {
      deep: true, // 监听数组内部元素的变化
      handler(newArray) {
        this.updateClickedStates(newArray)
        this.Proofreading()
      // 遍历整个数组，找到并清空空数组
        for (let i = 0; i < newArray.length; i++) {
          if (Array.isArray(newArray[i]) && newArray[i].length === 0) {
            newArray.splice(i, 1)
            i-- // 从当前位置重新检查，因为数组已经变化
          }
        }
      }
    },
    value: {
      handler (newVal) {
        this.tapds = newVal
        this.updateClickedStates(newVal)
      },
      immediate: true
    },
    'recommend.title' () {
      // this.getRecommendLabels()
    },
    'recommend.desc' () {
      // this.getRecommendLabels()
    },
    // selectedLabels (newVal) {
    //   this.$emit('input', newVal)
    // }
  },
  mounted () {
    this.Proofreading()
    this.getLabelRecently()
    document.addEventListener('scroll', this.handleScroll);
    document.addEventListener('click', this.hideDropdown)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.hideDropdown)
    document.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    /**
     * 字符串去除标签类型的字符等
     * @param {string} str 
     * @returns {string}
     */
     processString(str) {
      if (!str || typeof str !== 'string') {
        return ''
      }
      // 去除HTML标签
      str = str.replace(/<[^>]*>/g, '') 
      // 去除特殊字符
      str = str.replace(/&nbsp;/g, ' ')
      str = str.replace(/&amp;/g, '&')
      str = str.replace(/&lt;/g, '<')
      str = str.replace(/&gt;/g, '>')
      // 可以继续添加其他特殊字符的替换规则
      return str
    },
    Proofreading () {
      setTimeout(() => {
        if (this.content !== "1") {
          this.Addlabels = false;
          let style = document.createElement('style')
               style.textContent = `
                .tags #tab-49::after { content: ' *';color: red;display:none}
                .tags #tab-59::after { content: ' *';color: red;display:none}
                .tags #tab-98::after { content: ' *';color: red;display:none}
                .tags #tab-64::after { content: ' *';color: red;display:none}
                `
                document.head.appendChild(style)
        } else {
          
              const T = this.tags.find(item=>item.category_id === 8)
              const P = this.tags.find(item=>item.category_id === 9)
              const M = this.tags.find(item=>item.category_id === 11)
              const D = this.tags.find(item=>item.category_id === 10)
              if(T){
                 let style = document.createElement('style')
               style.textContent = `
                .tags #tab-49::after { content: ' *';color: red;display:inline-block}`
                document.head.appendChild(style)
              }else if(!T){
                 let style = document.createElement('style')
                 style.textContent = `
                .tags #tab-49::after { display:none}`
                document.head.appendChild(style)
              }
              if(P){
                 let style = document.createElement('style')
                 style.textContent = `
                .tags #tab-59::after { content: ' *';color: red;display:inline-block}`
                document.head.appendChild(style)
              }else if(!P){
                let style = document.createElement('style')
                style.textContent = `
                .tags #tab-59::after {display:none}`
                document.head.appendChild(style)
              }
              if(D){
                let style = document.createElement('style')
                 style.textContent = `
                .tags #tab-98::after { content: ' *';color: red;display:inline-block}`
                document.head.appendChild(style)
              }else if(!D){
                let style = document.createElement('style')
                style.textContent = `
                .tags #tab-98::after {display:none}`
                document.head.appendChild(style)
              }
              if(M){
                let style = document.createElement('style')
                 style.textContent = `
                .tags #tab-64::after { content: ' *';color: red;display:inline-block}`
                document.head.appendChild(style)
              }else if(!M){
                let style = document.createElement('style')
                style.textContent = `
                .tags #tab-64::after {display:none}`
                document.head.appendChild(style)
              }
         const hasChannelPosition = this.tags.some(label => {
            return label.parent_id === 1
          });
          this.Addlabels = !hasChannelPosition;
          if(hasChannelPosition){
            this.Addlabels=false
            const newArray = this.tags.filter(item=>item.category_id===8||item.category_id===9||item.category_id===10||item.category_id===11)
            if (newArray.length) {
             for (let i = 0; i < newArray.length; i++) {
               const data = this.tags.find(val=>val.parent_id===oneStairList[newArray[i].category_id])
               if(data===undefined){
                 const obj=this.labelOptions.find(item=>(oneStairList[newArray[i].category_id]===item.category_id))
                 if (obj) {
                    this.Addlabels = true
                    this.addsss = `${obj.category_name}标签不能为空`
                    return
                 }
               }
             }
            }
            } else {
              this.addsss = `专业相关内容通道职位标签不能为空`
            }
          }
          
        
      }, 500);
    },
    checkName () {
      axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`, {
        params: {
          page_no: 1,
          page_size: 6,
          category_id: this.selectedOptions.category_id === undefined ? '' : this.selectedOptions.category_id[1],
          label_name: this.selectedOptions.label_name,
          showCount: 6,
          label_type: 5,
          order_by: 'label_type'
        },
        withCredentials: true
      }).then((res) => {
        if (res.data.code === 200) {
          if (res.data.data.total !== 0) {
            this.nackname = true
            this.nackList = res.data.data.records
            this.Determine(res.data.data.records)
          } else {
            this.nackname = false
            this.Determines = false
          }
        }
      })
      this.appStatus = false
    },
    Determine (arr) {
        const valuesInArray1 = this.tags.flatMap(obj => [obj.label_name, obj.category_id])
        for (const obj2 of arr) {
            if (valuesInArray1.includes(obj2.label_name) && valuesInArray1.includes(obj2.category_id)) {
              this.Determines = true
            }
        }
    },
    checkNickName() {
      let reg = /^[A-Za-z0-9-+#&\\/()（）\u4e00-\u9fa5\s]{0,1000}$/ // 中文，数字，字母，下划线
      if (!reg.test(this.selectedOptions.label_name) && this.selectedOptions.label_name !== '') {
        this.errorMsg = '名称中不能包含特殊符号'
        this.nickFlag = true
      } else {
        if (this.handleValidor(this.selectedOptions.label_name) > 25) {
          this.errorMsg = '标签名称过长，请进行删减'
          this.nickFlag = true
        } else {
          this.errorMsg = ''
          this.nickFlag = false
        }
      }
      if ( this.selectedOptions.label_name && !this.errorMsg.length) {
        this.checkName()
      }else{
        this.appStatus = true
      }
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        return total || 0
      }
      return 0
    },
    loadMoreTags(id, val) {
      let params = {}
      if (val) {
        params={
          page_no: this.page.current,
          page_size: this.page.size,
          category_id: id,
          search_name: val,
          label_type: '5',
          order_by: 'label_type'
        }
      }else{
        params={
          page_no: this.page.current,
          page_size: this.page.size,
          category_id: id,
          label_type: '5',
          order_by: 'label_type'
        }
      }
      this.page.isRequesting = true
      axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`, {
        params,
        withCredentials: true
      }).then((res) => {
        if (res.data.code === 200) {
          this.visibleTags.push(...res.data.data.records)
          this.page.total = res.data.data.total
          this.updateClickedStates(this.visibleTags )
        }
      }).finally(() => {
        this.page.isRequesting = false
      })
    },
    handleScroll() {
      if (!this.dialogFormVisible) return
      if (this.page.total > this.visibleTags.length) {
        this.page.current++
        this.loadMoreTags(this.loadingId, this.searchInput)
      }
    },
    // 取消创建清除数据
    CancelCreation (formName) {
      this.inputVisible = false
      this.$refs[formName].resetFields()
      this.nickFlag = false
      this.appStatus = true
      this.nackname = false
      this.Determines = false
    },
    SubmitCreation (formName) {
        this.$refs[formName].validate((valid) => {
        if (valid) {
          // 有重复是不发请求创建 直接用, 如果已经push到tags里面的话就不需要再push了
          if (this.nackname) {
            const obj=this.tags.find(item=>item.label_id===this.nackList[0].label_id)
            if (obj==undefined) {
              this.tags.push(...this.nackList)
            }
            this.CancelCreation(formName)
            return
          }
          axios.post(`${this.commonUrl}/training/api/label/user/labelinfo/user-insert-label`, {
            label_name: this.selectedOptions.label_name,
          }, {
            withCredentials: true
          }).then((res) => {
            if (res.data.code === 200) {
              let addSubmit = {
                label_name: this.selectedOptions.label_name,
                clicked: true
              }
              addSubmit.label_id = res.data.data
              addSubmit.category_id = this.selectedOptions.category_id[1]
              this.tags.push(addSubmit)
              this.Determines = false
              this.CancelCreation(formName)
            } else {
              this.$message({
              type: 'warning',
              dangerouslyUseHTMLString: true,
              message: res.data.message
            })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleClose () {
      this.dialogFormVisible = false
      this.selectedLabels = []
      this.tags = []
      this.tags = this.Dialogtag
      this.selectedLabels.push(...this.Dialogtag)
      this.$emit('getSelectedLabelList', this.selectedLabels)
      this.$refs.labelSelect.clear()
    },
    Canceldialog () {
      this.dialogFormVisible = false
      this.selectedLabels = []
      this.tags = []
      this.tags = this.Dialogtag
      this.selectedLabels.push(...this.Dialogtag)
      this.$refs.labelSelect.clear()
      this.$emit('getSelectedLabelList', this.selectedLabels)
    },
    Submitdialog () {
      // 在用户端的情况下
      if (!this.isAdmin) {
        this.bingLabelSubmit()
        return
      }
      this.dialogFormVisible = false
      this.selectedLabels = this.tags
      this.tapds = []
      this.tapds.push(...this.selectedLabels)
      this.$emit('getSelectedLabelList', this.selectedLabels)
    },
    // 用在客户端时绑定标签
    bingLabelSubmit() {
      const absddd = this.tags.filter(item => item.isClosable !== true)
      const newArray = absddd.map(item => ({
        label_id: item.label_id,
        category_id: item.category_id,
        label_name: item.label_name
      }))
      axios
        .post(
          `${this.commonUrl}/training/api/label/user/labelinfo/update-course-labels-with-special`,
          {
            act_type: '2',
            course_ids: [this.course_id],
            labels: newArray,
            object_type: 'NetCourse',
            course_label_type: '3',
            course_name: this.course_name
          },
          {
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            this.Deletecoursecache()
            this.inputVisible = false
            this.dialogFormVisible = false
            this.$emit('input', this.tags)
            this.$emit('getSelectedLabelList', this.tags)
            const imageUrl = require('../assets/img/first_share.png')
            const imageseUrl = require('../assets/img/imgse.png')
            const messageContent = `
                <div>
                  &nbsp;&nbsp;&nbsp;<img src="${imageseUrl}" style="height: 20px;margin-left:-22px;"></img>
                  &nbsp;<strong style="color: #666666;font-family: "PingFang SC";font-size: 14px;
                  font-style: normal;font-weight: 400;line-height: normal;" >添加标签成功</strong>
                  <br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;<img src="${imageUrl}" style="height: 20px;margin-left:-22px;"></img>
                  &nbsp;<span style="color: #ff7a4f;font-family: "PingFang SC";font-size: 14px;
                  font-style: normal;font-weight: 500;line-height: normal;">通用积分+${res.data.remark}</span>
                </div>
              `
            if (res.data.remark > 0) {
              this.$message({
                dangerouslyUseHTMLString: true,
                message: messageContent,
                type: 'success',
                iconClass: ' ', // 这个会覆盖图标类名，为了实现去掉图标的操作
                customClass: 'el-message--success', // 这个是由于上面设置了iconClass会覆盖掉type，所以需要再设置
                duration: 5000 // 这个是时长设置
              })
            } else {
              this.$message({
                type: 'success',
                message: '添加标签成功'
              })
            }
          } else {
            this.$message({
              type: 'warm',
              message: res.data.message || '添加标签失败'
            })
          }
        })
    },
    Deletecoursecache () {
      axios
        .post(
          `${this.commonUrl}/training/api/netcourse/user/courseinfo/remove-net-course-info-cache?act_type=2&course_id=${this.course_id}`,
          {},
          {
            withCredentials: true
          }
        )
        .then(res => {
          if (res.data.code === 200) {
            console.log(res.data.code)
          }
        })
    },
    addToTaga(tag) {
      if (!tag.clicked) {
        this.tags = this.tags.concat(tag);
        tag.clicked = true
      } else {
        this.tags = this.tags.filter(item => item.label_id !== tag.label_id);
        tag.clicked = false
      }
    },
    handleCascaderChange(value) {
      // 处理选中事件
      if(this.selectedOptions.label_name && value.length && !this.errorMsg.length) {
        this.checkName()
      } else {
        this.appStatus = true
      }
    },
    processCategories(categories) {
      return categories.map(category => ({
        ...category,
        sub_categories: category.sub_categories.length > 0
          ? this.processCategories(category.sub_categories)
          : undefined
      }))
    },
    ClearLabel() {
      this.tags = []
      this.updateClickedStates(this.recently)
      this.updateClickedStates(this.recommendList)
    },
    showInput() {
      this.inputVisible = true
    },
    updateClickedStates (val) {
      for (const selectedLabel of val) {
        // 在 tagsArray 中查找具有相同 label_id 的标签
        const matchingTag = this.tags.find(tag => tag.label_id === selectedLabel.label_id)
        // 如果找到匹配的标签，设置 tag.clicked 为 true，否则设置为 false
        if (matchingTag) {
          this.$set(selectedLabel, 'clicked', true) // 使用Vue.set来确保响应式更新
        } else {
          this.$set(selectedLabel, 'clicked', false) // 使用Vue.set来确保响应式更新
        }
      }
    },
    getSelectedLabelList(val) {
      this.tags = val
      this.updateClickedStates(this.recently)
      this.updateClickedStates(this.recommendList)
      this.updateClickedStates(this.visibleTags)
      this.form.course_labels = this.tags
    },
    handleTagClose(tag) {
      tag.clicked = false
      // 执行删除操作
      const index = this.tags.indexOf(tag)
      if (index !== -1) {
        this.tags.splice(index, 1)
      }  
    },
    getLabelCategory (id) {
      return axios.get(`${this.commonUrl}/training/api/label/user/category/category_tree`, {
        withCredentials: true,
        params: {
          order_type: id
        }
      }).then((res) => {
        if (res.data.code === 200) {
          let list = []
          if (this.showLabelList.length > 0) {
            list = res.data.data.filter(item => {
              return item.sub_categories.length && this.showLabelList.includes(item.category_name)
            })
          } else {
             list = res.data.data.filter(item => {
              return item.sub_categories.length
            })
          }
          this.labelOptions = this.processCategories(list)
          if (this.labelOptions.length > 0) {
            if (id === '5') {
              this.activeTab = '全部'
               if (this.searchInput) {
                 this.searchId = ''
                this.habdleSearch(this.searchInput)
                return
              }
              this.getLabelLeaf()
            } else {
              this.activeTabs = ''
              this.$nextTick(() => {
                this.activeTabs = '全部'
              })
              if (this.bixuan){
                const categoryObj = this.labelOptions.find(item=>item.category_id === 1)
                if (categoryObj.category_name) {
                  this.activeTab = String(categoryObj.category_id)
                   if (this.searchInput) {
                     this.searchId = categoryObj.category_id
                    this.habdleSearch(this.searchInput)
                    return
                  }
                  this.getLabelLeaf(categoryObj.category_id)
                }
              }else{
              this.activeTab = String(this.labelOptions[0].category_id)
              // 设置默认选中第一个标签
              if (this.searchInput) {
                     this.searchId = this.labelOptions[0].category_id
                    this.habdleSearch(this.searchInput)
                    return
                  }
              this.getLabelLeaf(this.labelOptions[0].category_id)
              
              }
            }
          }
        }
      })
    },
    getLabelRecently () {
      axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/recently_used`, {
        withCredentials: true,
        params: {
          show_count: '5'
        }
      }).then((res) => {
        if (res.data.code === 200) {
          this.recently = res.data.data
          this.updateClickedStates(this.recently)
        }
      })
    },
    handleRadioChange (val) {
      if (val==='5') {
        this.visibleTags=[]
        this.page.current = 1
        this.loadingId = ''
      }
      this.getLabelCategory(val)
      this.Proofreading()
      this.content = val
      if (val === '1') {
        this.bixuan = true
      }else {
        this.bixuan = false
      }
    },
    updateRequest (val) {
      if (this.Tabarr === '全部') {
        this.paramd = {
              page_no: 1,
              page_size: 6,
              label_name: val.label_name,
              label_type: '5',
              order_by: 'label_type'
      }
      } else {
        this.paramd = {
              page_no: 1,
              page_size: 6,
              category_id: this.dataitem.category_id !== '' && this.dataitem.category_id !== undefined ? 
                           this.dataitem.category_id : this.labelOptions[0].category_id,
              label_name: val.label_name,
              label_type: '5',
              order_by: 'label_type'
      }
      }
      axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`, {
            params: this.paramd,
            withCredentials: true
          }).then((res) => {
            if (res.data.code === 200) {
              if (res.data.data.total !== 0) {
                this.visibleTags = [
                  val,
                  ...this.visibleTags.filter(
                    label => !(label.category_id === val.category_id && label.label_name === val.label_name))];
              }
            }
          })
    },
    habdleSearch(val){
      if (this.inputTimer) {
        clearTimeout(this.inputTimer);
      }
      this.inputTimer = setTimeout(() => {
       this.searchVisibleTags(val)
      }, 500)
    },
    searchVisibleTags(val){
      this.searchInput = val
      if(!val){
       this.getLabelLeaf(this.searchId)
      }else{
        this.paramd = {
              page_no: 1,
              page_size: this.page.size,
              category_id: this.searchId,
              search_name: val,
              label_type: '5',
              order_by: 'label_type'
      }
      axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`, {
            params: this.paramd,
            withCredentials: true
          }).then((res) => {
            if (res.data.code === 200) {
              if (res.data.data.total !== 0) {
                  this.page.total = res.data.data.total
                  this.visibleTags = res.data.data.records
                  if (res.data.data.total > res.data.data.records.length) {
                    this.page.current = 1
                    this.loadingId = this.searchId
                    this.handleScroll(val)
                  }
                
              }else{
                  this.visibleTags=[]
                  this.noTagsMessage='暂无匹配数据,'
                }
            }
          })
      }
     
    },
    updateList (val) {
      if (this.dataitem !== 480) {
        if (this.dataitem !== 481) {
        const hasDuplicate = this.visibleTags.some(
          label => label.category_id === val.category_id && label.label_name === val.label_name
        );
        if (hasDuplicate) {
          this.visibleTags = [
          val,
          ...this.visibleTags.filter(
            label => !(label.category_id === val.category_id && label.label_name === val.label_name))];
        } else {
          this.updateRequest(val)
        }
      } 
      } else {
        this.Tabarr = '全部'
        this.updateRequest(val)
      }
    },
    handleClickButton (tab, event) {
      this.Proofreading()
      this.page.current = 1
      this.loadingId = JSON.parse(tab.$attrs['data-item']).category_id
      this.searchId = JSON.parse(tab.$attrs['data-item']).category_id
      this.dataitem = JSON.parse(tab.$attrs['data-item'])
      if (JSON.parse(tab.$attrs['data-item']) !== 480) {
        this.activeTabs = ''
        this.$nextTick(() => {
          this.activeTabs = '全部'
        })
         if(this.searchInput) {
           this.habdleSearch(this.searchInput)
            return
          }
        this.handleClick(tab)
      } else {
        let id = ''
        this.searchId = ''
        if(this.searchInput) {
        this.habdleSearch(this.searchInput)
        return
       }
        this.getLabelLeaf(id)
      }
    },
    handleClick(tab) {
      this.Proofreading()
      this.page.current = 1
      this.loadingId = JSON.parse(tab.$attrs['data-item']).category_id
      this.searchId = JSON.parse(tab.$attrs['data-item']).category_id
      this.dataitem = JSON.parse(tab.$attrs['data-item'])
      this.dataid = JSON.parse(tab.$attrs['data-item']) !== 481
      if (JSON.parse(tab.$attrs['data-item']) !== 481) {
        let cateId = JSON.parse(tab.$attrs['data-item']).category_id
         if(this.searchInput) {
        this.habdleSearch(this.searchInput)
        return
      }
        this.getLabelLeaf(cateId)
       
      } else {
        let id = JSON.parse(tab.$attrs['data-id'])
         this.searchId = JSON.parse(tab.$attrs['data-id'])
         if(this.searchInput) {
        this.habdleSearch(this.searchInput)
        return
      }
        this.getLabelLeaf(id)
      }
    },
    getLabelLeaf (id) {
      this.page.isRequesting = true
      return axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`, {
        params: {
          page_no: 1,
          page_size: this.page.size,
          category_id: id,
          label_type: '5',
          order_by: 'label_type'
        },
        withCredentials: true
      }).then((res) => {
        if (res.data.code === 200) {
          this.visibleTags = []
          this.visibleTags = res.data.data.records
          if (!this.visibleTags.length) {
            this.noTagsMessage='暂无标签数据,'
          }
          this.page.total = res.data.data.total
          this.updateClickedStates(this.visibleTags)
          if (this.page.total > this.visibleTags.length) {
            this.page.current = 1
            this.loadingId = id
            this.handleScroll()
          }
        }
      }).finally(() => {
        this.page.isRequesting = false
      })
    },
    // 推荐标签
    getRecommendLabels: debounce(function () {
      axios.post(`${this.commonUrl}/training/api/businessCommon/manage/label/get_recommend_labels_v2`, {
        object: 'online_course',
        title: (this.recommend && this.recommend.title) || '',
        content: this.processString(this.recommend.desc || ''),
        show_count: this.maxNum
      }, {
        withCredentials: true
      }).then((res) => {
        if (res.data.code === 200) {
          this.recommendList = res.data.data.map(item => {
            return {
              ...item,
              label_name: item.name
            }
          })
          this.updateClickedStates(this.recommendList)
        }
      })
    }, 1500),
    // 搜索
    getSearchList: debounce(function () {
      axios.get(`${this.commonUrl}/training/api/label/user/labelinfo/label_match`, {
        params: {
          name: this.inputVal,
          showCount: 6
        },
        withCredentials: true
      }).then((res) => {
        if (res.data.code === 200) {
          this.searchList = res.data.data || []
        }
      })
    }, 1000),
    // 搜索
    search () {
      if (!this.inputVal) {
        return
      }
      const reg = /[^a-zA-Z0-9\u4e00-\u9fa5]/g
      if (reg.test(this.inputVal)) {
        this.inputVal = this.inputVal.replace(reg, '')
        return
      }
      this.getSearchList()
    },
    // 显示隐藏面板
    togglePopper (visible) {
      if (this.firstTime) {
        Promise.all([this.getLabelCategory(1), this.getLabelLeaf(1)]).then(() => {
          this.showPopperEvent(visible)
          this.firstTime = false
        })
        return
      }
      this.showPopperEvent(visible)
    },
    showPopperEvent(visible) {
      this.tapds.forEach((item) => {
          item.default = "100";
        })
        this.Dialogtag = this.tapds.filter(tag => tag.default === "100");
        this.getRecommendLabels()
        this.$refs.labelSelect && this.$refs.labelSelect.clear()
        this.dialogFormVisible = true
        const isDef = visible !== undefined && visible !== null
        this.isShowDropdown = isDef ? visible : !this.isShowDropdown
        this.tags = this.tapds || this.selectedLabels
        this.Proofreading()
        setTimeout(() => {
          this.getLabelRecently()
        },500)
    },
    // 下拉框隐藏事件
    hideDropdown () {
      this.isShowDropdown = false
      this.inputVal = ''
      this.searchList = []
    },
    upDataTag (e) {
      this.tags.push(e)
    }
  }
}
</script>

<style lang="less" scoped>
.npm-radio-content /deep/.el-form-item__content {
  display: flex;
}

.item-tooltip[data-popper-reference-hidden] {
  visibility: hidden;
  pointer-events: none;
}

.input-label {
  :deep .el-input__inner {
    padding-right: 45px;
  }
}

.dialogHome {
  :deep .el-dialog {
    min-height: 800px;
  }

  :deep .el-dialog__body {
    padding: 20px 32px;
    padding-bottom: 5px;
    padding-top: 20px;
  }
}

.add-label-outermost-npm-dialog {
  :deep .el-radio__input.is-checked .el-radio__inner {
    border-color: #3464e0;
    background: #fff;
  }

  :deep .el-radio__input.is-checked .el-radio__inner:after {
    background: #3464e0;
  }
}

:deep .el-form-item {
  margin-bottom: 0px !important
}

.dialogbotton {
  display: flex;
  justify-content: flex-end
}

.cancel-button {
  width: 40px;
  height: 24px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Submit-button {
  width: 60px;
  height: 32px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  border-radius: 3px;
}

:deep .el-dialog {
  border-radius: 9px;
}

:deep .image-text-page .tag-form-item[data-v-5ebdae47] .el-form-item__label {
  margin-top: 0px;
}

.LabelResource {
  flex: 1;

  :deep .el-form-item__label {
    margin-top: 0px !important;
    white-space: nowrap;
    width: 75px;
  }

  :deep .el-form-item__label:after {
    content: ' *';
    color: red;
  }
}

.radio-list-center {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.label {
  color: #000000e6;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  margin-right: 20px;
}

.paratext {
  color: #00000080;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}

.addlLabel {
  cursor: pointer;
  color: #0052d9;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.ClearAllLabels {
  cursor: pointer;
  color: #e34d59;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  float: right;
  margin: 0 30px;
  height: 40px;
  line-height: 40px;
}

.dialogs :deep .el-dialog__body {
  padding: 11px 20px;
}

.dialogs :deep .el-dialog .el-dialog__header {
  border-bottom: 0px !important
}

:deep .el-form-item__label {
  width: 89px;
}

:deep .el-form--inline .el-form-item__content {
  width: 100%;
}

:deep .el-form--inline .el-form-item {
  display: flex;
  margin-right: 0px;
}

.chaungjian {
  :deep .el-form-item__content {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
}

.show-star {
  :deep #tab-1::after {
    content: ' *';
    color: red;
  }
}

.tags {
  :deep .el-tabs__nav-wrap::after {
    height: 0px;
  }

  :deep .el-tabs__header {
    border-bottom: 1px solid var(--color-border-2, #E5E6EB);
    margin: 0;
  }

  :deep .el-tabs__nav {}

  :deep .el-tabs__item {
    padding: 10px;
    color: #4e5969 !important;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400 !important;
    line-height: 20px;
  }

  :deep .el-tabs__nav-prev {
    /* 添加你的样式修改 */
    width: 0;
    height: 0;
  }

  :deep .el-icon-arrow-left:before {
    color: #fff;
  }

  :deep .el-tabs__nav-prev::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 6px 5px 0;
    border-color: transparent #00000099 transparent transparent;
    left: 1px;
    top: 17px;
  }

  :deep .el-tabs__nav-next {
    width: 0px;
    /* 调整按钮的宽度 */
    height: 20px;
    /* 调整按钮的高度 */
  }

  /* 创建一个实体三角形 */
  :deep .el-tabs__nav-next::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 6px;
    border-color: transparent transparent transparent #00000099;
    right: 0px;
    top: 17px;
  }
}

.tabsto {
  :deep .el-tabs__nav.is-top {
    height: 32px
  }

  :deep .el-tabs__item.is-active:after {
    content: '';
    color: inherit;
  }

  :deep .el-tabs__item.is-top:after {
    content: '';
    color: inherit;
  }

  :deep .el-tabs__nav-wrap::after {
    height: 0px;
  }

  :deep .el-tabs__header {
    border-bottom: 0px !important;
  }

  :deep .el-icon-arrow-left:before {
    color: currentColor
  }

  /* 内部 'tabsto' 类的样式 */
  /* 你可以在这里添加适用于 'tabsto' 类的样式 */
  :deep .el-tabs__nav-prev {
    /* 添加你的样式修改 */
    width: 20;
    height: 0;
  }

  :deep .el-tabs__nav-prev::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0px 0px 0px 0;
    border-color: transparent transparent transparent transparent;
    left: 1px;
    top: 17px;
  }

  :deep .el-tabs__nav-next {
    width: 10px;
    /* 调整按钮的宽度 */
    height: 20px;
    /* 调整按钮的高度 */
  }

  /* 创建一个实体三角形 */
  :deep .el-tabs__nav-next::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0px 0 0px 0px;
    border-color: transparent transparent transparent transparent;
    right: 0px;
    top: 17px;
  }
}

.clicked-tag {
  border-color: #0052d9;
  border: 1px solid #0052d9 !important;
  background: #F2F3FF !important;
  color: #0052d9 !important;
  font-weight: 500;
}

.clicked {
  color: #0052d9;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}

.cascader-class {
  :deep .el-input__validateIcon {
    display: none;
  }
}

.tagsd {
  min-height: 38px;

  :deep .el-form-item__label {
    color: #919191;
    margin-top: 5px;
    padding-right: 0px;
  }

  :deep .el-form-item__content {
    margin-top: 5px
  }
}

.npm-radio-content .label-select-component.project-tag-box {
  width: 240px;
  float: right;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
  margin: 10px;
}

.custom-height {
  height: 32px;
  line-height: 32px
}

.custom-tag {
  cursor: pointer;
  margin: 6px;
  background: #F7F8FA;
  min-width: 69px;
  margin-right: 15px;
  /* 设置标签之间的间距 */
  border-radius: 0 15px 15px 0;
  /* 添加右边的半圆效果 */
  //   color: #fff;
  color: #00000099;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  height: 24px;
  line-height: 22px;
  border: 1px solid #DCDCDC;

  :deep .el-tag__close {
    padding-left: 5px;
    color: #0052d9;
  }

  :deep .el-tag__close:hover {
    background-color: #F5F7F9;
    /* 你想要的颜色 */
    color: #0052d9;
  }
}

.label-select-component {
  width: 100%;

  .cascader-component {
    position: relative;
  }

  .cascader-select {
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;

    &.focus {
      border: 1px solid #0052d9ff;
    }

    .select-input {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .tag {
        max-width: 100%;
        height: 24px;
        background: #e7e7e7;
        border-radius: 4px;
        box-sizing: border-box;
        color: rgba(0, 0, 0, 0.9);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-flex;
        align-items: center;
        margin: 3px 0 3px 4px;
        padding: 0 8px;

        .tag-close {
          width: 16px;
          height: 16px;
          color: rgba(0, 0, 0, 0.4);
          font-size: 14px;
          font-weight: bold;
          font-style: normal;
          line-height: 16px;
          text-align: center;
          margin-left: 2px;
          cursor: pointer;
        }
      }

      &>input {
        flex: 1;
        height: 30px;
        min-width: 60px;
        padding: 2px 8px;
        color: #606266;
        font-size: 14px;
        border: 0;
        outline: none;
        box-sizing: border-box;

        &::placeholder {
          color: rgba(0, 0, 0, 0.4);
          font-size: 13px;
        }
      }
    }

    .clear {
      margin-right: 8px;

      &>span {
        width: 14px;
        height: 14px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 50%;
        color: #fff;
        font-size: 12px;
        font-weight: bold;
        line-height: 13px;
        display: flex;
        justify-content: center;
        cursor: pointer;
      }
    }
  }

  .recommend {
    margin-top: 6px;
    line-height: 24px;
    display: flex;

    .tip {
      color: #666;
      font-size: 12px;
      display: flex;
      align-items: center;
      flex-shrink: 0;

      &>i {
        margin-right: 5px;
        font-size: 14px;
      }
    }

    .recommend-label {
      margin-left: 16px;

      .tag {
        max-width: 100%;
        height: 24px;
        background: #e7e7e7;
        border-radius: 4px;
        box-sizing: border-box;
        color: rgba(0, 0, 0, 0.9);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-flex;
        align-items: center;
        margin: 0 4px;
        padding: 0 8px;
        cursor: pointer;
      }
    }
  }

  .cascader-dropdown {
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    background: #ffffffff;
    box-shadow: 0 3px 14px 2px rgba(0, 0, 0, 0.1), 0 8px 10px 1px rgba(0, 0, 0, 0.06), 0 5px 5px -3px rgba(0, 0, 0, 0.05);
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 6px;
    z-index: 10;

    .cascader-panel {
      display: flex;

      .cascader-list {
        min-width: 180px;
        max-height: 320px;
        overflow-y: auto;
        padding: 8px;

        &:not(:last-of-type) {
          border-right: 1px solid #e7e7e7;
        }
      }

      .cascader-node {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        margin: 0;

        &:hover {
          background: #ecf2fe;
        }

        &.active {
          background: #ecf2fe;
          color: #0052d9;
        }
      }
    }

    .search-panel {
      min-width: 220px;
      padding: 8px;

      .search-item {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 8px;

        &:hover {
          background: #ecf2fe;
        }

        &.active {
          background: #ecf2fe;
          color: #0052d9;
        }
      }

      .search-empty {
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
