package com.tencent.hr.knowservice.businessCommon.service;

import com.tencent.hr.common.util.encrypt.DigestUtil;
import com.tencent.hr.knowservice.utils.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Map;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2022/10/27
 * @version: 1.0
 */
@Service
@Slf4j
public class BaseProxyService {

    @Qualifier("httpRestTemplate")
    @Autowired
    RestTemplate restTemplate;

    /**
     * post请求
     *
     * @param host
     * @param api
     * @param params
     * @param header
     * @return
     */
    public String postForApiWithHeaders(String host, String api, Map<String, Object> params, HttpHeaders header) {
        String url = host + api;
        String result = "";
        HttpEntity<Map> requestEntity = new HttpEntity<>(params, header);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);
            result = responseEntity.getBody();
        } catch (Exception e) {
            log.error("BaseProxyService 请求服务失败，请求地址=【{}】，请求参数=【{}】，错误信息=【{}】",url,params.toString(),e.getMessage(), e);
        }
        return result;
    }

    /**
     * get请求
     *
     * @param host
     * @param api
     * @param params
     * @param header
     * @return
     */
    public String getForApiWithHeaders(String host, String api, Map<String, Object> params, HttpHeaders header) {
        String url = buildQueryUrl(host, api, params);
        String result = "";
        HttpEntity<String> requestEntity = new HttpEntity<>(header);
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(url);
        URI uri = uriComponentsBuilder.build(true).toUri();
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.GET, requestEntity, String.class);
            result = responseEntity.getBody();
        } catch (Exception e) {
            log.error("BaseProxyService 请求服务失败,请求地址=【{}】，请求参数=【{}】,错误信息=【{}】",url,params.toString(),e.getMessage(), e);
        }
        return result;
    }

    /**
     * post请求，请求体是json格式数据
     * @param host
     * @param api
     * @param jsonBody
     * @param headers
     * @return
     */
    public String postJsonStrWithHeaders(String host, String api, String jsonBody, HttpHeaders headers) {
        String url = host + api;
        String result = "";
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url.trim(), requestEntity, String.class);
            result = responseEntity.getBody();
        } catch (Exception e) {
            log.error("BaseProxyService 请求服务失败,请求地址=【{}】，请求参数=【{}】,错误信息=【{}】",url,jsonBody.toString(),e.getMessage(), e);
        }
        return result;
    }

    /**
     * post请求，请求体是json格式数据 使用UTF_8对响应体ISO_8859_1编码格式进行转换
     * @param host
     * @param api
     * @param jsonBody
     * @param headers
     * @return
     */
    public String postJsonStrWithHeaders2(String host, String api, String jsonBody, HttpHeaders headers) {
        String url = host + api;
        String result = "";
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url.trim(), requestEntity, String.class);
            result = responseEntity.getBody();
            result = new String(result.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("BaseProxyService 请求服务失败,请求地址=【{}】，请求参数=【{}】,错误信息=【{}】",url,jsonBody.toString(),e.getMessage(), e);
        }
        return result;
    }

    /**
     * 构建query查询路径
     *
     * @param host
     * @param api
     * @param paraMap
     * @return
     */
    private String buildQueryUrl(String host, String api, Map<String, Object> paraMap){
        String url = host + api;
        if (MapUtils.isNotEmpty(paraMap)) {
            String paramStr = MapUtil.createLinkStringEncoder(paraMap);
            url += "?" + paramStr;
        }
        return url;
    }

    /**
     * 简单版本esb请求头，没有用户信息
     * @param appName
     * @param token
     * @return
     */
    public  HttpHeaders getESBHeaderSimple(String appName, String token) {
        HttpHeaders headers = new HttpHeaders();
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String sign = DigestUtil.sha256().digest(appName + token + timestamp).toLowerCase();
        headers.add("hrgw-timestamp", timestamp);
        headers.add("hrgw-appname", appName);
        headers.add("hrgw-signature", sign);
        return headers;
    }

    /**
     * 获取esb请求头
     * @param appName
     * @param token
     * @param staffId
     * @param staffName
     * @return
     */
    public  HttpHeaders getESBHeader(String appName, String token,String staffId,String staffName) {
        //内网的租户信息默认使用 tencent
        String corpkey = "tencent";
        HttpHeaders headers = new HttpHeaders();
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String sign = DigestUtil.sha256().digest(appName + token + timestamp).toLowerCase();
        headers.add("hrgw-timestamp", timestamp);
        headers.add("hrgw-appname", appName);
        headers.add("hrgw-signature", sign);
        headers.add("caagw-staffid", staffId);
        headers.add("caagw-username", staffName);
        //租户信息
        headers.add("cc-corpkey", corpkey);
        headers.add("cc-corpid", corpkey);
        //兼容以前的老系统
        headers.add("staffid", staffId);
        headers.add("staffName", staffName);
        return headers;
    }


}
