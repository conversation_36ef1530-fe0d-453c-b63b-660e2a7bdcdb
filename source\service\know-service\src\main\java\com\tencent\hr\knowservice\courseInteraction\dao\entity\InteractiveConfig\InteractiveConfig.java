package com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveConfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description:
 * @author: shizhouwang
 * @createDate: 2023/10/11
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class InteractiveConfig {
    /**
     * 互动规则唯一标识id
     */
    private String _id;
    /**
     * 课程类型
     */
    private int actType;
    /**
     * 课程类型名
     */
    private String actTypeName;
    /**
     * 绑定该互动配置的课程的唯一标识
     */
    private String courseId;
    /**
     * 互动配置值 -- 选择类型的配置项
     */
    private List<ConfigurationsOfSelect> configurationsOfSelect;

    /**
     * 是否有效
     */
    private boolean enabled;
    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String createdAt;
    /**
     * 最后更新人id
     */
    private String updaterId;
    /**
     * 最后更新人姓名
     */
    private String updaterName;
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private String updatedAt;
}
