package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActLabels;
import com.tencent.hr.knowservice.businessCommon.dto.manage.ActLabelDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【act_labels(课程标签表)】的数据库操作Mapper
* @createDate 2022-11-18 10:03:46
* @Entity com.tencent.hr.knowservice.businessCommon.dao.entity.ActLabels
*/
public interface ActLabelsMapper extends BaseMapper<ActLabels> {


    List<ActLabelDto> findLabelByName(@Param("name") String name, @Param("count") String count);


    List<ActLabelDto> findLabelForCourseList(@Param("name") String name, @Param("count") String count, @Param("actType") String actType);


    List<ActLabelDto> findLabelByClassifyId(@Param("classifyId") Integer classifyId);
}




