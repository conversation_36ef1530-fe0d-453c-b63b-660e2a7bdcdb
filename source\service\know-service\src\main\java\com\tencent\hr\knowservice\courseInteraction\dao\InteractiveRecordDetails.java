package com.tencent.hr.knowservice.courseInteraction.dao;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class InteractiveRecordDetails implements Serializable {


	/**
	 * 题目id
	 */
	private String questionId;
	/**
	 * 课程类型
	 */
	private Integer actType;
	/**
	 * 课程类型名
	 */
	private String actTypeName;
	/**
	 * 课程id
	 */
	private String courseId;
	/**
	 * 互动类型 CHOOSE--选择 VOTE-投票 (时间切片-快照)
	 */
	private String activeType;
	/**
	 * 完成条件 choose--选择即可；correct--选择正确 (时间切片-快照)
	 */
	private String completionConditions;
	/**
	 * 互动行为
	 */
	private String activeAnswer;
	/**
	 * 是否正确
	 */
	private Boolean enabledCorrect;
	/**
	 * 创建人id
	 */
	private Integer creatorId;
	/**
	 * 创建人姓名
	 */
	private String creatorName;
	/**
	 * 创建时间
	 */
	private Date createdAt;
	/**
	 * 互动介绍(时间切片-快照)
	 */
	private String introduction;
	/**
	 * 互动介绍英文版(时间切片-快照)
	 */
	private String introductionEn;
	/**
	 * 选项记录(时间切片-快照),对mongo数据中的selectContent进行json格式储存
	 */
	private String selectContent;
	/**
	 * 互动时间-切片（秒）
	 */
	private Integer activeTime;
}
