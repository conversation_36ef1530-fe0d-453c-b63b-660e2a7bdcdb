## PostSelector 岗位选择器
> 贡献者：miraclehe(何名宇)；kenoxia(夏超男)；最近更新时间：2021-01-21；

用于选择岗位，涉及的数据源均为远程数据

### 基础用法

适用广泛的基础选择，提供3种方式选择岗位，用 Tag 展示已选岗位<br/>
<br/>1. 输入关键字搜索，使用下拉菜单展示筛选后的岗位； 2. 点击右侧按钮打开弹窗，懒加载岗位树<br/>
<br/>提醒: 请避免在选择后动态改变选择器的宽度，这会造成 Tag 区域样式显示问题

:::demo `v-model` 的值为当前被选中的岗位选项的 **PostID** 属性值，单选模式下需删除原有已选项才能进行再次选择。设置 `multiple` 属性即可启用多选，此时`v-model`的值为当前选中值所组成的数组
```html
<template>
  <div class="block">
    <span class="demonstration">单选模式</span>
    <sdc-post-selector v-model="value1" showFullTag></sdc-post-selector>
  </div>
  <div class="block">
    <span class="demonstration">多选模式</span>
    <sdc-post-selector multiple v-model="value2"></sdc-post-selector>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: []
      }
    }
  }
</script>
```
:::

### 设置初始选中项

由于选择器所需的岗位选项至少需要包含 `PostID` 、`PostName`、`PostFullName` 等属性，而本地没有完整的岗位数据，因此不能简单通过修改 `v-model` 值来设置初始选择项

:::demo 可通过 `setSelected` 方法设置初始选中项，它的输入参数是一个对象或该类对象组成的数组，对象应包含 `{ PostName, PostID, PostFullName }` 等属性，如果用户初始选中项字段与选择器默认的不一致，可通过 `props` 属性配置，同时，用户通过change事件得到的选中项里的字段名也会修改为 `props` 里定义的名称
```html
<template>
  <div class="block">
    <sdc-post-selector ref="selector1" v-model="value1" multiple></sdc-post-selector>
  </div>
  <div class="block">
    <sdc-post-selector ref="selector2" v-model="value2" :props="myProps" multiple></sdc-post-selector>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: [],
        value2: [],
        myProps: {
          postID: 'id',
          postName: 'name',
          postFullName: 'fullName'
        }
      }
    },
    mounted() {
      const initial1 = [{ PostID: 11, PostName: '总经理', PostFullName: 'TEG技术工程事业群/企业IT部/总经理' }]
      this.$refs.selector1.setSelected(initial1)
      const initial2 = [{ id: 11, name: '总经理', fullName: 'TEG技术工程事业群/企业IT部/总经理' }]
      this.$refs.selector2.setSelected(initial2)
    }
  }
</script>
```
:::

### 限制选项范围

用于仅提供某组织下的岗位选择<br/>
<br/>提醒: 动态切换组织时，已有选项需手动调用方法清空（考虑与组织选择器联动和数据回显等场景，选择器不会主动对已有选项做处理）

:::demo 可通过 `range.unitID` 属性设置仅选择某组织下的**子级**岗位;可通过 `range.isContainSubUnit` 属性设置仅选择某组织下的子组织岗位;可通过 `range.NotContainVirtualUnit` 属性设置是否能选择虚拟组织下的岗位;可通过 `defaultExpandedKeys` 属性设置一级默认展开节点
```html
<template>
  <sdc-post-selector 
    v-model="value" 
    :range="range" 
    placeholder="请选择" 
    :defaultExpandedKeys="[953]" 
    @change="change"
  ></sdc-post-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: [],
        range: { 
          unitID: [953, 29294, 956, 29292, 958, 14129, 78, 2233, 2234],
          isContainSubUnit: true,
          NotContainVirtualUnit: true
        }
      }
    },
    methods:{
      change(val){
        console.log(val)
      }
    }
  }
</script>
```
:::


### 文本域

用于多行展示被选择的岗位，通过设置 `textarea` 属性启用

:::demo 可通过 `height` 属性设置高度
```html
<template>
  <sdc-post-selector v-model="value" :height="170" textarea multiple placeholder="请选择"></sdc-post-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    }
  }
</script>
```
:::

### 尺寸

:::demo 可通过 `size` 属性指定输入框的尺寸，除了默认的大小外，提供了**medium**、**small**尺寸
```html
<template>
  <div class="block size-block">
    <span class="demonstration">默认尺寸</span>
    <sdc-post-selector v-model="value1"></sdc-post-selector>
  </div>
  <div class="block size-block">
    <span class="demonstration">中等尺寸</span>
    <sdc-post-selector size="medium" v-model="value2"></sdc-post-selector>
  </div>
  <div class="block size-block">
    <span class="demonstration">较小尺寸</span>
    <sdc-post-selector size="small" v-model="value3"></sdc-post-selector>
  </div>
</template>
<script>
  export default {
    data(){
      return{
        value1: '',
        value2: '',
        value3: ''
      }
    }
  }
</script>
```
:::

### 弹窗插入到body中

:::demo 可通过 `modalAppendToBody` 属性设置弹窗插入位置。默认false，设置为true时，弹窗会插入到body中。
```html
<template>
  <sdc-post-selector v-model="value" modalAppendToBody/>
</template>
<script>
  export default {
    data(){
      return{
        value: ''
      }
    }
  }
</script>
```
:::

### 数据源

岗位数据均从远程服务器获取，内置了3个默认数据源接口方法满足选择器的功能需求，用户如需替换自己的数据源，需按规范定义接口方法和数据结构，通过3个对应的属性传入`Function`<br/>每个`Function`接受一个输入参数用于指明需获取的岗位数据，返回一个`Promise`对象，除 **`getTreeData`** 以外，完成状态的结果值为包含**岗位对象**的**数组**，**岗位对象**至少包含 **`{ PostName, PostID, PostFullName }`** 三个属性<sup>**[1]**</sup>

:::demo **`getDataList`** 用于输入关键字筛选岗位的场景。输入参数为一个`String`类型的关键字字符串, Promise的结果值如 **[1]** 所示<br/><br/>**`getTreeData`** 用于点击岗位树中的组织节点，懒加载其子节点的场景。输入参数为一个能指明组织的`UnitID`，请注意，初始获取第一级节点时，输入的 **UnitID** 为 **0** ，Promise完成状态的结果值为包含`{ post, unit }`属性的对象，`post`属性值是一个包含**岗位对象**的**数组**，要求同 **[1]** ， `unit`属性值是一个包含**组织对象**的**数组**，**组织对象**至少包含 `{ UnitName, UnitID }` 两个属性<br/><br/>**`getChildrenData`** 用于点击岗位树中组织节点的多选框，批量选择岗位的场景（考虑数据量，目前仅选择**子级**岗位）。输入参数为一个能指明组织的`UnitID`，Promise的结果值如 **[1]** 所示

```html
<template>
  <sdc-post-selector v-model="value" multiple 
    modalWidth="1000px"
    :getDataList="customGetDataList"
    :getTreeData="customGetTreeData"
    :getChildrenData="customGetChildrenData">
  </sdc-post-selector>
</template>
<script>
  export default {
    data(){
      return{
        value: []
      }
    },
    methods: {
      /**
       * @method 模糊搜索对应的岗位
       * @param {String} name 关键字字符串
       * @returns 返回带有岗位列表数据的promise
       */
      customGetDataList(name) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          const remoteData = [
            { PostID: 11, PostName: '总经理', PostFullName: 'TEG技术工程事业群/企业IT部/总经理' },
            { PostID: 12, PostName: '副总经理', PostFullName: 'TEG技术工程事业群/企业IT部/副总经理' },
            { PostID: 13, PostName: '秘书', PostFullName: 'TEG技术工程事业群/企业IT部/秘书' },
            { PostID: 14, PostName: '业务合作伙伴', PostFullName: 'TEG技术工程事业群/企业IT部/业务合作伙伴' },
            { PostID: 15, PostName: '公司副总裁', PostFullName: 'TEG技术工程事业群/公司副总裁' },
            { PostID: 21, PostName: '高级副总裁', PostFullName: 'CDG企业发展事业群/高级副总裁' },
            { PostID: 22, PostName: 'CSO', PostFullName: 'CDG企业发展事业群/CSO' },
            { PostID: 23, PostName: '公司副总裁', PostFullName: 'CDG企业发展事业群/公司副总裁' },
            { PostID: 31, PostName: '业务合作伙伴', PostFullName: 'S3职能系统－HR与管理线/人力资源平台部/业务合作伙伴' },
            { PostID: 32, PostName: '高级管理顾问', PostFullName: 'S3职能系统－HR与管理线/高级管理顾问' }
          ]
          resolve(remoteData.filter(item => item.PostFullName.indexOf(name) !== -1))
        })
      },
      /**
       * @method 根据组织ID获取该组织下的子级组织、子级岗位列表
       * @param {String} UnitID 组织ID
       * @returns 返回带有组织、岗位列表数据的promise
       */
      customGetTreeData(UnitID) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          let data = []
          if (UnitID === 0) {
            data = {
              unit: [
                { UnitID: 1, UnitName: 'TEG技术工程事业群' },
                { UnitID: 2, UnitName: 'CDG企业发展事业群' },
                { UnitID: 3, UnitName: 'S3职能系统－HR与管理线' }
              ]
            }
          }
          if (UnitID === 1) {
            data = {
              post: [{ PostID: 15, PostName: '公司副总裁', PostFullName: 'TEG技术工程事业群/公司副总裁' }],
              unit: [{ UnitID: 4, UnitName: '企业IT部' }]
            }
          }
          if (UnitID === 2) {
            data = {
              post: [
                { PostID: 21, PostName: '高级副总裁', PostFullName: 'CDG企业发展事业群/高级副总裁' },
                { PostID: 22, PostName: 'CSO', PostFullName: 'CDG企业发展事业群/CSO' },
                { PostID: 23, PostName: '公司副总裁', PostFullName: 'CDG企业发展事业群/公司副总裁' }
              ]
            }
          }
          if (UnitID === 3) {
            data = {
              post: [{ PostID: 32, PostName: '高级管理顾问', PostFullName: 'S3职能系统－HR与管理线/高级管理顾问' }],
              unit: [{ UnitID: 5, UnitName: '人力资源平台部' }]
            }
          }
          if (UnitID === 4) {
            data = {
              post: [
                { PostID: 11, PostName: '总经理', PostFullName: 'TEG技术工程事业群/企业IT部/总经理' },
                { PostID: 12, PostName: '副总经理', PostFullName: 'TEG技术工程事业群/企业IT部/副总经理' },
                { PostID: 13, PostName: '秘书', PostFullName: 'TEG技术工程事业群/企业IT部/秘书' },
                { PostID: 14, PostName: '业务合作伙伴', PostFullName: 'TEG技术工程事业群/企业IT部/业务合作伙伴' }
              ]
            }
          }
          if (UnitID === 5) {
            data = {
              post: [{ PostID: 31, PostName: '业务合作伙伴', PostFullName: 'S3职能系统－HR与管理线/人力资源平台部/业务合作伙伴' }]
            }
          }
          resolve(data)
        })
      },
      /**
       * @method 根据组织ID获取该组织下子级岗位列表
       * @param {String} UnitID 组织ID
       * @returns 返回带有岗位列表数据的promise
       */
      customGetChildrenData(UnitID) {
        // 这里是模拟后端处理
        return new Promise((resolve, reject) => {
          let data = []
          if (UnitID === 1) {
            data = [
              // { PostID: 11, PostName: '总经理', PostFullName: 'TEG技术工程事业群/企业IT部/总经理' },
              // { PostID: 12, PostName: '副总经理', PostFullName: 'TEG技术工程事业群/企业IT部/副总经理' },
              // { PostID: 13, PostName: '秘书', PostFullName: 'TEG技术工程事业群/企业IT部/秘书' },
              // { PostID: 14, PostName: '业务合作伙伴', PostFullName: 'TEG技术工程事业群/企业IT部/业务合作伙伴' },
              { PostID: 15, PostName: '公司副总裁', PostFullName: 'TEG技术工程事业群/公司副总裁' }
            ]
          }
          if (UnitID === 2) {
            data = [
              { PostID: 21, PostName: '高级副总裁', PostFullName: 'CDG企业发展事业群/高级副总裁' },
              { PostID: 22, PostName: 'CSO', PostFullName: 'CDG企业发展事业群/CSO' },
              { PostID: 23, PostName: '公司副总裁', PostFullName: 'CDG企业发展事业群/公司副总裁' }
            ]
          }
          if (UnitID === 3) {
            data = [
              // { PostID: 31, PostName: '业务合作伙伴', PostFullName: 'S3职能系统－HR与管理线/人力资源平台部/业务合作伙伴' },
              { PostID: 32, PostName: '高级管理顾问', PostFullName: 'S3职能系统－HR与管理线/高级管理顾问' }
            ]
          }
          if (UnitID === 4) {
            data = [
              { PostID: 11, PostName: '总经理', PostFullName: 'TEG技术工程事业群/企业IT部/总经理' },
              { PostID: 12, PostName: '副总经理', PostFullName: 'TEG技术工程事业群/企业IT部/副总经理' },
              { PostID: 13, PostName: '秘书', PostFullName: 'TEG技术工程事业群/企业IT部/秘书' },
              { PostID: 14, PostName: '业务合作伙伴', PostFullName: 'TEG技术工程事业群/企业IT部/业务合作伙伴' }
            ]
          }
          if (UnitID === 5) {
            data = [
              { PostID: 31, PostName: '业务合作伙伴', PostFullName: 'S3职能系统－HR与管理线/人力资源平台部/业务合作伙伴' }
            ]
          }
          resolve(data)
        })
      }
    }
  }
</script>
```
:::

### PostSelector Attributes
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| value / v-model | 绑定值 | string/number/Array | — | — |
| multiple | 是否多选 | boolean | — | false |
| size | 输入框尺寸 | string | medium/small | — |
| search | 是否模糊搜索 | boolean | — | true |
| disabled | 是否禁用 | boolean | — | false |
| showTotal | 多选且非textarea模式下，是否显示后置的已选数量 | boolean | — | true |
| showPostID | 筛选时是否展示PostID | boolean | — | false |
| placeholder | 占位符 | string | — | — |
| showLastLevels | 是否只展示最后一级 | boolean | — | true |
| showFullTag | 是否在输入框中展示完整的tag | boolean | — | false |
| filterEnableFlag | 是否只包含有效岗位 | boolean | — | true |
| defaultExpandedKeys | 一级默认展开的节点的unitID的数组 | array | — | [] |
| range | 限制选项范围，具体见下表 | Object | — | - |
| props | 数据字段别名，具体见下表 | object | — | — |
| selectClass | 选择框自定义类名 | string | — | — |
| modalClass | 弹窗自定义类名 | string | — | — |
| modalWidth | 弹窗自定义宽度 | string | 参考Modal弹窗组件width | '800px' |
| modalAppendToBody | 弹窗自身是否插入至 body 元素上。 | boolean | — | false |
| getDataList | 通过关键字获取对应岗位的方法 | Function | — | — |
| getTreeData | 通过组织标识获取其子组织、岗位的方法 | Function | — | — |
| getChildrenData | 通过组织标识获取其下所有岗位的方法 | Function | — | — |

### PostSelector Events
| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| change | 选中项发生变化时触发 | 目前的选中项 |

### PostSelector Methods
| 方法名 | 说明 | 参数 |
| ---- | ---- | ---- |
| setSelected | 用于外部直接设置选中项 | 包含PostName、PostID、PostFullName属性的对象或其组成的数组 |
| clearSelected | 用于清空选中项 | — |

### range 
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| unitID | 组织ID, 仅选择该组织下的子级岗位, 会先查对应的组织 | number/Array | — | - |
| isContainSubUnit | 是否包含子级岗位 | boolean | — | true |
| NotContainVirtualUnit | 是否包含虚拟组织岗位 | boolean | — | false |

### props
| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| postID | 岗位ID字段名 | string | — | 'PostID' |
| postName | 岗位名称字段名 | string | — | 'PostName' |
| postFullName | 岗位完整名称字段名 | string | — | 'PostFullName' |
| unitID | 组织ID字段名 | string | — | 'UnitID' |
