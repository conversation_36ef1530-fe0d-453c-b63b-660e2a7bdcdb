package com.tencent.hr.knowservice.courseInteraction.controller.user;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveConfigOutDto;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveSubmitDto;
import com.tencent.hr.knowservice.courseInteraction.service.InteractionService;
import com.tencent.hr.knowservice.courseInteraction.service.InteractiveRecordService;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @description:
 * @author: shi<PERSON>wang
 * @createDate: 2023/10/10
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/courseInteraction/user/interaction")
public class InteractionUserController {
    @Autowired
    InteractionService interactionService;

    @Autowired
    InteractiveRecordService recordService;

    /**
     * 获取课程互动配置项目
     * @param courseId 课程id
     * @param actType 课程类型枚举值
     * @return 配置项目
     */
    @GetMapping("/get-course-interaction")
    public TransDTO<InteractiveConfigOutDto> getBaseInteractionConfig(@RequestParam("courseId") String courseId,
                                                                      @RequestParam("actType") Integer actType){
        InteractiveConfigOutDto data =  interactionService.getBaseInteractionConfig(courseId,actType);
        return new TransDTO<InteractiveConfigOutDto>().withData(data).withCode(HttpStatus.SC_OK).withSuccess(true);
    }

    /**
     * 互动提交
     * @param dto
     * @return
     */
    @PostMapping("save-interaction-record")
    public TransDTO saveInteractionRecord(@RequestBody @Valid InteractiveSubmitDto dto){
        return recordService.saveInteractionRecord(dto);
    }

}
