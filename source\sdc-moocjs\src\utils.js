let messageCallback = null
let onloadCallback = null
let onPlayCallback = null
let onPauseCallback = null
let onCompleteCallback = null
let onErrorInfoCallback = null
let onStratAnswerCallback = null
let onEndAnswerCallback = null
let onAnswerDetailCallback = null
let onDetailBackHomeCallback = null
let mousemoveCallback = null
let iframeDOM = null
function getMessageMethods(e) {
  if (e.data.type === 'Mooc-task') {
    if (e.data.events === 'connect') {
      // 第三方发送过来建立联系的消息
      onloadCallback && onloadCallback(e)
      // 告知第三方接收到消息
      utils.postMessage(iframeDOM, 'accept')
    }
    if (e.data.events === 'play') {
      onPlayCallback && onPlayCallback(e)
    }
    if (e.data.events === 'pause') {
      onPauseCallback && onPauseCallback(e)
    }
    if (e.data.events === 'complete') {
      onCompleteCallback && onCompleteCallback(e)
    }
    if (e.data.events === 'sendErrorInfo') {
      onErrorInfoCallback && onErrorInfoCallback(e)
    }
    if (e.data.events === 'startAnswer') {
      onStratAnswerCallback && onStratAnswerCallback(e)
    }
    if (e.data.events === 'endAnswer') {
      onEndAnswerCallback && onEndAnswerCallback(e)
    }
    if (e.data.events === 'answerDetail') {
      onAnswerDetailCallback && onAnswerDetailCallback(e)
    }
    if (e.data.events === 'detailBackHome') {
      onDetailBackHomeCallback && onDetailBackHomeCallback(e)
    }
    if (e.data.events === 'mousemove') {
      mousemoveCallback && mousemoveCallback(e)
    }
    messageCallback && messageCallback(e.data)
  }
}

const utils = {
  // 监听嵌入的第三方页面加载完成（一般在第三方页面的crated、ready、mouted触发发送消息）
  onload: (callback, dom) => {
    onloadCallback = callback
    iframeDOM = dom
  },
  // 监听第三方资源播放
  onPlay: (callback) => {
    onPlayCallback = callback
  },
  // 监听第三方资源暂停
  onPause: (callback) => {
    onPauseCallback = callback
  },
  // 监听第三方资源完成学习
  onComplete: (callback) => {
    onCompleteCallback = callback
  },
  // 监听第三方页面异常信息上报
  onErrorInfo: (callback) => {
    onErrorInfoCallback = callback
  },
  // iframe页面鼠标监听事件通信
  mousemoveIframe: (callback) => {
    mousemoveCallback = callback
  },
  // 考试系统-开始考试/开始练习
  onStratAnswer: (callback) => {
    onStratAnswerCallback = callback
  },
  // 考试系统-结束考试/结束始练习
  onEndAnswer: (callback) => {
    onEndAnswerCallback = callback
  },
  // 考试系统-详情页
  onAnswerDetail: (callback) => {
    onAnswerDetailCallback = callback
  },
  // 考试系统-详情页返回首页
  onDetailBackHome: (callback) => {
    onDetailBackHomeCallback = callback
  },
  // 所有来自iframe的消息事件
  messageListener: (callback, dom) => {
    messageCallback = callback
    iframeDOM = dom
  },
  // 向iframe发送消息
  postMessage: (iframeDOM, events, params, orgin = '*') => {
    if (!iframeDOM) return
    iframeDOM.contentWindow.postMessage({
      type: 'Mooc-task',
      events,
      params
    }, orgin)
  },
  // 通知第三方资源暂停播放
  setPause: (iframeDOM) => {
    utils.postMessage(iframeDOM, 'setPause')
  },
  // 通知第三方资源开始播放
  setPlay: (iframeDOM) => {
    utils.postMessage(iframeDOM, 'setPlay')
  },
  // 通知v8系统-隐藏视频完成状态
  hideFinishStatus: (iframeDOM) => {
    console.log('hideFinishStatus')
    utils.postMessage(iframeDOM, 'hideFinishStatus')
  },

  registerMessageListener: () => {
    window.addEventListener('message', getMessageMethods)
  },
  removeMessageListener: () => {
    window.removeEventListener('message', getMessageMethods)
  }
}

export default utils
