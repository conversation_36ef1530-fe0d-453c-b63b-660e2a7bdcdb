import babel from '@rollup/plugin-babel';
import { terser } from 'rollup-plugin-terser';

export default {
  input: 'src/index.js',
  output: [
    {
      file: 'dist/sdc-moocjs.js',
      format: 'umd',
      name: 'MOOCSDK',
    },
    {
      file: 'dist/sdc-moocjs.min.js',
      format: 'umd',
      name: 'MOOCSD<PERSON>',
      plugins: [terser()],
    },
  ],
  plugins: [babel({ babelHelpers: 'bundled' })],
};