@font-face {
  font-family: 'icomoon';
  src:  url('./fonts/icomoon.eot?h6xgdm');
  src:  url('./fonts/icomoon.eot?h6xgdm#iefix') format('embedded-opentype'),
  url('./fonts/icomoon.ttf?h6xgdm') format('truetype'),
  url('./fonts/icomoon.woff?h6xgdm') format('woff'),
  url('./fonts/icomoon.svg?h6xgdm#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-rate-face-off:before {
  content: "\e900";
}
.icon-rate-face-1:before {
  content: "\e901";
}
.icon-rate-face-2:before {
  content: "\e902";
}
.icon-rate-face-3:before {
  content: "\e903";
}
