package com.tencent.hr.knowservice.courseInteraction.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class InteractiveRecordVo {

    /**
     * 互动标题
     */
    private String title;
    /**
     * 互动标题-英文
     */
    private String titleEn;

    /**
     * 互动介绍
     */
    private String introduction;

    /**
     * 互动介绍
     */
    private String introductionEn;

    /**
     * 选项记录
     */
    private String selectContent;

    /**
     * 互动时间片
     */
    private Integer activeTime;
    /**
     * 记录表id
     */
    private Integer id;
    /**
     * 答题记录ID，使用学习记录的id，这样还可以查询对应的学习记录ID
     */
    private String recordId;
    /**
     * 题目id
     */
    private String questionId;
    /**
     * 课程类型
     */
    private Integer actType;
    /**
     * 课程类型名
     */
    private String actTypeName;
    /**
     * 课程id
     */
    private String courseId;
    /**
     * 互动类型 CHOOSE--选择 VOTE-投票 (时间切片-快照)
     */
    private String activeType;
    /**
     * 完成条件 choose--选择即可；correct--选择正确 (时间切片-快照)
     */
    private String completionConditions;
    /**
     * 互动行为
     */
    private String activeAnswer;
    /**
     * 是否正确
     */
    private Boolean enabledCorrect;
    /**
     * 互动分数
     */
    private Float score;
    /**
     * 删除时间
     */
    private Boolean enabled;
    /**
     * 创建人id
     */
    private Integer creatorId;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 最后修改人id
     */
    private Integer updateId;
    /**
     * 最后修改人姓名
     */
    private String updateName;
    /**
     * 最后修改时间
     */
    private Date updatedAt;
}
