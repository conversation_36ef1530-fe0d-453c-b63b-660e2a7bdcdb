package com.tencent.hr.knowservice.courseInteraction.dto;

import com.tencent.hr.knowservice.courseInteraction.dto.condition.InteractiveAnswers;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 互动提交DTO
 * <AUTHOR>
 */
@Data
public class InteractiveSubmitDto {

    @NotBlank(message = "interactiveConfigId 不能为空")
    private String interactiveConfigId;
    /**
     * 某一个时间点的互动配置唯一标识（支持同一个时间点，多个不同的配置）
     */
    private String interactiveId;

    @NotBlank(message = "recordId 学习记录id不能为空")
    private String recordId;

    private List<InteractiveAnswers> answers;

}
