package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.proxy.GroupMatrixSerivceApi;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.DeviceUtil;
import com.tencent.hr.knowservice.utils.JsonUtils;
import com.tencent.hr.knowservice.utils.MobileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@Slf4j
@RequestMapping("/api/businessCommon/common/recommend")
public class RecommendController {

    @Autowired
    GroupMatrixSerivceApi groupMatrixService;

    /**
     * 搜索相似课程。
     * 注意，目前相似课程组装是业务系统自己组装。
     * @param moduleId 模块id
     * @param itemId 业务系统id
     * @param limit 限制
     * @return
     */
    @GetMapping("/get-similar-item")
    public TransDTO getSimilarItem(@RequestParam("module_id") Integer moduleId,
                                   @RequestParam("item_id") Integer itemId,
                                   @RequestParam(value = "limit",required = false,defaultValue = "5") Integer limit) {
        ContextEntity current = GatewayContext.current();
        Integer staffId = Integer.valueOf(current.getStaffId());
        String searchSysId = "A9BiosXihR0h46ThNsAX";
        List<String> moduleIds = new ArrayList<>();
        moduleIds.add(String.valueOf(moduleId));
        List<String> sysIds = new ArrayList<>();
        sysIds.add(searchSysId);
        //过滤条件
        Map<String, Object> filters = new HashMap<>();
        //相关推荐不需要面授课。所以面授课的相关推荐时需要去掉filters中的module_id。面授课的 moduleId = 2
        if (2 != moduleId){
            filters.put("module_id", moduleIds);
        }
        filters.put("status", new int[]{1});
        filters.put("sys_id",sysIds);

        //返回数据格式
        Map<String, Object> data = new HashMap<>();
        data.put("source_fields_include", new String[]{"id", "module_id", "module_name", "item_id", "title","brief", "labels", "view_count", "thumbnail_url", "href", "created_at", "updated_at", "origin_data"});
        data.put("must_not_exists_fields", new String[]{"deleted_at"});
        data.put("filter_terms", filters);

        Map<String, Object> params = new HashMap<>();
        params.put("userId", staffId);
        params.put("itemId", searchSysId + "_" + moduleId + "_" + itemId);
        params.put("limit", limit);
        params.put("data", data);
        //获取数据
        String result =  groupMatrixService.similarCourse(params);
        TransDTO dataDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO>() {});
        boolean isMobile = DeviceUtil.isMobile(current.getCurrentRequest());
        if(isMobile) {
            if (dataDTO.getSuccess()) {
                ArrayList<LinkedHashMap<String, Object>> items = (ArrayList<LinkedHashMap<String, Object>>) dataDTO.getData();
                if (items != null) {
                    for (LinkedHashMap<String, Object> item : items) {
                        if (item.containsKey("thumbnail_url") && item.get("thumbnail_url") != null) {
                            String imgUrl = item.get("thumbnail_url").toString();
                            item.put("thumbnail_url", MobileUtils.transferMobilePath(imgUrl));
                        }
                    }
                }
            }
        }


        //不管是否成功，都不提示错误信息到前端
        dataDTO.setSuccess(true);
        dataDTO.setCode(HttpStatus.SC_OK);
        return dataDTO;
    }
}
