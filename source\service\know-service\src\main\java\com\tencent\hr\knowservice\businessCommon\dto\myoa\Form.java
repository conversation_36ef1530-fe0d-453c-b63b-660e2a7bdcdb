package com.tencent.hr.knowservice.businessCommon.dto.myoa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 自定义表单
 * <AUTHOR>
 */
@Data
public class Form {

    /**
     * 控件名称
     */
    private String name;

    /**
     * 控件值的集合，数组类型
     */
    private List<String> values;

    /**
     * 控件的默认值
     */
    @JsonProperty(value = "default_value")
    private String defaultValue;

    /**
     * 控件的描述
     */
    private String description;

    /**
     * 控件的类型，目前仅支持五种类型：TextBox,DropDownList,RadioBox,CheckBox,DatePicker
     */
    private String uiType;

    /**
     * 提交表单用，决定此字段是否必填，布尔类型
     */
    @JsonProperty(value = "is_required")
    private Boolean isRequired;

    /**
     * <font color="red">[暂不支持]</font> 提交表单用，自定义的校验工具，详细结构见下表
     */
    private Validator validator;

    /**
     * <font color="red">[暂不支持]</font> UI的一些附加描述信息,用于应用系统和MyOA前端渲染沟通使用
     */
    @JsonProperty(value = "ui_tags")
    private Map uiTags;
}
