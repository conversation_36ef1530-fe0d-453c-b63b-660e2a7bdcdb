## FlowEngine 流程引擎
> 贡献者：cxyxhhuang(黄鑫杰)；jeel<PERSON>(刘志杰)；最近更新时间：2020-10-12；

该组件基于@antv/g6开源库开发，依赖Vue环境。

<a href="http://test.hrflow.oa.com/flowDefinition" target="_blank">体验地址点这里：http://test.hrflow.oa.com/flowDefinition</a>


### 引入方式

```
tnpm i @tencent/sdc-flow-engine --save
```

### 使用方式

```
// 引用相关文件，在引用组件
import { FlowEngine, FlowTrack } from '@tencent/sdc-flow-engine'

...

components: {
  FlowEngine,
  FlowTrack
}

// 空数据格式，不传data默认为空数据
data() {
  graphData: {
    nodes: {},
    lines: {}
  }，
  trackData: {
    InstanceId: 0,
    FlowDefine: {},
    CurFlowInstance: {},
    InstActivities: [],
    InstTaskItems: [],
    Handlers: []
  }
}

...

// data为绑定的数据
<FlowEngine :data="graphData"/>
<FlowTrack :data="trackData"/>

```

### 基本图示

FlowEngine

<img src="../../assets/img/flow/img1.png" width="100%">

FlowTrack

<img src="../../assets/img/flow/img2.png" width="100%">

### FlowEngine使用

组件区域介绍

<img src="../../assets/img/flow/img3.png" width="100%">

基本操作

1. 创建节点：左侧节点区域，点击某个节点并拖动到右侧图形区域，释放鼠标创建节点
2. 节点拖动：按住节点移动鼠标
3. 节点连线：首先点击工具栏的连线图标，然后鼠标点击某个节点，拖动到另一个节点释放鼠标，若想在对节点操作，需再次点击连线图标，取消连线模式，双击画布也可以取消
4. 移动画布：点击画布其他区域，移动鼠标
5. 画布放大缩小：鼠标滚轮滚动或者点击工具栏的放大缩小图标
6. 画布自适应布局：点击工具栏的第三个图标
7. 画布原始比例居中：点击工具栏第四个图标
8. 查看节点/连线属性：点击节点/连线后，在右侧详细属性可以查看
9. 修改节点/连线属性：选中节点/连线后，在右侧没详细属性修改
10. 删除节点/连线：选中节点/连线，点击回退键
11. 上传/下载数据文件：点击工具栏的上传/下载图标(第七和第八个)
12. 清空画布：点击工具栏第六个图标


### FlowEngine获取数据

```
// 绑定ref
<FlowEngine ref="flowEngine" :data="graphData"/>

...

// 获取数据
this.$refs.flowEngine.getData()
```

### FlowEngine校验处理人是否合法

```
// 绑定ref
<FlowEngine ref="flowEngine" :data="graphData"/>

...

// 获取校验数据
let result = this.$refs.flowEngine.validateHandlerName()
```
返回结果：
`result: {
  valid: Boolean,
  errorList: Array
}`

valid代表是否通过校验，errorList代表未通过校验任务节点的名称

### FlowEngine只读模式
```
// 设置viewMode为true
<FlowEngine ref="flowEngine" :data="graphData" :viewMode="true"/>
```
只读模式下，用户不可创建/删除节点，工具栏连线，清空，上传功能不可用，节点属性不可修改

### FlowEngine节点锁定属性

若想锁定节点的属性或功能可以设置`showLockSwitch`属性为true（注意不要设置`viewMode`属性为true），设置该属性后会在详细属性面板里出现节点锁定项，目前支持锁定名称、负责人、删除功能，锁定名称和负责人在后续该字段无法被修改，锁定删除功能，该字段将无法删除

```
// 设置showLockSwitch为true
<FlowEngine ref="flowEngine" :data="graphData" :showLockSwitch ="true"/>
```

<img src="../../assets/img/flow/img4.png" width="300">

后续想使用锁定功能，则不设置`showLockSwitch`属性（默认为false），如果节点有锁定项，则后面在操作该节点时，会有相应的提示内容

<img src="../../assets/img/flow/img5.png" width="300">

### FlowEngine员工选择器功能

若想使用员工选择器功能，可以传入一个`getStaffList`属性，例如：

```
// 设置getStaffList为true，或者是一个返回promise函数
<FlowEngine
  :data="graphData"
  :showLockSwitch="false"
  :getStaffList="true"/>
```

传入后，组件会显示员工选择器组件，如图所示

<img src="../../assets/img/flow/img7.png" width="300">

函数必须返回一个Promise对象，并且resolve的数据为`StaffName`和`StaffID`的对象数组

```
getStaffList() {
  return new Promise((resolve, reject) => {
    resolve([
      {
        "StaffName": "test1",
        "StaffID": "123"
      },
      {
        "StaffName": "test2",
        "StaffID": "456"
      },
    ])
  })
}
```

### FlowEngine自定义工具栏

若需要自定义工具栏，可以使用`toolBar`属性，中间分隔线使用|，不加此属性默认全部显示，目前支持的工具有：

- zoomOut：放大
- zoomIn：缩小
- fitView：自适应屏幕
- fitCenter：原比例自动居中
- addLine：添加连线
- clear：清空画布
- downloadFile：下载文件
- uploadFile：上传文件
- help：帮助

```
<FlowEngine
  ref="flowEngine"
  :data="graphData"
  :toolBar="['zoomOut', 'zoomIn', '|', 'fitView', 'fitCenter', '|',
  'addLine', 'clear', '|', 'downloadFile', 'uploadFile', '|', 'scrollZoom', 'help']"/>
```

### FlowEngine 属性

| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| data | 源数据 | Object | — | { nodes: {}, lines: {} }
| viewMode | 是否为只读模式，只读模式下，不可创建/删除节点，工具栏连线，清空，上传功能不可用，节点属性不可修改 | Boolean | true/false | false
| showLockSwitch | 是否显示锁定节点开关 | Boolean | true/false | false
| showRoleAndTaskInput | 是否显示详细数据处理人角色和处理人URL | Boolean | true/false | true
| showLineAttributes | 是否显示边数据面板中的名称和流转条件及边上的文字 | Boolean | true/false | true
| canScrollZoom | 鼠标在画布上滚动是否放大/缩小画布 | Boolean | true/false | true
| getStaffList | 若给组件传getStaffList属性为true或者自定义函数，则将员工名输入框替换成员工选择器，支持远程搜索 | Boolean/Function | true/false/自定义函数 | false
| toolBar | 自定义工具栏内容 | Array | - | ['zoomOut', 'zoomIn', '\|', 'fitView', 'fitCenter', '\|', 'addLine', 'clear', '\|', 'downloadFile', 'uploadFile', '\|', 'help']

### FlowTrack 属性

| 参数      | 说明          | 类型      | 可选值                           | 默认值  |
|---------- |-------------- |---------- |--------------------------------  |-------- |
| data | 源数据 | Object | — | { InstanceId: 0, FlowDefine: {}, CurFlowInstance: {}, InstActivities: [], InstTaskItems: [], Handlers: []}
