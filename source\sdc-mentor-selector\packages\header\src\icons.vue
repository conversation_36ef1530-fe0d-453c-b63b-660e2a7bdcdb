<template>
  <fragment>
    <sdc-link><i class="el-icon-setting"></i></sdc-link>
    <sdc-link><i class="el-icon-bell"></i></sdc-link>
  </fragment>
</template>

<script>
  import { Fragment } from 'vue-fragment'
  import { locale } from 'mixins'
  import SdcLink from 'packages/link'

  export default {
    name: 'sdc-icons',
    mixins: [locale],
    components: {
      Fragment,
      SdcLink
    }
  }
</script>
