package com.tencent.hr.knowservice.businessCommon.service.MultiLang;


import com.tencent.hr.common.util.encrypt.DigestUtil;
import com.tencent.hr.knowservice.businessCommon.constans.CacheExpireConstant;
import com.tencent.hr.knowservice.businessCommon.constans.CommonCacheKeyEnum;
import com.tencent.hr.knowservice.businessCommon.dto.MultiLang.Lang;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.JsonUtils;
import com.tencent.hr.knowservice.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Map;

@Component
public class MultiLanguageService {
    @Autowired
    private RedisUtil redisUtil;
    @Value("${extapi.multiLanguage.host}")
    private String host;
    @Value("${extapi.multiLanguage.moocSystemId}")
    private String moocSystemId;
    @Value("${extapi.multiLanguage.moocToken}")
    private String moocToken;

    @Value("${extapi.rio.token}")
    private String rioToken;

    /**
     * 双语资源转换
     *
     * @param recourseKey
     * @return
     */
    public String getRecourseText(String systemId,String token,String modules,String recourseKey) {
        String language = Lang.ZH_CN;
        ContextEntity current = GatewayContext.current();
        if(current != null){
            //获取上下文中的语言版本
            language = current.getLanguage();
        }
        return getRecourseText(systemId,token,modules,recourseKey,language);
    }

    /**
     * 双语资源转换
     *
     * @param resourceKey
     * @return
     */
    public String getRecourseText(String systemId,String token,String modules,String resourceKey,String language) {
        String resourceText = "";
        if (StringUtils.isNotEmpty(resourceKey)) {
            Map<String, Object> map = null;
            //获取上下文中的语言版本
            if (language == null) {
                //没传默认中文
                language = Lang.ZH_CN;
            }
            String redisKey = CommonCacheKeyEnum.LanguageInfo.getKeyName() + systemId + modules + language;
            Object redisValue = redisUtil.get(redisKey);
            if (redisValue == null) {
                String path = "/api/out/getresourcejson2?systemid=" + systemId + "&languagename=" + language + "&modules=" + modules;
                HttpHeaders httpHeaders = setHeaders(systemId,token);
                StringBuffer errorMsg = new StringBuffer();
                //获取对应语言数据
                String result = HttpUtil.sendGetByRestTemplate(host, path, httpHeaders, errorMsg);
                if (StringUtils.isEmpty(errorMsg.toString())) {
                    map = JsonUtils.jsonToMap(result);
                    if(!map.isEmpty()) {
                        redisUtil.set(redisKey, map);
                        //缓存30分钟
                        redisUtil.expire(redisKey, CacheExpireConstant.CacheExpireEnum.Cache_Time_Expire_30_minute.getTime());
                    }
                }
            } else {
                map = (Map<String, Object>) redisValue;
            }
            if (map != null) {
                String result = (String) map.get(resourceKey);
                if (result != null) {
                    resourceText = result;
                }
            }
        }
        return resourceText;
    }

    /**
     * 设置请求头
     *
     * @return
     */
    private HttpHeaders setHeaders(String systemId,String token) {
        String timestamp = String.valueOf(Instant.now().getEpochSecond());
        String signature = DigestUtil.sha256().digest(timestamp + token).toLowerCase();
        //rio 应用网关头
        HttpHeaders httpHeaders = HeaderSignUtil.getRioHeader(rioToken);
        //多语的头
        httpHeaders.add("systemid", systemId);
        httpHeaders.add("timestampoa", timestamp);
        httpHeaders.add("signatureoa", signature);

        return httpHeaders;
    }

    /**
     * 获取mooc后台资源的多语
     *
     * @param recourseKey
     * @return
     */
    public String getApiRecourseText(String recourseKey) {
        String language = Lang.ZH_CN;
        ContextEntity current = GatewayContext.current();
        if(current != null){
            //获取上下文中的语言版本
            language = current.getLanguage();
        }
        return getRecourseText(moocSystemId,moocToken,"Api",recourseKey,language);
    }

    /**
     * 获取mooc后台资源的多语
     *
     * @param recourseKey
     * @param defaultText 获取失败后的默认值
     * @return
     */
    public String getApiRecourseText(String recourseKey,String defaultText) {
        String result = "";
        String language = Lang.ZH_CN;
        ContextEntity current = GatewayContext.current();
        if(current != null){
            //获取上下文中的语言版本
            language = current.getLanguage();
        }
        try {
            result = getRecourseText(moocSystemId, moocToken, "Api", recourseKey, language);
        }catch (Exception ex){
            result = defaultText;
        }
        if(StringUtils.isEmpty(result)){
            result = defaultText;
        }
        return result;
    }



}
