package com.tencent.hr.knowservice.framework.config;

import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 给Feign请求统一加上请求的staffid和staffname
 *
 * <AUTHOR>
 */
@Configuration
public class FeignClientsConfiguration {

    @Bean
    public RequestInterceptor requestInterceptor() {
        return (RequestTemplate requestTemplate) -> {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String staffId = request.getHeader("staffid");
                String staffName = request.getHeader("staffname");
                if (StringUtils.isBlank(staffId)){
                    staffId = request.getHeader(Constants.TASHeaderEnum.STAFF_ID.toString());
                    staffName = request.getHeader(Constants.TASHeaderEnum.USER_NAME.toString());
                }
                requestTemplate.header("staffid", staffId);
                requestTemplate.header("staffname", staffName);

                requestTemplate.header("caagw-staffid", staffId);
                requestTemplate.header("caagw-username", staffName);
            }

        };
    }

}
