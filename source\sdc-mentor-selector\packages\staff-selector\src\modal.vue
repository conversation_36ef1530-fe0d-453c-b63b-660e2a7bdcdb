<template>
  <div class="selector-modal">
    <sdc-modal ref="modal" :title="modalProps.title" :width="modalWidth || '750px'" adaptive :showFooter="false" :customClass="modalClass" :appendToBody="modalAppendToBody" :class="{'selector-modal-append-to-body': modalAppendToBody}">
      <div slot="body">
        <div class="left-side">
          <div class="tree-list">
            <el-tree ref="tree" :check-strictly="true" :props="treeProps" :data="treeData" lazy node-key="id" :load="loadNode" v-if="opened" v-loading="loading" @node-click="nodeClick">
              <span class="tree-node" slot-scope="{ node, data }">
                <span>
                  <img :src="data[map.avatar] || avatarUrl" v-on:error="notFound" v-if="data.type===map.type.staff" class="tree-node-avatar">
                  <span class="tree-node-text">{{ node.label }}</span>
                </span>
                <el-checkbox v-model="node.checked" v-if="multiple || data.type === map.type.staff" :indeterminate="node.indeterminate" @change="handleCheckChange(node)" @click.native.stop/>
              </span>
            </el-tree>
          </div>
        </div>
        <div class="right-side">
          <div class="selected-info">
            <span>{{selectedItemsText}}</span>
            <i class="el-icon-delete" @click="handleClear" v-show="selectedData.length"></i>
          </div>
          <div class="selected-list" :key="reloadKey">
            <div class="list-item" v-for="item in getSelectedItems(selectedData)" :key="item[map.staffID]">
              <el-tooltip placement="top-end" :content="item._text" :disabled="item._text.length <= item._modal.minLength" popper-class="sdc-selector-modal-popper">
                <span>
                  <img :src="item[map.avatar] || avatarUrl" v-on:error="notFound" class="list-item-avatar">
                  <span class="list-item-name">{{textEllipsis(item._text, item._modal) }}</span>
                </span>
              </el-tooltip>
              <span @click="handleDelete(item)" class="list-item-icon"><i class="el-icon-error"></i></span>
            </div>
          </div>
          <div class="modal-buttons">
            <el-button size="small" @click="hide">{{$st('sdc.selector.modal.cancel')}}</el-button>
            <el-button size="small" type="primary" @click="handleConfirm">{{$st('sdc.selector.modal.ok')}}</el-button>
          </div>
        </div>
      </div>
    </sdc-modal>
  </div>
</template>

<script>
  import { DataUtil } from 'sdc-core'
  import { textEllipsis, hasOwn, isNotLogin } from 'sdc-webui/src/utils/main'
  import { modal, locale, map } from 'mixins'
  import SetImg from 'directives/set-img'
  import Toast from 'packages/toast'
  
  export default {
    name: 'selector-modal',
    inject: ['multiple', 'textarea', 'nodeKey', 'map', 'change', 'selectedText', 'treeProps', 'modalProps', 'queryParams', 'getTreeData', 'getChildrenData', 'getCurrentItem', 'getRange', 'modalClass', 'modalWidth', 'defaultExpandedKeys', 'modalAppendToBody'],
    mixins: [modal, locale, map],
    directives: {
      setImg: SetImg
    },
    props: {
      data: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        selectedCount: 0,
        selectedData: [],
        treeData: [],
        opened: false,
        loading: false,
        resolveTree: null,
        lastLimitUnitID: '',
        reloadKey: 0
      }
    },
    computed: {
      selectedItemsText() {
        return this.selectedText.replace('$count', this.selectedCount)
      },
      avatarUrl() {
        return require('packages/theme-grace/img/avatar.gif')
      }
    },
    methods: {
      textEllipsis,
      notFound(event) {
        const ele = event.srcElement
        ele.src = this.avatarUrl
        ele.onerror = null
      },
      showModal() {
        if (this.opened) { // 非首次打开
          const level0Node = this.$refs.tree.root
          if (level0Node.childNodes.length === 0) {
            // 上一次打开加载失败时，需重新加载
            this.loadNode(level0Node, this.resolveTree)
          } else if (hasOwn(this.getRange(), 'unitID') && this.getRange().unitID !== this.lastLimitUnitID) {
            // 用户外部设置unitID时，需重新加载
            level0Node.childNodes = []
            this.loadNode(level0Node, this.resolveTree)
            this.lastLimitUnitID = this.getRange().unitID
          }
        } else { // 首次打开再访问数据
          this.lastLimitUnitID = this.getRange().unitID
          this.opened = true
        }
        this.selectedData = DataUtil.clone(this.data)
        this.selectedCount = this.selectedData.length
        const traverse = (node) => {
          const childNodes = node.root ? node.root.childNodes : node.childNodes
          childNodes.forEach(node => {
            const data = node.data
            if (data.type === this.map.type.staff) {
              const checked = this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])
              if (checked !== node.checked) {
                this.handleCheckItem(node, checked)
              }
            }
            traverse(node)
          })
        }
        if (this.$refs.tree) {
          traverse(this.$refs.tree.root)
        }
        this.show()
      },
      handleCheckItem(node, checked) {
        // const data = node.data
        node.indeterminate = checked === 'half'
        node.checked = checked === true
        const childNodes = node.childNodes.filter(item => item.data.type === this.map.type.staff)
        childNodes.forEach(child => {
          this.handleCheckItem(child, checked)
        })
        const parent = node.parent
        if (parent && parent.level !== 0) {
          this.reInitChecked(parent)
        }
      },
      handleCheckChange(node) {
        const { id, label, type, isLeaf, indeterminate, ...data } = node.data
        this.handleCheckItem(node, node.checked)
        if (type === this.map.type.staff) {
          if (!this.multiple && node.checked) {
            this.selectedData.forEach(data => {
              this.handleDelete(data)
            })
          }
          this.updateSelectedData([data], node.checked ? 'add' : 'delete', node.parent.data)
        } else if (type === this.map.type.unit) {
          this.getChildrenData(data[this.map.unitID], { ...this.queryParams, ...this.getRange() }).then(res => {
            this.updateSelectedData(res, node.checked ? 'add' : 'delete', data)
          })
        }
        this.$emit('check', node)
      },
      handleDelete(data) {
        this.updateSelectedData([data], 'delete')
        const node = this.$refs.tree.getNode(data[this.map.staffID])
        if (node) {
          this.handleCheckItem(node, false)
        }
      },
      handleConfirm() {
        const data = DataUtil.clone(this.selectedData)
        this.change(data)
        this.hide()
      },
      reInitChecked(node) {
        const { all, none, half } = this.getChildState(node.childNodes.filter(item => item.data.type === this.map.type.staff)) // 获取子节点的checked情况
        if (all) {
          node.checked = true
          node.indeterminate = false
        } else if (half) {
          node.checked = false
          node.indeterminate = true
        } else if (none) {
          node.checked = false
          node.indeterminate = false
        }
        const parent = node.parent
        if (parent && parent.level !== 0) {
          this.reInitChecked(parent)
        }
      },
      getChildState(nodes) {
        let all = nodes.length > 0
        let none = true
        nodes.forEach(node => {
          if (node.checked !== true || node.indeterminate) {
            all = false
          }
          if (node.checked !== false || node.indeterminate) {
            none = false
          }
        })
        return { all, none, half: !all && !none }
      },
      updateSelectedData(list, type, node) {
        if (type === 'add') {
          list.forEach(data => {
            if (!this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])) {
              data[this.map.unitID] = node[this.map.unitID]
              data[this.map.unitName] = node[this.map.unitName]
              data[this.map.unitFullName] = node[this.map.unitFullName]
              this.selectedData.push(data)
              this.selectedCount++
            }
          })
        } else if (type === 'delete') {
          list.forEach(data => {
            const index = this.selectedData.findIndex(item => item[this.nodeKey] === data[this.nodeKey])
            if (index !== -1) {
              this.selectedData.splice(index, 1)
              this.selectedCount--
            }
          })
        }
      },
      loadNode(node, resolve) {
        if (!this.resolveTree) {
          this.resolveTree = resolve
        }
        // 判断组件是否有传unitID进来，有传的话需要兼容处理，原先Number类型，现在Array类型，需要把Number转Array
        const baseId = this.getRange().unitID ? (!Array.isArray(this.getRange().unitID) ? [this.getRange().unitID] : this.getRange().unitID) : 0
        const id = node.level === 0 ? baseId : node.data[this.map.unitID]
        const isCache = (!hasOwn(this.getRange(), 'unitID') && node.level === 0) // 由外部给定顶层组织，和点击node时不读取缓存
        node.level === 0 && (this.loading = true)
        this.getTreeData(id, { ...this.queryParams, isCache, ...this.getRange() }).then(res => {
          let staffList = res[this.map.type.staff] || []
          let unitList = res[this.map.type.unit] || []
          // 添加tree节点需要的一些属性
          staffList = staffList.map(data => ({
            ...data,
            id: data[this.nodeKey], // 用于设置checkbox的选中状态
            label: data[this.map.staffName],
            isLeaf: true,
            type: this.map.type.staff,
            indeterminate: false
          }))
          unitList = unitList.map(data => ({
            ...data,
            id: 'unitID_' + data[this.map.unitID], // 用于设置checkbox的选中状态
            label: data[this.map.unitName],
            isLeaf: false,
            type: this.map.type.unit,
            indeterminate: false
          }))
          const children = staffList.concat(unitList)
          if (this.loading) {
            this.loading = false
          }
          resolve(children)
          if (this.$refs.tree) {
            const checkedNodeKeys = staffList.filter(data => this.selectedData.some(item => item[this.nodeKey] === data[this.nodeKey])).map(item => item[this.nodeKey])
            this.$refs.tree.setCheckedKeys([...checkedNodeKeys, ...this.$refs.tree.getCheckedKeys()])
          }
          this.reInitChecked(node)
          if (node.level === 0 && this.defaultExpandedKeys && this.defaultExpandedKeys.length) {
            node.childNodes.forEach(item => {
              if (this.defaultExpandedKeys.includes(item.data[this.map.unitID])) {
                item.expand()
              }
            })
          }
        }).catch(res => {
          resolve([])
          Toast(isNotLogin(res) ? this.$st('sdc.selector.notLogin') : this.$st('sdc.selector.failed'), 2000)
          if (this.loading) {
            this.loading = false
          }
          node.expanded = false
          node.loaded = false
          node.isLeaf = false
        })
      },
      handleClear() {
        this.selectedData.length = 0
        this.reloadKey++
        this.selectedCount = 0
        this.$refs.tree.setCheckedKeys([])
      },
      nodeClick(data, node) {
        if (data.type === this.map.type.staff) {
          node.checked = !node.checked
          this.handleCheckChange(node)
        }
      }
    }
  }
</script>
