## Wxheader 应用市场定制页面头部
> 贡献者：j<PERSON><PERSON>(刘志杰)；最近更新时间：2021-08-24

header组件是页面的头部，是layout的组件的一部分，可以单独使用header组件。

### 基础用法

:::demo 
```html
<template>
  <sdc-wxheader ref="wxheader" :wxData="wxdata" :activeAppKey="appKey" :userName="userName" :avatarUrl="avatar"></sdc-wxheader>
</template>
<script>
  export default {
    data() {
      return {
        wxdata: {
          "corpName": "测试企业",
          "menus": [
              {
                  "appKey": "contacts",
                  "appName": "人事通讯录",
                  "appUrl": "http://contacts-wxvendor.test-caagw.yunassess.com/"
              },
              {
                  "appKey": "regist",
                  "appName": "入职管理",
                  "appUrl": "http://regist-wxvendor.test-caagw.yunassess.com/pc"
              },
              {
                  "appKey": "vi",
                  "appName": "远程面试",
                  "appUrl": "http://vi-wxvendor.test-caagw.yunassess.com/"
              }
          ],
          "homeUrl": "http://assistant-wxvendor.test-caagw.yunassess.com/management",
          "guideUrl": "http://assistant-wxvendor.test-caagw.yunassess.com/management/guide",
          "userMenus": [
              {
                  "name": "智能客服",
                  "url": "https://aiwx.html5.qq.com/chat?key=be12f87b223653c6a867a2898ccdf5c58755ac8f6dad4f680ef0af308909300751edea14253fa432b47c52002fb0d58701ed78777efcdd618d8cbb66e67b3640e76b9bcfa1f6d3914fd9ef153aba27b0&id="
              }
          ]
        },
        appKey:'contacts',
        userName:'zhijie',
        avatar:'http://wework.qpic.cn/bizmail/ibNWofxhmpySxHiajpWLv0nJM28ORSSfS9g53GhJYbo98DH62PTrWm4g/00'
      }
    },
    mounted() {
      //TODO 初始化企业微信jssdk
      this.$refs.wxheader.initOpenDataComplate(false)
    }
  }
</script>
```
:::


### Header Attributes
| 参数          | 说明                                | 类型     | 可选值 | 默认值             |
| ------------- | ----------------------------------- | -------- | ------ | ------------------ |
| scope         | 布局类型(内网、外网)                | string   | oa/oc  | oc                 |
| wxData        | `getNavInfoByCorpKey`获取的导航数据 | object   | —      | —                  |
| activeAppKey  | 导航栏当前选择的应用key             | string   | —      | —                  |
| userName      | 登录用户名                          | string   | —      | —                  |
| avatarUrl     | 登录用户头像地址                    | string   | —      | —                  |
| logoutMethod  | 自定义退出登录方法                  | function | —      | 默认跳到`/_logout` |
| enableSidebar | 启用企微渠道侧边栏                  | boolean  | —      | false              |
