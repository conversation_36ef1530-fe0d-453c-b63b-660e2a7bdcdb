package com.tencent.hr.knowservice.businessCommon.controller.crontab;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.service.ClassRoomService;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/30/17:40
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/crontab/common")
public class BusinessCrontabController {

    @Autowired
    ClassRoomService classRoomService;

    /**

     * 推送班级全员数据
     * @param
     * @return
     */
    @PostMapping("/pushAllCourseStudent")
    public TransDTO pushAllStudent(){
        int i = classRoomService.pushAllCourseStudent();
        return new TransDTO().withSuccess(true).withCode(HttpStatus.SC_OK).withData(i);
    }
}
