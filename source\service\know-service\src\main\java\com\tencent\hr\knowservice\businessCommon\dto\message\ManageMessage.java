package com.tencent.hr.knowservice.businessCommon.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 发送信息工具包实体类
 */
@Data
public class ManageMessage extends MessageBase {

    /**
     *  发送类型
     */
    private String sendType;

    /**
     * 目标学员
     */
    private String targetStudent;

    /**
     * 是否需要发送
     */
    private Boolean canSend;

    //---------------------- 邮件 -------------------------//
    /**
     * 邮件抄送人
     */
    private String mailCc;
    /**
     * 邮件密送人
     */
    private String bcc;
    /**
     * 邮件模板id
     */
    private String templateId;


    //---------------------- HR助手 -------------------------//
    /**
     * 消息类型 （0 文字消息 1 图文消息）
     */
    private Boolean msgType = false;
    /**
     * 消息的链接地址
     */
    private String url;
    /**
     * 消息的图片地址
     */
    private String picUrl;

    //---------------------- 日历 -------------------------//
    /**
     * 日历提醒组织方
     */
    private String organize;
    /**
     * 地址
     */
    private String location;
    /**
     * 开始时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    /**
     *
     */
    private String moduleName;

    /**
     *  每次发送有信息的人数
     */
    private int sendPersonCount = 5000;

}
