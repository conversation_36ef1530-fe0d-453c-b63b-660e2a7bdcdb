package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.tencent.hr.knowservice.businessCommon.dao.entity.ProdFileReadRecordsSummary;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ProdFileReadRecordsSummaryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ProdFileReadRecordsSummary record);

    int insertSelective(ProdFileReadRecordsSummary record);

    ProdFileReadRecordsSummary selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ProdFileReadRecordsSummary record);

    int updateByPrimaryKey(ProdFileReadRecordsSummary record);

    List<ProdFileReadRecordsSummary> getFileViewCountByFileIds(List<Integer> fileIds);
}