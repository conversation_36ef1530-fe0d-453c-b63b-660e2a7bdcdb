/**
 * api接口统一管理
 */
import { get, post } from './http'

// 培养项目
let url = ''
if (!(location.hostname.endsWith('.woa.com') || location.hostname.endsWith('.oa.com'))) {
  url = '/prejob/api'
}
// 获取学期id
export const getSpocTermId = (params) => get(url + '/training/api/community/user/getSpocTermId', params)
// 获取课程列表
export const getCourseList = (params, ids) => get('/prejob/api/v1/user/course-info/get-course-list?' + ids, params)
// 获取帖子列表
export const getPostList = (params) => post(url + '/training/api/community/user/community_post_search', params)
// 获取帖子详情/单个帖子信息
export const getPostDetails = (params) => get(url + '/training/api/community/user/get_post_detail',  params )
// 点赞
export const postOperate = (params) => post(url + '/training/api/community/user/community_post_operate',  params )
// 获取话题列表
export const getTopicList = (params) => post(url + '/training/api/community/user/get_community_topic_infos',  params )
// 获取话题详情
export const getTopicDetail = (params) => post(url + '/training/api/community/user/get-topic-detail-info', params)
// 发布帖子
export const savePost = (params) => post(url + '/training/api/community/user/save_community_post',  params )
// 编辑帖子
export const editPost = (params) => post(url + '/training/api/community/user/update_community_post',  params )
// 获取帖子回帖
export const getComments = (params) => post(url + '/training/api/community/user/comment/get_comments',  params )
// 回帖
export const addComment = (params) => post(url + '/training/api/community/user/comment/add',  params )
// 删除回帖
export const deleteComment = (params, id) => post(url + '/training/api/community/user/comment/delete/' + id,  params )
// 回帖置顶
export const sticky = (params) => post(url + '/training/api/community/user/comment/sticky',  params )
// 回帖点赞
export const praised = (params) => post(url + '/training/api/community/user/comment/praised',  params )
// 获取话题回帖量
export const getTopicCommentCount = (params) => post(url + '/training/api/community/user/get-topic-detail-info',  params )
// 获取我的回帖信息
export const getReplyMsg = (params) => get(url + '/training/api/community/user/get_my_reply_msg',  params )