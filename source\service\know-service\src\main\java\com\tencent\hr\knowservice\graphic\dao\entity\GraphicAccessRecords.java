package com.tencent.hr.knowservice.graphic.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * graphic_access_records
 *
 *
 *
 */
@Data
public class GraphicAccessRecords implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 图文id
     */
    private Integer graphicId;

    /**
     * 学员id
     */
    private Integer staffId;

    /**
     * 学员姓名
     */
    private String staffName;

    /**
     * 记录来源(Qlearning/Email/Tips/Area/CourseList/Graphic/Other)
     */
    private String fromType;

    /**
     * 分享来源人id
     */
    private Integer fromStaffId;

    /**
     * 分享来源人姓名
     */
    private String fromStaffName;

    /**
     * 是否移动端0：是1:否
     */
    private Integer isMobile;

    /**
     * 开始浏览时间
     */
    private Date startTime;

    /**
     * 结束浏览时间
     */
    private Date endTime;

    /**
     * 浏览时间
     */
    private Long duration;

    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人ID
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;
    /**
     * 学习上报id
     */
    private String lrsStudyId;

    private static final long serialVersionUID = 1L;

}