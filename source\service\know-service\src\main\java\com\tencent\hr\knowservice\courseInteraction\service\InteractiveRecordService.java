package com.tencent.hr.knowservice.courseInteraction.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.courseInteraction.dao.entity.InteractiveRecordEntity;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveRecordInfoDto;
import com.tencent.hr.knowservice.courseInteraction.dto.InteractiveSubmitDto;
import com.tencent.hr.knowservice.courseInteraction.vo.InteractiveRecordExcelVo;

import java.io.IOException;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-10-12 09:33:27
 */
public interface InteractiveRecordService extends IService<InteractiveRecordEntity> {

    /**
     * 互动提交
     * @param dto
     * @return
     */
    TransDTO saveInteractionRecord(InteractiveSubmitDto dto);

    /**
     * 互动记录分页查询
     * @param current
     * @param size
     * @return
     */
    TransDTO getInteractiveRecordPage(int current, int size,Integer actType,String courseId);

    /**
     * 活动记录导出列表
     * @return
     */
    List<InteractiveRecordExcelVo> getInteractiveRecordList(int current, int size,Integer actType,String courseId);

    InteractiveRecordInfoDto getInteractiveRecord(Integer id) throws IOException;

    /**
     * 获取互动详情
     * @param id
     * @return
     */
    TransDTO getInteractiveRecords(String id);
}

