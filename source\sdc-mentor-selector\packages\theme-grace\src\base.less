@import "./vars.less";
@import "./normalize.less";

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /*点击元素清除默认高亮*/
  -webkit-tap-highlight-color: transparent;
}
html, body, #app {
  height: 100%;
}
a {
  color: @color-text-black;
  text-decoration: none;
}
a:focus,
a:hover,
a:visited {
  outline: none;
}
img {
  // width: 100%;
  border: none;
  vertical-align: middle;
}
a, img {
  /*清除默认链接，图片长按的下拉菜单*/
  -webkit-touch-callout: none;
}
input, textarea {
  outline: none;
  /*清除默认在ios上的文本框外观*/
  -webkit-appearance: none;
}
textarea {
  resize: none;
}
ul {
  list-style: none;
}
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  outline: none;
}
/*.clear-fix::after {*/
/*  clear: both;*/
/*  content: '';*/
/*  display: block;*/
/*  width: 0;*/
/*  height: 0;*/
/*  visibility: hidden;*/
/*}*/
.clear-fix::before,
.clear-fix::after {
  content: "";
  display: table;
}
.clear-fix::after {
  clear: both;
}
.clear-fix {
  *zoom: 1;
}
.left {
  float: left;
}
.right {
  float: right;
}
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.brick-item {
  cursor: pointer;
  transition: all .2s linear;
  &:hover {
    box-shadow: 0 15px 30px rgba(0,0,0,.1);
    transform: translate3d(0,-2px,0);
  }
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 9999;
  filter: alpha(Opacity=65);
  -moz-opacity: .65;
  opacity: .65;
}
.fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  border-top: none;
  background-color: rgba(244,249,255,.9);
  box-shadow: 0 5px 5px rgba(0,0,0,.1);
  z-index: 9999;
}
.fixed-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  border: none;
  background-color: rgba(244,249,255,.9);
  box-shadow: 5px 0 5px rgba(0,0,0,.1);
  z-index: 9999;
}
// element override style
.el-tooltip__popper.is-light {
  border: 0 !important;
  box-shadow: 0 0 7px 0 rgba(171,171,171,.4);
  &[x-placement^=top] .popper__arrow {
    border-top-color: #f5f7f9 !important;
  }
  &[x-placement^=bottom] .popper__arrow {
    border-bottom-color: #f5f7f9 !important;
  }
  &[x-placement^=left] .popper__arrow {
    border-left-color: #f5f7f9 !important;
  }
  &[x-placement^=right] .popper__arrow {
    border-right-color: #f5f7f9 !important;
  }
}

.el-input__inner, .el-textarea__inner {
  border-radius: 4px;
}

.el-switch.is-disabled.is-checked {
  opacity: 1;
  span {
    border-color: #CED9F8;
    background-color: #CED9F8;
  }
}

button.el-button {
  font-weight: normal;
}
