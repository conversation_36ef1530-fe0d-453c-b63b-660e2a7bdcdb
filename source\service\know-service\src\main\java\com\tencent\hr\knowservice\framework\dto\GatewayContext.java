package com.tencent.hr.knowservice.framework.dto;


import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: vincentyqwu
 * @createDate: 2020/6/3
 * @version: 1.0
 */
@Slf4j
public class GatewayContext {

    private static final ThreadLocal<ContextEntity> threadLocal = new ThreadLocal<ContextEntity>();

    public GatewayContext() {
    }

    public static void unset() {
        threadLocal.remove();
    }

    public static void set(ContextEntity val) {
        threadLocal.set(val);
    }

    public static ContextEntity get() {
        return threadLocal.get();
    }

    public static ContextEntity current() {
        return get();
    }


/*    private HttpServletRequest currentRequest;
    private ContextEntity current;
    private String env;

    public GatewayContext() {
        currentRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    public HttpServletRequest CurrentRequest() {
        return currentRequest;
        //return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }*/

    /**
     * 获取当前请求的网关header
     * ESB 渠道的Header信息
     {
         x-stgw-time=1615274315.860;
         x-client-proto=http;
         x-forwarded-proto=http,http;
         x-client-proto-ver=HTTP/1.1;
         x-real-ip=*************;
         x-forwarded-for=*************,*************;
         accept=text/plain, application/json, application/*+json;
         hrgw-timestamp=1615274315;
         hrgw-appname=portal-activity;
         hrgw-signature=5a063d28359bb22456ae605e998782ebb63cd0dac73840ef396f9a35f4c41aa1;
         staffid=186627;
         staffname=shizhouwang;
         caagw-staffid=186627;
         caagw-username=shizhouwang;
         cc-corpid=tencent;
         tsf-metadata=%7B%22ai%22%3A%22application-ydznq6ev%22%2C%22av%22%3A%22v20210305-7%22%2C%22sn%22%3A%22portal-activity%22%2C%22ii%22%3A%22s3rp-training-activity-675b4b65b6-qdfrk%22%2C%22gi%22%3A%22group-yqorgzdv%22%2C%22li%22%3A%22172.23.20.98%22%2C%22ni%22%3A%22namespace-4y43ljba%22%7D;
         user-agent=Java/1.8.0_191;
         caagw-corpid=cid;
         caagw-globalid=186627;
         caagw-nickname=shizhouwang;
         caagw-appkey=portal-activity;
         caagw-corpname=tencent;
         caagw-corpkey=tencent;
         caagw-platform=tsf;
         env=i;
         caagw-timestamp=1615274315;
         caagw-signature=5a063d28359bb22456ae605e998782ebb63cd0dac73840ef396f9a35f4c41aa1;
         caagw-channel=ESB;
         forwarded=proto=http;
         host=v2.ntsgw.woa.com;
         for=\"*************:46476\";
         x-forwarded-prefix=/api/esb/content-center;
         x-forwarded-port=80;
         x-forwarded-host=v2.ntsgw.woa.com;
         x-b3-traceid=8775817b85e0530a;
         x-b3-spanid=bdfeebbc470dfedf;
         x-b3-parentspanid=8775817b85e0530a;
         x-b3-sampled=1;
         host=v2.ntsgw.woa.com;
     }
     *
     * SSO 渠道的Header信息
      {
         x-real-ip=************;
         pragma=no-cache;
         cache-control=no-cache;
         user-agent=Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.25 Safari/537.36 Core/1.70.3861.400 QQBrowser/10.7.4313.400;accept=image/webp,image/apng,image/*,*\/*;
         q=0.8;
         referer=http://portal.learn.oa.com/activity/user/personalCenter;
         accept-language=zh-CN,zh;
         q=0.9;
         cookie=x-tofapi-host-key=178115eb3e8-d584112b3c9e0b83064b1238d5d73a4214954e8c;
         x-host-key-ngn=178115f7dde-49bcdc2933ad0588b12da9ee4a47b6dc53790e16; RIO_TCOA_TICKET=tof:EA145C4CA20635F26CD1F49CDD07532C645F79E106574FB7C815A60D6D21CAE6D57F2C7993BD6A266E73BBCDCE1E212415CCC4162D52C8E0994DD610F593E308DD0B65C4BFC7A73BD2444DEF2A4C9309371BF729F18CE17989E91D8F50238D0A9F2D4BBC28E71AD1CE499022112AE93BC7C1BD3FB65F469BA21A9B4E44D9BCE0ED53C94ECC1BC650D3D1E684A6FC8F9CC5D83682B953A1472263E1ED4E0F83E2556C6A933D540A603983EE0010F49A0FFA56D8E3767120696C8A2C06B77B1C351B50BB5D026910FF;
         x-host-key-front=17814b1dc07-4b03b405206ab5ec5574ffec6d4f774c0993ff6e;
         x-proxy-by=SmartProxy PC-OA-Gate;
         x-rio-seq=fc570e0a:017815da0c4d:08cb28;
         x-sg-ip-chain=************,************,************;
         x-ngn-network=intranet;x-client-ip=************;
         x-client-ip-port=************:58701;
         staffid=186627;
         staffname=shizhouwang;
         x-ext-data=;
         timestamp=1615274315;
         signature=B1CE94A2B1838CAFA2E58918FA36467905E78AD1701248C5E4C4406935C9FC5A;
         accept-encoding=gzip;
         hrgw-channel=SSO;
         forwarded=proto=http;
         host=*************;
         for=\"*************:11211\";
         x-forwarded-for=************,*************;
         x-forwarded-proto=http,http;
         x-forwarded-prefix=/api/sso/content-center;
         x-forwarded-port=80;
         x-forwarded-host=*************;
         x-b3-traceid=0504642562bb35d0;
         x-b3-spanid=6d64daff857c17c7;
         x-b3-parentspanid=0504642562bb35d0;
         x-b3-sampled=1;
         host=*************;
     }
     * PUB 渠道的Header信息
     {
         host=*************;
         content-type=text/json;
         charset=utf-8;
         user-agent=RIO_HTTP_API 1.0;
         accept-charset=utf-8;
         wcfrequestid=;
         accept-encoding=gzip;
         x-forwarded-host=*************;
         x-forwarded-proto=http;
         x-forwarded-prefix=/api/pub/content-center;
         tsf-metadata=%7B%22ai%22%3A%22application-gyqn68dy%22%2C%22av%22%3A%22hr-zuul-gateway_v20200807-5%22%2C%22sn%22%3A%22hr-zuul-gateway181-new%22%2C%22ii%22%3A%22tsf-gateway-zuul-prod-181-health-new-cd49c99f6-bflp8%22%2C%22gi%22%3A%22group-vkqn2gnv%22%2C%22li%22%3A%22172.23.5.196%22%2C%22ni%22%3A%22namespace-4y43ljba%22%7D;
         x-forwarded-port=80;
         staffname=system;
         x-forwarded-for=*************;
         tsf-tags=%5B%7B%22k%22%3A%22hrgw-channel%22%2C%22v%22%3A%22PUB%22%2C%22f%22%3A%5B%220%22%5D%7D%2C%7B%22k%22%3A%22staffname%22%2C%22v%22%3A%22system%22%2C%22f%22%3A%5B%220%22%5D%7D%2C%7B%22k%22%3A%22staffid%22%2C%22v%22%3A%22-1%22%2C%22f%22%3A%5B%220%22%5D%7D%5D;
         staffid=-1;
         hrgw-channel=PUB;
         content-length=2;
         connection=Keep-Alive;
         x-b3-traceid=677e1e44dc208455;
         x-b3-spanid=776cd40374818211;
         x-b3-parentspanid=677e1e44dc208455;
         x-b3-sampled=1;
     }

     * @return
     */
    /*public ContextEntity Current() {

        String staffId = "";
        String staffName = "";
        String globalId = "";
        String nickName = "";
        String appKey = "";
        String corpKey = "";
        String corpId = "";
        String corpName = "";
        String platform = "";
        String regionId = "";
        String channel = "";

        //改为每次重新获取用户身份
        try {
            Properties prop = PropertiesLoaderUtils.loadProperties(new ClassPathResource("config/application.properties"));
            env = prop.getProperty("spring.profiles.active");
        } catch (Exception ex) {
            env = "prod";
        }

            String logstr = "Current网关header信息：";
            Enumeration<String> er = currentRequest.getHeaderNames();
            while(er.hasMoreElements()) {
                String name = (String) er.nextElement();
                String value = currentRequest.getHeader(name);
                logstr += name + "=" + value + ";";

            }
            log.info(logstr);

            channel = this.currentRequest.getHeader(Constants.TASHeaderEnum.CHANNEL.toString());
            staffId = this.currentRequest.getHeader(Constants.TASHeaderEnum.STAFF_ID.toString());
            staffName = this.currentRequest.getHeader(Constants.TASHeaderEnum.USER_NAME.toString());
            globalId = this.currentRequest.getHeader(Constants.TASHeaderEnum.GLOBAL_ID.toString());
            nickName = this.currentRequest.getHeader(Constants.TASHeaderEnum.NICK_NAME.toString());
            corpKey = this.currentRequest.getHeader(Constants.TASHeaderEnum.CORP_KEY.toString());
            appKey = this.currentRequest.getHeader(Constants.TASHeaderEnum.APP_KEY.toString());
            corpId = this.currentRequest.getHeader(Constants.TASHeaderEnum.CORP_ID.toString());
            corpName = this.currentRequest.getHeader(Constants.TASHeaderEnum.CORP_NAME.toString());
            platform = this.currentRequest.getHeader(Constants.TASHeaderEnum.PLAT_FORM.toString());
            regionId = this.currentRequest.getHeader(Constants.TASHeaderEnum.REGION_ID.toString());
            //sso取网关的appid,esb和pub去用户自己传的appid
            if("ESB".equals(channel)||"PUB".equals(channel)){
                appKey = this.currentRequest.getHeader(Constants.TASHeaderEnum.HRGW_APPID.toString());
                corpId = this.currentRequest.getHeader(Constants.TASHeaderEnum.CC_CORPID.toString());
                corpName = this.currentRequest.getHeader(Constants.TASHeaderEnum.CC_CORPNAME.toString());
            }
        //}

        current = new ContextEntity();
        Integer StaffNo = 0;
        try {
            StaffNo = Integer.valueOf(staffId);
        } catch (Exception ex) {
            StaffNo = 0;
        }
        current.setStaffNo(StaffNo);
        current.setStaffId(staffId);
        current.setStaffName(staffName);
        current.setGlobalId(globalId);
        current.setNickName(nickName);
        current.setAppId(appKey);
        current.setCorpKey(corpKey);
        current.setCorpId(corpId);
        current.setCorpName(corpName);
        current.setPlatform(platform);
        current.setRegionId(regionId);
        current.setChannel(channel);

        return current;
    }*/


/*    *//**
     * 获取当前请求的应用Appid
     *
     * @return
     *//*
    public String getCurrentAppId() {
        String appId = "";
        AppInfoDTO app = this.getCurrentApp();

        if (app != null) {
            appId = app.getAppId();
        }
        return appId;
    }

    *//**
     * 获取当前请求的应用信息
     *
     * @return
     *//*
    public AppInfoDTO getCurrentApp() {
        AppInfoDTO app = null;
        //根据域名或站点目录来获取数据
        String path = this.CurrentRequest().getRequestURL().toString();
        try {
            URL url = new URL(path);
            // 获取主机名
            String host = url.getHost();

            String appId = "";
            AppInfoService appInfoService = (AppInfoService) SpringContextUtil.getBean("appInfoService");
            app = appInfoService.getAppByHost(host);
            if (app == null) {
                //该域名未注册
                throw new RestException("该域名:" + host + " 未绑定应用！");
            }
        } catch (MalformedURLException ex) {
            //未识别正确的请求地址
            throw new RestException("请求地址：" + path + " 不格式有误！");
        }
        return app;
    }*/
}
