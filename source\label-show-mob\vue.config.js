const path = require('path')
module.exports = {
    pages: {
        index:{
            entry: 'examples/main.js',
            template: 'public/index.html',
            filename: 'index.html'
        }
    },
    chainWebpack: config => {
        config.module
        .rule('js')
        .include
            .add('/packages')
            .end()
        .use('babel')
        .loader('babel-loader')
        .tap(options => {
          return options
        })

       const urlLoader = config.module.rule('images')
        // 清除已有的所有 loader,一定要进行的操作
        // 如果你不这样做，接下来的 loader 会附加在该规则现有的 loader 之后
        // 具体原理可自行查看webpack官网
        urlLoader.uses.clear()
        urlLoader
        .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
        .use('url-loader')
        .loader('url-loader')
        .options( {
          limit: 999999, // 1k
          name:'js/img/[name].[ext]',
          fallback: 'file-loader',  // 当超过1024byte时，会回退使用file-loader
          context: path.resolve(__dirname,'./src'),
          publicPath: '/' //采用根路径
        })
    },
    devServer: {
      host: 'test.woa.com',
      port: 4399,
      hot: true, // 自动保存
      disableHostCheck: true,
      overlay: {
        warnings: true,
        errors: true
      },
      proxy: {
        // 本地连接后台本地
        '/api': {
          target: 'http://test.portal.learn.oa.com', // 后台ip地址
          changOrigin: true,
          pathRewrite: {
            '^/api': ''
          }
        }
      }
    }
}
