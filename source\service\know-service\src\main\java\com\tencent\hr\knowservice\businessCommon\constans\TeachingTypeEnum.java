package com.tencent.hr.knowservice.businessCommon.constans;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/22/15:56
 * @version: 1.0
 */
public enum TeachingTypeEnum {
    OFFLINE_TEACHING("线下授课", 1, 2),
    ONLINE_TEACHING("线上授课",2,1),
    COMPREHENSIVE_TEACHING("结合授课",3,3);

    private String teachingTypeName;

    private Integer teachingType;

    private Integer classRoomTeachingType;

    TeachingTypeEnum(String teachingTypeName, Integer teachingType, Integer classRoomTeachingType) {

        this.teachingTypeName = teachingTypeName;
        this.teachingType = teachingType;
        this.classRoomTeachingType = classRoomTeachingType;
    }

    public String getTeachingTypeName() {
        return teachingTypeName;
    }

    public void setTeachingTypeName(String teachingTypeName) {
        this.teachingTypeName = teachingTypeName;
    }

    public Integer getTeachingType() {
        return teachingType;
    }

    public void setTeachingType(Integer teachingType) {
        this.teachingType = teachingType;
    }

    public Integer getClassRoomTeachingType() {
        return classRoomTeachingType;
    }

    public void setClassRoomTeachingType(Integer classRoomTeachingType) {
        this.classRoomTeachingType = classRoomTeachingType;
    }

    public static TeachingTypeEnum getActTeachingTypeEnum(Integer teachingType){
        if (teachingType == null){
            return null;
        }
        for (TeachingTypeEnum value : TeachingTypeEnum.values()) {
            if (teachingType.equals(value.getTeachingType())){
                return value;
            }
        }
        return null;
    }
}
