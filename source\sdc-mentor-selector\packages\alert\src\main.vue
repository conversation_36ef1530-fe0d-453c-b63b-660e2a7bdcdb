<template>
  <sdc-modal ref="modal" custom-class="sdc-alert" :title="title || $st('sdc.alert.title')"
             :size="size" :show-footer="false" @cancel="handleClose" @close="handleClose">
    <p slot="body" class="content">{{message || $st('sdc.alert.message')}}</p>
  </sdc-modal>
</template>

<script>
  import { DataType } from 'sdc-core'
  import { modal, locale } from 'mixins'
  import SdcModal from 'packages/modal'

  export default {
    name: 'sdc-alert',
    mixins: [modal, locale],
    data() {
      return {
        title: '',
        message: '',
        size: '',
        close: null
      }
    },
    methods: {
      handleClose() {
        DataType.isFunction(this.close) && this.close()
        this.hide()
        document.body.removeChild(this.$refs.modal.$el)
      }
    },
    components: {
      SdcModal
    }
  }
</script>
