package com.tencent.hr.knowservice.businessCommon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 延伸课程分页
 */
@Data
public class ExtendContentOutPageDTO {

    private Integer id;
    /**
     * 标题
     */
    private String contentName;

    /**
     * 跳转链接
     */
    private String href;
    /**
     * 1：置顶 0：非置顶
     */
    private Integer toped;

    /**
     * 关联时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone ="GMT+8")
    private Date createdAt;
    /**
     * 产品类型
     *
     */
    private Integer actType;

    /**
     * 产品id
     */
    private Integer actId;

    /**
     * 关联产品类型
     */
    private Integer contentModuleId;
    /**
     * 关联产品类型名称
     */
    private String contentModuleName;
    /**
     * 关联产品id
     */
    private String contentItemId;

    /**
     * 1：站内内容 2:自定义内容
     */
    private Integer contentType;
    /**
     * 观看数
     */
    private Integer viewCount;
    /**
     * 评分
     */
    private Double avgScore;
    /**
     * 关联产品创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone ="GMT+8" )
    private Date contentCreatedTime;

    /**
     * 可参与人群
     */
    private String targetPeople;

    /**
     * 课时
     */
    private Double estDur;

    /**
     * 字数
     */
    private Integer wordNum;

    /**
     * 封面图上传途径(contentcenter | zhihui | ql)
     */
    private String photoStorageType;

    /**
     * 封面图ID(内容中心的id或是智绘设计的id)
     */
    private String photoId;

    /**
     * 课程图片URL
     */
    private String photoUrl;

    /**
     * 点赞数
     */
    private Integer praiseCount;

    /**
     * 图文课程简介
     */
    private String courseDesc;

    private Integer duration;
    
    /**
     * 最后修改时间
     */
    private Date updatedAt;

}
