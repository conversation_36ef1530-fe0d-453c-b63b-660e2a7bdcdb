package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.tencent.hr.knowservice.businessCommon.dao.entity.VBaseEmpInfo;
import com.tencent.hr.knowservice.businessCommon.vo.DeptBpVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface VBaseEmpInfoMapper {
    VBaseEmpInfo getBaseEmpInfo(Integer staffId);

    /**
     * 获取所有员工
     * 在职、试用状态
     * 正式、试用、顾问、实习
     * @return
     */
    List<VBaseEmpInfo> getAllBaseEmpInfo();

    List<VBaseEmpInfo> getEmpInfosByNames(List<String> staffNames);

    /**
     * 获取上级可以查看的人
     * @param staffId
     * @param checkStaffId
     * @return
     */
   int getStaffIdCount(@Param("checkStaffId") Integer checkStaffId,@Param("staffId") Integer staffId);

    /**
     * 通过上级id 查询在orgList中的这些人 哪些具有查看权限
     * @param staffId
     * @param orgList
     * @return
     */
    List<Integer> getStaffIdByParent(@Param("staffId") Integer staffId,@Param("orgList") List<Integer> orgList);
    /**
     * 通过部门id获取员工信息
     */
    List<VBaseEmpInfo> getBaseEmpInfoByDeptId(@Param("deptId") String deptId);

    /**
     * 校验员工 checkStaffId 是否属于 staffId 所在的组织BP
     * @param staffId
     * @param checkStaffId
     * @return
     */
    int getOrgBpStaffCount(@Param("staffId") Integer staffId, @Param("checkStaffId") Integer checkStaffId);

    int getSubStaffIdCount(@Param("staffId") Integer staffId,@Param("moocCourseId") String moocCourseId);

    int getBpStaffCount(@Param("staffId") Integer staffId, @Param("moocCourseId")String moocCourseId);

    /**
     * 获取组织BP
     * @param staffId
     * @return
     */
    List<Integer> getBpStaffs(@Param("staffId") Integer staffId);

    /**
     * 通过部门id 查询每个部门对应的bp
     * @param deptIds
     * @return
     */
    List<DeptBpVo> getBpByDeptIds(@Param("deptIds") List<String> deptIds);
}
