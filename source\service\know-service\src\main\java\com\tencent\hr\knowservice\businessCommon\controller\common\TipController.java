package com.tencent.hr.knowservice.businessCommon.controller.common;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.common.PopupAndStaffMsgDTO;
import com.tencent.hr.knowservice.businessCommon.service.TipService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 提示弹窗
 */
@RestController
@Slf4j
@RequestMapping("/api/businessCommon/common/tip")
public class TipController {

    @Autowired
    private TipService tipService;

    /**
     * 关闭提示弹窗
     * @param actType 18:新图文 15课单 16 案例 999 qlearning
     * @return
     */
    @GetMapping("/close_popup")
    public TransDTO<Boolean> closePopup(@RequestParam("act_type") Integer actType,
                                        @RequestParam(value = "staff_id" ,required = false) Integer staffId,
                                        @RequestParam(value = "staff_name" ,required = false) String staffName){
        TransDTO<Boolean> dto = new TransDTO<>();
        Boolean result = tipService.closePopup(actType,staffId,staffName);
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withData(result);
    }

    /**
     * 获取是否关闭弹窗信息
     * @param actType 18:新图文 15课单 16 案例 999 qlearning
     * @return
     */
    @GetMapping("/get_popup_msg")
    public TransDTO<PopupAndStaffMsgDTO> getPoPupMsg(@RequestParam("act_type") Integer actType,
                                                     @RequestParam(value = "staff_id" ,required = false) Integer staffId){
        TransDTO<PopupAndStaffMsgDTO> dto = new TransDTO<>();
        PopupAndStaffMsgDTO pupMsg = tipService.getPoPupMsg(actType,staffId);
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withData(pupMsg);
    }

    /**
     * 弹窗展示控制开关
     * @return
     */
    @GetMapping("/display_control")
    public TransDTO<Boolean>  displayControl(){
        TransDTO<Boolean> dto = new TransDTO<>();
        Boolean result = tipService.displayControl();
        return dto.withCode(HttpStatus.SC_OK).withSuccess(true).withData(result);
    }
}
