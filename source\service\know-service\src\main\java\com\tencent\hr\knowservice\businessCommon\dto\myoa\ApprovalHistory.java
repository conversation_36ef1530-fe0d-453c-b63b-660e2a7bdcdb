package com.tencent.hr.knowservice.businessCommon.dto.myoa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 审批历史
 * <AUTHOR>
 */
@Data
public class ApprovalHistory {
    /**
     * 审批者的名称
     */
    private String approver;

    /**
     * 审批者的审批动作，例如同意，拒绝等
     */
    private String action;

    /**
     * 审批的步骤
     */
    private String step;

    /**
     * 审批者的意见
     */
    private String opinion;

    /**
     * 审批的时间,格式为:yyyy-MM-ddTHH:mm:ss
     */
    @JsonProperty(value = "approval_time")
    private String approvalTime;

    /**
     * 系统备注
     */
    private String remark;

}
