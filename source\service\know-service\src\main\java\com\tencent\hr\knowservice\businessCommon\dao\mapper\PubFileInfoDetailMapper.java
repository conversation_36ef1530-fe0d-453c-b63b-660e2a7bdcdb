package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfoDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【pub_file_info_detail(音视频文件详细信息)】的数据库操作Mapper
* @createDate 2022-12-15 15:59:51
* @Entity com.tencent.hr.knowservice.businessCommon.dao.entity.PubFileInfoDetail
*/
public interface PubFileInfoDetailMapper extends BaseMapper<PubFileInfoDetail> {

    /**
     * 更新 details
     * @param details
     */
    void batchUpdate(@Param("details") PubFileInfoDetail details);
}




