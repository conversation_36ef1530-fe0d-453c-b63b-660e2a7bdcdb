package com.tencent.hr.knowservice.businessCommon.constans;

/**
 * @description:
 * @author: vin<PERSON><PERSON><PERSON><PERSON>
 * @createDate: 2020/6/3
 * @version: 1.0
 */
public abstract  class Constants {


    /**
     * 过期时间相关枚举
     */
    public static enum CacheExpireEnum{
        //未读消息的有效期为30天
        Cache_Time_Expire_5_second(5L),
        Cache_Time_Expire_30_second(30L),
        Cache_Time_Expire_1_minute(60L),
        Cache_Time_Expire_5_minute(5 * 60L),
        Cache_Time_Expire_30_minute(30 * 60L),
        Cache_Time_Expire_1_hour(60 * 60L),
        Cache_Time_Expire_12_hour(12 * 60 * 60L),
        Cache_Time_Expire_1_day(24 * 60 * 60L),
        Cache_Time_Expire_3_day(3*24 * 60 * 60L),
        Cache_Time_Expire_1_week(7 * 24 * 60 * 60L),
        Cache_Time_Expire_1_month(30 * 24 * 60 * 60L)
        ;

        /**
         * 过期时间
         */
        private Long time;

        CacheExpireEnum(Long time) {
            this.time = time;
        }

        public Long getTime() {
            return time;
        }
    }

    public static  enum ErrorCodeEnum {
        OK(200,"OK","OK"),
        FAIL(-10000,"FAIL","其它错误"),
        PARAM_VALIDATE_FAILED(-11001,"PARAM_VALIDATE_FAILED","参数验证失败"),
        PARAM_CHEAK_SIGNATURE_FAIL(-11002,"PARAM_CHEAK_SIGNATURE_FAIL","接口签名验证失败"),
        PARAM_CREATE_SIGNATURE_FAIL(-11003,"PARAM_CREATE_SIGNATURE_FAIL","接口签名生成失败"),
        DATA_VALIDATE_ERROR(-11004,"DATA_VALIDATE_ERROR","数据校验失败"),
        PARAM_HEADER_VALIDATE_FAILED(-11005,"PARAM_HEADER_VALIDATE_FAILED","未获取到header信息"),


        SYS_JSON_DECODE_FAIL(-12001,"SYS_JSON_DECODE_FAIL","json解析失败"),
        SYS_JSON_ENCODE_FAIL(-12002,"SYS_JSON_ENCODE_FAIL","json编码失败"),
        SYS_REDIS_GET_ERROR(-12010,"SYS_REDIS_GET_ERROR","redis GET 出错"),
        SYS_REDIS_SET_ERROR(-12011,"SYS_REDIS_SET_ERROR","redis SET 出错"),

        AUTH_CHECK_HR_RIGHT_FAIL(-13001,"AUTH_CHECK_HR_RIGHT_FAIL","无权限"),
        AUTH_NOT_LOGIN(-13002,"AUTH_NOT_LOGIN","用户未登陆，请重新登录"),

        MULTIPLE_TASKS_OPENING(-14001,"MULTIPLE_TASKS_OPENING","不支持多任务同时学习"),
        MULTIPLE_DEVICES_OPENING(-14002,"MULTIPLE_DEVICES_OPENING","不支持多个设备同时学习"),
        RELOAD_TASK_LIST_OPENING(-14003,"RELOAD_TASK_LIST_OPENING","任务已更新,请重新获取"),

        CALL_NOT_ALLOWED_METHOD(-50000,"CALL_NOT_ALLOWED_METHOD","不被允许的http方法"),
        CALL_GROW_MATRIX_FAIL(-50001,"CALL_GROW_MATRIX_FAIL","调用成长矩阵接口失败"),
        CALL_HR_RIGHT_FAIL(-50002,"CALL_HR_RIGHT_FAIL","调用hrright接口失败"),
        CALL_NOT_ALLOWED_CHANNEL(-50003,"CALL_NOT_ALLOWED_CHANNEL","不支持调用模式"),
        ;
        private Integer code;
        private String name;
        private String message;

        ErrorCodeEnum(Integer code,String name,String message){
            this.code=code;
            this.name=name;
            this.message=message;
        }

        public Integer getCode(){return this.code;}

        public String  getName(){return this.name;}

        public String getMessage(){return this.message;}
    }
    public static enum OrderByEnum{
        DESC("DESC"),
        ASC("ASC");
        private String _key;
        OrderByEnum(String key){
            this._key=key;
        }

        @Override
        public String toString(){
            return this._key;
        }
    }

    public static enum ContentTypeEnum{
        UGC("UGC"),
        PGC("PGC");
        private String _key;
        ContentTypeEnum(String key){
            this._key=key;
        }

        @Override
        public String toString(){
            return this._key;
        }
    }

    public static enum TASHeaderEnum{
        GLOBAL_ID("caagw-globalid"),
        STAFF_ID("caagw-staffid"),
        USER_NAME("caagw-username"),
        NICK_NAME("caagw-nickname"),
        APP_KEY("caagw-appkey"),
        CORP_KEY("caagw-corpkey"),
        CORP_ID("caagw-corpid"),
        CORP_NAME("caagw-corpname"),
        PLAT_FORM("caagw-platform"),
        REGION_ID("caagw-regionid"),
        CHANNEL("caagw-channel"),
        HRGW_APPID("hrgw-appname"),
        CC_CORPID("cc-corpid"),
        CC_CORPNAME("cc-corpname"),
        Language("lang"),
        ;
        private String _key;
        TASHeaderEnum(String key){
            this._key=key;
        }

        @Override
        public String toString(){
            return this._key;
        }
    }







}

