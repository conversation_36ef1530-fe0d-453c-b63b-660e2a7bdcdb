/*
 * @Description: 
 * @Autor: v_whaigong
 * @LastEditTime: 2024-03-28 17:34:31
 */
import { DataType } from 'sdc-core'
import CoreService from './core.service'
export default class LevelService {
  static getData({ positionSystemTypeIdList = [0], positionSystemIdList = [1], positionClanIdList = undefined, positionLevelIdList = undefined } = {}) {
    const params = {
      queryCondition: {
        argMap: {
          positionSystemTypeIdList: LevelService.formatList(positionSystemTypeIdList) || [0], // 列表--通道族体系类型ld
          positionSystemIdList: LevelService.formatList(positionSystemIdList) || [1], // 列表-通道族体系ld
          positionClanIdList: LevelService.formatList(positionClanIdList), // 列表-职位族Id
          positionLevelIdList: LevelService.formatList(positionLevelIdList) // 列表-职级Id
        }
      }
    }
    return CoreService.http.post('/api/sso/dos-interface-server/open-api/config/hrmd/md-api-public-core-selector-position-level/sdc-webui/data', { params })
  }

  static getList(range) {
    return LevelService.getData(range).then(res => {
      const result = res.content.reduce((prev, current) => {
        const item = prev.find(item => item.id === current.positionClanId)
        const levelObj = {
          id: current.positionLevelId,
          label: current.positionLevelNameCn,
          labelEn: current.positionLevelNameEn,
          value: current.positionLevelId
        }
        if (item) {
          item.children.push(levelObj)
        } else {
          prev.push({ 
            id: current.positionClanId,
            label: current.positionClanNameCn,
            labelEn: current.positionClanNameEn,
            value: current.positionClanId,
            children: [levelObj] 
          })
        }
        return prev
      }, [])
      return result
    })
  }

  static formatList(arr) {
    return (arr && !DataType.isEmptyArray(arr)) ? arr : undefined
  }
}
