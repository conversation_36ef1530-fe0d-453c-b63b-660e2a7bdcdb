<template>
  <div class="label-select-component">
    <div class="cascader-component">
      <span 
      @click="togglePopper(true)" 
      class="marker-tag"
      :dt-areaid="dtTags('area')"
      :dt-eid="dtTags('eid')"
      :dt-remark="dtTags('remark')"
      >
        <slot name="showContent">
          打标签
        </slot>
      </span>
      <el-dialog
        title=""
        width="800px"
        top="8vh"
        :close-on-click-modal="false"
        class="dialogHome"
        style="border-radius: 9px"
        :visible.sync="dialogFormVisible"
        @close="Canceldialog"
      >
        <div slot="title" class="title">
          <el-row>
            <span class="Contentss">{{ "添加内容标签" }}</span>
            <img :src="require('../assets/img/first_share.png')" /><span>
              {{
                "为内容添加标签可获得2通用积分，单日上限20积分，全年上限200积分"
              }}
            </span>
          </el-row>
        </div>

        <el-form :inline="true">
          <!-- 系统原有标签 -->
          <el-form-item class="form-block">
            <el-form-item>
              <div class="label-text-tips">
                <p>
                  内容已有
                  <span>{{ oldTags.length }}</span>
                  个标签
                </p>
              </div>
              <el-tag
                disable-transitions
                v-for="tag in oldTags"
                :key="tag.label_id"
                class="original-tag"
                :type="String(tag.label_type)"
              >
                <div class="original-name">
                  {{ tag.label_name }}
                </div>
              </el-tag>
            </el-form-item>
          </el-form-item>
          <!-- 用户添加标签 -->
          <el-form-item class="form-block">
            <el-form-item>
              <div class="label-text-tips">
                <p>
                  我添加了
                  <span>{{ tags.length }}</span>
                  个标签
                </p>
              </div>
              <el-tag
                disable-transitions
                v-for="(tag, index) in tags"
                :key="index"
                :closable="true"
                class="user-tag"
                @close="handleTagClose(tag)"
              >
                <div class="user-name">
                  {{ tag.label_name }}
                </div>
              </el-tag>
            </el-form-item>
          </el-form-item>
          <el-form-item class="search-list">
            <div class="search-input">
              <LabelSelectComponent
                ref="searchInput"
                v-model="searchTags"
                class="project-tag-box"
                :tags="tags"
                :oldTags="oldTags"
                @deleteTags="deleteTags"
                @getSelectedLabelList="getSelectedLabelList"
                :labelNodeEnv="labelNodeEnv"
              >
              </LabelSelectComponent>
            </div>
            <p class="create-tips">
              如果找不到合适的标签，<span
                class="create-button"
                @click="showInput"
                >点击创建自定义标签</span
              >
            </p>
          </el-form-item>
          <el-form-item class="recently-used-label">
            <!-- <br> -->
            <el-form-item label="最近使用 ： ">
              <el-tag
                disable-transitions
                v-for="tag in recently"
                :key="tag.label_name + tag.label_id"
                :type="tag.type"
                class="recently-used-tag"
                :class="{ checked: tag.checked, disabled: tag.disabled }"
                @click="addToTaga(tag)"
              >
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="tag.category_full_name"
                  placement="bottom-start"
                >
                  <div>
                    {{ tag.label_name }}
                  </div>
                </el-tooltip>
              </el-tag>
            </el-form-item>
          </el-form-item>

          <el-dialog
            :close-on-click-modal="false"
            width="352px"
            height="240px"
            style="margin-top: 5%"
            title="创建自定义标签"
            :visible.sync="inputVisible"
            class="create-dialogs"
            @close="CancelCreation('establish')"
            append-to-body
          >
            <div>
              <span class="title-tips"
                >标签名称建议不超过25个中文字符，请勿创建重复/无关标签</span
              >
            </div>
            <br />
            <el-form
              :inline="true"
              ref="establish"
              :model="establish"
              :rules="rules"
            >
              <el-form-item style="margin-bottom: 15px !important">
                <div class="block">
                  <span class="demonstration">分类</span>
                  <span class="title">其他/自定义</span>
                </div>
              </el-form-item>
              <el-form-item prop="label_name" style="margin-bottom: 0px">
                <div class="block">
                  <span class="demonstration">名称</span>
                  <el-input
                    v-model.trim="establish.label_name"
                    placeholder="请输入标签名称"
                    style="width: 270px"
                    class="input-label"
                    @input="checkNickName"
                  >
                    <template slot="suffix" v-if="establish.label_name">
                      <div style="height: 36px; line-height: 36px">
                        {{
                          establish.label_name
                            ? handleValidor(establish.label_name, 25)
                            : 0
                        }}/25
                      </div>
                    </template>
                  </el-input>
                  <span
                    class="labelnameerror"
                    style="
                      margin-left: 59px;
                      height: 10px;
                      color: red;
                      margin-top: 10px;
                      display: block;
                    "
                    v-show="nickFlag"
                  >
                    {{ errorMsg }}
                  </span>
                </div>
              </el-form-item>
              <div
                style="margin-left: 50px; color: #245bb8; margin-top: 15px"
                v-show="nackname && establish.label_name && !errorMsg"
              >
                <span>已存在此标签,确认后将直接选中相同的标签</span>
              </div>
            </el-form>
            <div slot="footer" class="dialog-footer dialogbotton">
              <el-button class="cancel-button" @click="CancelCreation('establish')"
                ><span>取 消</span></el-button
              >
              <el-button
                class="cancel-button"
                :disabled="appStatus"
                type="primary"
                @click="SubmitCreation('establish')"
                ><span>确 定</span></el-button
              >
            </div>
          </el-dialog>
        </el-form>
        <div slot="footer" class="dialog-footer dialogbotton sub-footer">
          <el-button class="Submit-button" @click="Canceldialog"
            >取 消</el-button
          >
          <el-button
            class="Submit-button"
            :disabled="subDisabled"
            type="primary"
            @click="Submitdialog"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import LabelSelectComponent from "./LabelSelectComponent.vue";
export default {
  name: "sdc-labeling",
  components: {
    LabelSelectComponent,
  },
  props: {
    // 双向绑定值
    value: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "请输入标签名称",
    },
    // 禁止回车创建
    disableCreate: {
      type: Boolean,
      default: false,
    },
    // 是否有推荐（课程名称-title、描述-desc）
    recommend: {
      type: Object,
      default() {
        return null;
      },
    },
    // 最多选择数量
    maxNum: {
      type: Number,
      default: 5,
    },
    // 可清空
    clearable: {
      type: Boolean,
      default: true,
    },
    showLabelList: {
      type: Array,
      default: () => [],
    },
    course_id: {
      //课程id
      type: Number,
    },
    course_name: {
      //课程名称
      type: String,
    },
    course_type: {
      //课程类型
      type: Number,
    },
    labelNodeEnv: {
      type: String,
      // default: "test",
      default: 'production'
    },
    courseInfo: {
      // 课程详情数据 showBurialPoint = true时必传
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      oldTags: [], //已有的标签
      tags: [], //用户选中的标签
      recently: [], //最近使用的标签
      inputVisible: false, //创建自定义标签弹窗
      establish: {
        label_name: "",
      }, //创建自定义标签的内容
      dialogFormVisible: false, //是否打开标签组件
      nickFlag: false, //错误提示
      searchTags: [], //用来存储从搜索框选择的数据，后续优化可以去掉
      errorMsg: "", //错误文案
      nackname: false, //标签存在提示
      starTags: [], //用来存第一次的标签
      oldTagsAll: [], //所有数据
      // appStatus: true,//自定义确定按钮是否禁用
      urlInfo: {
        production: "//learn.woa.com",
        test: "//test-portal-learn.woa.com",
      },
      form: {
        course_name: "",
        course_desc: "",
      },
      rules: {
        label_name: [
          { required: true, message: "请输入名称", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    tags: {
      deep: true,
      handler(newVal) {
        if (this.recently.length > 0) {
          for (let i = 0; i < this.recently.length; i++) {
            const checkedList = newVal.find(
              (item) => item.label_id === this.recently[i].label_id
            );
            if (checkedList !== undefined) {
              this.recently[i].checked = true;
            } else {
              this.recently[i].checked = false;
            }
          }
        }
      },
    },
  },
  computed: {
    commonUrl() {
      return location.hostname.endsWith(".woa.com")
        ? this.urlInfo[this.labelNodeEnv]
        : this.urlInfo[this.labelNodeEnv];
    },
    newTags() {
      return [...this.tags, ...this.oldTags];
    },
    appStatus() {
      return this.establish.label_name && !this.nickFlag ? false : true;
    },
    subDisabled() {
      if (this.tags.length < 1) {
        return true;
      }
      if (this.starTags.length) {
        const arr1 = this.tags.slice().sort((a, b) => a.label_id - b.label_id);
        const arr2 = this.starTags.slice().sort((c, d) => c.label_id - d.label_id);
        if (arr1.length === arr2.length) {
          for (let i = 0; i < arr1.length; i++) {
            if (arr1[i].label_id !== arr2[i].label_id) {
              return false;
            } else {
              return true;
            }
          }
        } else {
          return false;
        }
      }
      return false;
    },
    dtTags () {
      return (type) => {
        let { mooc_course_id, page, page_type, container } = this.courseInfo
        if (mooc_course_id) {
          if (type === 'area') {
            return `area_${mooc_course_id}_打标签`
          } else if (type === 'eid') {
            return `element_${mooc_course_id}_打标签`
          } else {
            return JSON.stringify({
              page, // 任务名称
              page_type,
              container, // 板块的名称
              click_type: 'button',
              content_type: '',
              content_id: '',
              content_name: '打标签',
              act_type: '',
              container_id: '',
              page_id: '',
              terminal: 'pc'
            })
          }
        }
      }
    }
  },
  methods: {
    async togglePopper() {
      this.dialogFormVisible = true;
      await this.getRecentlyTags();
      await this.getGroupUser();
    },
    async getRecentlyTags() {
      //获取最近使用标签
      const res = await axios.get(
        `${this.commonUrl}/training/api/label/user/labelinfo/recently_used`,
        {
          params: {
            show_count: 5,
          },
          withCredentials: true,
        }
      );
      if (res.data.code === 200 && res.data.data.length > 0) {
        const data = res.data.data;
        if (this.oldTags.length > 0) {
          for (let i = 0; i < data.length; i++) {
            const obj = this.oldTags.find(
              (item) => item.label_id === data[i].label_id
            );
            if (obj !== undefined) {
              data[i].disabled = true;
            }
          }
        }
        if (this.tags.length > 0) {
          for (let i = 0; i < data.length; i++) {
            const obj = this.tags.find(
              (item) => item.label_id === data[i].label_id
            );
            if (obj !== undefined) {
              data[i].checked = true;
            }
          }
        }
        this.recently = data;
      }
    },
    async getGroupUser() {
      //获取原来已选标签
      const res = await axios.get(
        `${this.commonUrl}/training/api/label/user/labelinfo/course-label-group-user`,
        {
          params: {
            act_id: this.course_id,
            act_type: this.course_type
            // act_id: 10797,
            // act_type: 2,
          },
          withCredentials: true,
        }
      );
      if (res.data.code === 200) {
        this.oldTags = res.data.data.ignore_current_label_list;
        if (this.recently.length > 0) {
          for (let i = 0; i < this.recently.length; i++) {
            const obj = this.oldTags.find(
              (item) => item.label_id === this.recently[i].label_id
            );
            if (obj !== undefined) {
              this.recently[i].disabled = true;
            }
          }
        }
        this.tags = res.data.data.current_label_list;
        this.starTags = [...res.data.data.current_label_list];
        this.oldTagsAll = [
          ...res.data.data.current_label_list,
          ...res.data.data.ignore_current_label_list,
        ];
      }
    },

    showInput() {
      //创建自定义标签按钮
      this.inputVisible = true;
    },
    async checkNickName() {
      //输入标签名称
      let reg = /^[A-Za-z0-9-+#&\\/()（）\u4e00-\u9fa5\s]{0,1000}$/; // 中文，数字，字母，下划线
      // this.computedStrLen(this.remarkName)
      if (
        !reg.test(this.establish.label_name) &&
        this.establish.label_name !== ""
      ) {
        this.errorMsg = "名称中不能包含特殊符号";
        this.nickFlag = true;
      } else {
        if (this.handleValidor(this.establish.label_name) > 25) {
          this.errorMsg = "标签名称过长，请进行删减";
          this.nickFlag = true;
        } else {
          this.errorMsg = "";
          this.nickFlag = false;
        }
      }
      if (this.establish.label_name && !this.errorMsg.length) {
        const data = await this.checkName();
        if (data !== undefined && data.length > 0) {
          this.nackname = true;
        } else {
          this.nackname = false;
        }
        //  this.appStatus = false
      } else {
        // this.appStatus = true
      }
    },
    async checkName() {
      // 获取当前创建的标签是否已经存在
      const res = await axios.get(
        `${this.commonUrl}/training/api/label/user/labelinfo/get_label_basicinfo`,
        {
          params: {
            page_no: 1,
            page_size: 6,
            label_name: this.establish.label_name,
            label_type: 5,
            order_by: "label_type",
          },
          withCredentials: true,
        }
      );
      if (res.data.code === 200 && res.data.data.total !== 0) {
        return res.data.data.records;
      }
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g);
        const zhCount = china && china.join("").length;
        const enCount = Math.ceil((value.length - zhCount) / 2);
        const total = zhCount + enCount;
        if (total > num) {
          // this.selectedOptions.label_name = value.slice(0, -1)
        }
        return total || 0;
      }
      return 0;
    },
    Canceldialog() {
      //取消事件
      this.dialogFormVisible = false;
      const selectedLabels = this.$refs.searchInput.selectedLabels;
      const newLabels = this.oldTagsAll.filter((item2) =>
        selectedLabels.some((item1) => item1.label_id === item2.label_id)
      );
      this.$refs.searchInput.selectedLabels = newLabels;
    },
    async Submitdialog() {
      //提交确定事件
      const res = await axios.post(
        `${this.commonUrl}/training/api/label/user/labelinfo/batch_labeling_client2`,
        {
          act_type: this.course_type,
          course_ids: [this.course_id],
          labels: this.newTags,
          object_type: "NetCourse",
          course_label_type: "3",
          course_name: this.course_name,
        },
        {
          withCredentials: true,
        }
      );
      if (res.data.code === 200) {
        this.$emit("getSelectedLabelList", this.newTags);
        this.$emit("input", this.newTags);
        this.Deletecoursecache();
        const imageUrl = require("../assets/img/first_share.png");
        const imageseUrl = require("../assets/img/imgse.png");
        const messageContent = `
                <div>
                  &nbsp;&nbsp;&nbsp;<img src="${imageseUrl}" style="height: 20px;margin-left:-22px;"></img>
                  &nbsp;<strong style="color: #666666;font-family: "PingFang SC";font-size: 14px;
                  font-style: normal;font-weight: 400;line-height: normal;" >添加标签成功</strong>
                  <br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;<img src="${imageUrl}" style="height: 20px;margin-left:-22px;"></img>
                  &nbsp;<span style="color: #ff7a4f;font-family: "PingFang SC";font-size: 14px;
                  font-style: normal;font-weight: 500;line-height: normal;">通用积分+${res.data.remark}</span>
                </div>
              `;
        if (res.data.remark > 0) {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: messageContent,
            type: "success",
            iconClass: " ", // 这个会覆盖图标类名，为了实现去掉图标的操作
            customClass: "el-message--success", // 这个是由于上面设置了iconClass会覆盖掉type，所以需要再设置
            duration: 5000, // 这个是时长设置
          });
        } else {
          this.$message({
            type: "success",
            message: "添加标签成功",
          });
        }
      } else {
        this.$message({
          type: "success",
          message: "添加标签成功",
        });
      }
      this.dialogFormVisible = false;
    },
    Deletecoursecache() {
      axios
        .post(
          `${this.commonUrl}/training/api/netcourse/user/courseinfo/remove-net-course-info-cache?act_type=2&course_id=${this.course_id}`,
          {},
          {
            withCredentials: true,
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            console.log(res.data.code);
          }
        });
    },
    async getSelectedLabelList(val) {
      this.searchTags = val;
      const filteredArr = this.searchTags.find(
        (item) => !this.newTags.some((val) => val.label_id === item.label_id)
      );
      if (filteredArr !== undefined) {
        this.tags.push(filteredArr);
      }
    },
    CancelCreation(formName) {
      //关闭新建标签弹窗
      this.inputVisible = false;
      this.establish.label_name = "";
      this.errorMsg = ''
      this.$refs[formName].resetFields();
      this.nickFlag = false
    },
    SubmitCreation(formName) {
      //创建标签确定按钮
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const data = await this.checkName();
          if (data !== undefined && data.length > 0) {
            //该标签在数据库已存在
            this.filtrTags(data);
            this.CancelCreation('establish');
          } else {
            //不存在，添加进数据库
            const res = await axios.post(
              `${this.commonUrl}/training/api/label/user/labelinfo/user-insert-label`,
              {
                label_name: this.establish.label_name,
              },
              { withCredentials: true }
            );
            if (res.data.code === 200) {
              const newData = await this.checkName();
              if (newData !== undefined && newData.length > 0) {
                this.tags.push( newData[0])
              }
            }
            this.CancelCreation('establish');
          }
        } else {
          return false;
        }
      });
    },
    filtrTags(data) {
      //判断当前标签是否已被选
      const obj = this.newTags.find(
        (item) => data[0].label_id === item.label_id
      );
      if (obj !== undefined) {
        //已存在
        return;
      } else {
        //不存在
        this.tags.push(data[0]);
      }
    },
    deleteTags(val) {
      this.deleteTag(val);
    },
    handleTagClose(tag) {
      //删除已选标签
      this.deleteTag(tag);
      this.$refs.searchInput.changeSelectedLabel(tag, "delete");
    },
    deleteTag(tag) {
      //删除已选标签
      this.tags = this.tags.filter((item) => item.label_id !== tag.label_id);
    },
    addToTaga(tag) {
      //最近使用
      if (tag.disabled) return;
      const existTags = this.tags.find(
        (item) => item.label_id === tag.label_id
      );
      if (existTags !== undefined) {
        tag.checked = false;
        this.deleteTag(existTags);
      } else {
        tag.checked = true;
        this.tags.push(tag);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dialogHome {
  :deep .el-tag {
    margin: 2px 0;
    height: 24px;
    line-height: 22px;
  }
  :deep .el-dialog__body {
    padding: 0 18px;
  }
  :deep .el-dialog {
    border-radius: 12px;
  }
  :deep .el-dialog__footer {
    padding: 0;
    .sub-footer {
      height: 64px;
      line-height: 64px;
      padding-right: 20px;
    }
  }
  :deep .el-dialog .el-dialog__header {
    height: 56px;
    padding: 0 20px;
    border-bottom: solid 1px #dcdfe6;
  }
  .title {
    height: 56px;
    line-height: 56px;
    // border-bottom: #e7e7e7 solid 1px;
    position: relative;
    img {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      height: 18px !important;
      width: 18px !important;
    }
    span {
      padding-left: 22px;
      padding-right: 50px;
      color: #ff7a4f;
      font-size: 14px;
    }
    .Contentss {
      padding: 0 24px 0 12px;
      overflow: hidden;
      color: #000000e6;
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
  }
  .form-block {
    display: block;
  }
  .label-text-tips {
    width: 100%;
    padding-left: 14px;
    p {
      line-height: 24px;
      margin: 20px 0 12px;
    }
    span {
      font-weight: 600;
      font-family: "PingFang SC";
      font-size: 16px;
      color: #0052d9;
    }
  }
  .original-tag {
    padding-left: 0px;
    margin-left: 14px;
    margin-right: 10px;
    border-radius: 0 16px 16px 0;
    background-color: #ffffff;
    border: 1px solid #b5d4ff;
    .original-name {
      margin-left: 5px;
      color: #b5d4ff;
      height: 20px;
      display: inline-block;
      min-width: 28px;
    }
  }
  .user-tag {
    padding-left: 0px;
    margin-left: 14px;
    margin-right: 10px;
    border-radius: 0 16px 16px 0;
    border: 1px solid #237fe9;
    .user-name {
      background-color: #f2f3ff;
      color: #1f7ceb;
      margin-left: 5px;
      height: 20px;
      display: inline-block;
      min-width: 28px;
    }
  }
  .search-list {
    padding-left: 10px;
    .search-input {
      display: inline-block;
      width: 350px;
    }
    .create-tips {
      display: inline-block;
      padding-left: 20px;
      color: #989898;
      .create-button {
        color: #0155e1;
        cursor: pointer;
      }
    }
  }
  .recently-used-label {
    padding: 10px 0 10px 10px;
    border-bottom: #e7e7e7 solid 1px;
    margin-bottom: 0;
    display: block;
    width: 100%;
    .recently-used-tag {
      color: #767677;
      border: 1px solid #e9e9e9;
      border-radius: 0 15px 15px 0;
      background-color: #f7f8fa;
      margin-right: 20px;
      :hover {
        cursor: pointer;
      }
    }
    // .recently-used-tag .hover{
    //    cursor: pointer;
    // }
    .checked {
      color: #0068e6;
      background-color: #f2f3ff;
      border: 1px solid #237fe9;
    }
    .disabled {
      color: #b5d4ff;
      border: 1px solid #b5d4ff;
      background-color: #fff;
    }
  }
}
.create-dialogs {
  :deep .el-dialog__body {
    padding: 30px 20px;
  }
  :deep .el-dialog {
    border-radius: 6px;
     .el-dialog__header {
      padding: 16px 16px 0 16px;
      border-bottom: 0;
      .el-dialog__title{
        display: inline-block;
        height: 22px;
        line-height: 22px;
        font-size: 14px;
        font-weight: 600;
        font-family: "PingFang SC";
        color: #000000e6;
      }
    }
    .el-dialog__body{
      padding: 8px 16px 0 16px;
      .title-tips{
        color: #e37318;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
  :deep .el-dialog__footer {
    padding: 10px 20px 20px;
  }
  .block {
    display: block;
    .title {
      margin-left: 10px;
    }
    .demonstration {
      float: left;
    }
    .cascader-class {
      float: right;
      margin-left: 10px;
    }
    .input-label {
      margin-left: 10px;
    }
  }
  .cancel-button {
    width: 40px;
    height: 24px;
    font-size: 12px;
    position: relative;
    span {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
