package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.common.util.StringUtil;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.constans.ModuleIdEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.*;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.*;

import com.tencent.hr.knowservice.businessCommon.dto.*;
import com.tencent.hr.knowservice.businessCommon.proxy.ContentServiceApi;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ActExtendStudyDto;
import com.tencent.hr.knowservice.framework.dto.ExtendStudyDto;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import com.tencent.hr.knowservice.businessCommon.constans.CacheExpireConstant;
import com.tencent.hr.knowservice.graphic.constant.CacheKeyEnum;
import com.tencent.hr.knowservice.graphic.dto.TargetPeopleDTO;
import com.tencent.hr.knowservice.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FileProdConnService {

    @Resource
    private FileProdConnMapper fileProdConnMapper;
    @Autowired
    ActNetCourseSummaryMapper actNetCourseSummaryMapper;
    @Autowired
    ActCourseSummaryMapper actCourseSummaryMapper;
    @Autowired
    ActActivitySummaryMapper actActivitySummaryMapper;
    @Autowired
    LiveSummaryMapper liveSummaryMapper;
    @Autowired
    WechatArticleMapper wechatArticleMapper;
    @Resource
    ProdFileReadRecordsSummaryMapper prodFileReadRecordsSummaryMapper;
    @Autowired
    ContentServiceApi contentServiceApi;
    @Autowired
    FaceClassProxyService faceClassProxyService;
    @Resource(name = "taskExecutor")
    ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    RedisUtil redisUtil;

    @Autowired
    private ActPraiseService praiseService;

    @Autowired
    private ProdFileSummaryService prodFileSummaryService;

    @Value("${shortUrl.graphicUrl}")
    private String graphicUrl;

    @Value("${shortUrl.netCourseUrl}")
    private String netCourseUrl;

    /**
     * 获取延伸课程列表
     *
     * @param actId
     * @param actType
     * @return
     */
    public List<ExtendContentOutPageDTO> getExtendContentPage(Integer actId,Integer actType,Integer scenarioType) {
        List<ExtendContentPageDTO> contentPageDTOIPage = fileProdConnMapper.queryExtendContentByActId(actId,actType,scenarioType);
        List<ExtendContentOutPageDTO> dtos = getExtendContentOutPageDTOS(contentPageDTOIPage);
        return dtos;
    }

    /**
     * 延伸课程转换参数给前端
     * @param contentPageDTOIPage
     * @return
     */
    private List<ExtendContentOutPageDTO> getExtendContentOutPageDTOS(List<ExtendContentPageDTO> contentPageDTOIPage) {
        List<ExtendContentOutPageDTO> dtos = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(contentPageDTOIPage)){
            for (ExtendContentPageDTO dto : contentPageDTOIPage) {
                ExtendContentOutPageDTO extendContentOutPageDTO = new ExtendContentOutPageDTO();
                extendContentOutPageDTO.setId(dto.getId());
                extendContentOutPageDTO.setContentName(dto.getConnProdName());
                extendContentOutPageDTO.setToped(dto.getToped());
                extendContentOutPageDTO.setCreatedAt(dto.getCreatedAt());
                extendContentOutPageDTO.setActType(dto.getProdType());
                extendContentOutPageDTO.setActId(dto.getProdId());
                extendContentOutPageDTO.setContentModuleId(dto.getConnProdModuleId());
                extendContentOutPageDTO.setContentModuleName(dto.getConnProdModuleName());
                extendContentOutPageDTO.setContentItemId(dto.getConnProdItemId());
                extendContentOutPageDTO.setContentType(dto.getContentType());
                extendContentOutPageDTO.setViewCount(dto.getViewCount());
                extendContentOutPageDTO.setAvgScore(dto.getAvgScore());
                extendContentOutPageDTO.setContentCreatedTime(dto.getConnProdCreatedTime());
                extendContentOutPageDTO.setHref(dto.getConnProdUrl());
                extendContentOutPageDTO.setPraiseCount(dto.getPraiseCount());
                extendContentOutPageDTO.setTargetPeople(dto.getTargetPeople());
                extendContentOutPageDTO.setPhotoStorageType(dto.getPhotoStorageType());
                extendContentOutPageDTO.setPhotoId(dto.getPhotoId());
                extendContentOutPageDTO.setPhotoUrl(dto.getPhotoUrl());
                extendContentOutPageDTO.setCourseDesc(dto.getDescription());
                extendContentOutPageDTO.setDuration(dto.getDuration());
                dtos.add(extendContentOutPageDTO);
            }
        }
        return dtos;
    }

    /**
     * 获取最新分数和播放量
     *
     * @param records
     */
    private void getNewestViewCountAndAvgScore(List<ExtendContentPageDTO> records) {
        if (!CollectionUtils.isEmpty(records)) {
            List<Integer> netCourseIds = new ArrayList<>();
            List<Integer> faceCourseIds = new ArrayList<>();
            List<String> liveIds = new ArrayList<>();
            List<Integer> activityIds = new ArrayList<>();
            List<Integer> articleIds = new ArrayList<>();
            List<Integer> fileIds = new ArrayList<>();
            List<String> searchContents = new ArrayList<>();
            //课程封面图片和详情信息从成长矩阵拉取
            List<String> imageAndDescSearchIds = new ArrayList<>();
            for (ExtendContentPageDTO record : records) {
                Integer moduleId = record.getConnProdModuleId();
                ModuleIdEnum moduleEnum = ModuleIdEnum.getModuleEnum(moduleId);
                String itemId = record.getConnProdItemId();
                imageAndDescSearchIds.add(moduleId + "|" + itemId);
                switch (moduleEnum) {
                    case NET_COURSE:
                        netCourseIds.add(Integer.valueOf(itemId));
                        break;
                    case FACE_COURSE:
                        faceCourseIds.add(Integer.valueOf(itemId));
                        break;
                    case LIVE:
                        liveIds.add(itemId);
                        break;
                    case ACTIVITY:
                        activityIds.add(Integer.valueOf(itemId));
                        break;
                    case LITTLE_CASE:
                    case NOTE:
                    case HANG_JIA:
                        String search = moduleId + "|" + itemId;
                        searchContents.add(search);
                        break;
                    case ARTICLE:
                        articleIds.add(Integer.valueOf(itemId));
                        break;
                    case DOCUMENTATION:
                        fileIds.add(Integer.valueOf(itemId));
                }
            }
            Future<List<ActNetCourseSummary>> submitActNetCourse = taskExecutor.submit(() -> {
                return getActNetCourseSummaries(netCourseIds);
            });
            Future<List<ActCourseSummary>> submitActCourse = taskExecutor.submit(() -> {
                return getActCourseSummaries(faceCourseIds);
            });
            Future<List<LiveSummary>> submitLive = taskExecutor.submit(() -> {
                return getLiveSummaries(liveIds);
            });
            Future<List<ActActivitySummary>> submitActActivity = taskExecutor.submit(() -> {
                return getActActivitySummaries(activityIds);
            });
            Future<List<WechatArticle>> submitWechatArticle = taskExecutor.submit(() -> {
                return getWechatArticles(articleIds);
            });
            Future<List<ProdFileReadRecordsSummary>> submitProdFile = taskExecutor.submit(() -> {
                return getProdFileReadRecordsSummarys(fileIds);
            });
            searchContents.addAll(imageAndDescSearchIds);
            //去重
            searchContents = searchContents.stream().distinct().collect(Collectors.toList());
            List<Map<String, Object>> data = getContentOutSearchResDTOS(searchContents);
            try {
                List<ActNetCourseSummary> actNetCourseSummaryList = submitActNetCourse.get();
                List<ActCourseSummary> actCourseSummaryList = submitActCourse.get();
                List<LiveSummary> liveSummaryList = submitLive.get();
                List<ActActivitySummary> actActivitySummaryList = submitActActivity.get();
                List<WechatArticle> wechatArticleList = submitWechatArticle.get();
                List<ProdFileReadRecordsSummary> prodFileList = submitProdFile.get();
                for (ExtendContentPageDTO record : records) {
                    Integer moduleId = record.getConnProdModuleId();
                    ModuleIdEnum moduleEnum = ModuleIdEnum.getModuleEnum(moduleId);
                    String itemId = record.getConnProdItemId();
                    //根据成长矩阵查询的数据，进行赋值
                    if (!CollectionUtils.isEmpty(data)) {
                        Optional<Map<String, Object>> optional = data.stream().
                                filter(item -> StringUtils.equals(itemId, item.get("item_id").toString())
                                        && StringUtil.equals(String.valueOf(moduleId),item.get("module_id").toString())).findFirst();
                        if (optional.isPresent()) {
                            Map<String, Object> map = optional.get();
                            //设置简介
                            record.setDescription(map.get("description")==null?null:map.get("description").toString());
                            //封面地址
                            record.setPhotoUrl(map.get("photo_url")==null?null:map.get("photo_url").toString());
                            //时长、字数
                            record.setDuration(map.get("duration")==null?0:Integer.valueOf(map.get("duration").toString()));
                        }
                    }
                    switch (moduleEnum) {
                        case NET_COURSE:
                            if (!CollectionUtils.isEmpty(actNetCourseSummaryList)) {
                                Optional<ActNetCourseSummary> optional = actNetCourseSummaryList.stream().filter(item -> StringUtils.equals(itemId, item.getNetCourseId().toString())).findFirst();
                                if (optional.isPresent()) {
                                    ActNetCourseSummary actNetCourseSummary = optional.get();
                                    record.setAvgScore(actNetCourseSummary.getAvgScore());
                                    record.setViewCount(actNetCourseSummary.getViewCount());
                                    record.setPraiseCount(actNetCourseSummary.getPraiseCount());
                                }
                            }
                            break;
                        case FACE_COURSE:
                            if (!CollectionUtils.isEmpty(actCourseSummaryList)) {
                                Optional<ActCourseSummary> optional = actCourseSummaryList.stream().filter(item -> StringUtils.equals(itemId, item.getCourseId().toString())).findFirst();
                                if (optional.isPresent()) {
                                    ActCourseSummary actCourseSummary = optional.get();
                                    record.setAvgScore(actCourseSummary.getAvgScore());
                                    record.setViewCount(actCourseSummary.getViewCount());
                                    Integer praiseCount = praiseService.getPraiseCount(String.valueOf(ActTypeEnum.FACE_COURSE.getActType()), String.valueOf(record.getId()));
                                    record.setPraiseCount(praiseCount);
                                }
                            }
                            break;
                        case LIVE:
                            if (!CollectionUtils.isEmpty(liveSummaryList)) {
                                Optional<LiveSummary> optional = liveSummaryList.stream().filter(item -> StringUtils.equals(itemId, item.getLiveId())).findFirst();
                                if (optional.isPresent()) {
                                    LiveSummary liveSummary = optional.get();
                                    record.setViewCount(liveSummary.getUv());
                                    Integer avgScore = liveSummary.getAvgScore();
                                    record.setAvgScore(avgScore==null?0.0:Double.valueOf(avgScore));
                                    Integer praiseCount = praiseService.getPraiseCount(String.valueOf(ActTypeEnum.LIVE.getActType()), String.valueOf(record.getId()));
                                    record.setPraiseCount(praiseCount);
                                }
                            }
                            break;
                        case ACTIVITY:
                            if (!CollectionUtils.isEmpty(actActivitySummaryList)) {
                                Optional<ActActivitySummary> optional = actActivitySummaryList.stream().filter(item -> StringUtils.equals(itemId, item.getActivityId().toString())).findFirst();
                                if (optional.isPresent()) {
                                    ActActivitySummary actActivitySummary = optional.get();
                                    record.setViewCount(actActivitySummary.getViewCount());
                                    Integer praiseCount = praiseService.getPraiseCount(String.valueOf(ActTypeEnum.ACTIVITY.getActType()), String.valueOf(record.getId()));
                                    record.setPraiseCount(praiseCount);
                                }
                            }
                            break;
                        case LITTLE_CASE:
                            if (!CollectionUtils.isEmpty(data)) {
                                Optional<Map<String, Object>> optional = data.stream().
                                        filter(item -> StringUtils.equals(itemId, item.get("item_id").toString())
                                                && ModuleIdEnum.LITTLE_CASE.getModuleId().equals(item.get("module_id"))).findFirst();
                                if (optional.isPresent()) {
                                    Map<String, Object> map = optional.get();
                                    record.setViewCount(map.get("view_count_uv")==null?0:(Integer) map.get("view_count_uv"));
                                    record.setAvgScore(map.get("avg_score") ==null?0.0:(Double)map.get("avg_score"));
                                    Integer praiseCount = praiseService.getPraiseCount(String.valueOf(ActTypeEnum.LITTLE_CASE.getActType()), String.valueOf(record.getId()));
                                    record.setPraiseCount(praiseCount);
                                }
                            }
                            break;
                        case NOTE:
                            if (!CollectionUtils.isEmpty(data)) {
                                Optional<Map<String, Object>> optional = data.stream().
                                        filter(item -> StringUtils.equals(itemId, item.get("item_id").toString())
                                                && ModuleIdEnum.NOTE.getModuleId().equals(item.get("module_id"))).findFirst();
                                if (optional.isPresent()) {
                                    Map<String, Object> map = optional.get();
                                    record.setViewCount(map.get("view_count_uv")==null?0:(Integer) map.get("view_count_uv"));
                                    record.setAvgScore(map.get("avg_score") ==null?0.0:(Double) map.get("avg_score"));
                                    Integer praiseCount = praiseService.getPraiseCount(String.valueOf(ActTypeEnum.NOTE.getActType()), String.valueOf(record.getId()));
                                    record.setPraiseCount(praiseCount);
                                }
                            }
                            break;
                        case HANG_JIA:
                            if (!CollectionUtils.isEmpty(data)) {
                                Optional<Map<String, Object>> optional = data.stream().
                                        filter(item -> StringUtils.equals(itemId, item.get("item_id").toString())
                                                && ModuleIdEnum.HANG_JIA.getModuleId().equals(item.get("module_id"))).findFirst();
                                if (optional.isPresent()) {
                                    Map<String, Object> map = optional.get();
                                    record.setViewCount(map.get("view_count_uv")==null?0:(Integer) map.get("view_count_uv"));
                                    record.setAvgScore(map.get("avg_score") ==null?0.0:(Double) map.get("avg_score"));
                                    Integer praiseCount = praiseService.getPraiseCount(String.valueOf(ActTypeEnum.HANG_JIA.getActType()), String.valueOf(record.getId()));
                                    record.setPraiseCount(praiseCount);
                                }
                            }
                            break;
                        case ARTICLE:
                            if (!CollectionUtils.isEmpty(wechatArticleList)) {
                                Optional<WechatArticle> optional = wechatArticleList.stream().filter(item -> StringUtils.equals(itemId, item.getArticleId().toString())).findFirst();
                                if (optional.isPresent()) {
                                    WechatArticle wechatArticle = optional.get();
                                    record.setViewCount(wechatArticle.getViewCount());
                                    record.setPraiseCount(wechatArticle.getPraiseCount());
                                }
                            }
                            break;
                        case DOCUMENTATION:
                            if (!CollectionUtils.isEmpty(prodFileList)){
                                Optional<ProdFileReadRecordsSummary> optional = prodFileList.stream().filter(item -> StringUtils.equals(itemId, item.getFileProdId().toString())).findFirst();
                                if (optional.isPresent()){
                                    ProdFileReadRecordsSummary prodFileReadRecordsSummary = optional.get();
                                    record.setViewCount(prodFileReadRecordsSummary.getViewCount());
                                    record.setPraiseCount(prodFileReadRecordsSummary.getPraiseCount());
                                }
                            }
                            break;
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("线程中断异常,msg={}", e.getMessage(), e);
            }
            catch (ExecutionException e) {
                log.error("线程池执行出错,msg={}", e.getMessage(), e);
            }
        }
    }

    private List<ProdFileReadRecordsSummary> getProdFileReadRecordsSummarys(List<Integer> fileIds) {
        List<ProdFileReadRecordsSummary> prodFileReadRecordsSummaryList =null;
        if (!CollectionUtils.isEmpty(fileIds)) {
            prodFileReadRecordsSummaryList = prodFileReadRecordsSummaryMapper.getFileViewCountByFileIds(fileIds);
//            List<ProdFileSummary> prodFileSummarys = prodFileSummaryService.getProdFileSummary(fileIds);
//            Map<Integer, ProdFileSummary> summaryMap = prodFileSummarys.stream().collect(Collectors.toMap(ProdFileSummary::getProdFileId, Function.identity()));
//            prodFileReadRecordsSummaryList.forEach(prodFileReadRecordsSummary -> {
//                ProdFileSummary prodFileSummary = summaryMap.get(prodFileReadRecordsSummary.getFileProdId());
//                prodFileReadRecordsSummary.setPraiseCount(prodFileSummary.getGoodCount());
//            });
        }
        return prodFileReadRecordsSummaryList;
    }

    /**
     * 获取案例、行家和文章观看量和评分数据
     *
     * @param searchContents
     * @return
     */
    public List<Map<String, Object>> getContentOutSearchResDTOS(List<String> searchContents) {
        List<Map<String, Object>> data = null;
        if (!CollectionUtils.isEmpty(searchContents)) {
            try {
                TransDTO transDTO = contentServiceApi.searchContentByModuleIdAndItemId(searchContents);
                if (transDTO.getSuccess() && transDTO.getCode() == HttpStatus.SC_OK) {
                    data = (List<Map<String, Object>>) transDTO.getData();
                } else {
                    log.error("远程获取成长矩阵数据失败,msg={}", transDTO.getMessage());
                }
            } catch (Exception e) {
                log.error("远程调用接口/api/v1/content/manage/contents/search-specific-content失败!,msg ={}", e.getMessage(),e);
            }
        }
        return data;
    }

    /**
     * 获取微信图文观看量和评分数据
     *
     * @param articleIds
     * @return
     */
    public List<WechatArticle> getWechatArticles(List<Integer> articleIds) {
        List<WechatArticle> wechatArticleList = null;
        if (!CollectionUtils.isEmpty(articleIds)) {
            LambdaQueryWrapper<WechatArticle> qw = new LambdaQueryWrapper<>();
            qw.select(WechatArticle::getArticleId, WechatArticle::getViewCount, WechatArticle::getPraiseCount);
            qw.in(WechatArticle::getArticleId, articleIds);
            wechatArticleList = wechatArticleMapper.selectList(qw);
        }
        return wechatArticleList;
    }

    /**
     * 获取活动观看量和评分数据
     *
     * @param activityIds
     * @return
     */
    public List<ActActivitySummary> getActActivitySummaries(List<Integer> activityIds) {
        List<ActActivitySummary> actActivitySummaryList = null;
        if (!CollectionUtils.isEmpty(activityIds)) {
            LambdaQueryWrapper<ActActivitySummary> qw = new LambdaQueryWrapper<>();
            qw.select(ActActivitySummary::getActivityId, ActActivitySummary::getViewCount);
            qw.in(ActActivitySummary::getActivityId, activityIds);
            actActivitySummaryList = actActivitySummaryMapper.selectList(qw);
        }
        return actActivitySummaryList;
    }

    /**
     * 获取直播观看量和评分数据
     *
     * @param liveIds
     * @return
     */
    public List<LiveSummary> getLiveSummaries(List<String> liveIds) {
        List<LiveSummary> liveSummaryList = null;
        if (!CollectionUtils.isEmpty(liveIds)) {
            LambdaQueryWrapper<LiveSummary> qw = new LambdaQueryWrapper<>();
            qw.select(LiveSummary::getAvgScore, LiveSummary::getLiveId, LiveSummary::getUv);
            qw.in(LiveSummary::getLiveId, liveIds);
            liveSummaryList = liveSummaryMapper.selectList(qw);
        }
        return liveSummaryList;
    }

    /**
     * 获取面授课观看量和评分数据
     *
     * @param faceCourseIds
     * @return
     */
    public List<ActCourseSummary> getActCourseSummaries(List<Integer> faceCourseIds) {
        List<ActCourseSummary> actCourseSummaryList = null;
        if (!CollectionUtils.isEmpty(faceCourseIds)) {
            LambdaQueryWrapper<ActCourseSummary> qw = new LambdaQueryWrapper<>();
            qw.select(ActCourseSummary::getAvgScore, ActCourseSummary::getCourseId, ActCourseSummary::getViewCount);
            qw.in(ActCourseSummary::getCourseId, faceCourseIds);
            actCourseSummaryList = actCourseSummaryMapper.selectList(qw);
        }
        return actCourseSummaryList;
    }

    /**
     * 获取网络课观看量和评分数据
     *
     * @param netCourseIds
     * @return
     */
    public List<ActNetCourseSummary> getActNetCourseSummaries(List<Integer> netCourseIds) {
        List<ActNetCourseSummary> actNetCourseSummaryList = null;
        if (!CollectionUtils.isEmpty(netCourseIds)) {
            LambdaQueryWrapper<ActNetCourseSummary> qw = new LambdaQueryWrapper<>();
            qw.select(ActNetCourseSummary::getAvgScore, ActNetCourseSummary::getNetCourseId, ActNetCourseSummary::getViewCount, ActNetCourseSummary::getPraiseCount);
            qw.in(ActNetCourseSummary::getNetCourseId, netCourseIds);
            actNetCourseSummaryList = actNetCourseSummaryMapper.selectList(qw);
        }
        return actNetCourseSummaryList;
    }

    /**
     * 添加延伸内容
     *
     * @param extendContentOutDTOS
     * @param actId
     * @param actType
     * @return
     */
    public Integer addExtendContents(List<ExtendContentOutDTO> extendContentOutDTOS, Integer actId, Integer actType) {
        ArrayList<ExtendContentDTO> extendContentDTOS = new ArrayList<>();
        //前端传的数据内部转换
        if (!CollectionUtils.isEmpty(extendContentOutDTOS)){
            for (ExtendContentOutDTO dto : extendContentOutDTOS) {
                ExtendContentDTO extendContentDTO = new ExtendContentDTO();
                extendContentDTO.setId(dto.getId());
                extendContentDTO.setConnProdModuleId(dto.getContentModuleId());
                extendContentDTO.setConnProdModuleName(dto.getContentModuleName());
                extendContentDTO.setConnProdItemId(dto.getContentItemId());
                extendContentDTO.setContentType(dto.getContentType());
                extendContentDTO.setConnProdName(dto.getContentName());
                extendContentDTO.setConnProdUrl(dto.getHref());
                extendContentDTO.setConnProdCoverId(dto.getContentCoverImgId());
                extendContentDTO.setViewCount(dto.getViewCount());
                extendContentDTO.setAvgScore(dto.getAvgScore());
                extendContentDTO.setConnProdCreatedTime(dto.getContentCreatedTime());
                extendContentDTO.setTargetPeople(dto.getTargetPeople());
                extendContentDTOS.add(extendContentDTO);
            }
        }
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
       //拿到自定义课程
        List<ExtendContentDTO> collect = extendContentDTOS.stream().filter(item -> item.getContentType().equals(2)).collect(Collectors.toList());
        //内部课程去重
        extendContentDTOS=extendContentDTOS.stream().filter(o -> o.getContentType().equals(1))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getConnProdModuleId() + ";" + o.getConnProdItemId()))), ArrayList::new));
        if (!CollectionUtils.isEmpty(collect)){
            extendContentDTOS.addAll(collect);
        }
        Integer prodId = actId;
        Integer prodType = actType;
        LambdaQueryWrapper<FileProdConn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileProdConn::getProdId, prodId);
        wrapper.eq(FileProdConn::getProdType, prodType);
        wrapper.eq(FileProdConn::getEnabled, true);
        List<FileProdConn> prodConns = fileProdConnMapper.selectList(wrapper);
        List<FileProdConn> addFileProdConns = new ArrayList<>();
        if (CollectionUtils.isEmpty(prodConns)) {
            int orderNo = 1;
            for (ExtendContentDTO contentDTO : extendContentDTOS) {
                FileProdConn fileProdConn = new FileProdConn();
                setFileProConnData(contentDTO, fileProdConn, prodId, prodType, staffId, staffName);
                //新增数据添加排序号
                fileProdConn.setOrderNo(orderNo++);
                addFileProdConns.add(fileProdConn);
            }
        } else {
            List<FileProdConn> updateFileProdConns = new ArrayList<>();
            List<Integer> existIds = extendContentDTOS.stream().map(ExtendContentDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Integer> ids = prodConns.stream().map(FileProdConn::getId).collect(Collectors.toList());
            ids.removeAll(existIds);
            if (!CollectionUtils.isEmpty(ids)) {
                LambdaUpdateWrapper<FileProdConn> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(FileProdConn::getEnabled, false);
                updateWrapper.in(FileProdConn::getId, ids);
                //软删除页面已删除数据
                fileProdConnMapper.update(null, updateWrapper);
            }
            int orderNo = 1;

            for (ExtendContentDTO contentDTO : extendContentDTOS) {
                FileProdConn fileProdConn = new FileProdConn();
                if (contentDTO.getId() == null) {
                    setFileProConnData(contentDTO, fileProdConn, prodId, prodType, staffId, staffName);
                    //新增数据添加排序号
                    fileProdConn.setOrderNo(orderNo++);
                    addFileProdConns.add(fileProdConn);
                } else {
                    //存在数据修改排序号
                    fileProdConn.setId(contentDTO.getId());
                    fileProdConn.setOrderNo(orderNo++);
                    fileProdConn.setUpdateId(staffId);
                    fileProdConn.setUpdateName(staffName);
                    fileProdConn.setUpdatedAt(new Date());
                    updateFileProdConns.add(fileProdConn);
                }
            }
            if (!CollectionUtils.isEmpty(updateFileProdConns)) {
                fileProdConnMapper.updateBatch(updateFileProdConns);
            }
        }
        if (!CollectionUtils.isEmpty(addFileProdConns)) {
            fileProdConnMapper.insertBatch(addFileProdConns);
        }
        //删除缓存
        removeExtendContentListCache(actId,actType,null);
        return extendContentDTOS.size();
    }

    /**
     * 设置延伸课程参数数据
     * @param contentDTO
     * @param fileProdConn
     * @param prodId
     * @param prodType
     * @param staffId
     * @param staffName
     */
    private void setFileProConnData(ExtendContentDTO contentDTO, FileProdConn fileProdConn, Integer prodId, Integer prodType, Integer staffId, String staffName) {
        BeanUtils.copyProperties(contentDTO, fileProdConn);
        fileProdConn.setProdId(prodId);
        fileProdConn.setProdType(prodType);
        fileProdConn.setConnProdType(getContentActType(fileProdConn.getConnProdModuleId()));
        fileProdConn.setConnProdTypeName(fileProdConn.getConnProdModuleName());
        fileProdConn.setEnabled(true);
        fileProdConn.setCreatorId(staffId);
        fileProdConn.setCreatorName(staffName);
        fileProdConn.setCreatedAt(new Date());
        fileProdConn.setUpdateId(staffId);
        fileProdConn.setUpdateName(staffName);
        fileProdConn.setUpdatedAt(new Date());
    }

    /**
     * 获取课程的actType
     * @param moduleId
     * @return
     */
    private Integer getContentActType(Integer moduleId) {
        String moduleName = ModuleIdEnum.getModuleName(moduleId);
        return ActTypeEnum.getActType(moduleName);
    }

    /**
         * 添加延伸内容
         *
         * @param extendStudyDto
         * @return
         */
    public boolean addExtendStudy(ActExtendStudyDto extendStudyDto) {
        if (CollectionUtils.isEmpty(extendStudyDto.getExtendList())) {
            return false;
        }
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        String staffName = GatewayContext.current().getStaffName();
        //延伸学习数据去重
        ArrayList<ExtendStudyDto> studyDtos = extendStudyDto.getExtendList().stream().filter(o -> o.getContentType().equals(1))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getConnProdModuleId() + ";" + o.getConnProdItemId()))), ArrayList::new));

        Integer prodType = extendStudyDto.getActType();
        LambdaQueryWrapper<FileProdConn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileProdConn::getProdId, extendStudyDto.getActType());
        wrapper.eq(FileProdConn::getProdType, prodType);
        wrapper.eq(FileProdConn::getEnabled, true);
        List<FileProdConn> prodConns = fileProdConnMapper.selectList(wrapper);
        List<FileProdConn> addFileProdConns = new ArrayList<>();
        if (CollectionUtils.isEmpty(prodConns)) {
            int orderNo = 1;
            for (ExtendStudyDto studyDto : studyDtos) {
                FileProdConn fileProdConn = new FileProdConn();
                BeanUtils.copyProperties(studyDto, fileProdConn);
                fileProdConn.setProdId(extendStudyDto.getActId());
                fileProdConn.setProdType(prodType);
                fileProdConn.SetCreatAndUpdate(staffId, staffName);
                //新增数据添加排序号
                fileProdConn.setOrderNo(orderNo++);
                addFileProdConns.add(fileProdConn);
            }
        } else {
            List<FileProdConn> updateFileProdConns = new ArrayList<>();
            List<Integer> existIds = studyDtos.stream().map(ExtendStudyDto::getId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Integer> ids = prodConns.stream().map(FileProdConn::getId).collect(Collectors.toList());
            ids.removeAll(existIds);
            if (!CollectionUtils.isEmpty(ids)) {
                LambdaUpdateWrapper<FileProdConn> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(FileProdConn::getEnabled, false);
                updateWrapper.in(FileProdConn::getId, ids);
                //软删除页面已删除数据
                fileProdConnMapper.update(null, updateWrapper);
            }
            int orderNo = 1;
            for (ExtendStudyDto studyDto : studyDtos) {
                FileProdConn fileProdConn = new FileProdConn();
                if (studyDto.getId() == null) {
                    BeanUtils.copyProperties(studyDto, fileProdConn);
                    fileProdConn.setProdId(extendStudyDto.getActId());
                    fileProdConn.setProdType(prodType);
                    fileProdConn.SetCreatAndUpdate(staffId, staffName);
                    //新增数据添加排序号
                    fileProdConn.setOrderNo(orderNo++);
                    addFileProdConns.add(fileProdConn);
                } else {
                    //存在数据修改排序号
                    fileProdConn.setId(studyDto.getId());
                    fileProdConn.setOrderNo(orderNo++);
                    fileProdConn.SetUpdate(staffId, staffName);
                    updateFileProdConns.add(fileProdConn);
                }
            }
            if (!CollectionUtils.isEmpty(updateFileProdConns)) {
                fileProdConnMapper.updateBatch(updateFileProdConns);
            }
        }
        if (!CollectionUtils.isEmpty(addFileProdConns)) {
            fileProdConnMapper.insertBatch(addFileProdConns);
        }

        return true;
    }

    /**
     * 获取延伸课程列表
     * @param actId
     * @param actType
     * @return
     */
    public List<ExtendContentOutPageDTO> getExtentContentList(Integer actId,Integer actType,Integer scenarioType) {
        Integer staffId = Integer.valueOf(GatewayContext.current().getStaffId());
        String redisKey;
        if(scenarioType == null){
            //延申学习列表缓存
            redisKey = CacheKeyEnum.ExtendContentList.getKeyName() +actType+":"+actId;
        }else {
            //课前延申学习列表缓存，由于班级延申学习配置走的是v8，所有要v8那边进行缓存修改
            redisKey = CacheKeyEnum.ExtendContentList.getKeyName() +actType+":"+actId+":"+scenarioType;
        }
        List<ExtendContentOutPageDTO> extendContentOutPageDTOS = null;
        if (redisUtil.get(redisKey) == null) {
           List<ExtendContentPageDTO> dtos = fileProdConnMapper.queryExtendContentByActId(actId,actType,scenarioType);
            //获取最新评分和播放量
            getNewestViewCountAndAvgScore(dtos);
            //转换为对前端的字段格式
            extendContentOutPageDTOS= getExtendContentOutPageDTOS(dtos);
            if (!CollectionUtils.isEmpty(extendContentOutPageDTOS)) {
                redisUtil.set(redisKey, extendContentOutPageDTOS, CacheExpireConstant.CacheExpireEnum.Cache_Time_Expire_5_minute.getTime());
            }
        } else {
            extendContentOutPageDTOS = (List<ExtendContentOutPageDTO>) redisUtil.get(redisKey);
        }
        Iterator<ExtendContentOutPageDTO> iterator = extendContentOutPageDTOS.iterator();
        while (iterator.hasNext()) {
            ExtendContentOutPageDTO dto = iterator.next();
            //文章&网课跳转地址处理为短码地址
            if (dto.getContentModuleId() == 8 && dto.getContentItemId().length() < 20){
                String href = graphicUrl + "?scheme_type=graphic&graphic_id=" + dto.getContentItemId();
                dto.setHref(href);
            } else if (dto.getContentModuleId() == 1){
                String href = netCourseUrl + "?course_id=" + dto.getContentItemId();
                dto.setHref(href);
            }

            String targetPeople = dto.getTargetPeople();
            if (StringUtils.isNotBlank(targetPeople)) {
//                    List<TargetPeopleDTO> targetPeopleDTOS = JsonUtils.jsonToList(targetPeople, TargetPeopleDTO.class);

                List<TargetPeopleDTO> targetPeopleDTOS = JsonUtil.getListByJson(targetPeople, TargetPeopleDTO.class);
                if (!CollectionUtils.isEmpty(targetPeopleDTOS)) {
                    //判断当前用户属不属于目标人群，不属于就不展示这条数据
                    Optional<TargetPeopleDTO> optional = targetPeopleDTOS.stream().filter(item -> item.getStaffId().equals(staffId)).findFirst();
                    if (!optional.isPresent()) {
                        iterator.remove();
                    }
                }
            }
        }
        return extendContentOutPageDTOS;
    }
    /**
     * 删除延伸课程列表缓存
     *
     * @param actId
     * @param actType
     */
    public void removeExtendContentListCache(Integer actId,Integer actType,Integer scenarioType) {
        String redisKey;
        if(scenarioType == null){
            //延申学习列表缓存
            redisKey = CacheKeyEnum.ExtendContentList.getKeyName() +actType+":"+actId;
        }else {
            //面授课的课前延申学习列表缓存
            redisKey = CacheKeyEnum.ExtendContentList.getKeyName() +actType+":"+actId+":"+scenarioType;
        }
        redisUtil.del(redisKey);
    }
    public List<ExtendStudyDto> getExtendStudys(Integer prodId, Integer ProdType) {

        QueryWrapper<FileProdConn> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prod_id", prodId);
        queryWrapper.eq("prod_type", ProdType);
        queryWrapper.eq("enabled", true);
        queryWrapper.orderByAsc("order_no");
        List<FileProdConn> conns = fileProdConnMapper.selectList(queryWrapper);
        ArrayList<ExtendStudyDto> extendStudyDtos = new ArrayList<>();
        conns.forEach( fileProdConn -> {
            ExtendStudyDto extendStudyDto = new ExtendStudyDto();
            BeanUtils.copyProperties(fileProdConn, extendStudyDto);
            extendStudyDtos.add(extendStudyDto);
        });
        return extendStudyDtos;
    }

    public Object getExtendStudyList(Integer netCourseId, Integer prodType, int pageNo, int pageSize) {

        QueryWrapper<FileProdConn> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prod_id", netCourseId);
        queryWrapper.eq("prod_type", prodType);
        queryWrapper.eq("enabled", true);
        queryWrapper.orderByAsc("order_no");
        Page<FileProdConn> page = fileProdConnMapper.selectPage(new Page<>(pageNo, pageSize), queryWrapper);
        ArrayList<ExtendStudyDto> extendStudyDtos = new ArrayList<>();
        page.getRecords().forEach( fileProdConn -> {
            ExtendStudyDto extendStudyDto = new ExtendStudyDto();
            BeanUtils.copyProperties(fileProdConn, extendStudyDto);
            extendStudyDtos.add(extendStudyDto);
        });

        Page<ExtendStudyDto> objectPage = new Page<>();
        BeanUtils.copyProperties(page, objectPage);
        objectPage.setRecords(extendStudyDtos);
        return objectPage;
    }

    /**
     * 保存延申学习
     * @param addDto
     */
    public String courseConn(ExtendContentAddDTO addDto) {
        addDto.setStaffId(Integer.parseInt( GatewayContext.current().getStaffId()));
        addDto.setStaffName(GatewayContext.current().getStaffName());
        removeExtendContentListCache(addDto.getProdId(),addDto.getProdType(),addDto.getUsageScenario());
        return faceClassProxyService.courseConn(addDto);
    }

    /**
     * 删除延申学习
     * @param extendDelDTO
     * @return
     */
    public String deleteConn(ExtendDelDTO extendDelDTO) {
        extendDelDTO.setStaffId(Integer.parseInt( GatewayContext.current().getStaffId()));
        extendDelDTO.setStaffName(GatewayContext.current().getStaffName());
        FileProdConn fileProdConn = fileProdConnMapper.selectById(extendDelDTO.getId());
        if(fileProdConn == null){
            throw new LogicException("延申学习不存在或已解除关联！");
        }
        Integer prodType = fileProdConn.getProdType();
        Integer prodId = fileProdConn.getProdId();
        Integer usageScenario = fileProdConn.getUsageScenario();
        //删除延申学习缓存
        removeExtendContentListCache(prodId,prodType,usageScenario);
        return faceClassProxyService.deleteConn(extendDelDTO);
    }

    /**
     * 设置延申学习置顶
     * @param extendSetTopDTO
     * @return
     */
    public String courseConnSetTop(ExtendSetTopDTO extendSetTopDTO) {
        extendSetTopDTO.setStaffId(Integer.parseInt( GatewayContext.current().getStaffId()));
        extendSetTopDTO.setStaffName(GatewayContext.current().getStaffName());
        FileProdConn fileProdConn = fileProdConnMapper.selectById(extendSetTopDTO.getId());
        if(fileProdConn == null){
            throw new LogicException("延申学习不存在或已解除关联！");
        }
        Integer prodType = fileProdConn.getProdType();
        Integer prodId = fileProdConn.getProdId();
        Integer usageScenario = fileProdConn.getUsageScenario();
        //删除延申学习缓存
        removeExtendContentListCache(prodId,prodType,usageScenario);
        return faceClassProxyService.courseConnSetTop(extendSetTopDTO);
    }


}
