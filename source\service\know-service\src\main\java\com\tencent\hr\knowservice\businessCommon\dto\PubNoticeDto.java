package com.tencent.hr.knowservice.businessCommon.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Date;

@Data
public class PubNoticeDto {
    /**
     * 公告ID
     */
    @TableId(type = IdType.AUTO)
    private Integer noticeId;
    /**
     * 标题
     */
    private String title;
    /**
     * 公告内容
     */
    private String content;
    /**
     * 资源id
     */
    private String actId;
    /**
     * 资源类型
     */
    private String actType;
    /**
     * 状态 0未发布 1已发布 2已取消
     */
    private String status;
    /**
     * 查看人数
     */
    private Integer viewCount;
    /**
     * 是否有效 0否 1是
     */
    private Boolean enabled;
    /**
     * 计划发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preparePublishTime;
    /**
     * 创建人Id
     */
    private String creatorId;
    /**
     * 是否已读
     */
    private Boolean Viewed;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;
    private static final long serialVersionUID = 1L;
}
