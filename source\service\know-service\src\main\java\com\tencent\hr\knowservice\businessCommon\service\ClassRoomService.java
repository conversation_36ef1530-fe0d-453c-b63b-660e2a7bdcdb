package com.tencent.hr.knowservice.businessCommon.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tencent.hr.knowservice.businessCommon.constans.ActTypeEnum;
import com.tencent.hr.knowservice.businessCommon.constans.TeachingTypeEnum;
import com.tencent.hr.knowservice.businessCommon.dao.entity.*;
import com.tencent.hr.knowservice.businessCommon.dao.mapper.*;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.*;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.framework.dto.ContextEntity;
import com.tencent.hr.knowservice.framework.dto.GatewayContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: liqiang
 * @createDate: 2023/08/18/17:33
 * @version: 1.0
 */
@Service
@Slf4j
public class ClassRoomService {

    @Autowired
    InteractiveClassroomAssociationMapper interactiveClassroomAssociationMapper;
    @Autowired
    ActActivityMapper actActivityMapper;

    @Autowired
    ActClassMapper actClassMapper;

    @Autowired
    TargetPeopleServiceProxy targetPeopleServiceProxy;

    @Autowired
    ClassRoomProxyService classRoomProxyService;

    @Autowired
    ActClassTeachersMapper actClassTeachersMapper;

    @Autowired
    ActActivityTeachersMapper actActivityTeachersMapper;

    @Autowired
    ActClassAttendanceMapper actClassAttendanceMapper;

    @Value("${extapi.classroom.classroomToken}")
    String classroomToken;

    @Value("${extapi.classroom.classroomUrl}")
    String classroomUrl;

    /**
     * 开启互动课堂
     * @param insert
     */
    @Transactional(rollbackFor = Exception.class)
    public String insertClassRoom(InteractiveClassroomAssociationInDTO insert) {
        InteractiveClassroomAssociation classroomAssociation = findByActTypeAndItemId(insert.getActType(), insert.getItemId());
        if(classroomAssociation != null){
            throw new LogicException("该课程已关联互动课堂！");
        }
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //调用互动课堂服务入参实体类
        ClassRoomProxyInDTO proxyInDTO = new ClassRoomProxyInDTO();
        if(ActTypeEnum.CLASSES.getActType().equals(insert.getActType())){
            //班级
            ActClassDTO actClass = findActClassById(Integer.valueOf(insert.getItemId()));
            if(actClass == null){
                throw new LogicException("班级不存在！");
            }
            //必须在班级结束之前开启
            if(actClass.getEndTime() != null && actClass.getEndTime().before(new Date())){
                throw new LogicException("班级已结束，无法启用互动课堂！");
            }
            //只有包含线上授课的方式才有会议id
            if(actClass.getTeachingType().intValue() == TeachingTypeEnum.ONLINE_TEACHING.getTeachingType() || actClass.getTeachingType().intValue() == TeachingTypeEnum.COMPREHENSIVE_TEACHING.getTeachingType()){
                proxyInDTO.setLessonNumber(actClass.getMeetingCode());
            }
            proxyInDTO.setTitle(actClass.getClassName());
            proxyInDTO.setStartTime(sf.format(actClass.getStartTime()));
            proxyInDTO.setEndTime(sf.format(actClass.getEndTime()));
            proxyInDTO.setUserId(actClass.getHeadTeacherName());
            Integer classRoomTeachingType = TeachingTypeEnum.getActTeachingTypeEnum(actClass.getTeachingType().intValue()).getClassRoomTeachingType();
            proxyInDTO.setLessonType(classRoomTeachingType);
            List<String> teacherNames = findClassInnerTeacherNames(actClass.getClassId());
            teacherNames.add(0,actClass.getHeadTeacherName());
            if(CollectionUtils.isEmpty(teacherNames)){
                teacherNames.add(actClass.getCreatorName());
            }
            proxyInDTO.setTeacher(teacherNames);
        }else if(ActTypeEnum.ACTIVITY.getActType().equals(insert.getActType())){
            ActActivityInfoDTO activity = findActivityById(Integer.valueOf(insert.getItemId()));
            if(activity == null){
                throw new LogicException("活动不存在！");
            }
            //必须在活动结束之前开启
            if(activity.getEndTime() != null && activity.getEndTime().before(new Date())){
                throw new LogicException("活动已结束，无法启用互动课堂！");
            }
            //只有包含线上授课的方式才有会议id
            if(activity.getTeachingType().intValue() == TeachingTypeEnum.ONLINE_TEACHING.getTeachingType()){
                proxyInDTO.setLessonNumber(activity.getMeetingCode());
            }
            proxyInDTO.setTitle(activity.getActivityName());
            proxyInDTO.setStartTime(sf.format(activity.getStartTime()));
            proxyInDTO.setEndTime(sf.format(activity.getEndTime()));
            proxyInDTO.setUserId(activity.getHeadTeacherName());
            Integer classRoomTeachingType = TeachingTypeEnum.getActTeachingTypeEnum(activity.getTeachingType()).getClassRoomTeachingType();
            proxyInDTO.setLessonType(classRoomTeachingType);
            List<String> activityTeacherNames = findActivityTeacherNames(activity.getActivityId());
            activityTeacherNames.add(0,activity.getHeadTeacherName());
            if(CollectionUtils.isEmpty(activityTeacherNames)){
                activityTeacherNames.add(activity.getCreatorName());
            }
            proxyInDTO.setTeacher(activityTeacherNames);
        }else {
            throw new LogicException("课程类型错误！");
        }
        proxyInDTO.setVerify(getClassroomVerify());
        String lessonId = classRoomProxyService.createLesson(proxyInDTO);
        if(null == lessonId){
            log.error("返回的互动课堂id为空！");
            throw new LogicException("开启互动课堂失败！");
        }
        ActTypeEnum actTypeEnum = ActTypeEnum.getActTypeEnum(insert.getActType());
        InteractiveClassroomAssociation entity = new InteractiveClassroomAssociation();
        entity.setItemId(insert.getItemId());
        entity.setActTypeName(actTypeEnum.getActTypeName());
        entity.setClassroomStatus((byte) 1);
        entity.setAuthScope(insert.getAuthScope().byteValue());
        entity.setClassroomTargetList(insert.getClassroomTargetList());
        entity.setActType(insert.getActType());
        entity.setClassroomId(lessonId);
        String classRoomUrl = classroomUrl.replace("{numb}",lessonId);
        entity.setClassroomUserUrl(classRoomUrl);
        Date currDate = new Date();
        entity.setCreatorId(Integer.valueOf(staffId));
        entity.setCreatorName(staffName);
        entity.setCreatedAt(currDate);
        entity.setUpdateId(Integer.valueOf(staffId));
        entity.setUpdateName(staffName);
        entity.setUpdatedAt(currDate);
        entity.setEnable(true);
        interactiveClassroomAssociationMapper.insert(entity);
        return classRoomUrl;
    }

    /**
     * 检查用户是否有访问互动课堂的权限
     * @param classroomId
     * @param staffId
     * @return
     */
    public boolean checkClassroomAuth(String classroomId,String staffId) {
        ContextEntity current = GatewayContext.current();
        if (staffId == null){
            if (StringUtils.isEmpty(current.getStaffId())){
                throw new LogicException("获取不到当前用户id");
            }
            staffId = current.getStaffId();
        }
        QueryWrapper<InteractiveClassroomAssociation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable",1)
                        .eq("classroom_id",classroomId);
        queryWrapper.last("limit 1");
        InteractiveClassroomAssociation interactiveClassroomAssociation = interactiveClassroomAssociationMapper.selectOne(queryWrapper);
        if (interactiveClassroomAssociation == null || interactiveClassroomAssociation.getClassroomStatus() == 0){
            throw new LogicException("当前课程未开启互动课堂");
        }
        String itemId = interactiveClassroomAssociation.getItemId();
        Integer actType = interactiveClassroomAssociation.getActType();
        Integer authScope = Integer.valueOf(interactiveClassroomAssociation.getAuthScope());
        if (authScope == 0){
            return true;
        }else if (authScope == 1){
            ActTypeEnum actTypeEnum = ActTypeEnum.getActTypeEnum(actType);
            switch (actTypeEnum){
                case ACTIVITY:
                    ActActivityInfoDTO actActivity = findActivityById(Integer.valueOf(itemId));
                    if (actActivity == null){
                        throw new LogicException("课程不存在");
                    }
                    return checkStudentWithAttendance(actTypeEnum.getActType(),Integer.valueOf(itemId),Integer.valueOf(staffId));
                case CLASSES:
                    ActClassDTO actClass = findActClassById(Integer.valueOf(itemId));
                    if (actClass == null){
                        throw new LogicException("课程不存在");
                    }
                    return checkStudentWithAttendance(actTypeEnum.getActType(),Integer.valueOf(itemId),Integer.valueOf(staffId));
                default:
                    return false;
            }
        }else if (authScope == 2){
            String classroomTargetList = interactiveClassroomAssociation.getClassroomTargetList();
            return checkTargetId(classroomTargetList,staffId);
        }
        return false;
    }

    /**
     * 根据目标学员判断当前用户是否有权限
     * @param targetIds
     * @param staffId
     * @return
     */
    private boolean checkTargetId(String targetIds, String staffId) {
        if (StringUtils.isEmpty(targetIds)){
            return true;
        }
        return targetPeopleServiceProxy.checkRight(staffId, targetIds);
    }

    /**
     * 判断仅对班级学员开放学员是否有权限
     * @param actType
     * @param itemId
     * @param staffId
     * @return
     */
    private boolean checkStudentWithAttendance(Integer actType,Integer itemId,Integer staffId){
        QueryWrapper<ActClassAttendance> query = new QueryWrapper<>();
        query.eq("act_type",actType);
        query.eq("class_id",itemId);
        query.eq("staff_id",staffId);
        query.isNull("deleted_at");
        query.last("limit 1");
        ActClassAttendance actClassAttendance = actClassAttendanceMapper.selectOne(query);
        return actClassAttendance == null? false : true;
    }
    /**
     * 根据活动id查询活动
     * @param activityId
     * @return
     */
    private ActActivityInfoDTO findActivityById(Integer activityId){
        return actActivityMapper.findActivityById(activityId);
    }

    /**
     * 根据班级id查询班级
     * @param classId
     * @return
     */
    private ActClassDTO findActClassById(Integer classId){
        return actClassMapper.findActClassById(classId);
    }

    /**
     * 获取班级的所有内部讲师
     * @param classId
     * @return
     */
    private List<String> findClassInnerTeacherNames(Integer classId){
        List<String> teacherNames = new ArrayList<>();
        List<ActClassTeachers> classInnerTeachers = actClassTeachersMapper.findClassInnerTeachers(classId);
        for (ActClassTeachers classInnerTeacher : classInnerTeachers) {
            String staffName = classInnerTeacher.getStaffName();
            //获取英文名
            String substring = staffName.substring(0, staffName.indexOf("("));
            teacherNames.add(substring);
        }
        return teacherNames;
    }

    /**
     * 获取活动的所有内部讲师
     * @param activityId
     * @return
     */
    private List<String> findActivityTeacherNames(Integer activityId){
        List<String> teacherNames = new ArrayList<>();
        List<ActActivityTeachers> activityTeachers = actActivityTeachersMapper.findActivityTeachers(activityId);
        for (ActActivityTeachers activityTeacher : activityTeachers) {
            String staffName = activityTeacher.getStaffName();
            //获取英文名
            String substring = staffName.substring(0, staffName.indexOf("("));
            teacherNames.add(substring);
        }
        return teacherNames;
    }

    /**
     * 推送互动课堂全量学员数据
     */
    public void pushCourseStudent(Integer actType, String itemId) {
        InteractiveClassroomAssociation classroomAssociation = findByActTypeAndItemId(actType,itemId);
        if(classroomAssociation == null){
            throw new LogicException("该互动课堂不存在或未关联课程，请检查！");
        }
        checkCourseExist(actType,itemId);
        //根据课程id和课程类型查询所有学员
        List<ActClassAttendance> attendances = findAttendancesByTypeAndCourseId(classroomAssociation.getActType(), Integer.valueOf(itemId));
        List<String> empNames = attendances.stream().map(ActClassAttendance::getEmpName).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(empNames)){
            return;
        }
        ClassroomImportProxyDTO importProxyDTO = new ClassroomImportProxyDTO();
        importProxyDTO.setUser(empNames);
        importProxyDTO.setCode(classroomAssociation.getClassroomId());
        importProxyDTO.setVerify(getClassroomVerify());
        classRoomProxyService.pushAllStudent(importProxyDTO);
    }


    /**
     * 对所有互动课程进行推送全量学员数据（只推送当前时间在课程起始时间内的课程数据）
     * @return
     */
    public int pushAllCourseStudent(){
        //查询所有班级关联的互动课程信息,只查询当前时间在起始时间内 --包含班级的起始时间字段
        List<InteractiveClassroomAssociationDTO> allClassroomWithClass = interactiveClassroomAssociationMapper.findAllClassroomWithClassAndInTime();
        //查询所有班级关联的互动课程信息,只查询当前时间在起始时间内 --包含活动的起始时间字段
        List<InteractiveClassroomAssociationDTO> allClassroomWithActivity = interactiveClassroomAssociationMapper.findAllClassroomWithActivityAndInTime();
        List<InteractiveClassroomAssociationDTO> classroomAssociations = new ArrayList<>();
        classroomAssociations.addAll(allClassroomWithClass);
        classroomAssociations.addAll(allClassroomWithActivity);
        int count = 0;
        if(CollectionUtils.isNotEmpty(classroomAssociations)){
            for (InteractiveClassroomAssociationDTO association : classroomAssociations) {
                pushCourseStudent(association.getActType(),association.getItemId());
                count ++;
            }
        }
        return count;
    }

    /**
     * 根据互动课堂id查询关联课程
     * @param actType
     * @param itemId
     * @return
     */
    public InteractiveClassroomAssociation findByActTypeAndItemId(Integer actType,String itemId){
        QueryWrapper<InteractiveClassroomAssociation> query = new QueryWrapper<>();
        query.eq("enable",1);
        query.eq("item_id",itemId);
        query.eq("act_type",actType);
        query.last("limit 1");
        return interactiveClassroomAssociationMapper.selectOne(query);
    }

    /**
     * 根据课程类型和课程id查询对应的学员
     * @param actType
     * @param courseId
     * @return
     */
    public List<ActClassAttendance> findAttendancesByTypeAndCourseId(Integer actType,Integer courseId){
        return actClassAttendanceMapper.findAttendancesByTypeAndCourseId(actType,courseId);
    }

    /**
     * 获取互动课堂配置信息
     * @param actType
     * @param itemId
     * @return
     */
    public ClassRoomOutDTO getClassRoom(Integer actType, String itemId) {
        checkCourseExist(actType,itemId);
        InteractiveClassroomAssociation classroomAssociation = findByActTypeAndItemId(actType,itemId);
        if(classroomAssociation == null){
            throw new LogicException("该课程未配置互动课堂！");
        }
        ClassRoomOutDTO classRoomOutDTO = new ClassRoomOutDTO();
        BeanUtils.copyProperties(classroomAssociation,classRoomOutDTO);
        return classRoomOutDTO;
    }

    /**
     * 修改互动课堂配置信息
     * @param insert
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateClassRoom(InteractiveClassroomAssociationInDTO insert) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        checkCourseExist(insert.getActType(),insert.getItemId());
        InteractiveClassroomAssociation classroomAssociation = findByActTypeAndItemId(insert.getActType(), insert.getItemId());
        if(classroomAssociation == null){
            throw new LogicException("该课程未关联互动课堂！");
        }
        //调用互动课堂服务入参实体类
        ClassRoomProxyInDTO proxyInDTO = new ClassRoomProxyInDTO();
        if(ActTypeEnum.CLASSES.getActType().equals(insert.getActType())){
            //班级
            ActClassDTO actClass = findActClassById(Integer.valueOf(insert.getItemId()));
            if(actClass == null){
                throw new LogicException("班级不存在！");
            }
            //必须在班级结束之前开启
            if(actClass.getEndTime() != null && actClass.getEndTime().before(new Date())){
                throw new LogicException("班级已结束，无法修改互动课堂！");
            }
            //只有包含线上授课的方式才有会议id
            if(actClass.getTeachingType().intValue() == TeachingTypeEnum.ONLINE_TEACHING.getTeachingType() || actClass.getTeachingType().intValue() == TeachingTypeEnum.COMPREHENSIVE_TEACHING.getTeachingType()){
                proxyInDTO.setLessonNumber(actClass.getMeetingCode());
            }
            proxyInDTO.setCode(classroomAssociation.getClassroomId());
            proxyInDTO.setTitle(actClass.getClassName());
            proxyInDTO.setStartTime(sf.format(actClass.getStartTime()));
            proxyInDTO.setEndTime(sf.format(actClass.getEndTime()));
            proxyInDTO.setUserId(actClass.getHeadTeacherName());
            Integer classRoomTeachingType = TeachingTypeEnum.getActTeachingTypeEnum(actClass.getTeachingType().intValue()).getClassRoomTeachingType();
            proxyInDTO.setLessonType(classRoomTeachingType);
            List<String> teacherNames = findClassInnerTeacherNames(actClass.getClassId());
            teacherNames.add(0,actClass.getHeadTeacherName());
            if(CollectionUtils.isEmpty(teacherNames)){
                teacherNames.add(actClass.getCreatorName());
            }
            proxyInDTO.setTeacher(teacherNames);
        }else if(ActTypeEnum.ACTIVITY.getActType().equals(insert.getActType())){
            ActActivityInfoDTO activity = findActivityById(Integer.valueOf(insert.getItemId()));
            if(activity == null){
                throw new LogicException("活动不存在！");
            }
            //必须在活动结束之前开启
            if(activity.getEndTime() != null && activity.getEndTime().before(new Date())){
                throw new LogicException("活动已结束，无法修改互动课堂！");
            }
            //只有包含线上授课的方式才有会议id
            if(activity.getTeachingType().intValue() == TeachingTypeEnum.ONLINE_TEACHING.getTeachingType()){
                proxyInDTO.setLessonNumber(activity.getMeetingCode());
            }
            proxyInDTO.setTitle(activity.getActivityName());
            proxyInDTO.setStartTime(sf.format(activity.getStartTime()));
            proxyInDTO.setEndTime(sf.format(activity.getEndTime()));
            proxyInDTO.setUserId(activity.getHeadTeacherName());
            Integer classRoomTeachingType = TeachingTypeEnum.getActTeachingTypeEnum(activity.getTeachingType()).getClassRoomTeachingType();
            proxyInDTO.setLessonType(classRoomTeachingType);
            List<String> activityTeacherNames = findActivityTeacherNames(activity.getActivityId());
            activityTeacherNames.add(0,activity.getHeadTeacherName());
            if(CollectionUtils.isEmpty(activityTeacherNames)){
                activityTeacherNames.add(activity.getCreatorName());
            }
            proxyInDTO.setTeacher(activityTeacherNames);
        }else {
            throw new LogicException("课程类型错误！");
        }
        proxyInDTO.setVerify(getClassroomVerify());
        //修改互动课堂
        String lessonId = classRoomProxyService.updateLesson(proxyInDTO);
        if(null == lessonId){
            log.error("返回的互动课堂id为空！");
            throw new LogicException("修改互动课堂失败！");
        }
        String staffId = GatewayContext.current().getStaffId();
        String staffName = GatewayContext.current().getStaffName();
        classroomAssociation.setUpdateId(Integer.valueOf(staffId));
        classroomAssociation.setUpdateName(staffName);
        classroomAssociation.setUpdatedAt(new Date());
        classroomAssociation.setEnable(true);
        classroomAssociation.setAuthScope(insert.getAuthScope().byteValue());
        classroomAssociation.setClassroomTargetList(insert.getClassroomTargetList());
        String classRoomUrl = classroomUrl.replace("{numb}",lessonId);
        classroomAssociation.setClassroomUserUrl(classRoomUrl);
        classroomAssociation.setClassroomId(lessonId);
        interactiveClassroomAssociationMapper.updateByPrimaryKeySelective(classroomAssociation);
    }


    /**
     * 互动课堂开启前置判断
     * @param actType
     * @param itemId
     * @return
     */
    public ClassroomRightCheckDTO classroomRightCheck(Integer actType, String itemId) {
        ClassroomRightCheckDTO checkDTO = new ClassroomRightCheckDTO();
        if(ActTypeEnum.CLASSES.getActType().equals(actType)){
            //面授课班级
            ActClassDTO actClass = findActClassById(Integer.valueOf(itemId));
            if(actClass == null){
                throw new LogicException("班级不存在！");
            }
            checkDTO.setItemId(actClass.getClassId().toString());
            checkDTO.setActType(actType);
            checkDTO.setStartTime(actClass.getStartTime());
            checkDTO.setEndTime(actClass.getEndTime());
        }else if(ActTypeEnum.ACTIVITY.getActType().equals(actType)){
            //活动校验
            ActActivityInfoDTO activity = findActivityById(Integer.valueOf(itemId));
            if(activity == null){
                throw new LogicException("活动不存在！");
            }
            checkDTO.setItemId(activity.getActivityId().toString());
            checkDTO.setActType(actType);
            checkDTO.setStartTime(activity.getStartTime());
            checkDTO.setEndTime(activity.getEndTime());
        }else {
            throw new LogicException("课程类型错误！");
        }
        //是否已经存在互动课堂
        InteractiveClassroomAssociation classroomAssociation = findByActTypeAndItemId(actType,itemId);
        if(classroomAssociation == null){
            checkDTO.setHasClassroom(false);
        }else {
            checkDTO.setHasClassroom(true);
            checkDTO.setClassroomId(classroomAssociation.getClassroomId());
        }
        return checkDTO;
    }

    public void checkCourseExist(Integer actType, String itemId){
        if(ActTypeEnum.CLASSES.getActType().equals(actType)){
            //班级
            ActClassDTO actClass = findActClassById(Integer.valueOf(itemId));
            if(actClass == null){
                throw new LogicException("班级不存在！");
            }
        }else if(ActTypeEnum.ACTIVITY.getActType().equals(actType)){
            ActActivityInfoDTO activity = findActivityById(Integer.valueOf(itemId));
            if(activity == null){
                throw new LogicException("活动不存在！");
            }
        }else {
            throw new LogicException("课程类型错误！");
        }
    }

    /**
     * 获取互动课堂的签名
     * @return
     */
    private String getClassroomVerify(){
        long currentTimeMillis = System.currentTimeMillis() / 1000;
        String token = classroomToken + currentTimeMillis;
        return DigestUtils.md5DigestAsHex(token.getBytes()).toLowerCase();
    }

    /**
     * 同步课程后同时同步互动课堂
     * @param actType
     * @param itemId
     */
    public void syncClassroom(Integer actType, String itemId) {
        //反查互动课堂是否存在
        InteractiveClassroomAssociation classroomAssociation = findByActTypeAndItemId(actType, itemId);
        //有些课程没有开启互动课堂，如果没有查询到就直接返回
        if(classroomAssociation == null){
            return;
        }
        InteractiveClassroomAssociationInDTO updateEntity = new InteractiveClassroomAssociationInDTO();
        updateEntity.setActType(actType);
        updateEntity.setItemId(itemId);
        updateEntity.setAuthScope((int)classroomAssociation.getAuthScope());
        updateEntity.setClassroomTargetList(classroomAssociation.getClassroomTargetList());
        updateClassRoom(updateEntity);
    }
}
