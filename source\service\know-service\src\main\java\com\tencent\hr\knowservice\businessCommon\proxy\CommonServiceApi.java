package com.tencent.hr.knowservice.businessCommon.proxy;

import com.tencent.hr.base.dto.TransDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = "training-portal-common", url = "${project.portal-common-host}")
public interface CommonServiceApi {

    /**
     * 获取智绘封面
     *
     * @param title 获取相应的图片素材的文字，最少4个字符
     * @return
     */
    @GetMapping("/api/v1/common/zhihui/smartcover")
    public TransDTO getSmartCover(@RequestParam(value = "title") String title,
                                  @RequestParam(value = "num") Integer num) ;

}
