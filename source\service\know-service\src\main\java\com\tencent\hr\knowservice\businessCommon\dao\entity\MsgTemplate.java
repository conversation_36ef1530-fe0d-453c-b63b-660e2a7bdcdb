package com.tencent.hr.knowservice.businessCommon.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * msg_template
 * <AUTHOR>
@Data
public class MsgTemplate implements Serializable {
    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模块类型
     */
    private Integer actType;

    /**
     * 模板类型,邮件：mail | 短信：sms|机器人：robot
     */
    private String templateType;

    /**
     * 对应功能，消息通知：notify | 催办：remind
     */
    private String moduleName;

    /**
     * 标题
     */
    private String title;

    /**
     * 发送方地址
     */
    private String sendFrom;

    /**
     * 默认抄送人
     */
    private String bcc;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 是否有效
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private String creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private String updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    private Date updatedAt;

    private static final long serialVersionUID = 1L;
}