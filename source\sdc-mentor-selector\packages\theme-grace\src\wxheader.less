@import "./header.less";

.sdc-wxheader {
  .logo-text{
    // width: 220px;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;
  }
  .ellipsis {
    overflow     : hidden;
    white-space  : nowrap;
    text-overflow: ellipsis;
  }
  .header-icons {
    display: flex;
    // width: 180px;
    margin-right: 20px;
    a{
      display: flex !important;
      align-items: center;
      margin-right: 30px;
      &:last-child{
        margin-right: 0px;
      }
      i{
        font-size: 20px;
        margin-right: 5px;
        &.icon-appstore{
          display: inline-block;
          line-height: 1;
          width: 20px;
          height: 20px;
          background-image: url("../img/icon-appstore.svg"); 
        }
      }
    }
  }
}
