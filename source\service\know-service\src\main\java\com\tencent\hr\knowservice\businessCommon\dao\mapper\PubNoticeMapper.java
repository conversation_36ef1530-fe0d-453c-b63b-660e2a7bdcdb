package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tencent.hr.knowservice.businessCommon.dao.entity.PubNotice;
import com.tencent.hr.knowservice.businessCommon.dto.PubNoticeDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PubNoticeMapper extends BaseMapper<PubNotice> {
    int deleteByPrimaryKey(Integer noticeId);

    int insert(PubNotice record);

    int insertSelective(PubNotice record);

    PubNotice selectByPrimaryKey(Integer noticeId);

    int updateByPrimaryKeySelective(PubNotice record);

    int updateByPrimaryKey(PubNotice record);
    //公告列表及查看的人数
    IPage<PubNoticeDto> getPubNoticeList(IPage<PubNoticeDto> page, @Param("actType") Integer actType, @Param("actId") String actId);

    List<PubNoticeDto> getPubNoticeListPublish(@Param("actType") Integer actType, @Param("actId") String actId,@Param("status") Integer status);
}