package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.InteractiveClassroomAssociation;
import com.tencent.hr.knowservice.businessCommon.dto.classRoom.InteractiveClassroomAssociationDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InteractiveClassroomAssociationMapper extends BaseMapper<InteractiveClassroomAssociation> {
    int deleteByPrimaryKey(Integer id);

    int insert(InteractiveClassroomAssociation record);

    int insertSelective(InteractiveClassroomAssociation record);

    InteractiveClassroomAssociation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InteractiveClassroomAssociation record);

    int updateByPrimaryKey(InteractiveClassroomAssociation record);

    List<InteractiveClassroomAssociationDTO> findAllClassroomWithClassAndInTime();

    List<InteractiveClassroomAssociationDTO> findAllClassroomWithActivityAndInTime();
}