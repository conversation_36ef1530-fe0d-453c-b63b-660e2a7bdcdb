package com.tencent.hr.knowservice.businessCommon.service;


import com.tencent.hr.common.util.JsonUtil;
import com.tencent.hr.knowservice.businessCommon.constans.Constants;
import com.tencent.hr.knowservice.framework.advice.exception.LogicException;
import com.tencent.hr.knowservice.mooc.constant.CacheKeyEnum;
import com.tencent.hr.knowservice.mooc.dto.moocCourse.MoocUserCourseMobileQRCodeDto;
import com.tencent.hr.knowservice.utils.HeaderSignUtil;
import com.tencent.hr.knowservice.utils.HttpUtil;
import com.tencent.hr.knowservice.utils.RedisUtil;
import jodd.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.http.HttpHeaders;

import java.io.*;
import java.util.Map;

@Service
@Slf4j
//@RestController
public class MobileQRCodeService {

    @Value("${qrcode-apply.applyUrl}")
    String qrcodeApplyUrl;

    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${com.appSetting.tenantCode}")
    private String tenantCode;

    @Value("${extapi.rio.token}")
    private String token;

    @Autowired
    private RedisUtil redisUtil;


    public String getAccessToken() {
//        String tokenKeyName = CacheKeyEnum.MoocQRCodeAccessToken.getKeyName();
//        String accessToken = (String) redisUtil.get(tokenKeyName);
//        if (StringUtils.isNotEmpty(accessToken)) {
//            return accessToken;
//        }
        String api = "/cgi-bin/token" + "?grant_type=client_credential&appid=wxbfe4046e0c71d516&secret=2e432d21c52be3b73d3989a823cf827e";
        HttpHeaders rioHeader = HeaderSignUtil.getRioHeader(token);
        String result = HttpUtil.sendGetByRestTemplate(qrcodeApplyUrl, api, rioHeader);
        Map map = JsonUtil.getMapByJson(result);
        String accessToken = (String) map.get("access_token");
//        if (StringUtils.isNotEmpty(accessToken)) {
//            redisUtil.set(tokenKeyName, accessToken, Constants.CacheExpireEnum.Cache_Time_Expire_1_minute.getTime());
//        } else {
//            log.error("调用获取接口凭证出错，result={}", map);
//        }
        if (StringUtils.isEmpty(accessToken)) {
            log.error("调用获取接口凭证出错，result={}", map);
        }
        return accessToken;
    }

    public String getMobileCode(MoocUserCourseMobileQRCodeDto dto) {
        if(StringUtils.isEmpty(dto.getScene())){
            throw new LogicException("scene值不能为null");
        }
        String cacheKey = CacheKeyEnum.MoocCourseQRCode
                .getKeyName().concat(dto.getScene())
                .concat(":")
                .concat(dto.getPage());

        String result = (String) redisUtil.get(cacheKey);
        if(StringUtils.isNotEmpty(result)){
            return result;
        }

        String accessToken = getAccessToken();
        String api = "/wxa/getwxacodeunlimit" + "?access_token=" + accessToken;
        HttpHeaders rioHeader = HeaderSignUtil.getRioHeader(token);
        String paramBody = JsonUtil.toJson(dto);
        result = HttpUtil.sendPostByRestTemplate(qrcodeApplyUrl, api, paramBody, rioHeader);
        redisUtil.set(cacheKey, result, Constants.CacheExpireEnum.Cache_Time_Expire_1_day.getTime());
        return result;
    }
}
