package com.tencent.hr.knowservice.businessCommon.dto.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class PubBannerDto {

    /**
     * id
     */
    private Integer id;

    /**
     * 功能模块编码
     */
    private Integer actType;

    /**
     * 功能模块的单据id
     */
    private Integer actId;


    /**
     * 1 普通图片banner模式  2  跳转内容模块模式
     */
    private Integer bannerType;


    /**
     * 轮播图名称
     */
//    @NotBlank
    private String bannerName;

    /**
     * 轮播图链接
     */
//    @NotBlank
    private String linkUrl;

    /**
     * 轮播图片
     */
//    @NotBlank
    private String imgContentId;

    /**
     * 轮播图背景色
     */
//    @NotBlank
    private String bgColor;

    /**
     * 0 下架 1 上架
     */
    private  Integer status ;

    /**
     * 课程描述
     */
    private String decription;


    /**
     * 浏览量
     */
    private Integer viewCount;


    /**
     * 展示顺序
     */
    private Integer orderNo;

    /**
     * 是否删除
     */
    private Boolean enabled;

    /**
     * 创建人Id
     */
    private Integer creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后修改人Id
     */
    private Integer updateId;

    /**
     * 最后修改人姓名
     */
    private String updateName;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    private Integer current;

    private Integer size;
}
