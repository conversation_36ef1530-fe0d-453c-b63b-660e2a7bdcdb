package com.tencent.hr.knowservice.businessCommon.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.myoa.MyOaCloseAndDiscardDto;
import com.tencent.hr.knowservice.businessCommon.dto.myoa.MyOaWorkItemDto;
import com.tencent.hr.knowservice.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class MessageService {
    @Value("${com.appSetting.appId}")
    private String appId;

    @Value("${com.appSetting.appToken}")
    private String appToken;

    @Value("${extapi.messageService.host}")
    private String host;

    @Value("${extapi.myoa.category}")
    private String myoaCategory;

    @Autowired
    BaseProxyService baseProxyService;

    /**
     * 发机器人
     * @param title
     * @param receivers
     * @param content
     * @param prepareSendTime
     * @param actType
     * @param actId
     * @return
     */
    public String SendBot(String title,String receivers, String content, Date prepareSendTime, String actType, String actId){
        final String api = "/api/v1/common/service/message/send-bot";
        //参数
        Map<String, Object> param = new HashMap<>();
        Map<String, Object> contentMap = new HashMap<>();
        param.put("title", title);
        param.put("msg_type", "markdown");
        contentMap.put("content",content);
        param.put("content", JsonUtils.objectToJson(contentMap));
        param.put("receiver", receivers);
        param.put("sender", "xiaoteng");
        param.put("can_send", true);
        param.put("prepare_send_time", prepareSendTime);
        param.put("act_id", actId);
        param.put("act_type", actType);
        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        //发送请求
        try {
            String result = baseProxyService.postForApiWithHeaders(host, api, param, headers);
            //处理结果
            TransDTO<String> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<String>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                log.error("发送机器人消息失败,result={}", result);
                return "";
            }
            String data = transDTO.getData();
            return data;
        } catch (Exception e) {
            log.error("发送机器人消息失败！", e);
            return "";
        }
    }

    /**
     * 发Tips
     * @param title
     * @param receivers
     * @param content
     * @param prepareSendTime
     * @param actType
     * @param actId
     * @return
     */
    public String SendTips(String title,String receivers, String content, Date prepareSendTime, String actType, String actId){
        final String api = "/api/v1/common/service/message/send-tips";
        //参数
        Map<String, Object> param = new HashMap<>();
        param.put("title", title);
        param.put("content",content);
        param.put("receiver", receivers);
        param.put("can_send", true);
        param.put("prepare_send_time", prepareSendTime);
        param.put("act_id", actId);
        param.put("act_type", actType);
        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        //发送请求
        try {
            String result = baseProxyService.postForApiWithHeaders(host, api, param, headers);
            //处理结果
            TransDTO<String> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<String>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                log.error("发送企业微信tips消息失败,result={}", result);
                return "";
            }
            String data = transDTO.getData();
            return data;
        } catch (Exception e) {
            log.error("发送企业微信tips消息失败！", e);
            return "";
        }
    }

    /**
     * 发邮件
     * @param title
     * @param receivers
     * @param content
     * @param prepareSendTime
     * @param actType
     * @param actId
     * @return
     */
    public String SendMail(String title,String receivers, String content, Date prepareSendTime, String actType, String actId){
        final String api = "/api/v1/common/service/message/send-mail";
        //参数
        Map<String, Object> param = new HashMap<>();
        param.put("title", title);
        param.put("content",content);
        param.put("receiver", receivers);
        param.put("sender", "ACADEMY");
        param.put("can_send", true);
        param.put("prepare_send_time", prepareSendTime);
        param.put("act_id", actId);
        param.put("act_type", actType);
        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        //发送请求
        try {
            String result = baseProxyService.postForApiWithHeaders(host, api, param, headers);
            //处理结果
            TransDTO<String> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<String>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                log.error("发送邮件消息失败,result={}", result);
                return "";
            }
            String data = transDTO.getData();
            return data;
        } catch (Exception e) {
            log.error("发送邮件消息失败！", e);
            return "";
        }
    }

    /**
     * hrAssistant
     * @param title
     * @param receivers
     * @param content
     * @param prepareSendTime
     * @param actType
     * @param actId
     * @return
     */
    public String SendHrAssistant(String title,String receivers, String content, Date prepareSendTime, String actType, String actId) {
        final String api = "/api/v1/common/service/message/send-hr-assistant";
        //参数
        Map<String, Object> param = new HashMap<>();
        param.put("title", title);
        param.put("content",content);
        param.put("receiver", receivers);
        param.put("sender", "ACADEMY");
        param.put("can_send", true);
        param.put("prepare_send_time", prepareSendTime);
        param.put("act_id", actId);
        param.put("act_type", actType);
        //构建参数请求体
        String appCode = appId;
        String appKey = appToken;
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appCode, appKey);
        //发送请求
        try {
            String result = baseProxyService.postForApiWithHeaders(host, api, param, headers);
            //处理结果
            TransDTO<String> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<String>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || !transDTO.getSuccess()) {
                log.error("发送邮件消息失败,result={}", result);
                return "";
            }
            String data = transDTO.getData();
            return data;
        } catch (Exception e) {
            log.error("发送邮件消息失败！", e);
            return "";
        }
    }

    /**
     * 创建(发送)MyOa消息
     * @param item
     * @return
     */
    public String SendMyOa(MyOaWorkItemDto item){
        final String api = "/api/v1/common/service/message/send-myoa";
        //参数
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appId, appToken);
        //发送请求
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String result = baseProxyService.postJsonStrWithHeaders2(host, api, objectMapper.writeValueAsString(item), headers);
            //处理结果
            TransDTO<String> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<String>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || Boolean.TRUE.equals(!transDTO.getSuccess())) {
                log.error("发送MyOa消息失败,result={}", result);
                return "";
            }
            return transDTO.getData();
        } catch (Exception e) {
            log.error("发送MyOa消息失败！", e);
            return "";
        }
    }

    /**
     * 关闭MyOa消息
     * @param closeMyOa
     * @return
     */
    public String CloseMyOa(MyOaCloseAndDiscardDto closeMyOa){
        if (closeMyOa.getCategory()==null){
            // 默认 人事
            closeMyOa.setCategory(myoaCategory);
        }
        if (closeMyOa.getProcessName()==null){
            // 默认 mooc
            closeMyOa.setProcessName("mooc");
        }
        final String api = "/api/v1/common/service/message/close-myoa";
        //参数
        HttpHeaders headers = baseProxyService.getESBHeaderSimple(appId, appToken);
        //发送请求
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String result = baseProxyService.postJsonStrWithHeaders2(host, api, objectMapper.writeValueAsString(closeMyOa), headers);
            //处理结果
            TransDTO<String> transDTO = JsonUtils.jsonToBean(result, new TypeReference<TransDTO<String>>() {
            });
            //请求失败
            if (StringUtils.isBlank(result) || null == transDTO || Boolean.TRUE.equals(!transDTO.getSuccess())) {
                log.error("关闭MyOa消息失败,result={}", result);
            }
            return transDTO.getData();
        } catch (Exception e) {
            log.error("关闭MyOa消息失败！", e);
            return null;
        }
    }


}
