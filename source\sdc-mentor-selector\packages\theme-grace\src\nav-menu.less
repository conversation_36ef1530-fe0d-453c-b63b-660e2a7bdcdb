@import "./vars.less";
@import "./mixins.less";
@import "./link.less";

.sdc-nav-menu {
  .el-menu{
    background-color: @color-transparent;
    &.el-menu--horizontal{
      border-bottom: none;
    }
    .el-submenu,.el-submenu.is-active{
      .el-submenu__title{
        border-bottom:none;
        background-color: @color-transparent !important;
        color: @color-text-white !important;
        margin-left: -20px;
        [class*="el-icon-"]{
          color: @color-text-white;
        }
        .el-submenu__icon-arrow{
          display: none;
        }
      }
    }
  }
}

.sdc-drop-menu .el-dropdown-menu__item {
  padding: 0;

  .nav-item {
    width: 100%;
    padding: 0 20px;
  }
}

.sdc-nav-menu-popper{
  .el-menu {
    padding: 10px 0;
    .el-menu-item,
    .el-submenu__title{
      position: relative;
      padding-left: 55px;
      color: @color-text-dark;
      i{
        color: @color-text-dark;
        &:first-child:not(.el-submenu__icon-arrow){
          position: absolute;
          left: 30px;
          top: 50%;
          transform: translateY(-50%);
          font-size: @font-16;
        }
      }
    }
    .is-opened>.el-submenu__title,
    .el-menu-item:hover,
    .el-submenu__title:hover{
      .font-color(@color-theme) !important;
      background-color: @color-bg-hover;
      [class*="el-icon-"]{
        .font-color(@color-theme);
      }
    }
  }
}
