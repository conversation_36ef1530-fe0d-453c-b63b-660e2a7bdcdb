package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.WechatArticle;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface WechatArticleMapper extends BaseMapper<WechatArticle> {
    int deleteByPrimaryKey(Integer articleId);

    int insert(WechatArticle record);

    int insertSelective(WechatArticle record);

    WechatArticle selectByPrimaryKey(Integer articleId);

    int updateByPrimaryKeySelective(WechatArticle record);

    int updateByPrimaryKey(WechatArticle record);
}