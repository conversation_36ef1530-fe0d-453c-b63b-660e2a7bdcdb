package com.tencent.hr.knowservice.businessCommon.proxy;

import com.tencent.hr.base.dto.TransDTO;
import com.tencent.hr.knowservice.businessCommon.dto.TransCustomDTO;
import com.tencent.hr.knowservice.graphic.dto.comment.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.security.auth.message.AuthException;
import java.util.Map;

@FeignClient(name = "portal-qlearning-adapter", url = "${project.qlearning-adapter-host}")
public interface AdapterServiceApi {
    /**
     * 获取用户的目标学员列表
     *
     * @return
     */
    @GetMapping("/api/adapter/common/user/get_user_targets")
    public Map<String, Object> getUserTargetList(@RequestParam("staffid") Integer staffId);

    /**
     * 判断是否点赞
     *
     * @param actType
     * @param actId
     * @param staffId
     * @return
     */
    @GetMapping("/api/adapter/common/praise/check-praised")
    public Map<String, Object> checkPraised(@RequestParam("act_type") String actType,
                                            @RequestParam("act_id") String actId,
                                            @RequestParam(value = "staff_id") Integer staffId);

    /**
     * 用户点赞
     *
     * @param actType
     * @param actId
     * @param staffId
     * @param staffName
     * @return
     */
    @GetMapping("/api/adapter/common/praise/add-praise")
    public Map<String, Object> addPraise(@RequestParam("act_type") String actType,
                                         @RequestParam("act_id") String actId,
                                         @RequestParam(value = "staff_id") Integer staffId,
                                         @RequestParam(value = "staff_name") String staffName);

    /**
     * 用户取消点赞
     *
     * @param actType
     * @param actId
     * @param staffId
     * @return
     */
    @GetMapping("/api/adapter/common/praise/delete-praise")
    public Map<String, Object> deletePraise(@RequestParam("act_type") String actType,
                                            @RequestParam("act_id") String actId,
                                            @RequestParam(value = "staff_id") Integer staffId);

    /**
     * 判断是否收藏
     *
     * @param actType
     * @param actId
     * @param staffId
     * @return
     */
    @GetMapping("/api/adapter/common/favorite/check-favorited")
    public Map<String, Object> checkFavorited(@RequestParam("act_type") String actType,
                                              @RequestParam("act_id") String actId,
                                              @RequestParam(value = "staff_id") Integer staffId
    );

//    /**
//     * 获取用户是否收藏
//     *
//     * @param query
//     * @return
//     * @throws AuthException
//     */
//    @PostMapping("/api/adapter/common/favorite/check-favorited-batch")
//    public Map<String,Object> checkFavoritedBatch(@RequestBody CheckFavoriteDto query);

    /**
     * 添加收藏
     *
     * @param actType
     * @param actId
     * @param staffId
     * @param staffName
     * @return
     * @throws AuthException
     */
    @GetMapping("/api/adapter/common/favorite/add-favorite")
    public Map<String, Object> addFavorite(@RequestParam("act_type") String actType,
                                           @RequestParam("act_id") String actId,
                                           @RequestParam("act_name") String actName,
                                           @RequestParam(value = "staff_id") Integer staffId,
                                           @RequestParam(value = "staff_name") String staffName
    );


    /**
     * 取消收藏
     *
     * @param actType
     * @param actId
     * @param staffId
     * @return
     * @throws AuthException
     */
    @GetMapping("/api/adapter/common/favorite/delete-favorite")
    public Map<String, Object> deleteFavorite(@RequestParam("act_type") String actType,
                                              @RequestParam("act_id") String actId,
                                              @RequestParam(value = "staff_id") Integer staffId
    );

    /**
     * 获取分类下的标签
     *
     * @param classifyFullPath
     * @return
     * @throws AuthException
     */
    @GetMapping("/api/adapter/common/label/getlabels")
    public String getLabels(@RequestParam("classify_full_path") String classifyFullPath,
                            @RequestParam(value = "act_type", required = false) String actType);

    /**
     * 保存标签
     *
     * @param labelName
     * @return
     * @throws AuthException
     */
    @GetMapping("/api/adapter/common/label/savelabel")
    public String saveLabel(@RequestParam("label_name") String labelName,
                            @RequestParam(value = "classify_id", required = false) String classifyId,
                            @RequestParam(value = "act_type", required = false) String actType
    );

    /**
     * 获取标签
     *
     * @param labelName
     * @return
     * @throws AuthException
     */
    @GetMapping("/api/adapter/common/label/auto-com-labels")
    public String autoComLabels(@RequestParam("label_name") String labelName,
                                @RequestParam(value = "act_type", required = false) String actType,
                                @RequestParam("count") String count);

    /**
     * 新增评论
     *
     * @return
     */
    @PostMapping("/api/adapter/common/comment/add")
    TransCustomDTO addComment(@RequestBody ActCommentDto actCommentDto);

    /**
     * 获取评论列表
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/api/adapter/common/comment/get_comments")
    TransDTO getComments(@RequestBody ActCommentDto actCommentDto);

    /**
     * 评论删除
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/api/adapter/common/comment/delete")
    TransDTO deleteComment(@RequestBody ActDeleteCommentDto actCommentDto);

    /**
     * 评论隐藏
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/api/adapter/common/comment/show")
    TransDTO showComment(@RequestBody ActShowCommentDto actCommentDto);

    /**
     * 评论点赞
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/api/adapter/common/comment/praised")
    TransDTO praisedComment(@RequestBody ActPraisedCommentDto actCommentDto);

    /**
     * 评论置顶
     *
     * @param actCommentDto
     * @return
     */
    @PostMapping("/api/adapter/common/comment/sticky")
    TransDTO stickyComment(@RequestBody ActStickyCommentDto actCommentDto);

    /**
     * 获取评论数量
     *
     * @param actId
     * @return
     */
    @GetMapping("/api/adapter/common/comment/get_comment_count")
    TransDTO getCommentCount(@RequestParam("act_id") String actId);


    /**
     *
     */
    @PostMapping("/api/adapter/common/comment/add-study-record")
    TransDTO addStudyRecord(@RequestParam("act_id") Integer actId,
                            @RequestParam("my_study_progress") Integer myStudyProgress,
                            @RequestParam("is_finish") Integer isFinish,
                            @RequestParam("is_review") Integer isReview,
                            @RequestParam("total_study_time") Integer totalStudyTime,
                            @RequestParam("learn_record_id") Integer learnRecordId,
                            @RequestParam("from") String from,
                            @RequestParam("area_id") String areaId,
                            @RequestParam("staff_id") Integer staffId,
                            @RequestParam("staff_name") String staffName);
}
