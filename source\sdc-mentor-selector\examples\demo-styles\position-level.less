.demo-position-level {
  .block {
    padding: 30px 0;
    border-right: solid 1px #eff2f6;
    width: 50%;
    box-sizing: border-box;
    text-align: center;
    &:first-child {
      padding-right: 20px;
    }
    &:last-child {
      border-right: none;
      padding-left: 20px;
    }
    &.block-3{
      width: 33%;
      &:nth-child(2) {
        padding-left: 20px;
        padding-right: 20px;
      }
      .sdc-position-level{
        width: 100%;
      }
    }
  }
  .demonstration {
    text-align: center;
    display: block;
    color: #8492a6;
    font-size: 14px;
    margin-bottom: 20px;
  }
  .sdc-position-level{
    width: 390px;
  }
}

.demo-position-level .source > div {
  display: flex;
}