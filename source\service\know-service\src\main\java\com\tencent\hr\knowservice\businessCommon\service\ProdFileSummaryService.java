package com.tencent.hr.knowservice.businessCommon.service;

import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【prod_file_summary】的数据库操作Service实现
* @createDate 2023-06-06 10:25:20
*/
@Service
public class ProdFileSummaryService {

//    @Autowired
//    private ProdFileSummaryMapper prodFileSummaryMapper;
//
//    List<ProdFileSummary> getProdFileSummary(List<Integer> ids) {
//
//        if (CollectionUtils.isEmpty(ids)) {
//            return new ArrayList<>();
//        }
//
//        QueryWrapper<ProdFileSummary> queryWrapper = new QueryWrapper<>();
//        queryWrapper.in("prod_file_id", ids);
//
//        List<ProdFileSummary> fileSummaries = prodFileSummaryMapper.selectList(queryWrapper);
//        return fileSummaries;
//    }

}




