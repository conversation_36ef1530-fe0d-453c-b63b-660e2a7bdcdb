package com.tencent.hr.knowservice.businessCommon.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tencent.hr.knowservice.businessCommon.dao.entity.ActClassAttendance;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActClassAttendanceMapper extends BaseMapper<ActClassAttendance> {
    int deleteByPrimaryKey(Integer attId);

    int insert(ActClassAttendance record);

    int insertSelective(ActClassAttendance record);

    ActClassAttendance selectByPrimaryKey(Integer attId);

    int updateByPrimaryKeySelective(ActClassAttendance record);

    int updateByPrimaryKey(ActClassAttendance record);

    List<ActClassAttendance> findAttendancesByTypeAndCourseId(@Param("actType") Integer actType, @Param("classId") Integer classId);
}