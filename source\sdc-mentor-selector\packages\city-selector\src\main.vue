<template>
  <SdcCascader ref="cascader" :getData="getData" :map="map" :level="level" :value.sync="internalValue" v-bind="$attrs" v-on="$listeners" class="sdc-city-selector"/>
</template>

<script>
import SdcCascader from 'packages/cascader'
import cityService from 'api/city.service'
  export default {
    name: 'sdc-city-selector',
    components: { SdcCascader },
    props: {
      promise: Promise,
      value: {
        require: true
      },
      map: Object,
      level: {
        type: Number,
        default: 2
      }
    },
    computed: {
      cascaderProps() {
        const config = this.map || {}
        return {
          multiple: config.multiple !== false,
          emitPath: config.emitPath === true,
          value: config.value || 'value',
          label: config.label || 'label',
          children: config.children || 'children'
        }
      },
      internalValue: {
        get() {
          return this.value
        },
        set(newValue) {
          this.$emit('input', newValue)
        }
      }
    },
    methods: {
      async getData() {
        try {
          const result = this.promise ? await this.promise : await cityService.getList()
          // 内置数据源一二级id有相同的，需要转换
          if (!this.promise && !this.cascaderProps.emitPath && this.level === 2) {
            result.forEach(item => {
              item[this.cascaderProps.value] = `prefix0-${item[this.cascaderProps.value]}`
            })
          }
          return result
        } catch (error) {
          return []
        }
      },
      getCheckedNodes(leafOnly = false) {
        return this.$refs.cascader.getCheckedNodes(leafOnly)
      },
      clearSelected() {
        this.$refs.cascader.clearSelected()
      }
    }
  }
</script>
